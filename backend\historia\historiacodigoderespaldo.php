<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);

session_start();

//require_once '../config/database.php';

require_once __DIR__ . '/../config/database.php';


$CLAVEPAC = '';
$CEDULA = '';
$NOMBRE = '';
$SEXO = '';
$EDAD = '';
$current_visit_count = 0; // Nueva variable para el conteo de visitas

// Lógica para obtener/mantener el paciente seleccionado
if (isset($_GET['CLAVEPAC']) && !empty($_GET['CLAVEPAC'])) {
    $CLAVEPAC = $_GET['CLAVEPAC'];
    $CEDULA = $_GET['CEDULA'] ?? '';
    $_SESSION['CLAVEPAC'] = $CLAVEPAC;
    $_SESSION['CEDULA'] = $CEDULA;
    $_SESSION['SEXO_PACIENTE'] = $_GET['SEXO'] ?? '';
} else if (isset($_SESSION['CLAVEPAC']) && !empty($_SESSION['CLAVEPAC'])) {
    $CLAVEPAC = $_SESSION['CLAVEPAC'];
    $CEDULA = $_SESSION['CEDULA'] ?? '';
    $SEXO = $_SESSION['SEXO_PACIENTE'] ?? '';
} else {
    header('Location: buscar_paciente.php');
    exit;
}

// Cargar detalles completos del paciente y el número de visitas actual
if ($CLAVEPAC) {
    $stmt = $pdo->prepare("SELECT NOMBREAPELLIDO, SEXO, FECHANAC, CEDULA, VISITA FROM PACIENTES WHERE CLAVE = ?");
    $stmt->execute([$CLAVEPAC]);
    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($paciente) {
        $NOMBRE = $paciente['NOMBREAPELLIDO'];
        $SEXO = $paciente['SEXO'];
        $iconoSexo = strtoupper(substr($SEXO ?? '', 0, 1)) === 'F' ? '♀️' : (strtoupper(substr($SEXO ?? '', 0, 1)) === 'M' ? '♂️' : '⚧️');

        $_SESSION['CLAVEPAC'] = $CLAVEPAC;
        $_SESSION['CEDULA'] = $paciente['CEDULA'];
        $_SESSION['SEXO_PACIENTE'] = $SEXO;
        $CEDULA = $paciente['CEDULA'];

        if (!empty($paciente['FECHANAC'])) {
            $nac = new DateTime($paciente['FECHANAC']);
            $hoy = new DateTime();
            $EDAD = $hoy->diff($nac)->y . ' años';
        }
        $current_visit_count = (int)$paciente['VISITA']; // Obtener el número de visita
    } else {
        unset($_SESSION['CLAVEPAC']);
        unset($_SESSION['CEDULA']);
        unset($_SESSION['SEXO_PACIENTE']);
        header('Location: buscar_paciente.php');
        exit;
    }
} else {
    header('Location: buscar_paciente.php');
    exit;
}

$esMujer = (strtoupper(substr($_SESSION['SEXO_PACIENTE'] ?? '', 0, 1)) === 'F');
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Historia Clínica</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { overflow: hidden; }
        #menu { height: 100vh; overflow-y: auto; }
        #contenido { height: 100vh; overflow-y: auto; border-left: 1px solid #ccc; }
        .nav-tree a { cursor: pointer; }
        .nav-link.active { background-color: #0d6efd; color: #fff !important; }
        .submenu { display: none; }
        .nav-item.expanded .submenu { display: block; }
        .info-paciente { font-size: 0.9rem; color: #555; }
        /* Estilos para el nuevo botón */
        #btnMarcarVisita {
            background-color: #28a745; /* Verde */
            color: white;
            border: none;
            padding: 8px 12px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 14px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 5px;
            width: 100%;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <div class="col-md-3 bg-light" id="menu">
            <h4 class="mt-3">Historia Clínica</h4>
            <div class="info-paciente mb-3">
                <strong><?= htmlspecialchars($NOMBRE) ?></strong><br>
                <?= $iconoSexo ?? '' ?> <?= $EDAD ?> <br>
                CI: <?= htmlspecialchars($CEDULA) ?>
            </div>

            
           <div class="mb-2">
                <button type="button" id="btnMarcarVisita">Visita # <span id="numVisitaActual"><?= $current_visit_count ?></span></button>
            </div>

            <div class="d-grid gap-2 mb-3"> <a href="buscar_paciente.php" id="btnCambiarPaciente" class="btn btn-secondary btn-sm">
                    <i class="bi bi-person-plus"></i> Cambiar Paciente
                </a>
                <a href="../../index.php" id="btnMenuPrincipal" class="btn btn-info btn-sm">
                    <i class="bi bi-house"></i> Ir a Menú Principal
                    
                    
                    
                </a>
            </div>

            <ul class="nav flex-column nav-tree">
                <li class="nav-item expanded">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ ANTECEDENTES</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('antecedentes.php', this)">Personales-Familiares</a></li>
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=enfermedad_actual&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Enfermedad Actual</a></li>
                        <?php if ($esMujer): ?>
                            <li><a class="nav-link" onclick="cargar('gineco_obstetrico.php', this)">Ant. Gineco-Obstétrico</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ EXAMEN FÍSICO</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('examen_fisico.php', this)">Examen físico</a></li>
                        <?php if ($esMujer): ?>
                            <li><a class="nav-link" onclick="cargar('obstetricia.php', this)">Obstetricia</a></li>
                            <li><a class="nav-link" onclick="cargar('colposcopia.php', this)">Colposcopía</a></li>
                        <?php endif; ?>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ RESULTADOS DE LABORATORIO</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('hemograma.php', this)">Hemograma General</a></li>
                        <li><a class="nav-link" onclick="cargar('examenorina.php', this)">Orina I</a></li>
                        <li><a class="nav-link" onclick="cargar('coprologico.php', this)">Coprológico</a></li>
                        <li><a class="nav-link" onclick="cargar('bioquimico.php', this)">Bioquímico</a></li>
                        <li><a class="nav-link" onclick="cargar('archivos_subidos.php', this)">Archivos Subidos</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ PRUEBAS COMPLEMENTARIAS</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=complementarias&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Pruebas Complementarias</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ DIAGNÓSTICO</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=diagnostico&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Diagnóstico Médico</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ TRATAMIENTO</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=tratamientos&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Tratamientos</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ SEGUIMIENTO</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=seguimiento&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Seguimiento</a></li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link fw-bold" onclick="toggleMenu(this)">➤ INDICACIONES</a>
                    <ul class="ms-3 submenu">
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=estudios&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Estudios</a></li>
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=informes&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Informes</a></li>
                        <li><a class="nav-link" onclick="cargar('modulo_texto_simple1.php?TABLA=prescripcion&CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>', this)">Prescripción</a></li>
                        <li><a class="nav-link" onclick="cargar('certificado.php', this)">Certificado Médico</a></li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="col-md-9" id="contenido">
            <iframe id="iframeContenido" src="" style="width: 100%; height: 100%; border: none;"></iframe>
        </div>
    </div>
</div>

<script>
   const clave = <?= json_encode($CLAVEPAC) ?>;
   const cedula = <?= json_encode($CEDULA) ?>;
    let hasActivity = false; // Flag para detectar actividad en los iframes
    let visitCounted = false; // Flag para saber si la visita ya se ha contado en la sesión actual

    // Función para actualizar el contador de visitas en el botón
    function updateVisitaButton(count) {
        document.getElementById('numVisitaActual').textContent = count;
    }

    // --- Función para cargar contenido en el iframe ---
    function cargar(pagina, elemento) {
        let url = pagina;

        // Si la URL no tiene parámetros, agregamos los datos del paciente
        if (!pagina.includes('CLAVEPAC=')) {
            const sep = pagina.includes('?') ? '&' : '?';
            url += `${sep}CLAVEPAC=${clave}&CEDULA=${cedula}`;
        }

        document.getElementById('iframeContenido').src = url;

        document.querySelectorAll('.nav-tree .nav-link').forEach(link => {
            link.classList.remove('active', 'bg-primary', 'text-white');
        });

        if (elemento) {
            elemento.classList.add('active', 'bg-primary', 'text-white');
        }
    }

    // --- Lógica del menú plegable ---
    function toggleMenu(link) {
        // Cierra todos los menús abiertos
        document.querySelectorAll('.nav-item.expanded').forEach(item => {
            if (item !== link.closest('.nav-item')) { // No cerrar el actual si ya está expandido
                item.classList.remove('expanded');
            }
        });

        // Expande o contrae el menú actual
        const item = link.closest('.nav-item');
        item.classList.toggle('expanded'); // Toggle para expandir/contraer

        // Si se expande, carga automáticamente el primer hijo
        if (item.classList.contains('expanded')) {
            const primerHijo = item.querySelector('.submenu .nav-link');
            if (primerHijo) {
                const match = primerHijo.getAttribute('onclick').match(/'([^']+)'/);
                if (match && match[1]) {
                    cargar(match[1], primerHijo);
                }
            }
        }
    }

    // --- Lógica para detectar actividad en los iframes ---
    function setupIframeActivityDetection() {
        const iframe = document.getElementById('iframeContenido');
        iframe.onload = function() {
            try {
                // Acceder al contenido del iframe (solo si es del mismo origen)
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                // Añadir event listeners a todos los campos de entrada dentro del iframe
                iframeDoc.querySelectorAll('input, textarea, select').forEach(element => {
                    element.addEventListener('input', function() {
                        hasActivity = true;
                        // console.log('Actividad detectada en iframe'); // Para depuración
                    });
                    element.addEventListener('change', function() {
                        hasActivity = true;
                        // console.log('Actividad detectada en iframe (change)'); // Para depuración
                    });
                });

                // Opcional: Escuchar mensajes del iframe si los sub-formularios son complejos
                // y necesitan comunicar actividad de forma explícita.
                // iframe.contentWindow.postMessage('parent_ready_for_activity', '*');

            } catch (e) {
                // Manejar error de Same-Origin Policy si el iframe carga contenido de otro dominio
                console.warn('No se pudo acceder al contenido del iframe para detectar actividad (Same-Origin Policy o iframe aún no cargado):', e);
            }
        };
    }

    // --- Función para llamar al servidor y actualizar la visita ---
    function incrementarVisita(callback = null) {
        if (visitCounted) {
            // alert('La visita ya ha sido registrada en esta sesión.');
            if (callback) callback();
            return;
        }

        // Si no hay actividad Y el usuario NO ha confirmado
        if (!hasActivity && !confirm('No se detectaron cambios en el expediente. ¿Desea marcarla como visita real de todas formas?')) {
            if (callback) callback();
            return; // El usuario canceló
        }

        fetch('update_visita.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
           
             body: 'id_paciente=' + clave + '&cedula_paciente=' + encodeURIComponent(cedula)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Visita real registrada exitosamente. Visita # ' + data.new_visit_count);
                updateVisitaButton(data.new_visit_count);
                hasActivity = false; // Resetear la actividad
                visitCounted = true; // Marcar que la visita ya se contó para esta sesión
            } else {
                alert('Error al registrar la visita: ' + data.message);
                console.error('Error:', data.error);
            }
            if (callback) callback();
        })
        .catch(error => {
            console.error('Error en la solicitud AJAX para incrementar visita:', error);
            alert('Error de conexión al intentar registrar la visita.');
            if (callback) callback();
        });
    }

    // --- Event Listeners ---

    // Al cargar la página, configurar la detección de actividad en el iframe y expandir el primer menú
    window.onload = () => {
        setupIframeActivityDetection(); // Configurar detección de actividad en iframe
        const primero = document.querySelector('.nav-item.expanded .submenu .nav-link');
        if (primero) {
            const match = primero.getAttribute('onclick').match(/'([^']+)'/);
            if (match && match[1]) {
                cargar(match[1], primero);
            }
        }
    };

    // Evento click para el botón "Marcar Visita"
    document.getElementById('btnMarcarVisita').addEventListener('click', function() {
        incrementarVisita();
    });

    // Evento click para el botón "Cambiar Paciente"
    document.getElementById('btnCambiarPaciente').addEventListener('click', function(e) {
        // Prevenir la redirección inmediata
        e.preventDefault();

        // Si hubo actividad y la visita no fue contada
        if (hasActivity && !visitCounted) {
            if (confirm('Se detectaron cambios. ¿Desea marcar esta interacción como visita real antes de cambiar de paciente?')) {
                incrementarVisita(() => {
                    window.location.href = this.href; // Redirigir después de intentar marcar
                });
            } else {
                window.location.href = this.href; // Redirigir sin marcar
            }
        } else {
            window.location.href = this.href; // No hay actividad o ya se contó, redirigir directamente
        }
    });

    // Manejo de cierre de ventana (menos fiable con iframes y AJAX, pero se intenta)
    window.addEventListener('beforeunload', function (e) {
        if (hasActivity && !visitCounted) {
            // Esto es problemático con fetch/XMLHttpRequest asíncronos en beforeunload.
            // Para garantizar que la petición se envíe antes de que la página se cierre
            // completamente, navigator.sendBeacon() es la mejor opción para analytics/logs.
            // Para una actualización de DB crítica, el clic explícito o el guardado es mejor.

            // Si se debe intentar de todas formas:
            // return confirm('Se detectaron cambios. ¿Desea marcar esta interacción como visita real antes de salir?');
            // Si el usuario confirma, puedes enviar la solicitud de forma síncrona
            // (desaconsejado por bloquear la UI) o usar sendBeacon si solo es para "log".

            // Opción con sendBeacon (solo para POST sin esperar respuesta, ideal para logs/flags)
            // No mostrará confirmación al usuario para marcar la visita.
            // Si necesitas confirmación, la lógica de 'Cambiar Paciente' es más adecuada.
            // navigator.sendBeacon('update_visita.php', new URLSearchParams({ id_paciente: clave }));
        }
    });

    // Nota: Es posible que necesites ajustar la URL de `update_visita.php`
    // y `get_current_visit_count.php` según la estructura real de tus carpetas.
    // Asumo que están en la misma carpeta que `historia_menu.php` o accesible en `/api/`.
    // Si están en 'api' sería: fetch('../api/update_visita.php', ...)

</script>
</body>
</html>