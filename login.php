<?php
//login.php
session_start(); // Iniciar la sesión para manejar datos del usuario

// Verificar si el usuario ya está logueado
if (!empty($_SESSION['usuario'])) {
    if (isset($_SESSION['rol'])) { // Añadido para seguridad
        $rol = trim($_SESSION['rol']); // Limpiar espacios en blanco
        if ($rol === 'paciente') {
            header('Location: paciente_panel.php');
            exit();
        } elseif ($rol === 'secretaria') { // AÑADIDO: Redirección para secretaria
            header('Location: secretaria_panel.php');
            exit();
        } elseif ($rol === 'doctor') { // AÑADIDO: Redirección para doctor
            header('Location: doctor_panel_v2.php');
            exit();
        }
    }
    header('Location: index.php'); // Redirigir por defecto si el rol no está definido o es otro
    exit();
}

///require_once 'backend/config/database.php'; // Conexión a la base de datos

//require __DIR__ . '/backend/config/database.php';
// index.php (línea 6 aprox.)
require_once __DIR__ . '/backend/config/database.php';

$error = ''; // Para mensajes de error

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Obtener y limpiar datos del formulario
    $usuario = trim($_POST['usuario'] ?? '');
    $password = $_POST['password'] ?? '';

    if ($usuario === '' || $password === '') {
        $error = "Todos los campos son obligatorios.";
    } else {
        // Buscar el usuario en la base de datos
        $stmt = $pdo->prepare("select * from usuarios where usuario = ?");
        $stmt->execute([$usuario]);
        $user = $stmt->fetch();

        // Validar contraseña y existencia
        if ($user && password_verify($password, $user['password'])) {

          // Guardar datos en sesión
        $_SESSION['usuario'] = $user['usuario'];
        $_SESSION['clave'] = $user['clave'];
        $_SESSION['rol'] = trim($user['role']); // Limpiar espacios en blanco

        // AÑADIDO: Establecer el nombre de usuario para el panel de la secretaria y otros roles
        // Asumimos que la columna 'nombre' existe y es devuelta por 'select *'.
        // Si no existe, usará el valor de 'usuario' (el login).
        $_SESSION['nombre_usuario'] = $user['nombre'] ?? $user['usuario'];


        if (trim($user['role']) === 'paciente') {
            $_SESSION['cedula'] = $user['cedula'] ?? '';

            if (!empty($user['cedula'])) {
                // Buscar nombre del paciente en PACIENTES
                $stmt = $pdo->prepare("SELECT NOMBREAPELLIDO FROM PACIENTES WHERE CEDULA = ?");
                $stmt->execute([$user['cedula']]);
                $paciente = $stmt->fetch(PDO::FETCH_ASSOC);
                $_SESSION['nombre_paciente'] = $paciente['NOMBREAPELLIDO'] ?? 'Paciente';
            } else {
                $_SESSION['nombre_paciente'] = 'Paciente';
            }

            header('Location: paciente_panel.php');
            exit;
        } elseif (trim($user['role']) === 'secretaria') { // AÑADIDO: Redirección para secretaria
            header('Location: secretaria_panel.php');
            exit;
        } elseif (trim($user['role']) === 'doctor') { // AÑADIDO: Redirección para doctor
            $_SESSION['nombre_completo'] = $user['nombre'] ?? $user['usuario'];
            header('Location: doctor_panel_v2.php');
            exit;
        } else {
            header('Location: index.php'); // Otros roles van a index
        }
        exit;    
            
            
            
            
            
        } else {
            $error = "Usuario o contraseña incorrectos.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Mi Consultorio</title>

    <!-- Estilos -->
    <link rel="stylesheet" href="public/assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <h2 class="login-title">Iniciar Sesión</h2>

            <form action="login.php" method="POST" autocomplete="on">
                <div class="form-group">
                    <label for="usuario">Usuario:</label>
                    <input type="text" id="usuario" name="usuario" placeholder="Ingresa tu usuario" required>
                </div>

                <div class="form-group">
                    <label for="password">Contraseña:</label>
                    <input type="password" id="password" name="password" placeholder="Ingresa tu contraseña" required>
                </div>

                <button type="submit" class="btn-login">Iniciar sesión</button>

                <div class="login-links">
                    <a href="forgot-password.php">¿Olvidaste tu contraseña?</a>
                    <a href="registrar_paciente.php" class="registro-link">¿Eres paciente nuevo? Regístrate aquí</a>
                </div>

                <?php if (!empty($error)): ?>
                    <div style="text-align: center; margin-top: 15px;">
                        <span style="color: red; font-weight: bold;"><?php echo $error; ?></span>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
</body>
</html>

