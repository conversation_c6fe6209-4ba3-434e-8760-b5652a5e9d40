<?php
require_once '../config/database.php';
$config = include '../config/config.php';

$mensaje_error = '';
$mensaje_exito = '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

if (isset($_GET['id'])) {
    $paciente_id = $_GET['id'];
    $stmt = $pdo->prepare("SELECT * FROM pacientes WHERE ID = ?");
    $stmt->execute([$paciente_id]);
    $paciente = $stmt->fetch();
    if (!$paciente) {
        $mensaje_error = 'Paciente no encontrado.';
    }
} else {
    $paciente = null;
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nombre = $_POST['nombre'];
    $apellido = $_POST['apellido'];
    $edad = $_POST['edad'];
    $cedula = $_POST['cedula'];

    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CEDULA = ?");
    $stmt->execute([$cedula]);
    $paciente_existente = $stmt->fetch();

    if ($paciente_existente && (!$paciente || $paciente['CLAVE'] != $paciente_existente['ID'])) {
        $mensaje_error = "¡Atención! La cédula ya está registrada con el nombre: " . $paciente_existente['NOMBRES'] . ". ¿Deseas actualizar el registro?";
    } else {
        if ($paciente) {
            $stmt = $pdo->prepare("UPDATE PACIENTES SET NOMBRES = ?, APELLIDOS = ?, EDAD = ?, CEDULA = ? WHERE CLAVE = ?");
            $stmt->execute([$nombre, $apellido, $edad, $cedula, $paciente_id]);
            $mensaje_exito = "Paciente actualizado con éxito.";
        } else {
            $stmt = $pdo->prepare("INSERT INTO PACIENTES (NOMBRES, APELLIDOS, EDAD, CEDULA) VALUES (?, ?, ?, ?)");
            $stmt->execute([$nombre, $apellido, $edad, $cedula]);
            $mensaje_exito = "Paciente registrado con éxito.";
        }
    }
}

if(isset($_GET['search']) && !empty($_GET['search'])){
    $search = $_GET['search'];
    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE NOMBRES LIKE ? OR APELLIDOS LIKE ? OR CEDULA LIKE ?");
    $stmt->execute(["%$search%", "%$search%", "%$search%"]);
    $pacientes = $stmt->fetchAll();
} else {
    $stmt = $pdo->query("SELECT * FROM PACIENTES");
    $pacientes = $stmt->fetchAll();
}
?>

<!DOCTYPE html>
<html lang="es" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Pacientes</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <style>
        body { font-family: 'Inter', sans-serif; }
        [x-cloak] { display: none !important; }
    </style>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="h-full" x-data="{ drawerOpen: <?php echo isset($_GET['id']) ? 'true' : 'false'; ?> }">
    <!-- Static sidebar for desktop -->
    <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div class="flex grow flex-col gap-y-5 overflow-y-auto border-r border-gray-200 bg-white px-6 pb-4">
            <div class="flex h-16 shrink-0 items-center">
                <img class="h-8 w-auto" src="https://tailwindui.com/img/logos/mark.svg?color=indigo&shade=600" alt="Your Company">
            </div>
            <nav class="flex flex-1 flex-col">
                <ul role="list" class="flex flex-1 flex-col gap-y-7">
                    <li>
                        <ul role="list" class="-mx-2 space-y-1">
                            <li>
                                <a href="#" class="bg-gray-50 text-indigo-600 group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold">
                                    <svg class="h-6 w-6 shrink-0" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                    </svg>
                                    Pacientes
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
        </div>
    </div>

    <div class="lg:pl-72">
        <div class="sticky top-0 z-40 lg:mx-auto lg:max-w-7xl lg:px-8">
            <div class="flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-0 lg:shadow-none">
                <button type="button" class="text-gray-700 lg:hidden">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                    </svg>
                </button>

                <!-- Separator -->
                <div class="h-6 w-px bg-gray-200 lg:hidden" aria-hidden="true"></div>

                <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                    <form class="relative flex flex-1" action="" method="GET">
                        <label for="search-field" class="sr-only">Buscar</label>
                        <svg class="pointer-events-none absolute inset-y-0 left-0 h-full w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z" clip-rule="evenodd" />
                        </svg>
                        <input 
                            id="search-field"
                            class="block h-full w-full border-0 py-0 pl-8 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
                            placeholder="Buscar..."
                            type="search"
                            name="search"
                            value="<?php echo htmlspecialchars($search); ?>"
                        >
                    </form>
                </div>
            </div>
        </div>

        <main class="py-10">
            <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
                <?php if ($mensaje_error): ?>
                    <div class="rounded-md bg-red-50 p-4 mb-4">
                        <div class="flex">
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800"><?php echo $mensaje_error; ?></h3>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($mensaje_exito): ?>
                    <div class="rounded-md bg-green-50 p-4 mb-4">
                        <div class="flex">
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800"><?php echo $mensaje_exito; ?></h3>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Table -->
                <div class="mt-4 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                                <table class="min-w-full divide-y divide-gray-300">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Nombre</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Apellido</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Edad</th>
                                            <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Cédula</th>
                                            <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                                <span class="sr-only">Editar</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-white">
                                        <?php if (empty($pacientes)): ?>
                                            <tr>
                                                <td colspan="5" class="px-3 py-4 text-sm text-gray-500 text-center">No se encontraron pacientes.</td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($pacientes as $paciente): ?>
                                                <tr class="hover:bg-gray-50">
                                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                                        <div class="flex items-center">
                                                            <div class="h-8 w-8 flex-shrink-0 rounded-full bg-gray-200 flex items-center justify-center">
                                                                <span class="text-xs font-medium text-gray-500">
                                                                    <?php echo substr($paciente['NOMBRES'], 0, 1); ?>
                                                                </span>
                                                            </div>
                                                            <div class="ml-4">
                                                                <?php echo htmlspecialchars($paciente['NOMBRES']); ?>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><?php echo htmlspecialchars($paciente['APELLIDOS']); ?></td>
                                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><?php echo htmlspecialchars($paciente['EDAD']); ?></td>
                                                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><?php echo htmlspecialchars($paciente['CEDULA']); ?></td>
                                                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                                                        <button 
                                                            @click="drawerOpen = true; window.history.pushState(null, '', '?id=<?php echo $paciente['ID']; ?>')" 
                                                            type="button" 
                                                            class="text-indigo-600 hover:text-indigo-900"
                                                        >
                                                            Editar
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Slide-over panel -->
                <div 
                    x-cloak
                    x-show="drawerOpen" 
                    class="relative z-50" 
                    aria-labelledby="slide-over-title" 
                    role="dialog" 
                    aria-modal="true"
                >
                    <!-- Background backdrop -->
                    <div 
                        x-show="drawerOpen"
                        x-transition:enter="ease-in-out duration-500"
                        x-transition:enter-start="opacity-0"
                        x-transition:enter-end="opacity-100"
                        x-transition:leave="ease-in-out duration-500"
                        x-transition:leave-start="opacity-100"
                        x-transition:leave-end="opacity-0"
                        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                    ></div>

                    <div class="fixed inset-0 overflow-hidden">
                        <div class="absolute inset-0 overflow-hidden">
                            <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                                <div 
                                    x-show="drawerOpen"
                                    x-transition:enter="transform transition ease-in-out duration-500"
                                    x-transition:enter-start="translate-x-full"
                                    x-transition:enter-end="translate-x-0"
                                    x-transition:leave="transform transition ease-in-out duration-500"
                                    x-transition:leave-start="translate-x-0"
                                    x-transition:leave-end="translate-x-full"
                                    class="pointer-events-auto w-screen max-w-md"
                                >
                                    <form method="POST" class="flex h-full flex-col divide-y divide-gray-200 bg-white shadow-xl">
                                        <div class="h-0 flex-1 overflow-y-auto">
                                            <div class="bg-indigo-700 px-4 py-6 sm:px-6">
                                                <div class="flex items-center justify-between">
                                                    <h2 class="text-base font-semibold leading-6 text-white" id="slide-over-title">
                                                        <?php echo isset($_GET['id']) ? 'Editar Paciente' : 'Nuevo Paciente'; ?>
                                                    </h2>
                                                    <div class="ml-3 flex h-7 items-center">
                                                        <button
                                                            type="button"
                                                            class="relative rounded-md bg-indigo-700 text-indigo-200 hover:text-white focus:outline-none focus:ring-2 focus:ring-white"
                                                            @click="drawerOpen = false; window.history.pushState(null, '', window.location.pathname)"
                                                        >
                                                            <span class="absolute -inset-2.5"></span>
                                                            <span class="sr-only">Close panel</span>
                                                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex flex-1 flex-col justify-between">
                                                <div class="divide-y divide-gray-200 px-4 sm:px-6">
                                                    <div class="space-y-6 pb-5 pt-6">
                                                        <div>
                                                            <label for="nombre" class="block text-sm font-medium leading-6 text-gray-900">Nombre</label>
                                                            <div class="mt-2">
                                                                <input 
                                                                    type="text" 
                                                                    name="nombre" 
                                                                    id="nombre" 
                                                                    required
                                                                    value="<?php echo isset($_GET['id']) ? htmlspecialchars($paciente['NOMBRES']) : ''; ?>"
                                                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                                >
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <label for="apellido" class="block text-sm font-medium leading-6 text-gray-900">Apellido</label>
                                                            <div class="mt-2">
                                                                <input 
                                                                    type="text" 
                                                                    name="apellido" 
                                                                    id="apellido" 
                                                                    required
                                                                    value="<?php echo isset($_GET['id']) ? htmlspecialchars($paciente['APELLIDOS']) : ''; ?>"
                                                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                                >
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <label for="edad" class="block text-sm font-medium leading-6 text-gray-900">Edad</label>
                                                            <div class="mt-2">
                                                                <input 
                                                                    type="number" 
                                                                    name="edad" 
                                                                    id="edad" 
                                                                    required
                                                                    value="<?php echo isset($_GET['id']) ? htmlspecialchars($paciente['EDAD']) : ''; ?>"
                                                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                                >
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <label for="cedula" class="block text-sm font-medium leading-6 text-gray-900">Cédula</label>
                                                            <div class="mt-2">
                                                                <input 
                                                                    type="text" 
                                                                    name="cedula" 
                                                                    id="cedula" 
                                                                    required
                                                                    value="<?php echo isset($_GET['id']) ? htmlspecialchars($paciente['CEDULA']) : ''; ?>"
                                                                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                                >
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex flex-shrink-0 justify-end px-4 py-4">
                                            <button
                                                type="button"
                                                class="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                                @click="drawerOpen = false; window.history.pushState(null, '', window.location.pathname)"
                                            >
                                                Cancelar
                                            </button>
                                            <button
                                                type="submit"
                                                class="ml-4 inline-flex justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                                            >
                                                <?php echo isset($_GET['id']) ? 'Actualizar' : 'Guardar'; ?>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>

