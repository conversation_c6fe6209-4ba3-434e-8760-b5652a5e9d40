<?php
// update_visita.php

header('Content-Type: application/json'); // Indicamos que la respuesta será JSON

// Incluir la conexión a la base de datos
require_once '../config/database.php'; // Asegúrate de que esta ruta sea correcta

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $CLAVEPAC = filter_input(INPUT_POST, 'id_paciente', FILTER_UNSAFE_RAW); 
    // NUEVO: Obtener la cédula del paciente del POST
    $CEDULA_PACIENTE = filter_input(INPUT_POST, 'cedula_paciente', FILTER_UNSAFE_RAW); 
    if (empty($CLAVEPAC)) {
        $response['message'] = 'ID de paciente no proporcionado.';
        echo json_encode($response);
        exit();
    }
    // Opcional: Si la cédula es estrictamente necesaria y no se recibió
    if (empty($CEDULA_PACIENTE)) {
        $response['message'] = 'Cédula del paciente no proporcionada.';
        echo json_encode($response);
        exit();
    }

    // --- Obtener el valor de CONSULTORIO de la tabla CONFIG ---
    $CONSULTORIO = null;
    try {
        $stmt_consultorio = $pdo->prepare("SELECT CONSULTORIO FROM CONFIG LIMIT 1");
        $stmt_consultorio->execute();
        $row_consultorio = $stmt_consultorio->fetch(PDO::FETCH_ASSOC);

        if ($row_consultorio) {
            $CONSULTORIO = (int)$row_consultorio['CONSULTORIO'];
        } else {
            $response['message'] = 'No se pudo obtener el ID del consultorio de la tabla CONFIG.';
            echo json_encode($response);
            exit();
        }
    } catch (PDOException $e) {
        $response['message'] = 'Error al obtener el consultorio de la tabla CONFIG: ' . $e->getMessage();
        $response['error'] = $e->getMessage();
        echo json_encode($response);
        exit();
    }
    // --- FIN Obtener el valor de CONSULTORIO ---


    // --- Variables restantes para la tabla VISITAS ---
    $DOCTOR_ID = 1;   // ID del doctor. Idealmente, desde la sesión del usuario.
    $MOTIVOVISITA = "Consulta General"; // Valor por defecto o de un campo en la UI.
    $FECHAVISITA = date('Y-m-d'); // Fecha actual.

    try {
        $pdo->beginTransaction();

        // 1. Obtener y actualizar el número de visita del paciente en la tabla PACIENTES
        $stmt_select = $pdo->prepare("SELECT VISITA FROM PACIENTES WHERE CLAVE = ?");
        $stmt_select->execute([$CLAVEPAC]);
        $paciente = $stmt_select->fetch(PDO::FETCH_ASSOC);

        if ($paciente) {
            $current_visit = (int)$paciente['VISITA'];
            $new_visit_count = $current_visit + 1;

            $stmt_update_paciente = $pdo->prepare("UPDATE PACIENTES SET VISITA = ? WHERE CLAVE = ?");
            $stmt_update_paciente->execute([$new_visit_count, $CLAVEPAC]);

            // 2. Insertar un nuevo registro en la tabla VISITAS
            // NUEVO: Añadir 'CEDULA' a la lista de columnas y al VALUES
            $stmt_insert_visita = $pdo->prepare(
                "INSERT INTO VISITAS (CLAVEPAC, CONSULTORIO, DOCTOR, MOTIVOVISITA, FECHAVISITA, CEDULA)
                 VALUES (?, ?, ?, ?, ?, ?)"
            );
            $stmt_insert_visita->execute([
                $CLAVEPAC,
                $CONSULTORIO,
                $DOCTOR_ID,
                $MOTIVOVISITA,
                $FECHAVISITA,
                $CEDULA_PACIENTE // Usamos la cédula obtenida del POST
            ]);

            $pdo->commit();

            $response['success'] = true;
            $response['message'] = 'Visita y registro en tabla VISITAS guardados con éxito.';
            $response['new_visit_count'] = $new_visit_count;

        } else {
            $pdo->rollBack();
            $response['message'] = 'Paciente no encontrado.';
        }

    } catch (PDOException $e) {
        $pdo->rollBack();
        $response['message'] = 'Error en la base de datos: ' . $e->getMessage();
        $response['error'] = $e->getMessage();
    } catch (Exception $e) {
        $pdo->rollBack();
        $response['message'] = 'Error inesperado: ' . $e->getMessage();
        $response['error'] = $e->getMessage();
    }
} else {
    $response['message'] = 'Método de solicitud no permitido.';
}

echo json_encode($response);
exit();