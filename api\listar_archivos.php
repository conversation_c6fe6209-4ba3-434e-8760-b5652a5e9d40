<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

//include_once '../config/config.php';

header('Content-Type: application/json');
require_once __DIR__ . '/../config/config.php';

 $conn = getDBConnection();
$conn->set_charset("utf8");

// Verificar la conexión
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Obtener la cedula del parámetro de la URL
$cedula = isset($_GET['cedula']) ? $_GET['cedula'] : '';

// Verificar si la cédula está vacía
if (empty($cedula)) {
    echo json_encode(['status' => 'Error', 'message' => 'Cédula no proporcionada']);
    exit;
}


// Consultar la base de datos para obtener los archivos relacionados con la cédula, ordenados por fecha_subida de manera descendente
$sql = "SELECT CLAVE, CEDULA, ARCHIVO, DESCRIPCION, FECHA_SUBIDA 
        FROM RESULTADOS_PACIENTES 
        WHERE CEDULA = ? 
        ORDER BY FECHA_SUBIDA DESC";  // Ordenar por fecha_subida en orden descendente

$stmt = $conn->prepare($sql);
$stmt->bind_param("s", $cedula);
$stmt->execute();
$result = $stmt->get_result();

// Verificar si se encontraron resultados
if ($result->num_rows > 0) {
    $archivos = [];
    while ($row = $result->fetch_assoc()) {
        $archivos[] = $row;  // Agregar el archivo encontrado al array
    }
    // Devolver la lista de archivos en formato JSON
    echo json_encode(['status' => 'Success', 'data' => $archivos]);
} else {
    // No se encontraron archivos
    echo json_encode(['status' => 'Error', 'message' => 'No se encontraron archivos para esta cédula']);
}

// Cerrar la conexión
$conn->close();
?>
