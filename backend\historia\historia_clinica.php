<?php
// Iniciar sesión

ini_set('display_errors', 1);      // ¡solo en desarrollo!
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    // Redirigir a la página de selección de paciente
    header('Location: seleccionar_paciente.php');
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Conexión a la base de datos
//require_once '../config/database.php';

// index.php (línea 6 aprox.)
require_once __DIR__ . '/config/database.php';

// Función para obtener información del paciente
function getPatientInfo($pdo, $clavePac) {
    $sql = "SELECT CLAVE, NOMBRES, APELLIDOS, CEDULA, FECHANAC, SEXO, TELEFONO, ECORREO, ESTADOCIVIL, OCUPACION 
            FROM PACIENTES WHERE CLAVE = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Obtener datos del paciente
$paciente = getPatientInfo($pdo, $clavePac);

// Manejadores AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    // Manejar ANTECEDENTES
    if ($action === 'getAntecedentes') {
        $sql = "SELECT * FROM ANTECEDENTES WHERE CLAVEPAC = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$clavePac]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo json_encode($result ?: null);
        exit;
    }
    else if ($action === 'saveAntecedentes') {
        $familiares = $_POST['familiares'] ?? '';
        $personales = $_POST['personales'] ?? '';
        $patologicoFamiliares = $_POST['patologicoFamiliares'] ?? '';
        $habitos = $_POST['habitos'] ?? '';
        $patologicoPersonales = $_POST['patologicoPersonales'] ?? '';
        $vacunas = $_POST['vacunas'] ?? '';
        $alcohol = $_POST['alcohol'] ?? 'N';
        $cantAlcohol = $_POST['cantAlcohol'] ?? '';
        $desdeAlcohol = $_POST['desdeAlcohol'] ?? '';
        $fuma = $_POST['fuma'] ?? 'N';
        $cantFuma = $_POST['cantFuma'] ?? '';
        $desdeFuma = $_POST['desdeFuma'] ?? '';
        $droga = $_POST['droga'] ?? 'N';
        $cantDroga = $_POST['cantDroga'] ?? '';
        $desdeDroga = $_POST['desdeDroga'] ?? '';
        $tipoDroga = $_POST['tipoDroga'] ?? '';
        $altoRiesgo = $_POST['altoRiesgo'] ?? 'N';
        $causaAltoRiesgo = $_POST['causaAltoRiesgo'] ?? '';
        
        // Verificar si ya existe un registro
        $checkSql = "SELECT CLAVE FROM ANTECEDENTES WHERE CLAVEPAC = ?";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([$clavePac]);
        $checkResult = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        try {
            if ($checkResult) {
                // Actualizar registro existente
                $clave = $checkResult['CLAVE'];
                
                $sql = "UPDATE ANTECEDENTES SET 
                        FAMILIARES = ?, 
                        PERSONALES = ?, 
                        PATOLOGICOFAMILIARES = ?, 
                        HABITOS = ?, 
                        PATOLOGICOPERSONALES = ?, 
                        VACUNAS = ?, 
                        ALCOHOL = ?, 
                        CANTALCOHOL = ?, 
                        DESDEALCOHOL = ?, 
                        FUMA = ?, 
                        CANTFUMA = ?, 
                        DESDEFUMA = ?, 
                        DROGA = ?, 
                        CANTDROGA = ?, 
                        DESDEDROGA = ?, 
                        TIPODROGA = ?, 
                        ALTORIESGO = ?, 
                        CAUSAALTORIESGO = ? 
                        WHERE CLAVE = ?";
                        
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $familiares, $personales, $patologicoFamiliares, $habitos, 
                    $patologicoPersonales, $vacunas, $alcohol, $cantAlcohol, 
                    $desdeAlcohol, $fuma, $cantFuma, $desdeFuma, $droga, 
                    $cantDroga, $desdeDroga, $tipoDroga, $altoRiesgo, 
                    $causaAltoRiesgo, $clave
                ]);
            } else {
                // Insertar nuevo registro
                $sql = "INSERT INTO ANTECEDENTES (
                        CLAVEPAC, FAMILIARES, PERSONALES, PATOLOGICOFAMILIARES, 
                        HABITOS, PATOLOGICOPERSONALES, VACUNAS, ALCOHOL, 
                        CANTALCOHOL, DESDEALCOHOL, FUMA, CANTFUMA, DESDEFUMA, 
                        DROGA, CANTDROGA, DESDEDROGA, TIPODROGA, ALTORIESGO, CAUSAALTORIESGO) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $clavePac, $familiares, $personales, $patologicoFamiliares, 
                    $habitos, $patologicoPersonales, $vacunas, $alcohol, 
                    $cantAlcohol, $desdeAlcohol, $fuma, $cantFuma, $desdeFuma, 
                    $droga, $cantDroga, $desdeDroga, $tipoDroga, $altoRiesgo, 
                    $causaAltoRiesgo
                ]);
            }
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    
    // Manejar EXAMEN FISICO
    else if ($action === 'getExamenesFisicos') {
        $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
                PESO as peso, TALLA as talla, TAS as tas, TAD as tad, 
                FR as fr, FC as fc, PULSO as pulso, TEMP as temp, 
                HALLAZGOS as hallazgos, OTROS as otros, 
                IMC as imc, PERIMETRO_ABDOMINAL as perimetro_abdominal 
                FROM EXAMENFISICO WHERE CLAVEPAC = ? 
                ORDER BY FECHA_CAP DESC";
                
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$clavePac]);
        
        $examenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($examenes);
        exit;
    }
    else if ($action === 'getExamenFisico') {
        $id = $_POST['id'];
        
        $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
                PESO as peso, TALLA as talla, TAS as tas, TAD as tad, 
                FR as fr, FC as fc, PULSO as pulso, TEMP as temp, 
                HALLAZGOS as hallazgos, OTROS as otros, 
                IMC as imc, PERIMETRO_ABDOMINAL as perimetro_abdominal 
                FROM EXAMENFISICO WHERE CLAVE = ?";
                
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo json_encode($result ?: null);
        exit;
    }
    else if ($action === 'saveExamenFisico') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $peso = $_POST['peso'] ?? '';
        $talla = $_POST['talla'] ?? '';
        $tas = $_POST['tas'] ?? '';
        $tad = $_POST['tad'] ?? '';
        $fr = $_POST['fr'] ?? '';
        $fc = $_POST['fc'] ?? '';
        $pulso = $_POST['pulso'] ?? '';
        $temp = $_POST['temp'] ?? '';
        $hallazgos = $_POST['hallazgos'] ?? '';
        $otros = $_POST['otros'] ?? '';
        
        // Calcular IMC si se proporcionan peso y talla
        $imc = null;
        if (!empty($peso) && !empty($talla) && is_numeric($peso) && is_numeric($talla)) {
            $tallaMetros = $talla / 100;
            $imc = round($peso / ($tallaMetros * $tallaMetros), 2);
        }
        
        $perimetro_abdominal = $_POST['perimetro_abdominal'] ?? null;
        
        try {
            if ($id > 0) {
                // Actualizar registro existente
                $sql = "UPDATE EXAMENFISICO SET 
                        PESO = ?, TALLA = ?, TAS = ?, TAD = ?, 
                        FR = ?, FC = ?, PULSO = ?, TEMP = ?, 
                        HALLAZGOS = ?, OTROS = ?, 
                        IMC = ?, PERIMETRO_ABDOMINAL = ? 
                        WHERE CLAVE = ?";
                        
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $peso, $talla, $tas, $tad, $fr, $fc, 
                    $pulso, $temp, $hallazgos, $otros, 
                    $imc, $perimetro_abdominal, $id
                ]);
            } else {
                // Insertar nuevo registro
                $sql = "INSERT INTO EXAMENFISICO (
                        CLAVEPAC, PESO, TALLA, TAS, TAD, FR, FC, 
                        PULSO, TEMP, HALLAZGOS, OTROS, 
                        IMC, PERIMETRO_ABDOMINAL, FECHA_CAP) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
                        
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $clavePac, $peso, $talla, $tas, $tad, $fr, $fc, 
                    $pulso, $temp, $hallazgos, $otros, 
                    $imc, $perimetro_abdominal
                ]);
                
                $id = $pdo->lastInsertId();
            }
            
            // Obtener el registro actualizado/insertado
            $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
                    PESO as peso, TALLA as talla, TAS as tas, TAD as tad, 
                    FR as fr, FC as fc, PULSO as pulso, TEMP as temp, 
                    HALLAZGOS as hallazgos, OTROS as otros, 
                    IMC as imc, PERIMETRO_ABDOMINAL as perimetro_abdominal 
                    FROM EXAMENFISICO WHERE CLAVE = ?";
                    
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode($stmt->fetch(PDO::FETCH_ASSOC));
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteExamenFisico') {
        $id = $_POST['id'];
        
        try {
            $sql = "DELETE FROM EXAMENFISICO WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    
    // Manejar DIAGNOSTICO
    else if ($action === 'getDiagnosticos') {
        $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
                TEXTO as texto, CODIGO_CIE as codigo_cie 
                FROM DIAGNOSTICO 
                WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC";
                
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$clavePac]);
        
        $diagnosticos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($diagnosticos);
        exit;
    }
    else if ($action === 'getDiagnostico') {
        $id = $_POST['id'];
        
        $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
                TEXTO as texto, CODIGO_CIE as codigo_cie 
                FROM DIAGNOSTICO WHERE CLAVE = ?";
                
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo json_encode($result ?: null);
        exit;
    }
    else if ($action === 'saveDiagnostico') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $texto = $_POST['texto'] ?? '';
        $codigo_cie = $_POST['codigo_cie'] ?? null;
        
        try {
            if ($id > 0) {
                // Actualizar registro existente
                $sql = "UPDATE DIAGNOSTICO SET TEXTO = ?, CODIGO_CIE = ? WHERE CLAVE = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$texto, $codigo_cie, $id]);
            } else {
                // Insertar nuevo registro
                $sql = "INSERT INTO DIAGNOSTICO (CLAVEPAC, TEXTO, CODIGO_CIE, FECHA_CAP) VALUES (?, ?, ?, NOW())";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$clavePac, $texto, $codigo_cie]);
                
                $id = $pdo->lastInsertId();
            }
            
            // Obtener el registro actualizado/insertado
            $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
                    TEXTO as texto, CODIGO_CIE as codigo_cie 
                    FROM DIAGNOSTICO WHERE CLAVE = ?";
                
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode($stmt->fetch(PDO::FETCH_ASSOC));
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteDiagnostico') {
        $id = $_POST['id'];
        
        try {
            $sql = "DELETE FROM DIAGNOSTICO WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    
    // Manejar RESULTADOS DE LABORATORIO
    else if ($action === 'getResultados') {
        $sql = "SELECT ID as id, CEDULA as cedula, ARCHIVO as archivo, 
                DESCRIPCION as descripcion, 
                DATE_FORMAT(FECHA_SUBIDA, '%Y-%m-%d %H:%i') as fecha 
                FROM RESULTADOS_PACIENTES 
                WHERE CEDULA = ? ORDER BY FECHA_SUBIDA DESC";
                
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$paciente['CEDULA']]);
        
        $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode($resultados);
        exit;
    }
    else if ($action === 'deleteResultado') {
        $id = $_POST['id'];
        
        try {
            // Obtener nombre del archivo antes de eliminar
            $sql = "SELECT ARCHIVO FROM RESULTADOS_PACIENTES WHERE ID = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($row) {
                $archivo = $row['ARCHIVO'];
                
                // Eliminar archivo del servidor si existe
                $filePath = "uploads/" . $archivo;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
                
                // Eliminar registro de la base de datos
                $sql = "DELETE FROM RESULTADOS_PACIENTES WHERE ID = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$id]);
                
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Resultado no encontrado']);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Manejar subida de archivos
if (isset($_FILES['file'])) {
    $targetDir = "uploads/";
    
    // Crear directorio si no existe
    if (!file_exists($targetDir)) {
        mkdir($targetDir, 0777, true);
    }
    
    $timestamp = time();
    $fileName = $timestamp . "_" . basename($_FILES["file"]["name"]);
    $targetFile = $targetDir . $fileName;
    $uploadOk = 1;
    $fileType = strtolower(pathinfo($targetFile, PATHINFO_EXTENSION));
    
    // Verificar si el archivo ya existe
    if (file_exists($targetFile)) {
        echo json_encode(['success' => false, 'error' => 'El archivo ya existe.']);
        exit;
    }
    
    // Verificar tamaño del archivo (límite a 10MB)
    if ($_FILES["file"]["size"] > 10000000) {
        echo json_encode(['success' => false, 'error' => 'El archivo es demasiado grande.']);
        exit;
    }
    
    // Permitir ciertos formatos de archivo
    $allowedExtensions = ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx", "txt", "csv", "bmp", "webp"];
    if (!in_array($fileType, $allowedExtensions)) {
        echo json_encode(['success' => false, 'error' => 'Solo se permiten archivos JPG, JPEG, PNG, GIF, PDF, DOC, DOCX, XLS, XLSX, TXT, CSV, BMP y WEBP.']);
        exit;
    }
    
    // Intentar subir el archivo
    if (move_uploaded_file($_FILES["file"]["tmp_name"], $targetFile)) {
        // Archivo subido correctamente, ahora guardar en la base de datos
        $descripcion = isset($_POST['descripcion']) ? $_POST['descripcion'] : '';
        
        try {
            $sql = "INSERT INTO RESULTADOS_PACIENTES (CEDULA, ARCHIVO, DESCRIPCION) VALUES (?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$paciente['CEDULA'], $fileName, $descripcion]);
            
            $newId = $pdo->lastInsertId();
            
            // Obtener el registro insertado
            $sql = "SELECT ID as id, CEDULA as cedula, ARCHIVO as archivo, 
                    DESCRIPCION as descripcion, 
                    DATE_FORMAT(FECHA_SUBIDA, '%Y-%m-%d %H:%i') as fecha 
                    FROM RESULTADOS_PACIENTES WHERE ID = ?";
                    
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$newId]);
            
            echo json_encode($stmt->fetch(PDO::FETCH_ASSOC));
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'error' => 'Hubo un error al subir el archivo.']);
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Historia Clínica - Sistema Médico</title>
  <!-- Bootstrap 4 -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

  <style>
    /* Estructura general: barra lateral + contenido */
    .wrapper {
      display: flex;
      height: 100vh; /* Ocupa toda la pantalla, opcional */
    }
    .sidebar {
      width: 280px;
      background: #f8f9fa;
      border-right: 1px solid #ccc;
      padding: 15px;
      overflow-y: auto;
    }
    .sidebar .nav-link {
      color: #333;
      cursor: pointer;
      margin-bottom: 5px;
    }
    .sidebar .nav-link.active {
      background: #e2e6ea;
      font-weight: bold;
    }
    .main-content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
    /* Secciones principales ocultas por defecto */
    .section {
      display: none;
    }
    .section.active {
      display: block;
    }
    /* Para la lista de registros (fechas) */
    .lista-registros {
      border: 1px solid #ccc;
      height: 200px;
      overflow-y: auto;
      margin-bottom: 10px;
    }
    /* Estilos para el dropzone de archivos */
    .file-drop-area {
      border: 2px dashed #ccc;
      border-radius: 5px;
      padding: 20px;
      text-align: center;
      margin-bottom: 15px;
      cursor: pointer;
    }
    .file-drop-area.highlight {
      border-color: #007bff;
      background-color: rgba(0, 123, 255, 0.05);
    }
    .file-preview {
      margin-top: 10px;
      max-height: 150px;
      overflow: hidden;
    }
    .file-preview img {
      max-height: 100px;
      max-width: 100%;
    }
    /* Estilos para el árbol de navegación */
    .tree {
      list-style-type: none;
      padding-left: 0;
    }
    .tree ul {
      list-style-type: none;
      padding-left: 20px;
    }
    .tree-toggler {
      cursor: pointer;
      font-weight: bold;
      padding: 3px 0;
      display: block;
    }
    .tree-toggler:hover {
      color: #007bff;
    }
    .tree-toggler::before {
      content: "▶";
      display: inline-block;
      margin-right: 5px;
      font-size: 10px;
      transition: transform 0.2s;
    }
    .tree-toggler.expanded::before {
      transform: rotate(90deg);
    }
    .tree-items {
      display: none;
      padding-left: 15px;
    }
    .tree-item {
      padding: 3px 0 3px 15px;
      display: block;
      color: #495057;
      text-decoration: none;
      font-size: 0.9rem;
    }
    .tree-item:hover, .tree-item.active {
      color: #007bff;
      background-color: #f8f9fa;
      border-radius: 3px;
    }
    /* Indicador de alto riesgo */
    .alto-riesgo {
      color: #dc3545;
      font-weight: bold;
      margin-left: 10px;
    }
    /* Estilos para las pestañas */
    .custom-tabs .nav-link {
      padding: 0.5rem 1rem;
      border: 1px solid transparent;
      border-top-left-radius: 0.25rem;
      border-top-right-radius: 0.25rem;
    }
    .custom-tabs .nav-link.active {
      color: #495057;
      background-color: #fff;
      border-color: #dee2e6 #dee2e6 #fff;
    }
    /* Estilos para los botones de acción */
    .action-buttons {
      margin-bottom: 15px;
    }
    .action-buttons .btn {
      margin-right: 5px;
    }
  </style>
</head>
<body>

<div class="wrapper">
  <!-- Barra Lateral Principal -->
  <div class="sidebar">
    <h4>Historia Clínica</h4>
    <div class="mb-3">
      <?php if ($paciente): ?>
      <div class="text-muted small">
        <strong>Paciente:</strong> <?php echo htmlspecialchars($paciente['NOMBRES'] . ' ' . $paciente['APELLIDOS'] ?? 'N/A'); ?><br>
        <strong>Cédula:</strong> <?php echo htmlspecialchars($paciente['CEDULA'] ?? 'N/A'); ?>
        <?php 
        // Verificar si el paciente es de alto riesgo
        $sqlAltoRiesgo = "SELECT ALTORIESGO, CAUSAALTORIESGO FROM ANTECEDENTES WHERE CLAVEPAC = ? AND ALTORIESGO = 'S'";
        $stmtAltoRiesgo = $pdo->prepare($sqlAltoRiesgo);
        $stmtAltoRiesgo->execute([$clavePac]);
        $altoRiesgo = $stmtAltoRiesgo->fetch(PDO::FETCH_ASSOC);
        
        if ($altoRiesgo): 
        ?>
        <div class="alto-riesgo mt-1">
          ALTO RIESGO: <?php echo htmlspecialchars($altoRiesgo['CAUSAALTORIESGO']); ?>
        </div>
        <?php endif; ?>
      </div>
      <div class="mt-2">
        <a href="seleccionar_paciente.php" class="btn btn-sm btn-outline-secondary">Cambiar Paciente</a>
      </div>
      <?php else: ?>
      <div class="alert alert-warning small">Paciente no encontrado</div>
      <?php endif; ?>
    </div>
    
    <!-- Árbol de navegación de Historia Clínica -->
    <div class="tree-container">
      <ul class="tree">
        <li>
          <span class="tree-toggler nav-header expanded">Historia</span>
          <ul class="tree-items" style="display: block;">
            <li>
              <span class="tree-toggler nav-header">Antecedentes</span>
              <ul class="tree-items">
                <li><a href="#" class="nav-link tree-item" data-section="antecedentes-familiares">Antecedentes Familiares</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="enfermedad-actual">Enfermedad Actual</a></li>
                <?php if ($paciente && $paciente['SEXO'] === 'F'): ?>
                <li><a href="#" class="nav-link tree-item" data-section="gineco-obstetricos">Gineco-Obstétricos</a></li>
                <?php endif; ?>
              </ul>
            </li>
            <li><a href="#" class="nav-link tree-item" data-section="examen-fisico">Examen Físico</a></li>
            <li>
              <span class="tree-toggler nav-header">Resultados de Laboratorio</span>
              <ul class="tree-items">
                <li><a href="#" class="nav-link tree-item" data-section="hemograma">Hemograma General</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="orina-i">Orina I</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="orina-ii">Orina II</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="coprologico">Coprológico</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="bioquimico">Bioquímico</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="archivos-lab">Archivos Subidos</a></li>
              </ul>
            </li>
            <li>
              <span class="tree-toggler nav-header">Pruebas Complementarias</span>
              <ul class="tree-items">
                <li><a href="#" class="nav-link tree-item" data-section="complementarias">Complementarias</a></li>
              </ul>
            </li>
            <li><a href="#" class="nav-link tree-item" data-section="diagnostico">Diagnóstico</a></li>
            <li><a href="#" class="nav-link tree-item" data-section="tratamiento">Tratamiento</a></li>
            <li><a href="#" class="nav-link tree-item" data-section="seguimiento">Seguimiento</a></li>
            <li>
              <span class="tree-toggler nav-header">Indicaciones</span>
              <ul class="tree-items">
                <li><a href="#" class="nav-link tree-item" data-section="estudios">Estudios</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="informes">Informes</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="prescripcion">Prescripción</a></li>
                <li><a href="#" class="nav-link tree-item" data-section="certificado">Certificado Médico</a></li>
              </ul>
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>

  <!-- Contenido principal -->
  <div class="main-content">
    <!-- SECCIÓN: ANTECEDENTES -->
    <div id="section-antecedentes" class="section active">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>Antecedentes</h3>
        <div class="action-buttons">
          <button class="btn btn-primary" id="btnEditAntecedentes">Editar</button>
          <button class="btn btn-success" id="btnSaveAntecedentes" style="display: none;">Guardar</button>
          <button class="btn btn-secondary" id="btnCancelAntecedentes" style="display: none;">Cancelar</button>
        </div>
      </div>
      
      <!-- Subpestañas de Antecedentes -->
      <ul class="nav nav-tabs custom-tabs" id="tabs-antecedentes" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="personales-tab" data-toggle="tab" href="#content-personales" role="tab" aria-controls="content-personales" aria-selected="true">
            Personales-Familiares
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="habitos-tab" data-toggle="tab" href="#content-habitos" role="tab" aria-controls="content-habitos" aria-selected="false">
            Hábitos
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="riesgos-tab" data-toggle="tab" href="#content-riesgos" role="tab" aria-controls="content-riesgos" aria-selected="false">
            Factores de Riesgo
          </a>
        </li>
        <?php if ($paciente && $paciente['SEXO'] === 'F'): ?>
        <li class="nav-item">
          <a class="nav-link" id="gineco-tab" data-toggle="tab" href="#content-gineco" role="tab" aria-controls="content-gineco" aria-selected="false">
            Gineco-Obstétricos
          </a>
        </li>
        <?php endif; ?>
      </ul>
      <div class="tab-content mt-3" id="tabsContent-antecedentes">
        <!-- Personales-Familiares -->
        <div class="tab-pane fade show active" id="content-personales" role="tabpanel" aria-labelledby="personales-tab">
          <form id="form-antecedentes-personales">
            <div class="form-group">
              <label for="familiares">Antecedentes Familiares</label>
              <textarea class="form-control" id="familiares" name="familiares" rows="3" disabled></textarea>
            </div>
            <div class="form-group">
              <label for="personales">Antecedentes Personales</label>
              <textarea class="form-control" id="personales" name="personales" rows="3" disabled></textarea>
            </div>
            <div class="form-group">
              <label for="patologicoFamiliares">Antecedentes Patológicos Familiares</label>
              <textarea class="form-control" id="patologicoFamiliares" name="patologicoFamiliares" rows="3" disabled></textarea>
            </div>
            <div class="form-group">
              <label for="patologicoPersonales">Antecedentes Patológicos Personales</label>
              <textarea class="form-control" id="patologicoPersonales" name="patologicoPersonales" rows="3" disabled></textarea>
            </div>
            <div class="form-group">
              <label for="vacunas">Vacunas</label>
              <textarea class="form-control" id="vacunas" name="vacunas" rows="3" disabled></textarea>
            </div>
          </form>
        </div>
        
        <!-- Hábitos -->
        <div class="tab-pane fade" id="content-habitos" role="tabpanel" aria-labelledby="habitos-tab">
          <form id="form-antecedentes-habitos">
            <div class="form-group">
              <label for="habitos">Hábitos Generales</label>
              <textarea class="form-control" id="habitos" name="habitos" rows="3" disabled></textarea>
            </div>
            
            <div class="form-group">
              <label>Consumo de Alcohol</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="alcohol" id="alcohol-si" value="S" disabled>
                <label class="form-check-label" for="alcohol-si">Sí</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="alcohol" id="alcohol-no" value="N" checked disabled>
                <label class="form-check-label" for="alcohol-no">No</label>
              </div>
              
              <div id="alcohol-details" class="ml-4 mt-2" style="display: none;">
                <div class="form-row">
                  <div class="form-group col-md-6">
                    <label for="cantAlcohol">Cantidad</label>
                    <input type="text" class="form-control" id="cantAlcohol" name="cantAlcohol" disabled>
                  </div>
                  <div class="form-group col-md-6">
                    <label for="desdeAlcohol">Desde</label>
                    <input type="text" class="form-control" id="desdeAlcohol" name="desdeAlcohol" disabled>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label>Tabaquismo</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="fuma" id="fuma-si" value="S" disabled>
                <label class="form-check-label" for="fuma-si">Sí</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="fuma" id="fuma-no" value="N" checked disabled>
                <label class="form-check-label" for="fuma-no">No</label>
              </div>
              
              <div id="fuma-details" class="ml-4 mt-2" style="display: none;">
                <div class="form-row">
                  <div class="form-group col-md-6">
                    <label for="cantFuma">Cantidad</label>
                    <input type="text" class="form-control" id="cantFuma" name="cantFuma" disabled>
                  </div>
                  <div class="form-group col-md-6">
                    <label for="desdeFuma">Desde</label>
                    <input type="text" class="form-control" id="desdeFuma" name="desdeFuma" disabled>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label>Consumo de Drogas</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="droga" id="droga-si" value="S" disabled>
                <label class="form-check-label" for="droga-si">Sí</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="droga" id="droga-no" value="N" checked disabled>
                <label class="form-check-label" for="droga-no">No</label>
              </div>
              
              <div id="droga-details" class="ml-4 mt-2" style="display: none;">
                <div class="form-row">
                  <div class="form-group col-md-6">
                    <label for="tipoDroga">Tipo</label>
                    <input type="text" class="form-control" id="tipoDroga" name="tipoDroga" disabled>
                  </div>
                  <div class="form-group col-md-6">
                    <label for="cantDroga">Cantidad</label>
                    <input type="text" class="form-control" id="cantDroga" name="cantDroga" disabled>
                  </div>
                  <div class="form-group col-md-6">
                    <label for="desdeDroga">Desde</label>
                    <input type="text" class="form-control" id="desdeDroga" name="desdeDroga" disabled>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
        
        <!-- Factores de Riesgo -->
        <div class="tab-pane fade" id="content-riesgos" role="tabpanel" aria-labelledby="riesgos-tab">
          <form id="form-antecedentes-riesgos">
            <div class="form-group">
              <label>Paciente de Alto Riesgo</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="altoRiesgo" id="riesgo-si" value="S" disabled>
                <label class="form-check-label" for="riesgo-si">Sí</label>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="altoRiesgo" id="riesgo-no" value="N" checked disabled>
                <label class="form-check-label" for="riesgo-no">No</label>
              </div>
              
              <div id="riesgo-details" class="ml-4 mt-2" style="display: none;">
                <div class="form-group">
                  <label for="causaAltoRiesgo">Causa de Alto Riesgo</label>
                  <textarea class="form-control" id="causaAltoRiesgo" name="causaAltoRiesgo" rows="3" disabled></textarea>
                </div>
              </div>
            </div>
          </form>
        </div>
        
        <!-- Gineco-Obstétricos (solo para mujeres) -->
        <?php if ($paciente && $paciente['SEXO'] === 'F'): ?>
        <div class="tab-pane fade" id="content-gineco" role="tabpanel" aria-labelledby="gineco-tab">
          <form id="form-antecedentes-gineco">
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="menarquia">Menarquia (edad)</label>
                  <input type="text" class="form-control" id="menarquia" name="menarquia" disabled>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="ritmo">Ritmo</label>
                  <input type="text" class="form-control" id="ritmo" name="ritmo" disabled>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="fum">FUM</label>
                  <input type="date" class="form-control" id="fum" name="fum" disabled>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="gestas">Gestas</label>
                  <input type="number" class="form-control" id="gestas" name="gestas" disabled>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="paras">Paras</label>
                  <input type="number" class="form-control" id="paras" name="paras" disabled>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="cesareas">Cesáreas</label>
                  <input type="number" class="form-control" id="cesareas" name="cesareas" disabled>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="abortos">Abortos</label>
                  <input type="number" class="form-control" id="abortos" name="abortos" disabled>
                </div>
              </div>
            </div>
            
            <div class="form-group">
              <label for="observaciones_gineco">Observaciones</label>
              <textarea class="form-control" id="observaciones_gineco" name="observaciones_gineco" rows="3" disabled></textarea>
            </div>
          </form>
        </div>
        <?php endif; ?>
      </div>
    </div><!-- Fin Antecedentes -->

    <!-- SECCIÓN: EXAMEN FÍSICO -->
    <div id="section-examen" class="section">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>Examen Físico</h3>
        <div class="action-buttons">
          <button class="btn btn-primary" id="btnInsertExamen">Nuevo</button>
          <button class="btn btn-info" id="btnModifyExamen">Modificar</button>
          <button class="btn btn-danger" id="btnDeleteExamen">Eliminar</button>
          <button class="btn btn-success" id="btnSaveExamen" style="display: none;">Guardar</button>
          <button class="btn btn-secondary" id="btnCancelExamen" style="display: none;">Cancelar</button>
        </div>
      </div>
      
      <!-- Subpestañas: Examen Físico (General), Obstétrica, Colposcopia -->
      <ul class="nav nav-tabs custom-tabs" id="tabs-examen" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="fisico-tab" data-toggle="tab" href="#content-fisico" role="tab" aria-controls="content-fisico" aria-selected="true">
            Examen Físico (General)
          </a>
        </li>
        <?php if ($paciente && $paciente['SEXO'] === 'F'): ?>
        <li class="nav-item">
          <a class="nav-link" id="obstetrica-tab" data-toggle="tab" href="#content-obstetrica" role="tab" aria-controls="content-obstetrica" aria-selected="false">
            Obstétrica
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="colposcopia-tab" data-toggle="tab" href="#content-colposcopia" role="tab" aria-controls="content-colposcopia" aria-selected="false">
            Colposcopia
          </a>
        </li>
        <?php endif; ?>
      </ul>

      <div class="tab-content mt-3" id="tabsContent-examen">
        <!-- EXAMEN FÍSICO (GENERAL) -->
        <div class="tab-pane fade show active" id="content-fisico" role="tabpanel" aria-labelledby="fisico-tab">
          <!-- Lista de Fechas + Campos a la derecha -->
          <div class="row">
            <div class="col-md-3">
              <div class="lista-registros" id="dateListExamen"></div>
            </div>
            <div class="col-md-9">
              <form id="form-examen-fisico">
                <input type="hidden" id="idExamen" name="id" value="0">
                <div class="form-row">
                  <div class="form-group col-md-3">
                    <label>Fecha</label>
                    <input type="date" class="form-control" id="fechaExamen" name="fecha" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>TA (mmHg)</label>
                    <input type="text" class="form-control" id="taSistolica" name="tas" placeholder="Sist." disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>TA-d</label>
                    <input type="text" class="form-control" id="taDiastolica" name="tad" placeholder="Diast." disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>FC (lpm)</label>
                    <input type="text" class="form-control" id="fcExamen" name="fc" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>FR (rpm)</label>
                    <input type="text" class="form-control" id="frExamen" name="fr" disabled>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group col-md-2">
                    <label>TEMP (°C)</label>
                    <input type="text" class="form-control" id="tempExamen" name="temp" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>Pulso</label>
                    <input type="text" class="form-control" id="pulsoExamen" name="pulso" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>Peso (kg)</label>
                    <input type="text" class="form-control" id="pesoExamen" name="peso" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>Talla (m)</label>
                    <input type="text" class="form-control" id="tallaExamen" name="talla" disabled>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group col-md-2">
                    <label>IMC</label>
                    <input type="text" class="form-control" id="imcExamen" name="imc" disabled>
                  </div>
                  <div class="form-group col-md-3">
                    <label>Perímetro Abdominal (cm)</label>
                    <input type="text" class="form-control" id="perimetroAbdominalExamen" name="perimetro_abdominal" disabled>
                  </div>
                </div>
                <div class="form-group">
                  <label>Hallazgos</label>
                  <textarea class="form-control" rows="3" id="hallazgosExamen" name="hallazgos" disabled></textarea>
                </div>
                <div class="form-group">
                  <label>Otros</label>
                  <textarea class="form-control" rows="2" id="otrosExamen" name="otros" disabled></textarea>
                </div>
              </form>
            </div>
          </div>
        </div><!-- Fin Examen Físico (General) -->

        <!-- OBSTÉTRICA (solo para mujeres) -->
        <?php if ($paciente && $paciente['SEXO'] === 'F'): ?>
        <div class="tab-pane fade" id="content-obstetrica" role="tabpanel" aria-labelledby="obstetrica-tab">
          <!-- Lista de Fechas + Campos a la derecha -->
          <div class="row">
            <div class="col-md-3">
              <div class="lista-registros" id="dateListObst"></div>
            </div>
            <div class="col-md-9">
              <form id="form-obstetrica">
                <input type="hidden" id="idObstetrica" name="id" value="0">
                <div class="form-row">
                  <div class="form-group col-md-3">
                    <label>Fecha</label>
                    <input type="date" class="form-control" id="fechaObst" name="fecha" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>EG (sem)</label>
                    <input type="text" class="form-control" id="egObst" name="eg" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>AU (cm)</label>
                    <input type="text" class="form-control" id="auObst" name="au" disabled>
                  </div>
                  <div class="form-group col-md-2">
                    <label>Peso Fetal (g)</label>
                    <input type="text" class="form-control" id="pesoFetalObst" name="peso_fetal" disabled>
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group col-md-3">
                    <label>FUM</label>
                    <input type="date" class="form-control" id="fumObst" name="fum" disabled>
                  </div>
                  <div class="form-group col-md-3">
                    <label>FPP</label>
                    <input type="date" class="form-control" id="fppObst" name="fpp" disabled>
                  </div>
                  <div class="form-group col-md-3">
                    <label>Presentación</label>
                    <select class="form-control" id="presentacionObst" name="presentacion" disabled>
                      <option value="">Seleccione</option>
                      <option value="Cefálica">Cefálica</option>
                      <option value="Podálica">Podálica</option>
                      <option value="Transversa">Transversa</option>
                    </select>
                  </div>
                  <div class="form-group col-md-3">
                    <label>FCF (lpm)</label>
                    <input type="text" class="form-control" id="fcfObst" name="fcf" disabled>
                  </div>
                </div>
                <div class="form-group">
                  <label>Observaciones</label>
                  <textarea class="form-control" rows="3" id="obsObst" name="observaciones" disabled></textarea>
                </div>
              </form>
            </div>
          </div>
        </div><!-- Fin Obstétrica -->

        <!-- COLPOSCOPIA (solo para mujeres) -->
        <div class="tab-pane fade" id="content-colposcopia" role="tabpanel" aria-labelledby="colposcopia-tab">
          <div class="row">
            <div class="col-md-3">
              <div class="lista-registros" id="dateListColpo"></div>
            </div>
            <div class="col-md-9">
              <form id="form-colposcopia">
                <input type="hidden" id="idColposcopia" name="id" value="0">
                <div class="form-row">
                  <div class="form-group col-md-3">
                    <label>Fecha</label>
                    <input type="date" class="form-control" id="fechaColpo" name="fecha" disabled>
                  </div>
                </div>
                <div class="form-group">
                  <label>Hallazgos</label>
                  <textarea class="form-control" rows="4" id="hallazgosColpo" name="hallazgos" disabled></textarea>
                </div>
                <div class="form-group">
                  <label>Impresión Diagnóstica</label>
                  <textarea class="form-control" rows="2" id="impresionColpo" name="impresion" disabled></textarea>
                </div>
              </form>
            </div>
          </div>
        </div><!-- Fin Colposcopia -->
        <?php endif; ?>
      </div>
    </div><!-- Fin Examen Físico -->

    <!-- SECCIÓN: RESULTADOS DE LABORATORIO -->
    <div id="section-laboratorio" class="section">
      <h3>Resultados de Laboratorio</h3>
      <div id="laboratorio-content">
        <!-- El contenido se cargará dinámicamente según la opción seleccionada -->
        <div class="text-center py-5">
          <p>Seleccione una opción de laboratorio en el menú lateral.</p>
        </div>
      </div>
    </div>

    <!-- SECCIÓN: DIAGNÓSTICO -->
    <div id="section-diagnostico" class="section">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>Diagnóstico</h3>
        <div class="action-buttons">
          <button class="btn btn-primary" id="btnInsertDiagnostico">Nuevo</button>
          <button class="btn btn-info" id="btnModifyDiagnostico">Modificar</button>
          <button class="btn btn-danger" id="btnDeleteDiagnostico">Eliminar</button>
          <button class="btn btn-success" id="btnSaveDiagnostico" style="display: none;">Guardar</button>
          <button class="btn btn-secondary" id="btnCancelDiagnostico" style="display: none;">Cancelar</button>
        </div>
      </div>
      <div class="row">
        <div class="col-md-3">
          <div class="lista-registros" id="dateListDiagnostico"></div>
        </div>
        <div class="col-md-9">
          <form id="form-diagnostico">
            <input type="hidden" id="idDiagnostico" name="id" value="0">
            <div class="form-group">
              <label for="fechaDiagnostico">Fecha</label>
              <input type="date" class="form-control" id="fechaDiagnostico" name="fecha" disabled>
            </div>
            <div class="form-group">
              <label for="codigoCIE">Código CIE</label>
              <input type="text" class="form-control" id="codigoCIE" name="codigo_cie" disabled>
            </div>
            <div class="form-group">
              <label for="textoDiagnostico">Diagnóstico</label>
              <textarea class="form-control" rows="8" id="textoDiagnostico" name="texto" disabled></textarea>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- OTRAS SECCIONES (vacías) -->
    <div id="section-pruebas" class="section">
      <h3>Pruebas Complementarias</h3>
      <div id="complementarias-content">
        <!-- El contenido se cargará dinámicamente -->
        <div class="text-center py-5">
          <p>Seleccione una opción de pruebas complementarias en el menú lateral.</p>
        </div>
      </div>
    </div>
    
    <div id="section-tratamiento" class="section">
      <h3>Tratamiento</h3>
      <div id="tratamiento-content">
        <!-- El contenido se cargará dinámicamente -->
        <div class="text-center py-5">
          <p>Seleccione una opción de tratamiento en el menú lateral.</p>
        </div>
      </div>
    </div>
    
    <div id="section-seguimiento" class="section">
      <h3>Seguimiento</h3>
      <div id="seguimiento-content">
        <!-- El contenido se cargará dinámicamente -->
        <div class="text-center py-5">
          <p>Seleccione una opción de seguimiento en el menú lateral.</p>
        </div>
      </div>
    </div>
    
    <div id="section-indicaciones" class="section">
      <h3>Indicaciones</h3>
      <div id="indicaciones-content">
        <!-- El contenido se cargará dinámicamente -->
        <div class="text-center py-5">
          <p>Seleccione una opción de indicaciones en el menú lateral.</p>
        </div>
      </div>
    </div>

  </div><!-- main-content -->
</div><!-- wrapper -->

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
// -----------------------------------------------------
// Manejo del árbol de navegación
// -----------------------------------------------------
$(document).ready(function(){
  // Expandir/contraer nodos del árbol
  $('.tree-toggler').click(function() {
    $(this).toggleClass('expanded');
    $(this).next('.tree-items').slideToggle(300);
  });
  
  // Expandir el primer nivel por defecto
  $('.tree > li > .tree-toggler').addClass('expanded');
  
  // Manejar clic en elementos del árbol
  $('.tree-item').click(function(e) {
    e.preventDefault();
    
    // Marcar como activo
    $('.tree-item').removeClass('active');
    $(this).addClass('active');
    
    // Obtener la sección a mostrar
    var section = $(this).data('section');
    showSection(section);
  });
  
  // Función para mostrar la sección correspondiente
  function showSection(sectionId) {
    // Ocultar todas las secciones
    $('.section').removeClass('active').hide();
    
    // Mostrar la sección solicitada
    switch(sectionId) {
      case 'antecedentes-familiares':
        $('#section-antecedentes').addClass('active').show();
        $('#tabs-antecedentes a[href="#content-personales"]').tab('show');
        break;
      case 'enfermedad-actual':
        // Cargar formulario de enfermedad actual
        cargarFormulario('enfermedad-actual', '#section-antecedentes');
        $('#section-antecedentes').addClass('active').show();
        break;
      case 'gineco-obstetricos':
        $('#section-antecedentes').addClass('active').show();
        $('#tabs-antecedentes a[href="#content-gineco"]').tab('show');
        break;
      case 'examen-fisico':
        $('#section-examen').addClass('active').show();
        $('#tabs-examen a[href="#content-fisico"]').tab('show');
        loadExamenesFisicos();
        break;
      case 'hemograma':
        $('#section-laboratorio').addClass('active').show();
        cargarFormulario('hemograma', '#laboratorio-content');
        break;
      case 'orina-i':
        $('#section-laboratorio').addClass('active').show();
        cargarFormulario('orina-i', '#laboratorio-content');
        break;
      case 'orina-ii':
        $('#section-laboratorio').addClass('active').show();
        cargarFormulario('orina-ii', '#laboratorio-content');
        break;
      case 'coprologico':
        $('#section-laboratorio').addClass('active').show();
        cargarFormulario('coprologico', '#laboratorio-content');
        break;
      case 'bioquimico':
        $('#section-laboratorio').addClass('active').show();
        cargarFormulario('bioquimico', '#laboratorio-content');
        break;
      case 'archivos-lab':
        $('#section-laboratorio').addClass('active').show();
        cargarFormulario('archivos-lab', '#laboratorio-content');
        break;
      case 'complementarias':
        $('#section-pruebas').addClass('active').show();
        break;
      case 'diagnostico':
        $('#section-diagnostico').addClass('active').show();
        loadDiagnosticos();
        break;
      case 'tratamiento':
        $('#section-tratamiento').addClass('active').show();
        break;
      case 'seguimiento':
        $('#section-seguimiento').addClass('active').show();
        break;
      case 'estudios':
        $('#section-indicaciones').addClass('active').show();
        cargarFormulario('estudios', '#indicaciones-content');
        break;
      case 'informes':
        $('#section-indicaciones').addClass('active').show();
        cargarFormulario('informes', '#indicaciones-content');
        break;
      case 'prescripcion':
        $('#section-indicaciones').addClass('active').show();
        cargarFormulario('prescripcion', '#indicaciones-content');
        break;
      case 'certificado':
        $('#section-indicaciones').addClass('active').show();
        cargarFormulario('certificado', '#indicaciones-content');
        break;
      default:
        // Por defecto, mostrar antecedentes
        $('#section-antecedentes').addClass('active').show();
    }
  }
  
  // Función para cargar formularios dinámicamente
  function cargarFormulario(formulario, contenedor) {
    $.ajax({
      url: "cargar_formulario.php",
      type: "GET",
      data: { formulario: formulario },
      success: function(data) {
        $(contenedor).html(data);
      },
      error: function(xhr, status, error) {
        console.error("Error al cargar formulario:", error);
        $(contenedor).html('<div class="alert alert-danger">Error al cargar el formulario. Por favor, intente de nuevo.</div>');
      }
    });
  }
  
  // Expandir todos los nodos del árbol que tienen elementos activos
  function expandActiveNodes() {
    $('.tree-item.active').parents('.tree-items').each(function() {
      $(this).show();
      $(this).prev('.tree-toggler').addClass('expanded');
    });
  }
  
  // Inicializar: expandir nodos activos y mostrar la primera sección
  expandActiveNodes();
  $('.tree-item:first').click();
  
  // Cargar datos iniciales
  loadAntecedentes();
});

// -----------------------------------------------------
// ANTECEDENTES
// -----------------------------------------------------
function loadAntecedentes() {
  $.ajax({
    url: window.location.href,
    type: 'POST',
    data: {
      action: 'getAntecedentes'
    },
    dataType: 'json',
    success: function(data) {
      if (data) {
        // Personales-Familiares
        $('#familiares').val(data.FAMILIARES);
        $('#personales').val(data.PERSONALES);
        $('#patologicoFamiliares').val(data.PATOLOGICOFAMILIARES);
        $('#patologicoPersonales').val(data.PATOLOGICOPERSONALES);
        $('#vacunas').val(data.VACUNAS);
        
        // Hábitos
        $('#habitos').val(data.HABITOS);
        
        // Alcohol
        if (data.ALCOHOL === 'S') {
          $('#alcohol-si').prop('checked', true);
          $('#alcohol-details').show();
          $('#cantAlcohol').val(data.CANTALCOHOL);
          $('#desdeAlcohol').val(data.DESDEALCOHOL);
        } else {
          $('#alcohol-no').prop('checked', true);
          $('#alcohol-details').hide();
        }
        
        // Tabaquismo
        if (data.FUMA === 'S') {
          $('#fuma-si').prop('checked', true);
          $('#fuma-details').show();
          $('#cantFuma').val(data.CANTFUMA);
          $('#desdeFuma').val(data.DESDEFUMA);
        } else {
          $('#fuma-no').prop('checked', true);
          $('#fuma-details').hide();
        }
        
        // Drogas
        if (data.DROGA === 'S') {
          $('#droga-si').prop('checked', true);
          $('#droga-details').show();
          $('#tipoDroga').val(data.TIPODROGA);
          $('#cantDroga').val(data.CANTDROGA);
          $('#desdeDroga').val(data.DESDEDROGA);
        } else {
          $('#droga-no').prop('checked', true);
          $('#droga-details').hide();
        }
        
        // Alto Riesgo
        if (data.ALTORIESGO === 'S') {
          $('#riesgo-si').prop('checked', true);
          $('#riesgo-details').show();
          $('#causaAltoRiesgo').val(data.CAUSAALTORIESGO);
        } else {
          $('#riesgo-no').prop('checked', true);
          $('#riesgo-details').hide();
        }
      }
    },
    error: function(xhr, status, error) {
      console.error("Error al cargar antecedentes:", error);
    }
  });
  
  // Mostrar/ocultar detalles según selección de radio buttons
  $('input[name="alcohol"]').change(function() {
    if ($(this).val() === 'S') {
      $('#alcohol-details').show();
    } else {
      $('#alcohol-details').hide();
    }
  });
  
  $('input[name="fuma"]').change(function() {
    if ($(this).val() === 'S') {
      $('#fuma-details').show();
    } else {
      $('#fuma-details').hide();
    }
  });
  
  $('input[name="droga"]').change(function() {
    if ($(this).val() === 'S') {
      $('#droga-details').show();
    } else {
      $('#droga-details').hide();
    }
  });
  
  $('input[name="altoRiesgo"]').change(function() {
    if ($(this).val() === 'S') {
      $('#riesgo-details').show();
    } else {
      $('#riesgo-details').hide();
    }
  });
  
  // Botones de edición de antecedentes
  $('#btnEditAntecedentes').click(function() {
    // Habilitar campos
    $('#form-antecedentes-personales textarea').prop('disabled', false);
    $('#form-antecedentes-habitos textarea, #form-antecedentes-habitos input').prop('disabled', false);
    $('#form-antecedentes-riesgos textarea, #form-antecedentes-riesgos input').prop('disabled', false);
    $('#form-antecedentes-gineco textarea, #form-antecedentes-gineco input').prop('disabled', false);
    
    // Cambiar botones
    $(this).hide();
    $('#btnSaveAntecedentes, #btnCancelAntecedentes').show();
  });
  
  $('#btnCancelAntecedentes').click(function() {
    // Recargar datos originales
    loadAntecedentes();
    
    // Deshabilitar campos
    $('#form-antecedentes-personales textarea').prop('disabled', true);
    $('#form-antecedentes-habitos textarea, #form-antecedentes-habitos input').prop('disabled', true);
    $('#form-antecedentes-riesgos textarea, #form-antecedentes-riesgos input').prop('disabled', true);
    $('#form-antecedentes-gineco textarea, #form-antecedentes-gineco input').prop('disabled', true);
    
    // Cambiar botones
    $(this).hide();
    $('#btnSaveAntecedentes').hide();
    $('#btnEditAntecedentes').show();
  });
  
  // Guardar antecedentes
  $('#btnSaveAntecedentes').click(function() {
    var formData = {
      action: 'saveAntecedentes',
      familiares: $('#familiares').val(),
      personales: $('#personales').val(),
      patologicoFamiliares: $('#patologicoFamiliares').val(),
      patologicoPersonales: $('#patologicoPersonales').val(),
      habitos: $('#habitos').val(),
      vacunas: $('#vacunas').val(),
      alcohol: $('input[name="alcohol"]:checked').val(),
      cantAlcohol: $('#cantAlcohol').val(),
      desdeAlcohol: $('#desdeAlcohol').val(),
      fuma: $('input[name="fuma"]:checked').val(),
      cantFuma: $('#cantFuma').val(),
      desdeFuma: $('#desdeFuma').val(),
      droga: $('input[name="droga"]:checked').val(),
      tipoDroga: $('#tipoDroga').val(),
      cantDroga: $('#cantDroga').val(),
      desdeDroga: $('#desdeDroga').val(),
      altoRiesgo: $('input[name="altoRiesgo"]:checked').val(),
      causaAltoRiesgo: $('#causaAltoRiesgo').val()
    };
    
    $.ajax({
      url: window.location.href,
      type: 'POST',
      data: formData,
      dataType: 'json',
      success: function(response) {
        if (response.success) {
          alert("Antecedentes guardados correctamente.");
          
          // Deshabilitar campos
          $('#form-antecedentes-personales textarea').prop('disabled', true);
          $('#form-antecedentes-habitos textarea, #form-antecedentes-habitos input').prop('disabled', true);
          $('#form-antecedentes-riesgos textarea, #form-antecedentes-riesgos input').prop('disabled', true);
          $('#form-antecedentes-gineco textarea, #form-antecedentes-gineco input').prop('disabled', true);
          
          // Cambiar botones
          $('#btnSaveAntecedentes, #btnCancelAntecedentes').hide();
          $('#btnEditAntecedentes').show();
          
          // Recargar la página para actualizar el indicador de alto riesgo si cambió
          if ($('input[name="altoRiesgo"]:checked').val() === 'S') {
            location.reload();
          }
        } else {
          alert("Error al guardar antecedentes: " + response.error);
        }
      },
      error: function(xhr, status, error) {
        console.error("Error al guardar antecedentes:", error);
        alert("Error al guardar antecedentes. Consulte la consola para más detalles.");
      }
    });
  });
}

// -----------------------------------------------------
// EXAMEN FÍSICO
// -----------------------------------------------------
let currentExamenId = 0;
let examMode = 'view'; // 'view', 'edit', 'new'

function loadExamenesFisicos() {
  $.ajax({
    url: window.location.href,
    type: 'POST',
    data: {
      action: 'getExamenesFisicos'
    },
    dataType: 'json',
    success: function(data) {
      if (data && data.length > 0) {
        refreshDateListExamen(data);
        selectExamen(data[0].id);
      } else {
        clearFormExamen();
        updateExamenButtons('new');
      }
    },
    error: function(xhr, status, error) {
      console.error("Error al cargar exámenes físicos:", error);
    }
  });
}

function refreshDateListExamen(examenes) {
  let html = "<ul class='list-unstyled m-2'>";
  examenes.forEach((item) => {
    html += `<li><a href="#" onclick="selectExamen(${item.id})">${item.fecha}</a></li>`;
  });
  html += "</ul>";
  $("#dateListExamen").html(html);
}

function selectExamen(id) {
  currentExamenId = id;
  
  $.ajax({
    url: window.location.href,
    type: 'POST',
    data: {
      action: 'getExamenFisico',
      id: id
    },
    dataType: 'json',
    success: function(data) {
      if (data) {
        $('#idExamen').val(data.id);
        $('#fechaExamen').val(data.fecha);
        $('#taSistolica').val(data.tas);
        $('#taDiastolica').val(data.tad);
        $('#fcExamen').val(data.fc);
        $('#frExamen').val(data.fr);
        $('#tempExamen').val(data.temp);
        $('#pulsoExamen').val(data.pulso);
        $('#pesoExamen').val(data.peso);
        $('#tallaExamen').val(data.talla);
        $('#hallazgosExamen').val(data.hallazgos);
        $('#otrosExamen').val(data.otros);
        $('#imcExamen').val(data.imc);
        $('#perimetroAbdominalExamen').val(data.perimetro_abdominal);
        
        updateExamenButtons('view');
        disableExamenForm(true);
      }
    },
    error: function(xhr, status, error) {
      console.error("Error al cargar examen físico:", error);
    }
  });
}

function updateExamenButtons(mode) {
  examMode = mode;
  
  if (mode === 'view') {
    $('#btnInsertExamen, #btnModifyExamen, #btnDeleteExamen').show();
    $('#btnSaveExamen, #btnCancelExamen').hide();
  } else if (mode === 'edit' || mode === 'new') {
    $('#btnInsertExamen, #btnModifyExamen, #btnDeleteExamen').hide();
    $('#btnSaveExamen, #btnCancelExamen').show();
  }
}

function disableExamenForm(disabled) {
  $('#form-examen-fisico input, #form-examen-fisico textarea').prop('disabled', disabled);
}

function clearFormExamen() {
  $('#idExamen').val(0);
  $('#fechaExamen').val(new Date().toISOString().split('T')[0]);
  $('#taSistolica').val('');
  $('#taDiastolica').val('');
  $('#fcExamen').val('');
  $('#frExamen').val('');
  $('#tempExamen').val('');
  $('#pulsoExamen').val('');
  $('#pesoExamen').val('');
  $('#tallaExamen').val('');
  $('#hallazgosExamen').val('');
  $('#otrosExamen').val('');
  $('#imcExamen').val('');
  $('#perimetroAbdominalExamen').val('');
}

$(document).ready(function() {
  // Botones de Examen Físico
  $('#btnInsertExamen').click(function() {
    clearFormExamen();
    updateExamenButtons('new');
    disableExamenForm(false);
  });
  
  $('#btnModifyExamen').click(function() {
    if (currentExamenId > 0) {
      updateExamenButtons('edit');
      disableExamenForm(false);
    } else {
      alert("Seleccione un registro para modificar.");
    }
  });
  
  $('#btnDeleteExamen').click(function() {
    if (currentExamenId > 0) {
      if (confirm("¿Está seguro de eliminar este examen físico?")) {
        $.ajax({
          url: window.location.href,
          type: 'POST',
          data: {
            action: 'deleteExamenFisico',
            id: currentExamenId
          },
          dataType: 'json',
          success: function(response) {
            if (response.success) {
              alert("Examen físico eliminado correctamente.");
              loadExamenesFisicos();
            } else {
              alert("Error al eliminar examen físico: " + response.error);
            }
          },
          error: function(xhr, status, error) {
            console.error("Error al eliminar examen físico:", error);
            alert("Error al eliminar examen físico. Consulte la consola para más detalles.");
          }
        });
      }
    } else {
      alert("Seleccione un registro para eliminar.");
    }
  });
  
  $('#btnSaveExamen').click(function() {
    var formData = $('#form-examen-fisico').serialize();
    formData += '&action=saveExamenFisico';
    
    $.ajax({
      url: window.location.href,
      type: 'POST',
      data: formData,
      dataType: 'json',
      success: function(data) {
        if (data && data.id) {
          alert("Examen físico guardado correctamente.");
          loadExamenesFisicos();
          selectExamen(data.id);
        } else {
          alert("Error al guardar examen físico.");
        }
      },
      error: function(xhr, status, error) {
        console.error("Error al guardar examen físico:", error);
        alert("Error al guardar examen físico. Consulte la consola para más detalles.");
      }
    });
  });
  
  $('#btnCancelExamen').click(function() {
    if (currentExamenId > 0) {
      selectExamen(currentExamenId);
    } else {
      clearFormExamen();
    }
    updateExamenButtons('view');
  });
  
  // Calcular IMC automáticamente
  $('#pesoExamen, #tallaExamen').on('input', function() {
    const peso = parseFloat($('#pesoExamen').val());
    const talla = parseFloat($('#tallaExamen').val());
    
    if (peso && talla) {
      const tallaMetros = talla / 100;
      const imc = (peso / (tallaMetros * tallaMetros)).toFixed(2);
      $('#imcExamen').val(imc);
    }
  });
});

// -----------------------------------------------------
// DIAGNÓSTICO
// -----------------------------------------------------
let currentDiagnosticoId = 0;
let diagMode = 'view'; // 'view', 'edit', 'new'

function loadDiagnosticos() {
  $.ajax({
    url: window.location.href,
    type: 'POST',
    data: {
      action: 'getDiagnosticos'
    },
    dataType: 'json',
    success: function(data) {
      if (data && data.length > 0) {
        refreshDateListDiagnostico(data);
        selectDiagnostico(data[0].id);
      } else {
        clearFormDiagnostico();
        updateDiagnosticoButtons('new');
      }
    },
    error: function(xhr, status, error) {
      console.error("Error al cargar diagnósticos:", error);
    }
  });
}

function refreshDateListDiagnostico(diagnosticos) {
  let html = "<ul class='list-unstyled m-2'>";
  diagnosticos.forEach((item) => {
    html += `<li><a href="#" onclick="selectDiagnostico(${item.id})">${item.fecha}</a></li>`;
  });
  html += "</ul>";
  $("#dateListDiagnostico").html(html);
}

function selectDiagnostico(id) {
  currentDiagnosticoId = id;
  
  $.ajax({
    url: window.location.href,
    type: 'POST',
    data: {
      action: 'getDiagnostico',
      id: id
    },
    dataType: 'json',
    success: function(data) {
      if (data) {
        $('#idDiagnostico').val(data.id);
        $('#fechaDiagnostico').val(data.fecha);
        $('#textoDiagnostico').val(data.texto);
        $('#codigoCIE').val(data.codigo_cie);
        
        updateDiagnosticoButtons('view');
        disableDiagnosticoForm(true);
      }
    },
    error: function(xhr, status, error) {
      console.error("Error al cargar diagnóstico:", error);
    }
  });
}

function updateDiagnosticoButtons(mode) {
  diagMode = mode;
  
  if (mode === 'view') {
    $('#btnInsertDiagnostico, #btnModifyDiagnostico, #btnDeleteDiagnostico').show();
    $('#btnSaveDiagnostico, #btnCancelDiagnostico').hide();
  } else if (mode === 'edit' || mode === 'new') {
    $('#btnInsertDiagnostico, #btnModifyDiagnostico, #btnDeleteDiagnostico').hide();
    $('#btnSaveDiagnostico, #btnCancelDiagnostico').show();
  }
}

function disableDiagnosticoForm(disabled) {
  $('#form-diagnostico input, #form-diagnostico textarea').prop('disabled', disabled);
}

function clearFormDiagnostico() {
  $('#idDiagnostico').val(0);
  $('#fechaDiagnostico').val(new Date().toISOString().split('T')[0]);
  $('#textoDiagnostico').val('');
  $('#codigoCIE').val('');
}

$(document).ready(function() {
  // Botones de Diagnóstico
  $('#btnInsertDiagnostico').click(function() {
    clearFormDiagnostico();
    updateDiagnosticoButtons('new');
    disableDiagnosticoForm(false);
  });
  
  $('#btnModifyDiagnostico').click(function() {
    if (currentDiagnosticoId > 0) {
      updateDiagnosticoButtons('edit');
      disableDiagnosticoForm(false);
    } else {
      alert("Seleccione un registro para modificar.");
    }
  });
  
  $('#btnDeleteDiagnostico').click(function() {
    if (currentDiagnosticoId > 0) {
      if (confirm("¿Está seguro de eliminar este diagnóstico?")) {
        $.ajax({
          url: window.location.href,
          type: 'POST',
          data: {
            action: 'deleteDiagnostico',
            id: currentDiagnosticoId
          },
          dataType: 'json',
          success: function(response) {
            if (response.success) {
              alert("Diagnóstico eliminado correctamente.");
              loadDiagnosticos();
            } else {
              alert("Error al eliminar diagnóstico: " + response.error);
            }
          },
          error: function(xhr, status, error) {
            console.error("Error al eliminar diagnóstico:", error);
            alert("Error al eliminar diagnóstico. Consulte la consola para más detalles.");
          }
        });
      }
    } else {
      alert("Seleccione un registro para eliminar.");
    }
  });
  
  $('#btnSaveDiagnostico').click(function() {
    var formData = $('#form-diagnostico').serialize();
    formData += '&action=saveDiagnostico';
    
    $.ajax({
      url: window.location.href,
      type: 'POST',
      data: formData,
      dataType: 'json',
      success: function(data) {
        if (data && data.id) {
          alert("Diagnóstico guardado correctamente.");
          loadDiagnosticos();
          selectDiagnostico(data.id);
        } else {
          alert("Error al guardar diagnóstico.");
        }
      },
      error: function(xhr, status, error) {
        console.error("Error al guardar diagnóstico:", error);
        alert("Error al guardar diagnóstico. Consulte la consola para más detalles.");
      }
    });
  });
  
  $('#btnCancelDiagnostico').click(function() {
    if (currentDiagnosticoId > 0) {
      selectDiagnostico(currentDiagnosticoId);
    } else {
      clearFormDiagnostico();
    }
    updateDiagnosticoButtons('view');
  });
});

// Funciones globales para ser llamadas desde HTML
window.selectExamen = selectExamen;
window.selectDiagnostico = selectDiagnostico;
</script>

</body>
</html>
