<?php
include_once '../config/config.php';

$conn = getDBConnection();
$conn->set_charset("utf8");

// Obtener registros eliminados
$query = "SELECT * FROM ELIMINACIONES WHERE TABLA_ORIGEN = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $_GET['tabla']);
$stmt->execute();
$result = $stmt->get_result();

$data = [];
while ($row = $result->fetch_assoc()) {
    $data[] = $row;
}

$stmt->close();

// Responder con JSON
echo json_encode($data);
?>
