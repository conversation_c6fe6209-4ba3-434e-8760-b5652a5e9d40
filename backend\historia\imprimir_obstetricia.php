<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVE = $_GET['CLAVE'] ?? null;

if (!$CLAVE) {
  exit('❌ CLAVE no especificada.');
}

// Obtener el registro
$stmt = $pdo->prepare("SELECT * FROM OBSTETRICIA WHERE CLAVE = :CLAVE");
$stmt->execute(['CLAVE' => $CLAVE]);
$datos = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$datos) {
  exit('❌ Registro no encontrado.');
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Impresión de Control Obstétrico</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    @media print {
      .no-print { display: none; }
    }
    body {
      padding: 2rem;
      font-size: 1rem;
    }
    h4 {
      margin-bottom: 1rem;
    }
    .datos td {
      padding: 4px 8px;
    }
  </style>
</head>
<body>
  <div class="no-print mb-3">
    <button onclick="window.print()" class="btn btn-primary">🖨️ Imprimir</button>
    <a href="javascript:window.close()" class="btn btn-secondary">Cerrar</a>
  </div>

  <h4>📋 Control Obstétrico</h4>

  <table class="table table-bordered datos">
    <tr><th>📅 Fecha</th><td><?= $datos['FECHA_CAP'] ?></td></tr>
    <tr><th>Edad Gestacional (EG)</th><td><?= $datos['EG'] ?></td></tr>
    <tr><th>Peso</th><td><?= $datos['PESO'] ?></td></tr>
    <tr><th>Altura Uterina (AU)</th><td><?= $datos['AU'] ?></td></tr>
    <tr><th>FCF</th><td><?= $datos['FCF'] ?></td></tr>
    <tr><th>TA</th><td><?= $datos['TA'] ?></td></tr>
    <tr><th>Presentación</th><td><?= $datos['PRES'] ?></td></tr>
    <tr><th>Posición del bebé</th><td><?= $datos['POSBEBE'] ?></td></tr>
    <tr><th>FUM</th><td><?= $datos['FUM'] ?></td></tr>
    <tr><th>FPP</th><td><?= $datos['FPP'] ?></td></tr>
    <tr><th>TT (Tratamiento)</th><td><?= $datos['TT'] ?></td></tr>
    <tr><th>Patología del embarazo</th><td><?= $datos['PATOLOGIA'] ?></td></tr>
  </table>

  <h5 class="mt-4">🩺 Exploraciones</h5>
  <table class="table table-sm table-bordered datos">
    <tr><th>Ex. Clínico</th><td><?= $datos['EXCLINICO'] ?></td></tr>
    <tr><th>Ex. Mamas</th><td><?= $datos['EXMAMAS'] ?></td></tr>
    <tr><th>Ex. Odontológico</th><td><?= $datos['EXODONT'] ?></td></tr>
    <tr><th>Pelvis</th><td><?= $datos['PELVIS'] ?></td></tr>
    <tr><th>Papanicolaou</th><td><?= $datos['PAPANIC'] ?></td></tr>
    <tr><th>Colposcopia</th><td><?= $datos['COLPOSCOPIA'] ?></td></tr>
    <tr><th>Cérvix</th><td><?= $datos['CERVIX'] ?></td></tr>
  </table>

  <p class="text-muted mt-4">Generado por el sistema el <?= date('d/m/Y H:i') ?></p>
</body>
</html>
