<?php
/**
 * Script de prueba para WhatsApp
 * Usar para verificar que la configuración funciona correctamente
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../services/WhatsAppService.php';

echo "🧪 Pruebas de WhatsApp\n";
echo "====================\n\n";

try {
    // 1. Verificar configuración
    echo "1. Verificando configuración...\n";
    
    if (!$config['whatsapp']['enabled']) {
        echo "❌ WhatsApp no está habilitado en config.php\n";
        echo "   Cambia 'enabled' => true en la configuración\n\n";
    } else {
        echo "✅ WhatsApp habilitado\n";
    }
    
    if (empty($config['whatsapp']['twilio']['account_sid'])) {
        echo "❌ Account SID de Twilio no configurado\n";
    } else {
        echo "✅ Account SID configurado\n";
    }
    
    if (empty($config['whatsapp']['twilio']['auth_token'])) {
        echo "❌ Auth Token de Twilio no configurado\n";
    } else {
        echo "✅ Auth Token configurado\n";
    }
    
    if (empty($config['whatsapp']['twilio']['whatsapp_number'])) {
        echo "❌ Número de WhatsApp no configurado\n";
    } else {
        echo "✅ Número de WhatsApp configurado: " . $config['whatsapp']['twilio']['whatsapp_number'] . "\n";
    }
    
    echo "\n";
    
    // 2. Verificar tablas
    echo "2. Verificando tablas de base de datos...\n";
    
    $tables = ['whatsapp_messages', 'whatsapp_config', 'whatsapp_templates', 'patient_whatsapp', 'whatsapp_conversations'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ Tabla $table existe\n";
            } else {
                echo "❌ Tabla $table no existe\n";
                echo "   Ejecuta: php backend/database/setup_whatsapp.php\n";
            }
        } catch (Exception $e) {
            echo "❌ Error verificando tabla $table: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n";
    
    // 3. Verificar servicio WhatsApp
    echo "3. Verificando servicio WhatsApp...\n";
    
    $whatsappService = new WhatsAppService($config, $pdo);
    
    if ($whatsappService->isEnabled()) {
        echo "✅ Servicio WhatsApp habilitado y configurado\n";
    } else {
        echo "❌ Servicio WhatsApp no está completamente configurado\n";
    }
    
    echo "\n";
    
    // 4. Prueba de envío (solo si todo está configurado)
    if ($whatsappService->isEnabled()) {
        echo "4. ¿Quieres probar enviar un mensaje? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $response = trim(fgets($handle));
        fclose($handle);
        
        if (strtolower($response) === 'y' || strtolower($response) === 'yes') {
            echo "Ingresa el número de teléfono (con código de país, ej: +18091234567): ";
            $handle = fopen("php://stdin", "r");
            $phone = trim(fgets($handle));
            fclose($handle);
            
            if (!empty($phone)) {
                try {
                    $message = "🧪 Mensaje de prueba desde el sistema del consultorio.\n\nSi recibes este mensaje, ¡la integración funciona correctamente!";
                    $result = $whatsappService->sendMessage($phone, $message);
                    echo "✅ Mensaje enviado correctamente!\n";
                    echo "   SID: " . ($result['sid'] ?? 'N/A') . "\n";
                } catch (Exception $e) {
                    echo "❌ Error enviando mensaje: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    echo "\n";
    
    // 5. Información adicional
    echo "5. Información adicional:\n";
    echo "   - Webhook URL: " . ($config['whatsapp']['twilio']['webhook_url'] ?? 'No configurada') . "\n";
    echo "   - Archivo webhook: backend/api/whatsapp_webhook.php\n";
    echo "   - Logs: backend/logs/whatsapp_webhook.log\n";
    
    echo "\n📋 Próximos pasos si hay errores:\n";
    echo "1. Ejecutar setup: php backend/database/setup_whatsapp.php\n";
    echo "2. Configurar credenciales de Twilio en config.php\n";
    echo "3. Configurar webhook en Twilio Console\n";
    echo "4. Habilitar WhatsApp en config.php\n";
    
} catch (Exception $e) {
    echo "💥 Error en las pruebas: " . $e->getMessage() . "\n";
}

echo "\n🎉 Pruebas completadas!\n";
?>
