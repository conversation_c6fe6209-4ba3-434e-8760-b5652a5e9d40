<?php
session_start();
if (!isset($_SESSION['usuario']) || $_SESSION['rol'] !== 'paciente') {
    header("Location: ../../login.php");
    exit();
}

require_once '../config/database.php';
$nombre = $_SESSION['nombre_paciente'] ?? $_SESSION['usuario'];
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Solicitar Cita</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f9f9f9;
            font-family: Arial, sans-serif;
        }
        .card {
            max-width: 500px;
            margin: 60px auto;
            padding: 20px;
            box-shadow: 0px 4px 15px rgba(0,0,0,0.1);
            border-radius: 12px;
        }
    </style>
</head>
<body>
<div class="card">
    <h4 class="text-center mb-4">Solicitar Cita Médica</h4>
    <form id="form-cita" action="procesar_cita_paciente.php" method="POST">
        <div class="mb-3">
            <label for="fecha" class="form-label">Fecha de la cita:</label>
            <input type="date" class="form-control" id="fecha" name="fecha" min="<?= date('Y-m-d') ?>" required>
        </div>
        <div class="mb-3">
            <label for="hora" class="form-label">Hora disponible:</label>
            <select class="form-select" id="hora" name="hora" required>
                <option value="">-- Selecciona una hora --</option>
            </select>
        </div>
        <button type="submit" class="btn btn-primary w-100">Confirmar Cita</button>
        <div class="mt-3 text-center">
            <a href="../../paciente_panel.php" class="btn btn-link">Volver al panel</a>
        </div>
        
    
        <?php
        session_start();
        if (isset($_SESSION['error_cita'])) {
      echo '<div class="alert alert-warning text-center">' . $_SESSION['error_cita'] . '</div>';
       unset($_SESSION['error_cita']);
        }
        ?>

        
    </form>
</div>

<script>
document.getElementById('fecha').addEventListener('change', function () {
    const fecha = this.value;
    const horaSelect = document.getElementById('hora');
    horaSelect.innerHTML = '<option value="">Cargando horas...</option>';

    fetch(`horas_disponibles.php?fecha=${fecha}`)
        .then(res => res.json())
        .then(data => {
            horaSelect.innerHTML = '';
            if (data.length > 0) {
                data.forEach(hora => {
                    const option = document.createElement('option');
                    option.value = hora;
                    option.textContent = hora;
                    horaSelect.appendChild(option);
                });
            } else {
                horaSelect.innerHTML = '<option value="">No hay horas disponibles</option>';
            }
        })
        .catch(() => {
            horaSelect.innerHTML = '<option value="">Error al cargar</option>';
        });
});
</script>

</body>
</html>
