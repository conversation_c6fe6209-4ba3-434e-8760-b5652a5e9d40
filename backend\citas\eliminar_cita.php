<?php
// Incluir archivo de configuración para la conexión a la base de datos
include('../config/database.php');

// Verificar si se pasó la clave de la cita por la URL
if (isset($_GET['atender'])) {
    $claveCita = $_GET['atender'];

    try {
        // Eliminar la cita con la clave proporcionada
        $sqlEliminar = "DELETE FROM CITAMEDIC WHERE CLAVE = ?";
        $stmtEliminar = $pdo->prepare($sqlEliminar);
        $stmtEliminar->execute([$claveCita]);

        // Redirigir a la página de creación de citas con un mensaje de éxito
        header("Location: crear_cita.php?mensaje=eliminada");
        exit();
    } catch (PDOException $e) {
        // Si ocurre un error, mostrar el mensaje
        echo "<p class='error-message'>Error al eliminar la cita: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
} else {
    // Si no se pasó la clave, redirigir con un mensaje de error
    header("Location: crear_cita.php?mensaje=error_eliminar");
    exit();
}
?>
