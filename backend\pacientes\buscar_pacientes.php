<?php

// buscar_pacientes.php
session_start();
require_once '../config/database.php';

// Cargar listado de aseguradoras
$stmt_ars = $pdo->query("SELECT CLAVE, NOMBRE FROM ASEGURADORA ORDER BY NOMBRE");
$arsList = $stmt_ars->fetchAll(PDO::FETCH_ASSOC);

// Cargar listado de categorías (solo aquellas de tipo 'C')
$stmt_categ = $pdo->query("SELECT CLAVE, NOMBRE FROM CATEGORIAS WHERE TIPO = 'C' ORDER BY CLAVE");
$categList = $stmt_categ->fetchAll(PDO::FETCH_ASSOC);

// Función para buscar pacientes con paginación y filtros
function buscarPacientes($pdo, $busqueda = '', $limite = 50, $offset = 0) {
    $sql = "SELECT CLAVE, NOMBRES, APELLIDOS, CEDULA, SEXO, TELEFONO, CELULAR, ECORREO,
                   FECHANAC, ARS, CATEGORIA, ESTATUS,
                   CONCAT(NOMBRES, ' ', APELLIDOS) as NOMBREAPELLIDO,
                   TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) as EDAD
            FROM PACIENTES";

    $params = [];

    if (!empty($busqueda)) {
        $sql .= " WHERE (NOMBRES LIKE ? OR APELLIDOS LIKE ? OR CEDULA LIKE ? OR TELEFONO LIKE ? OR CELULAR LIKE ?)";
        $busquedaParam = "%$busqueda%";
        $params = [$busquedaParam, $busquedaParam, $busquedaParam, $busquedaParam, $busquedaParam];
    }

    $sql .= " ORDER BY NOMBRES ASC LIMIT ? OFFSET ?";
    $params[] = $limite;
    $params[] = $offset;

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Función para contar total de pacientes
function contarPacientes($pdo, $busqueda = '') {
    $sql = "SELECT COUNT(*) as total FROM PACIENTES";
    $params = [];

    if (!empty($busqueda)) {
        $sql .= " WHERE (NOMBRES LIKE ? OR APELLIDOS LIKE ? OR CEDULA LIKE ? OR TELEFONO LIKE ? OR CELULAR LIKE ?)";
        $busquedaParam = "%$busqueda%";
        $params = [$busquedaParam, $busquedaParam, $busquedaParam, $busquedaParam, $busquedaParam];
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
}

// Si es una petición AJAX para buscar pacientes
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'buscar') {
    $busqueda = $_POST['busqueda'] ?? '';
    $pagina = (int)($_POST['pagina'] ?? 1);
    $limite = 50; // Pacientes por página
    $offset = ($pagina - 1) * $limite;

    $pacientes = buscarPacientes($pdo, $busqueda, $limite, $offset);
    $total = contarPacientes($pdo, $busqueda);
    $totalPaginas = ceil($total / $limite);

    echo json_encode([
        'pacientes' => $pacientes,
        'total' => $total,
        'pagina' => $pagina,
        'totalPaginas' => $totalPaginas,
        'limite' => $limite
    ]);
    exit;
}

// Para compatibilidad con código existente - cargar solo los primeros 50
$pacientes = buscarPacientes($pdo, '', 50, 0);
?>

<!-- Los estilos se heredan del archivo principal gestion_pacientes.php -->

</head>
<!-- El contenido se incluye dentro del archivo principal gestion_pacientes.php -->

<script>
    var categList = <?php echo json_encode($categList); ?>;
    var arsList = <?php echo json_encode($arsList); ?>;
</script>

<!-- Header de la sección de búsqueda -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="text-primary mb-1">
            <i class="fas fa-search me-2"></i>
            Buscar Pacientes
        </h4>
        <p class="text-muted mb-0">
            <i class="fas fa-info-circle me-1"></i>
            Encuentra y gestiona la información de los pacientes registrados
        </p>
    </div>
    <div class="badge bg-primary fs-6">
        <i class="fas fa-users me-1"></i>
        Total: <span id="totalPacientes">0</span>
    </div>
</div>

<!-- Contenedor de resultados con diseño médico -->
<div id="contenedorTabla" class="medical-search-results">
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Cargando pacientes...</span>
        </div>
        <p class="mt-3 text-muted">Cargando información de pacientes...</p>
    </div>
</div>

<!-- jQuery (versión completa) -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.min.js"></script>

<script>
// Variables globales para paginación
let paginaActual = 1;
let totalPaginas = 1;
let busquedaActual = '';

// Función optimizada para cargar pacientes con paginación
function cargarPacientes(busqueda = '', pagina = 1, mostrarCargando = true) {
  if (mostrarCargando) {
    document.getElementById('contenedorTabla').innerHTML = `
      <div class="text-center p-4">
        <div class="spinner-border text-primary" role="status">
          <span class="sr-only">Cargando...</span>
        </div>
        <p class="mt-2">Cargando pacientes...</p>
      </div>
    `;
  }

  // Crear FormData para la petición
  const formData = new FormData();
  formData.append('action', 'buscar');
  formData.append('busqueda', busqueda);
  formData.append('pagina', pagina);

  fetch('buscar_pacientes.php', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    // Verificar que la respuesta tenga la estructura esperada
    if (!data.pacientes || !Array.isArray(data.pacientes)) {
      console.error("Respuesta inválida:", data);
      return;
    }

    // Actualizar variables globales
    paginaActual = data.pagina;
    totalPaginas = data.totalPaginas;
    busquedaActual = busqueda;

    // Generar HTML de la tabla
    let html = `
      <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
          <h5 class="mb-0">Pacientes encontrados: <span class="badge badge-primary">${data.total}</span></h5>
          <small class="text-muted">Página ${data.pagina} de ${data.totalPaginas}</small>
        </div>
        <div>
          <input type="text" id="busquedaRapida" class="form-control" placeholder="Buscar paciente..." value="${busquedaActual}">
        </div>
      </div>
      <div class="table-responsive">
        <table id="tablaPacientes" class="table table-hover table-sm">
          <thead class="thead-light">
            <tr>
              <th><i class="fas fa-id-card me-1"></i>Cédula</th>
              <th><i class="fas fa-user me-1"></i>Nombres</th>
              <th><i class="fas fa-user-tag me-1"></i>Apellidos</th>
              <th><i class="fas fa-birthday-cake me-1"></i>Edad</th>
              <th><i class="fas fa-phone me-1"></i>Teléfono</th>
              <th><i class="fas fa-tools me-1"></i>Acciones</th>
            </tr>
          </thead>
          <tbody>
    `;

    data.pacientes.forEach(paciente => {
      html += `
        <tr>
          <td>
            <span class="badge badge-light">${paciente.CEDULA}</span>
          </td>
          <td>
            <strong class="text-primary">${paciente.NOMBRES}</strong>
          </td>
          <td>
            <strong class="text-secondary">${paciente.APELLIDOS}</strong>
          </td>
          <td>
            <span class="badge badge-info">${paciente.EDAD || 'N/A'} años</span>
          </td>
          <td>
            <small class="text-muted">
              ${paciente.TELEFONO ? '<i class="fas fa-phone"></i> ' + paciente.TELEFONO : ''}
              ${paciente.CELULAR ? '<br><i class="fas fa-mobile"></i> ' + paciente.CELULAR : ''}
            </small>
          </td>
          <td>
            <div class="btn-group btn-group-sm" role="group">
              <button type="button" class="btn btn-outline-primary" onclick="editarPaciente(${paciente.CLAVE})" title="Editar Paciente">
                <i class="fas fa-edit"></i>
              </button>
              <button type="button" class="btn btn-outline-danger" onclick="eliminarPaciente(${paciente.CLAVE})" title="Eliminar Paciente">
                <i class="fas fa-trash"></i>
              </button>
              <a href="../citas/crear_cita.php?pre=${encodeURIComponent(paciente.CEDULA)}"
                 class="btn btn-outline-success"
                 title="Crear Cita Médica">
                <i class="fas fa-calendar-plus"></i>
              </a>
            </div>
          </td>
        </tr>
      `;
    });

    // Agregar paginación personalizada
    html += `
          </tbody>
        </table>
      </div>
      ${generarPaginacion(data.pagina, data.totalPaginas)}
    `;

    // Actualizar contenedor
    document.getElementById('contenedorTabla').innerHTML = html;

    // Actualizar contador de pacientes
    document.getElementById('totalPacientes').textContent = data.total;

    // Configurar búsqueda en tiempo real
    const inputBusqueda = document.getElementById('busquedaRapida');
    if (inputBusqueda) {
      inputBusqueda.addEventListener('input', debounce(function() {
        cargarPacientes(this.value, 1);
      }, 500));
    }

    // Inicializar DataTables con configuración optimizada (sin paginación propia)
    $('#tablaPacientes').DataTable({
      paging: false, // Deshabilitamos la paginación de DataTables
      ordering: true,
      searching: false, // Deshabilitamos la búsqueda de DataTables
      info: false, // Deshabilitamos la info de DataTables
      lengthChange: false, // Deshabilitamos el selector de cantidad
      order: [[1, 'asc']], // Ordena por NOMBRES
      language: {
        zeroRecords: "No se encontraron pacientes",
        emptyTable: "No hay pacientes disponibles"
          }
       
      }
  
    });
  })
  .catch(error => {
    console.error('Error al cargar los pacientes:', error);
    document.getElementById('contenedorTabla').innerHTML = `
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        Error al cargar los pacientes. Por favor, intenta de nuevo.
      </div>
    `;
  });
}

// Función para generar la paginación
function generarPaginacion(paginaActual, totalPaginas) {
  if (totalPaginas <= 1) return '';

  let html = `
    <nav aria-label="Paginación de pacientes">
      <ul class="pagination justify-content-center">
  `;

  // Botón anterior
  html += `
    <li class="page-item ${paginaActual <= 1 ? 'disabled' : ''}">
      <a class="page-link" href="#" onclick="cargarPacientes('${busquedaActual}', ${paginaActual - 1}); return false;">
        <i class="fas fa-chevron-left"></i> Anterior
      </a>
    </li>
  `;

  // Páginas
  const inicio = Math.max(1, paginaActual - 2);
  const fin = Math.min(totalPaginas, paginaActual + 2);

  if (inicio > 1) {
    html += `<li class="page-item"><a class="page-link" href="#" onclick="cargarPacientes('${busquedaActual}', 1); return false;">1</a></li>`;
    if (inicio > 2) html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
  }

  for (let i = inicio; i <= fin; i++) {
    html += `
      <li class="page-item ${i === paginaActual ? 'active' : ''}">
        <a class="page-link" href="#" onclick="cargarPacientes('${busquedaActual}', ${i}); return false;">${i}</a>
      </li>
    `;
  }

  if (fin < totalPaginas) {
    if (fin < totalPaginas - 1) html += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
    html += `<li class="page-item"><a class="page-link" href="#" onclick="cargarPacientes('${busquedaActual}', ${totalPaginas}); return false;">${totalPaginas}</a></li>`;
  }

  // Botón siguiente
  html += `
    <li class="page-item ${paginaActual >= totalPaginas ? 'disabled' : ''}">
      <a class="page-link" href="#" onclick="cargarPacientes('${busquedaActual}', ${paginaActual + 1}); return false;">
        Siguiente <i class="fas fa-chevron-right"></i>
      </a>
    </li>
  `;

  html += `
      </ul>
    </nav>
  `;

  return html;
}

// Función debounce para optimizar la búsqueda
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Cargar la tabla de pacientes cuando el documento esté listo
$(document).ready(function() {
  cargarPacientes();
});

// Funciones de validación mejoradas
function validarTelefonoInput(input) {
    // Limpiar caracteres no numéricos
    let valor = input.value.replace(/[^0-9]/g, '');

    // Limitar a 10 dígitos
    if (valor.length > 10) {
        valor = valor.substring(0, 10);
    }

    // Actualizar el valor del input
    input.value = valor;

    // Validación visual
    const errorDiv = document.getElementById(input.id + '-error');

    if (valor === '') {
        // Campo vacío es válido
        input.classList.remove('is-invalid');
        input.classList.remove('is-valid');
        if (errorDiv) errorDiv.textContent = '';
        input.setCustomValidity('');
    } else if (valor.length < 10) {
        // Menos de 10 dígitos - mostrar advertencia pero permitir envío
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');
        if (errorDiv) errorDiv.textContent = '';
        input.setCustomValidity('');
    } else if (valor.length === 10) {
        // 10 dígitos exactos - válido
        input.classList.remove('is-invalid');
        input.classList.add('is-valid');
        if (errorDiv) errorDiv.textContent = '';
        input.setCustomValidity('');
    }
}

function validarCorreo(input) {
    const correo = input.value;
    if (correo !== "" && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(correo)) {
        input.setCustomValidity("Por favor, ingresa un correo electrónico válido.");
    } else {
        input.setCustomValidity("");
    }
}


// Función para editar un paciente (genera el formulario completo de edición con pestañas)
function editarPaciente(clave) {
    console.log('Editando paciente con CLAVE:', clave);

    fetch('obtener_paciente.php?CLAVE=' + clave, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(paciente => {
        console.log('Datos del paciente recibidos:', paciente);

        if (paciente.error || !paciente.success) {
            console.error('Error del servidor:', paciente.error);
            alert('Error al cargar los datos del paciente: ' + (paciente.error || 'Error desconocido'));
            return;
        }
     
      // Si el paciente tiene una foto, usarla; de lo contrario, usar NOFOTO.BMP
      let rutaFoto;
      if (paciente.ARCHIVO && paciente.ARCHIVO.trim() !== "") {
          // Normalizar la ruta: asegurar que tenga la barra correcta
          let archivo = paciente.ARCHIVO.trim();

          // Corregir rutas mal formadas
          if (archivo.includes('FotografiasNOFOTO.BMP')) {
              archivo = 'fotografias/NOFOTO.BMP';
          } else if (archivo.includes('Fotografias/')) {
              archivo = archivo.replace('Fotografias/', 'fotografias/');
          } else if (!archivo.includes('/') && archivo !== 'fotografias/NOFOTO.BMP') {
              // Si no tiene barra y no es la ruta por defecto, agregar el directorio
              archivo = 'fotografias/' + archivo;
          }

          rutaFoto = `${archivo}?v=${new Date().getTime()}`;
      } else {
          rutaFoto = "fotografias/NOFOTO.BMP";
      }
      
        // Construir el bloque de la foto (ahora se muestra debajo del encabezado, justificado a la izquierda)
      
        let fotoHTML = `
          <div style="margin-bottom: 1rem;">
            <p>Foto del Paciente:</p>
            <img src="${rutaFoto}" alt="Foto del Paciente" style="max-width:150px;">
          </div>
          
        `;
 
  
   // Generar dinámicamente las opciones de Estado Civil según el sexo
    let estados = [];
    if (paciente.SEXO === 'Masculino') {
      estados = ["Soltero", "Casado", "Viudo", "Divorciado", "Separado", "Unión Libre"];
    } else if (paciente.SEXO === 'Femenino') {
      estados = ["Soltera", "Casada", "Viuda", "Divorciada", "Separada", "Unión Libre"];
    } else {
      estados = ["Soltero(a)", "Casado(a)", "Viudo(a)", "Divorciado(a)", "Separado(a)", "Unión Libre"];
    }
    
     let estadoOptions = `<option value="">Seleccionar</option>`;
    estados.forEach(estado => {
      estadoOptions += `<option value="${estado}" ${paciente.ESTADOCIVIL === estado ? 'selected' : ''}>${estado}</option>`;
    });
      
      // Generar las opciones del select de Categoría usando categList
    let categoriaOptions = `<option value="">Seleccionar Categoría</option>` +
     categList.map(cat => `<option value="${cat.CLAVE}" ${paciente.CATEGORIA == cat.CLAVE ? 'selected' : ''}>${cat.NOMBRE}</option>`).join('');

      
        // Generar el formulario de edición completo (con pestañas)
        let form = `
         <form id="editarPacienteForm" enctype="multipart/form-data" novalidate>
        
         ${fotoHTML}
        

  <div style="margin-bottom: 1rem;">
    <label for="nuevaFoto">Cambiar Foto:</label>
    <input type="file" class="form-control" id="nuevaFoto" name="nuevaFoto" accept=".jpg, .jpeg, .png, .bmp">
  </div>
      
          <input type="hidden" name="CLAVE" value="${paciente.CLAVE}">
          <input type="hidden" name="SINCRONIZADO" value="0">
          <h3>Datos Principales</h3>
               
               <div class="form-group" style="margin-bottom: 0.5rem;">
  <label for="CEDULA" style="margin-bottom: 0.2rem; display: block;">Cédula:</label>
  <input type="text" class="form-control" id="CEDULA" name="CEDULA" value="${paciente.CEDULA}" maxlength="11" required oninput="validarCedula(event)">
</div>
               
          <div class="form-group">
             <label for="NOMBRES">Nombres:</label>
             <input type="text" class="form-control" id="NOMBRES" name="NOMBRES" value="${paciente.NOMBRES}" required maxlength="35">
            </div>
            <div class="form-group">
             <label for="APELLIDOS">Apellidos:</label>
             <input type="text" class="form-control" id="APELLIDOS" name="APELLIDOS" value="${paciente.APELLIDOS}" required maxlength="35">
            </div>
          <div class="form-group">
            <label for="FECHANAC">Fecha de Nacimiento:</label>
            <input type="date" class="form-control" id="FECHANAC" name="FECHANAC" value="${paciente.FECHANAC}" required oninput="validarFechaNacimiento(event)">
          </div>
          <div class="form-group">
            <label for="SEXO">Sexo:</label>
            <select class="form-control" id="SEXO" name="SEXO" required>
              <option value="">Seleccionar</option>
              <option value="Masculino" ${paciente.SEXO === 'Masculino' ? 'selected' : ''}>Masculino</option>
              <option value="Femenino" ${paciente.SEXO === 'Femenino' ? 'selected' : ''}>Femenino</option>
              <option value="Indefinido" ${paciente.SEXO === 'Indefinido' ? 'selected' : ''}>Indefinido</option>
            </select>
          </div>
          
            <div class="form-group">
             <label for="RH">Tipo de Sangre (RH):</label>
             <select class="form-control" id="RH" name="RH" required>
              <option value="" disabled>Seleccione una opción</option>
                <option value="O+" <?php echo (trim($paciente['RH'] ?? '') === 'O+' ? 'selected' : ''); ?>>O+</option>
               <option value="O-" <?php echo (trim($paciente['RH'] ?? '') === 'O-' ? 'selected' : ''); ?>>O-</option>
               <option value="A+" <?php echo (trim($paciente['RH'] ?? '') === 'A+' ? 'selected' : ''); ?>>A+</option>
                <option value="A-" <?php echo (trim($paciente['RH'] ?? '') === 'A-' ? 'selected' : ''); ?>>A-</option>
                  <option value="B+" <?php echo (trim($paciente['RH'] ?? '') === 'B+' ? 'selected' : ''); ?>>B+</option>
              <option value="B-" <?php echo (trim($paciente['RH'] ?? '') === 'B-' ? 'selected' : ''); ?>>B-</option>
             <option value="AB+" <?php echo (trim($paciente['RH'] ?? '') === 'AB+' ? 'selected' : ''); ?>>AB+</option>
               <option value="AB-" <?php echo (trim($paciente['RH'] ?? '') === 'AB-' ? 'selected' : ''); ?>>AB-</option>
              </select>
            </div>

    <div class="form-group">
            <label for="PESOHABITUAL">Peso Habitual:</label>
            <input type="number" step="0.01" class="form-control" id="PESOHABITUAL" name="PESOHABITUAL" value="${paciente.PESOHABITUAL}">
          </div>      
          
          <div class="form-group">
            <label for="NACIONALIDAD">Nacionalidad:</label>
            <input type="text" class="form-control" id="NACIONALIDAD" name="NACIONALIDAD" value="${paciente.NACIONALIDAD}" required maxlength="35">
          </div>
          
          <h3>Información Adicional</h3>
          
           <div class="form-group">
          <label for="ESTADOCIVIL">Estado Civil:</label>
          <select class="form-control" id="ESTADOCIVIL" name="ESTADOCIVIL">
            ${estadoOptions}
          </select>
        </div>
                <div class="form-group">
             <label for="LUGARNAC">Lugar de Nacimiento:</label>
            <input type="text" class="form-control" id="LUGARNAC" name="LUGARNAC" value="${paciente.LUGARNAC}" maxlength="35">
            </div>
          <div class="form-group">
            <label for="OCUPACION">Ocupación:</label>
            <input type="text" class="form-control" id="OCUPACION" name="OCUPACION" value="${paciente.OCUPACION}" maxlength="35">
          </div>
          <div class="form-group">
            <label for="RELIGION">Religión:</label>
            <select class="form-control" id="RELIGION" name="RELIGION">
              <option value="">Seleccionar</option>
              <option value="Católico" ${paciente.RELIGION === "Católico" ? 'selected' : ''}>Católico</option>
              <option value="Cristiano Evangelice" ${paciente.RELIGION === "Cristiano Evangelice" ? 'selected' : ''}>Cristiano Evangelice</option>
              <option value="Testigo" ${paciente.RELIGION === "Testigo" ? 'selected' : ''}>Testigo</option>
              <option value="Ninguna" ${paciente.RELIGION === "Ninguna" ? 'selected' : ''}>Ninguna</option>
              <option value="Otra" ${paciente.RELIGION === "Otra" ? 'selected' : ''}>Otra</option>
            </select>
          </div>
          
          <!-- Pestañas para agrupar la información -->
          <ul class="nav nav-tabs" id="infoTab" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="domicilio-tab" data-toggle="tab" href="#domicilioForm" role="tab" aria-controls="domicilioForm" aria-selected="true">Domicilio</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="contacto-tab" data-toggle="tab" href="#contactoForm" role="tab" aria-controls="contactoForm" aria-selected="false">Contacto</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="poliza-tab" data-toggle="tab" href="#polizaForm" role="tab" aria-controls="polizaForm" aria-selected="false">Póliza</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="responsable-tab" data-toggle="tab" href="#responsableForm" role="tab" aria-controls="responsableForm" aria-selected="false">Responsable</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="familiar-tab" data-toggle="tab" href="#familiarForm" role="tab" aria-controls="familiarForm" aria-selected="false">Familiar</a>
            </li>
          </ul>
          
          <div class="tab-content pt-3" id="infoTabContent">
            <!-- Pestaña DOMICILIO -->
            <div class="tab-pane fade show active" id="domicilioForm" role="tabpanel" aria-labelledby="domicilio-tab">
              <div class="form-group">
                <label for="CALLE">Calle:</label>
                <input type="text" class="form-control" id="CALLE" name="CALLE" value="${paciente.CALLE}" maxlength="80">
              </div>
              <div class="form-group">
                <label for="PROVINCIA">Provincia:</label>
                <input type="text" class="form-control" id="PROVINCIA" name="PROVINCIA" value="${paciente.PROVINCIA}" maxlength="30">
              </div>
              <div class="form-group">
                <label for="LOCALIDAD">Localidad:</label>
                <input type="text" class="form-control" id="LOCALIDAD" name="LOCALIDAD" value="${paciente.LOCALIDAD}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="MUNICIPIO">Municipio:</label>
                <input type="text" class="form-control" id="MUNICIPIO" name="MUNICIPIO" value="${paciente.MUNICIPIO}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="PAIS">País:</label>
                <input type="text" class="form-control" id="PAIS" name="PAIS" value="${paciente.PAIS}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="REFERENCIA">Referencia:</label>
                <input type="text" class="form-control" id="REFERENCIA" name="REFERENCIA" value="${paciente.REFERENCIA}" maxlength="30">
              </div>
            </div>
            
            <!-- Pestaña CONTACTO -->
            <div class="tab-pane fade" id="contactoForm" role="tabpanel" aria-labelledby="contacto-tab">
              <div class="form-group">
            <label for="CELULAR">Teléfono Celular:</label>
             <input type="text" class="form-control" id="CELULAR" name="CELULAR" value="${paciente.CELULAR || ''}"
              placeholder="Ej: 8091234567"
              oninput="validarTelefonoInput(this)">
            <div class="invalid-feedback" id="CELULAR-error"></div>
            </div>
           <div class="form-group">
  <label for="TELEFONO">Teléfono de Casa:</label>
  <input type="text" class="form-control" id="TELEFONO" name="TELEFONO" value="${paciente.TELEFONO || ''}"
         placeholder="Ej: 8091234567"
         oninput="validarTelefonoInput(this)">
  <div class="invalid-feedback" id="TELEFONO-error"></div>
</div>
        <div class="form-group">
         <label for="TELTRABAJO">Teléfono del Trabajo:</label>
        <input type="text" class="form-control" id="TELTRABAJO" name="TELTRABAJO" value="${paciente.TELTRABAJO || ''}"
        placeholder="Ej: 8091234567"
        oninput="validarTelefonoInput(this)">
        <div class="invalid-feedback" id="TELTRABAJO-error"></div>
        </div>
            <div class="form-group">
             <label for="FAX">Fax:</label>
             <input type="text" class="form-control" id="FAX" name="FAX" value="${paciente.FAX || ''}"
             placeholder="Ej: 8091234567"
             oninput="validarTelefonoInput(this)">
             <div class="invalid-feedback" id="FAX-error"></div>
            </div>

              <div class="form-group">
                <label for="ECORREO">Correo Electrónico:</label>
                <input type="email" class="form-control" id="ECORREO" name="ECORREO" value="${paciente.ECORREO}" maxlength="50">
              </div>
            </div>
            
            <!-- Pestaña PÓLIZA (se utiliza NSS en lugar de POLIZA) -->
            <div class="tab-pane fade" id="polizaForm" role="tabpanel" aria-labelledby="poliza-tab">
             
             
              <div class="form-group">
             <label for="ARS">ARS:</label>
              <select class="form-control" id="ARS" name="ARS" required>
             <option value="">Seleccionar ARS</option>
             ${arsList.map(item => `<option value="${item.CLAVE}" ${paciente.ARS == item.CLAVE ? 'selected' : ''}>${item.NOMBRE}</option>`).join('')}
             </select>
            </div>
             
              <div class="form-group">
                <label for="PLANES">Planes:</label>
                <input type="text" class="form-control" id="PLANES" name="PLANES" value="${paciente.PLANES}" maxlength="20">
              </div>
              <div class="form-group">
                <label for="AFILIADO">Afiliado:</label>
                <input type="text" class="form-control" id="AFILIADO" name="AFILIADO" value="${paciente.AFILIADO}" maxlength="18">
              </div>
              <div class="form-group">
                <label for="VIGENCIA">Vigencia:</label>
                <input type="date" class="form-control" id="VIGENCIA" name="VIGENCIA" value="${paciente.VIGENCIA ? new Date(paciente.VIGENCIA).toISOString().substr(0,10) : ''}">
              </div>
              <div class="form-group">
                <label for="NSS">Número de Seguro Social (NSS):</label>
                <input type="text" class="form-control" id="NSS" name="NSS" readonly value="${paciente.CEDULA}">
              </div>
              
              <div class="form-group">
             <label for="CATEGORIA">Categoría:</label>
             <select class="form-control" id="CATEGORIA" name="CATEGORIA" required>
               <option value="">Seleccionar Categoría</option>${categList.map(cat => `<option value="${cat.CLAVE}" ${paciente.CATEGORIA==cat.CLAVE ? 'selected' : ''}>${cat.NOMBRE}</option>`).join('')}
             </select>
            </div>

            </div>
            
            <!-- Pestaña RESPONSABLE -->
            <div class="tab-pane fade" id="responsableForm" role="tabpanel" aria-labelledby="responsable-tab">
              <div class="form-group">
                <label for="NOMBRERESP">Nombre del Responsable:</label>
                <input type="text" class="form-control" id="NOMBRERESP" name="NOMBRERESP" value="${paciente.NOMBRERESP}" maxlength="50">
              </div>
              <div class="form-group">
                <label for="DIRECCIONRESP">Dirección del Responsable:</label>
                <input type="text" class="form-control" id="DIRECCIONRESP" name="DIRECCIONRESP" value="${paciente.DIRECCIONRESP}" maxlength="50">
              </div>
                 <div class="form-group">
             <label for="CEDULARESP">Cédula del Responsable:</label>
             <input type="text" class="form-control" id="CEDULARESP" name="CEDULARESP" 
              value="${paciente.CEDULARESP}"
                 maxlength="11" title="Ingrese la cédula del responsable"
              oninput="this.value = this.value.replace(/[^0-9]/g, '');">
                </div>
                <div class="form-group">
             <label for="TELEFONORESP">Teléfono del Responsable:</label>
              <input type="text" class="form-control" id="TELEFONORESP" name="TELEFONORESP"
               value="${paciente.TELEFONORESP || ''}"
              placeholder="Ej: 8091234567"
                oninput="validarTelefonoInput(this)">
              <div class="invalid-feedback" id="TELEFONORESP-error"></div>
                </div>

            </div>
            
            <!-- Pestaña FAMILIAR -->
            <div class="tab-pane fade" id="familiarForm" role="tabpanel" aria-labelledby="familiar-tab">
              <div class="form-group">
                <label for="FAMILIARPROX">Nombre del Familiar Próximo:</label>
                <input type="text" class="form-control" id="FAMILIARPROX" name="FAMILIARPROX" value="${paciente.FAMILIARPROX}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="DIRECCIONFAMILIAR">Dirección del Familiar Próximo:</label>
                <input type="text" class="form-control" id="DIRECCIONFAMILIAR" name="DIRECCIONFAMILIAR" value="${paciente.DIRECCIONFAMILIAR}" maxlength="35">
              </div>
                <div class="form-group">
         <label for="TELEFONOFAMILIAR">Teléfono del Familiar Próximo:</label>
         <input type="text" class="form-control" id="TELEFONOFAMILIAR" name="TELEFONOFAMILIAR"
         value="${paciente.TELEFONOFAMILIAR || ''}"
         placeholder="Ej: 8091234567"
         oninput="validarTelefonoInput(this)">
         <div class="invalid-feedback" id="TELEFONOFAMILIAR-error"></div>
            </div>
            </div>
          </div>
          
          <!-- Otros campos fuera de pestañas -->
         <div class="form-group mt-3">
        <label for="OBSERVACIONES">Observaciones (por la secretaria):</label>
        <textarea class="form-control" id="OBSERVACIONES" name="OBSERVACIONES" maxlength="60">${paciente.OBSERVACIONES ? paciente.OBSERVACIONES : ''}</textarea>   
          
            </div>
         
         
          <div class="form-group">
            <label>Nivel Escolar:</label><br>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelPrimario" value="0" ${paciente.NIVELESCOLAR == "0" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelPrimario">Primario</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelSecundario" value="1" ${paciente.NIVELESCOLAR == "1" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelSecundario">Secundario</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelUniversitario" value="2" ${paciente.NIVELESCOLAR == "2" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelUniversitario">Universitario</label>
            </div>
           
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelNinguno" value="3" ${paciente.NIVELESCOLAR == "3" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelNinguno">Ninguno</label>
            </div>
          </div>
         
          <div class="form-group">
            <label>Procedencia:</label><br>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaUrbano" value="1" ${paciente.PROCEDENCIA == "1" ? 'checked' : ''}>
              <label class="form-check-label" for="procedenciaUrbano">Urbano</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaRural" value="0" ${paciente.PROCEDENCIA == "0" ? 'checked' : ''}>
              <label class="form-check-label" for="procedenciaRural">Rural</label>
            </div>
          </div>
          <div class="form-group">
            <label for="RECORDCLINICA">Record Clínico:</label>
            <input type="text" class="form-control" id="RECORDCLINICA" name="RECORDCLINICA" value="${paciente.RECORDCLINICA}">
          </div>
          
          <button type="submit" class="btn btn-primary">Guardar Cambios</button>
        </form>
        `;

        document.getElementById('contenedorTabla').innerHTML = form;

        // Limpiar TODAS las validaciones HTML5 problemáticas
        setTimeout(() => {
            // Asegurar que el formulario tenga novalidate
            const form = document.getElementById('editarPacienteForm');
            if (form) {
                form.setAttribute('novalidate', 'novalidate');
                console.log('Formulario configurado con novalidate');
            }

            const inputs = document.querySelectorAll('#editarPacienteForm input, #editarPacienteForm select, #editarPacienteForm textarea');
            inputs.forEach(input => {
                // Limpiar TODAS las validaciones
                input.setCustomValidity('');
                input.removeAttribute('pattern');
                input.removeAttribute('required');
                input.removeAttribute('minlength');
                input.removeAttribute('maxlength');
                input.removeAttribute('min');
                input.removeAttribute('max');

                // Para campos de teléfono, remover validaciones específicas
                if (input.name && (input.name.includes('TELEFONO') || input.name === 'CELULAR' || input.name === 'FAX')) {
                    input.removeAttribute('title');
                    input.type = 'text'; // Asegurar que sea text, no tel
                    console.log(`Validaciones removidas de: ${input.name}`);
                }

                // Remover cualquier campo que contenga "ESP" en el nombre
                if (input.name && input.name.includes('ESP')) {
                    console.warn('Campo con ESP removido:', input.name);
                    input.remove();
                }
            });
        }, 100);

         // Manejar el envío del formulario de edición mediante AJAX
        document.getElementById('editarPacienteForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // LIMPIAR TODAS LAS VALIDACIONES ANTES DEL ENVÍO
            console.log('=== LIMPIANDO VALIDACIONES ANTES DEL ENVÍO ===');

            // Asegurar que el formulario tenga novalidate
            this.setAttribute('novalidate', 'novalidate');

            // Limpiar TODOS los campos
            const todosLosCampos = this.querySelectorAll('input, select, textarea');
            todosLosCampos.forEach(campo => {
                campo.setCustomValidity('');
                campo.removeAttribute('pattern');
                campo.removeAttribute('required');
                campo.removeAttribute('minlength');
                campo.removeAttribute('maxlength');
                campo.removeAttribute('min');
                campo.removeAttribute('max');
                campo.removeAttribute('title');

                console.log(`Validaciones removidas de: ${campo.name || 'sin nombre'}`);
            });

            // Validación simple - solo campos críticos
            const nombres = this.querySelector('input[name="NOMBRES"]');
            const apellidos = this.querySelector('input[name="APELLIDOS"]');
            const cedula = this.querySelector('input[name="CEDULA"]');

            if (!nombres || !nombres.value.trim()) {
                alert('El campo Nombres es obligatorio');
                if (nombres) nombres.focus();
                return;
            }

            if (!apellidos || !apellidos.value.trim()) {
                alert('El campo Apellidos es obligatorio');
                if (apellidos) apellidos.focus();
                return;
            }

            if (!cedula || !cedula.value.trim()) {
                alert('El campo Cédula es obligatorio');
                if (cedula) cedula.focus();
                return;
            }

            let formData = new FormData(this);
            let data = {};

            // Solo incluir campos válidos
            const camposValidos = [
                'NOMBRES', 'APELLIDOS', 'CEDULA', 'FECHANAC', 'SEXO', 'NACIONALIDAD',
                'RH', 'ECORREO', 'ESTADOCIVIL', 'LUGARNAC', 'OCUPACION', 'RELIGION', 'CALLE',
                'PROVINCIA', 'LOCALIDAD', 'MUNICIPIO', 'PAIS', 'REFERENCIA', 'CELULAR', 'TELEFONO',
                'TELTRABAJO', 'FAX', 'NOMBRERESP', 'DIRECCIONRESP', 'CEDULARESP', 'TELEFONORESP',
                'FAMILIARPROX', 'DIRECCIONFAMILIAR', 'TELEFONOFAMILIAR', 'ARS', 'PLANES', 'AFILIADO',
                'VIGENCIA', 'NSS', 'CATEGORIA', 'OBSERVACIONES', 'PESOHABITUAL', 'NIVELESCOLAR',
                'PROCEDENCIA', 'RECORDCLINICA'
            ];

            formData.forEach((value, key) => {
                if (camposValidos.includes(key)) {
                    data[key] = value;
                }
            });

            data['CLAVE'] = clave;
           
           let nuevaFoto = document.getElementById('nuevaFoto').files[0];
            if (nuevaFoto) {
                formData.append('nuevaFoto', nuevaFoto);
            }
          
             // ✅ Asegurar que CLAVE está siendo enviada correctamente
            formData.append('CLAVE', clave);  // 🔥 Añadir CLAVE explícitamente
          
            // ✅ Cambiar `Content-Type` → FormData lo define automáticamente
             fetch('actualizar_paciente.php', {
               method: 'POST',
               body: formData
              })
            
            .then(response => response.json())
            .then(responseData => {
                if (responseData.success) {
                    alert('Paciente actualizado con éxito');
                    window.location.href = 'gestion_pacientes.php?tab=buscar';
                } else {
                    alert('Error al actualizar paciente');
                }
            })
            .catch(error => console.error('Error al actualizar paciente:', error));
        });
    })
    .catch(error => {
        console.error('Error al obtener datos del paciente:', error);
        alert('Error de conexión al cargar los datos del paciente. Por favor, intente nuevamente.');
    });
}

        
// Función para eliminar un paciente (similar a la implementada previamente)
function eliminarPaciente(clave) {
    if (confirm('¿Estás seguro de eliminar este paciente?')) {
        fetch('eliminar_paciente.php', {
            method: 'POST',
            body: JSON.stringify({ CLAVE: clave }),
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(responseData => {
            if (responseData.success) {
                alert('Paciente eliminado con éxito');
                window.location.href = 'gestion_pacientes.php?tab=buscar';
            } else {
                alert('Error al eliminar paciente');
            }
        })
        .catch(error => console.error('Error al eliminar paciente:', error));
    }
}
</script>

<!-- Incluir jQuery, Popper.js y Bootstrap JS -->
<!-- Los scripts se cargan desde el archivo principal gestion_pacientes.php -->

