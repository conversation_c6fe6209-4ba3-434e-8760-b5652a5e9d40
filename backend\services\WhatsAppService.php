<?php
/**
 * Servicio para manejar la integración con WhatsApp
 * Soporta Twilio WhatsApp API
 */

class WhatsAppService {
    private $config;
    private $pdo;
    private $twilioSid;
    private $twilioToken;
    private $whatsappNumber;
    
    public function __construct($config, $pdo) {
        $this->config = $config;
        $this->pdo = $pdo;
        
        if ($config['whatsapp']['provider'] === 'twilio') {
            $this->twilioSid = $config['whatsapp']['twilio']['account_sid'];
            $this->twilioToken = $config['whatsapp']['twilio']['auth_token'];
            $this->whatsappNumber = $config['whatsapp']['twilio']['whatsapp_number'];
        }
    }
    
    /**
     * Verificar si WhatsApp está habilitado y configurado
     */
    public function isEnabled() {
        return $this->config['whatsapp']['enabled'] && 
               !empty($this->twilioSid) && 
               !empty($this->twilioToken) && 
               !empty($this->whatsappNumber);
    }
    
    /**
     * Enviar mensaje de WhatsApp
     */
    public function sendMessage($to, $message, $mediaUrl = null) {
        if (!$this->isEnabled()) {
            throw new Exception('WhatsApp no está habilitado o configurado correctamente');
        }
        
        // Asegurar que el número tenga el formato correcto
        $to = $this->formatPhoneNumber($to);
        
        $data = [
            'From' => 'whatsapp:' . $this->whatsappNumber,
            'To' => 'whatsapp:' . $to,
            'Body' => $message
        ];
        
        // Agregar media si se proporciona
        if ($mediaUrl) {
            $data['MediaUrl'] = $mediaUrl;
        }
        
        $response = $this->makeTwilioRequest('Messages.json', $data);
        
        // Guardar el mensaje en la base de datos
        $this->saveMessage($to, $message, 'outbound', $response['sid'] ?? null);
        
        return $response;
    }
    
    /**
     * Procesar mensaje recibido desde webhook
     */
    public function processIncomingMessage($webhookData) {
        $from = str_replace('whatsapp:', '', $webhookData['From'] ?? '');
        $message = $webhookData['Body'] ?? '';
        $messageSid = $webhookData['MessageSid'] ?? '';
        
        // Guardar mensaje recibido
        $this->saveMessage($from, $message, 'inbound', $messageSid);
        
        // Procesar comandos
        $response = $this->processCommand($from, $message);
        
        return $response;
    }
    
    /**
     * Procesar comandos de WhatsApp
     */
    private function processCommand($from, $message) {
        $message = strtolower(trim($message));
        
        // Comandos básicos
        switch ($message) {
            case 'hola':
            case 'inicio':
                return $this->sendWelcomeMessage($from);
                
            case 'cita':
            case 'solicitar cita':
                return $this->startAppointmentRequest($from);
                
            case 'mis citas':
                return $this->getMyAppointments($from);
                
            case 'ayuda':
            case 'help':
                return $this->sendHelpMessage($from);
                
            default:
                // Verificar si es confirmación de cita
                if (preg_match('/^(si|sí|confirmar|confirmo)\s*(\d+)?$/i', $message, $matches)) {
                    return $this->confirmAppointment($from, $matches[2] ?? null);
                }
                
                // Verificar si es cancelación
                if (preg_match('/^(no|cancelar|cancelo)\s*(\d+)?$/i', $message, $matches)) {
                    return $this->cancelAppointment($from, $matches[2] ?? null);
                }
                
                return $this->sendUnknownCommandMessage($from);
        }
    }
    
    /**
     * Enviar mensaje de bienvenida
     */
    private function sendWelcomeMessage($to) {
        $message = "¡Hola! 👋 Bienvenido al {$this->config['consultorio']['nombre']}\n\n";
        $message .= "Puedes usar estos comandos:\n";
        $message .= "• *CITA* - Solicitar una cita\n";
        $message .= "• *MIS CITAS* - Ver tus citas\n";
        $message .= "• *AYUDA* - Ver todos los comandos\n\n";
        $message .= "¿En qué puedo ayudarte?";
        
        return $this->sendMessage($to, $message);
    }
    
    /**
     * Enviar mensaje de ayuda
     */
    private function sendHelpMessage($to) {
        $message = "📋 *Comandos disponibles:*\n\n";
        $message .= "• *HOLA* - Mensaje de bienvenida\n";
        $message .= "• *CITA* - Solicitar una nueva cita\n";
        $message .= "• *MIS CITAS* - Ver tus citas programadas\n";
        $message .= "• *SÍ* o *CONFIRMAR* - Confirmar cita\n";
        $message .= "• *NO* o *CANCELAR* - Cancelar cita\n";
        $message .= "• *AYUDA* - Ver este mensaje\n\n";
        $message .= "Para solicitar una cita, simplemente escribe *CITA* y te guiaré paso a paso.";
        
        return $this->sendMessage($to, $message);
    }
    
    /**
     * Mensaje para comando no reconocido
     */
    private function sendUnknownCommandMessage($to) {
        $message = "❓ No entendí tu mensaje.\n\n";
        $message .= "Escribe *AYUDA* para ver los comandos disponibles.";
        
        return $this->sendMessage($to, $message);
    }
    
    /**
     * Formatear número de teléfono
     */
    private function formatPhoneNumber($phone) {
        // Remover espacios y caracteres especiales
        $phone = preg_replace('/[^\d+]/', '', $phone);
        
        // Si no empieza con +, agregar código de país (asumiendo República Dominicana +1809)
        if (!str_starts_with($phone, '+')) {
            if (strlen($phone) === 10) {
                $phone = '+1809' . $phone;
            } elseif (strlen($phone) === 7) {
                $phone = '+1809' . $phone;
            }
        }
        
        return $phone;
    }
    
    /**
     * Hacer petición a Twilio API
     */
    private function makeTwilioRequest($endpoint, $data) {
        $url = "https://api.twilio.com/2010-04-01/Accounts/{$this->twilioSid}/{$endpoint}";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, $this->twilioSid . ':' . $this->twilioToken);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 201 && $httpCode !== 200) {
            throw new Exception("Error en Twilio API: HTTP $httpCode - $response");
        }
        
        return json_decode($response, true);
    }
    
    /**
     * Guardar mensaje en base de datos
     */
    private function saveMessage($phone, $message, $direction, $messageSid = null) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO whatsapp_messages 
                (phone, message, direction, message_sid, created_at) 
                VALUES (?, ?, ?, ?, NOW())
            ");
            $stmt->execute([$phone, $message, $direction, $messageSid]);
        } catch (Exception $e) {
            // Log error but don't throw - no queremos que falle el envío por esto
            error_log("Error guardando mensaje WhatsApp: " . $e->getMessage());
        }
    }
    
    // Métodos para funcionalidades específicas (se implementarán después)
    private function startAppointmentRequest($from) {
        // TODO: Implementar solicitud de cita
        return $this->sendMessage($from, "🏥 Para solicitar una cita, por favor llama al consultorio o usa nuestro portal web.\n\nPronto podrás solicitar citas directamente por WhatsApp.");
    }
    
    private function getMyAppointments($from) {
        // TODO: Implementar consulta de citas
        return $this->sendMessage($from, "📅 Para consultar tus citas, por favor llama al consultorio o ingresa a nuestro portal web.\n\nPronto podrás consultar tus citas directamente por WhatsApp.");
    }
    
    private function confirmAppointment($from, $appointmentId = null) {
        // TODO: Implementar confirmación de cita
        return $this->sendMessage($from, "✅ Función de confirmación en desarrollo.\n\nPor favor confirma tu cita llamando al consultorio.");
    }
    
    private function cancelAppointment($from, $appointmentId = null) {
        // TODO: Implementar cancelación de cita
        return $this->sendMessage($from, "❌ Función de cancelación en desarrollo.\n\nPara cancelar tu cita, por favor llama al consultorio.");
    }
}
?>
