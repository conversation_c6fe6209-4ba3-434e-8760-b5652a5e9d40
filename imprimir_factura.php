<?php
//imprimir_factura.php
require_once __DIR__ . '/../config/database.php';  // $pdo ya apunta al tenant actual

// 1) Obtener datos del consultorio
$stmt    = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt->fetch(PDO::FETCH_ASSOC);

// 2) Procesar envío de formulario
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Recoger campos
    $concepto  = trim($_POST['concepto']);
    $precio    = floatval($_POST['precio']);
    $moneda    = $_POST['moneda']   ?? 'USD';
    $modopago  = $_POST['modopago'] ?? 'Transferencia';
    $fechapago = $_POST['fechapago']?? date('Y-m-d');

    // Campos bancarios
    $banco_destino             = $_POST['banco_destino']             ?? '';
    $cuenta_marcsoftware       = trim($_POST['cuenta_marcsoftware']    ?? '');
    $beneficiario_marcsoftware = $_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions';

    try {
        // Datos del cliente desde EMPRESA
        $cliente_nombre       = $empresa['NOMBRE']       ?? 'Cliente';
        $cliente_rnc          = $empresa['RNC']          ?? '';
        $cliente_telefono     = $empresa['TELEFONO']     ?? '';
        $cliente_email        = $empresa['CORREOE']      ?? '';
        $cliente_direccion    = $empresa['CALLE'] . ', ' .
                                $empresa['MUNICIPIO'] . ', ' .
                                $empresa['PROVINCIA'];
        $cliente_especialidad = $empresa['ESPECIALIDAD'] ?? '';
        $cliente_subdominio   = $empresa['SUBDOMINIO']   ?? '';

        // 2.1) Verificar o crear cliente
        $stmt = $pdo->prepare("SELECT CLAVE FROM CLIENTES_SOFTWARE WHERE RNC = ?");
        $stmt->execute([$cliente_rnc]);
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$row) {
            // Crear tabla clientes si no existe
            $pdo->exec("CREATE TABLE IF NOT EXISTS CLIENTES_SOFTWARE (
                CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                NOMBRE_COMPLETO VARCHAR(200),
                RNC VARCHAR(20),
                TELEFONO VARCHAR(20),
                EMAIL VARCHAR(100),
                DIRECCION VARCHAR(200),
                CIUDAD VARCHAR(100),
                PROVINCIA VARCHAR(100),
                ESPECIALIDAD VARCHAR(100),
                SUBDOMINIO VARCHAR(50),
                FECHA_REGISTRO DATE,
                ESTATUS CHAR(1) DEFAULT 'A'
            )");
            $ins = $pdo->prepare("INSERT INTO CLIENTES_SOFTWARE
                (NOMBRE_COMPLETO,RNC,TELEFONO,EMAIL,DIRECCION,CIUDAD,PROVINCIA,ESPECIALIDAD,SUBDOMINIO,FECHA_REGISTRO)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())");
            $ins->execute([
                $cliente_nombre,
                $cliente_rnc,
                $cliente_telefono,
                $cliente_email,
                $empresa['CALLE']       ?? '',
                $empresa['MUNICIPIO']   ?? '',
                $empresa['PROVINCIA']   ?? '',
                $cliente_especialidad,
                $cliente_subdominio
            ]);
            $cliente_clave = $pdo->lastInsertId();
        } else {
            $cliente_clave = $row['CLAVE'];
        }

        // 2.2) Crear tabla facturas si no existe
        $pdo->exec("CREATE TABLE IF NOT EXISTS FACTURAS_SOFTWARE (
            CLAVE INT AUTO_INCREMENT PRIMARY KEY,
            CLIENTE_CLAVE INT,
            NUMERO_FACTURA VARCHAR(20),
            CONCEPTO TEXT,
            PRECIO DECIMAL(10,2),
            MONEDA VARCHAR(5),
            MODO_PAGO VARCHAR(20),
            BANCO_DESTINO VARCHAR(50),
            CUENTA_DESTINO VARCHAR(30),
            BENEFICIARIO VARCHAR(100),
            FECHA_FACTURA DATE,
            FECHA_VENCIMIENTO DATE,
            ESTATUS VARCHAR(10) DEFAULT 'PENDIENTE',
            FECHA_CREACION DATETIME,
            USUARIO_CREACION INT
        )");

        // 2.3) Generar número automático
        $max = $pdo
            ->query("SELECT MAX(CAST(SUBSTRING(NUMERO_FACTURA,4) AS UNSIGNED)) AS m FROM FACTURAS_SOFTWARE")
            ->fetch(PDO::FETCH_ASSOC)['m'];
        $num = 'MS-' . (($max !== null ? intval($max) : 1000) + 1);

        // 2.4) Incluir datos de transferencia en el concepto
        $fullConcept = $concepto;
        if ($modopago === 'Transferencia' && $banco_destino) {
            $fullConcept .= "\n\nDATOS PARA TRANSFERENCIA:\n"
                         . "Banco: {$banco_destino}\n"
                         . "Cuenta: {$cuenta_marcsoftware}\n"
                         . "Beneficiario: {$beneficiario_marcsoftware}";
        }

        // 2.5) Insertar la factura
        $fecha_venc = date('Y-m-d', strtotime('+30 days', strtotime($fechapago)));
        $stmt = $pdo->prepare("INSERT INTO FACTURAS_SOFTWARE
            (CLIENTE_CLAVE,NUMERO_FACTURA,CONCEPTO,PRECIO,MONEDA,MODO_PAGO,
             BANCO_DESTINO,CUENTA_DESTINO,BENEFICIARIO,FECHA_FACTURA,FECHA_VENCIMIENTO,
             FECHA_CREACION,USUARIO_CREACION)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), 1)");
        $stmt->execute([
            $cliente_clave, $num, $fullConcept, $precio, $moneda, $modopago,
            $banco_destino, $cuenta_marcsoftware, $beneficiario_marcsoftware,
            $fechapago, $fecha_venc
        ]);

        // 3) Mostrar mensaje y enlace de impresión
        $lastId = $pdo->lastInsertId();
        echo "<div class='alert alert-success'>
                <h4>✅ Factura {$num} creada</h4>
                <p><strong>Cliente:</strong> {$cliente_nombre}</p>
                <p><strong>Monto:</strong> {$moneda} {$precio}</p>
                <p><strong>Vence:</strong> {$fecha_venc}</p>
                <p><a href='imprimir_factura.php?id={$lastId}' target='_blank' class='btn btn-primary'>
                  🖨️ Imprimir Factura
                </a></p>
              </div>";

    } catch (Exception $e) {
        echo "<div class='alert alert-danger'>Error: " . htmlspecialchars($e->getMessage()) . "</div>";
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Crear Factura - MarcSoftware Solutions</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <script>
    function toggleBankFields() {
      const modo = document.getElementById('modopago').value;
      document.getElementById('bank-fields').style.display = modo === 'Transferencia' ? 'block' : 'none';
    }
    document.addEventListener('DOMContentLoaded', toggleBankFields);
  </script>
</head>
<body>
<div class="container mt-4">
  <h2>🏢 Crear Factura - MarcSoftware Solutions</h2>
  <p class="text-muted">Facturar servicios de desarrollo de software a clientes</p>

  <?php if ($empresa): ?>
  <div class="card mb-4">
    <div class="card-header bg-info text-white">
      <h5>👨‍⚕️ Datos del Consultorio</h5>
    </div>
    <div class="card-body">
      <p><strong>Nombre:</strong> <?=htmlspecialchars($empresa['NOMBRE'])?></p>
      <p><strong>RNC:</strong> <?=htmlspecialchars($empresa['RNC'])?></p>
      <p><strong>Especialidad:</strong> <?=htmlspecialchars($empresa['ESPECIALIDAD'])?></p>
      <p><strong>Subdominio:</strong> <?=htmlspecialchars($empresa['SUBDOMINIO'])?></p>
      <p><strong>Dirección:</strong>
         <?=htmlspecialchars($empresa['CALLE'] . ', ' . $empresa['MUNICIPIO'] . ', ' . $empresa['PROVINCIA'])?></p>
      <p><strong>Tel:</strong> <?=htmlspecialchars($empresa['TELEFONO'])?> ·
         <strong>Email:</strong> <?=htmlspecialchars($empresa['CORREOE'])?></p>
    </div>
  </div>
  <?php endif; ?>

  <form method="POST">
    <div class="card mb-4">
      <div class="card-header"><h5>💰 Detalles de Facturación</h5></div>
      <div class="card-body">
        <div class="mb-3">
          <label class="form-label">Concepto/Servicio</label>
          <textarea name="concepto" class="form-control" rows="3" required><?=htmlspecialchars($_POST['concepto']??'')?></textarea>
        </div>
        <div class="row">
          <div class="col-md-4 mb-3">
            <label class="form-label">Precio</label>
            <input type="number" name="precio" step="0.01" class="form-control"
                   value="<?=htmlspecialchars($_POST['precio']??'')?>" required>
          </div>
          <div class="col-md-4 mb-3">
            <label class="form-label">Moneda</label>
            <select name="moneda" class="form-select">
              <option value="USD" <?=($_POST['moneda']??'')==='USD'?'selected':''?>>USD</option>
              <option value="DOP" <?=($_POST['moneda']??'')==='DOP'?'selected':''?>>DOP</option>
            </select>
          </div>
          <div class="col-md-4 mb-3">
            <label class="form-label">Modo de Pago</label>
            <select id="modopago" name="modopago" class="form-select" onchange="toggleBankFields()">
              <option <?=($_POST['modopago']??'')==='Transferencia'?'selected':''?>>Transferencia</option>
              <option <?=($_POST['modopago']??'')==='Efectivo'?'selected':''?>>Efectivo</option>
              <option <?=($_POST['modopago']??'')==='Cheque'?'selected':''?>>Cheque</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <div id="bank-fields" class="card mb-4" style="display:none;">
      <div class="card-header"><h5>🏦 Datos Bancarios</h5></div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4 mb-3">
            <label class="form-label">Banco Destino</label>
            <select name="banco_destino" class="form-select">
              <option value="">Seleccionar banco</option>
              <option <?=($_POST['banco_destino']??'')==='Banco Popular'?'selected':''?>>Banco Popular</option>
              <option <?=($_POST['banco_destino']??'')==='Banreservas'?'selected':''?>>Banreservas</option>
            </select>
          </div>
          <div class="col-md-4 mb-3">
            <label class="form-label">Cuenta MarcSoftware</label>
            <input type="text" name="cuenta_marcsoftware" class="form-control"
                   value="<?=htmlspecialchars($_POST['cuenta_marcsoftware']??'')?>">
          </div>
          <div class="col-md-4 mb-3">
            <label class="form-label">Beneficiario</label>
            <input type="text" name="beneficiario_marcsoftware" class="form-control"
                   value="<?=htmlspecialchars($_POST['beneficiario_marcsoftware']??'MarcSoftware Solutions')?>">
          </div>
        </div>
      </div>
    </div>

    <div class="mb-3">
      <label class="form-label">Fecha de Factura</label>
      <input type="date" name="fechapago" class="form-control"
             value="<?=htmlspecialchars($_POST['fechapago']??date('Y-m-d'))?>">
    </div>
    <button type="submit" class="btn btn-success btn-lg">💾 Generar Factura</button>
  </form>

</div>
</body>
</html>
