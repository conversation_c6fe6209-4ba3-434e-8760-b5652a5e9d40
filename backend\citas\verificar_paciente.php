<?php
header('Content-Type: application/json');
// Opcional: Añadir encabezados de seguridad
// header('Access-Control-Allow-Origin: https://tu-dominio.com');
// header('Access-Control-Allow-Methods: POST, GET');
// header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php'; // Incluir la configuración de la base de datos

// Verificar si se envió el parámetro 'paciente' a través de POST
if (isset($_POST['paciente'])) {
    // Normalizar el texto de la búsqueda
    $paciente = trim($_POST['paciente']);

    // Depuración: guarda el valor del paciente en un archivo de log con timestamp
    $logEntry = date('Y-m-d H:i:s') . " - Paciente: " . print_r($paciente, true) . PHP_EOL;
    file_put_contents('debug.log', $logEntry, FILE_APPEND);

    // Verificar que la variable no esté vacía
    if (empty($paciente)) {
        echo json_encode(['error' => 'El parámetro paciente está vacío']);
        exit;
    }

    try {
        // Determinar si el paciente es una CEDULA numérica
        if (ctype_digit($paciente)) {
            $stmt = $pdo->prepare("
                SELECT * 
                FROM PACIENTES 
                WHERE CEDULA = :cedula
            ");
            $stmt->execute(['cedula' => $paciente]);
        } else {
            $paciente = strtoupper($paciente);
            $stmt = $pdo->prepare("
                SELECT * 
                FROM PACIENTES 
                WHERE NOMBRES LIKE :nombres
            ");
            $stmt->execute(['nombres' => "%$paciente%"]);
        }

        // Obtener todos los resultados
        $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if ($resultados) {
            echo json_encode([
                'existe' => true,
                'pacientes' => array_map(function($paciente) {
                    return [
                        'NOMBRES' => $paciente['NOMBRES'],
                        'CEDULA' => $paciente['CEDULA']
                        // Añadir otros campos si es necesario
                    ];
                }, $resultados)
            ]);
        } else {
            echo json_encode(['existe' => false, 'mensaje' => 'Paciente no encontrado']);
        }
    } catch (PDOException $e) {
        // Registrar el error en un archivo de log
        file_put_contents('error.log', date('Y-m-d H:i:s') . " - " . $e->getMessage() . PHP_EOL, FILE_APPEND);
        // Mostrar un mensaje genérico al usuario
        echo json_encode(['error' => 'Error interno del servidor. Por favor, intenta nuevamente más tarde.']);
    }
} else {
    // Si no se envió el parámetro 'paciente'
    echo json_encode(['error' => 'No se envió el parámetro paciente']);
}
?>
