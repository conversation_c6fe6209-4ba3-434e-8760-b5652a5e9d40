<?php
session_start();
include('../config/database.php');
date_default_timezone_set('America/Santo_Domingo');

// Recibir la fecha seleccionada desde el frontend
$fechaSeleccionada = isset($_GET['fecha']) ? $_GET['fecha'] : date('Y-m-d');
$fechaHoy = date('Y-m-d');
$horaActual = date('H:i'); // Hora actual en formato 24h

// Obtener configuración del horario desde la base de datos
$sqldatosConfig = "SELECT HORAINICIO, HORAFIN, DURACION FROM CONFIG LIMIT 1";
$stmtdatosConfig = $pdo->prepare($sqldatosConfig);
$stmtdatosConfig->execute();
$datosConfig = $stmtdatosConfig->fetch(PDO::FETCH_ASSOC);

$horaInicio = substr($datosConfig['HORAINICIO'], 0, 2) . ':' . substr($datosConfig['HORAINICIO'], 2, 2);
$horaFin = substr($datosConfig['HORAFIN'], 0, 2) . ':' . substr($datosConfig['HORAFIN'], 2, 2);
$duracion = (int) $datosConfig['DURACION'];

// Función para generar todos los horarios posibles
function generarHorarios($horaInicio, $horaFin, $duracion) {
    $horarios = [];
    $current = strtotime($horaInicio);
    $end = strtotime($horaFin);

    while ($current <= $end) {
        $horarios[] = date('H:i', $current); // Formato 24h para comparación
        $current = strtotime("+$duracion minutes", $current);
    }

    return $horarios;
}

// Obtener horarios ocupados desde la base de datos
$sqlCitas = "SELECT HORACON FROM CITAMEDIC WHERE FECHACON = ?";
$stmtCitas = $pdo->prepare($sqlCitas);
$stmtCitas->execute([$fechaSeleccionada]);
$citasOcupadas = $stmtCitas->fetchAll(PDO::FETCH_COLUMN);

// Convertir a formato 24h
$citasOcupadas = array_map(function ($hora) {
    return date('H:i', strtotime($hora));
}, $citasOcupadas);

// Generar todos los horarios disponibles
$horarios = generarHorarios($horaInicio, $horaFin, $duracion);

// **FILTRAR HORAS OCUPADAS**
$horariosDisponibles = array_filter($horarios, function ($hora) use ($citasOcupadas, $fechaSeleccionada, $fechaHoy, $horaActual) {
    // Si la fecha es hoy, excluir horas pasadas
    if ($fechaSeleccionada === $fechaHoy && strtotime($hora) <= strtotime($horaActual)) {
        return false;
    }

    // Excluir horarios ocupados
    return !in_array($hora, $citasOcupadas);
});

// Convertir a formato AM/PM con "a.m." y "p.m."
$horariosDisponibles = array_map(function ($hora) {
    return str_replace(['AM', 'PM'], ['a.m.', 'p.m.'], date('h:i A', strtotime($hora)));
}, array_values($horariosDisponibles));

echo json_encode(array_values($horariosDisponibles));
?>
