<?php
/**
 * Sincroniza de forma bidireccional la fotografía de un paciente.
 */

function syncFotografiaBidireccional($cedula) {
    $directorioFotos = __DIR__ . '/fotografias/';
    $nombreArchivo = $cedula . '.bmp';
    $localPath = $directorioFotos . $nombreArchivo;
    $remoteUrl = "https://marcsoftware.com/mi_consultorio/backend/pacientes/fotografias/" . $nombreArchivo;

    // Verificar existencia local
    $localExists = file_exists($localPath);
    $localMtime = $localExists ? filemtime($localPath) : 0;

    // Obtener headers del archivo remoto
    $headers = @get_headers($remoteUrl, 1);
    $remoteExists = false;
    $remoteMtime = 0;
    if ($headers && strpos($headers[0], "200") !== false) {
        $remoteExists = true;
        if (isset($headers['Last-Modified'])) {
            $remoteMtime = strtotime($headers['Last-Modified']);
        }
    }

    // Lógica de sincronización
    if (!$localExists && $remoteExists) {
        // Descargar desde el servidor
        $content = file_get_contents($remoteUrl);
        if ($content !== false) {
            file_put_contents($localPath, $content);
            echo json_encode(["status" => "success", "message" => "Foto descargada"]);
        } else {
            echo json_encode(["status" => "error", "message" => "Error al descargar foto"]);
        }
    } elseif ($localExists && !$remoteExists) {
        // Subir al servidor
        $cfile = new CURLFile($localPath, 'image/bmp', $nombreArchivo);
        $ch = curl_init("https://marcsoftware.com/mi_consultorio/api/subir_foto.php");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, ['archivo' => $cfile, 'nombreArchivo' => $nombreArchivo]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        echo $response;
    } elseif ($localExists && $remoteExists) {
        if ($localMtime > $remoteMtime) {
            // Subir la foto local (más reciente)
            $cfile = new CURLFile($localPath, 'image/bmp', $nombreArchivo);
            $ch = curl_init("https://marcsoftware.com/mi_consultorio/api/subir_foto.php");
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, ['archivo' => $cfile, 'nombreArchivo' => $nombreArchivo]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);
            echo $response;
        } elseif ($remoteMtime > $localMtime) {
            // Descargar foto desde remoto (más reciente)
            $content = file_get_contents($remoteUrl);
            if ($content !== false) {
                file_put_contents($localPath, $content);
                echo json_encode(["status" => "success", "message" => "Foto actualizada"]);
            }
        }
    } else {
        echo json_encode(["status" => "success", "message" => "Sincronización completada"]);
    }
}

// Ejecutar sincronización
if (isset($_GET['cedula'])) {
    syncFotografiaBidireccional($_GET['cedula']);
} else {
    echo json_encode(["status" => "error", "message" => "Cédula no proporcionada"]);
}