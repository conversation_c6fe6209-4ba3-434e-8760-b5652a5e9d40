<?php
require_once '../config/database.php';
header('Content-Type: application/json');

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Validar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'error' => 'Método no permitido']);
    exit;
}

// Validar que se reciban datos
if (empty($_POST)) {
    echo json_encode(['success' => false, 'error' => 'No se recibieron datos']);
    exit;
}

// ✅ Recibir datos del formulario
$data = $_POST;

// Validar campos obligatorios
$camposObligatorios = ['CLAVE', 'NOMBRES', 'APELLIDOS', 'CEDULA'];
foreach ($camposObligatorios as $campo) {
    if (empty($data[$campo])) {
        echo json_encode(['success' => false, 'error' => "El campo $campo es obligatorio"]);
        exit;
    }
}

error_log("CLAVE recibida en PHP: " . $data['CLAVE']);

// ✅ Asegurar valores predeterminados si es necesario
$data['SINCRONIZADO'] = $data['SINCRONIZADO'] ?? 0;
$data['PROCEDENCIA'] = $data['PROCEDENCIA'] ?? '0';
$data['NSS'] = $data['CEDULA'] ?? null;

// ✅ Limpiar campos que no existen en la base de datos
$camposValidos = [
    'CLAVE', 'NOMBRES', 'APELLIDOS', 'CEDULA', 'FECHANAC', 'SEXO', 'NACIONALIDAD',
    'RH', 'ECORREO', 'ESTADOCIVIL', 'LUGARNAC', 'OCUPACION', 'RELIGION', 'CALLE',
    'PROVINCIA', 'LOCALIDAD', 'MUNICIPIO', 'PAIS', 'REFERENCIA', 'CELULAR', 'TELEFONO',
    'TELTRABAJO', 'FAX', 'NOMBRERESP', 'DIRECCIONRESP', 'CEDULARESP', 'TELEFONORESP',
    'FAMILIARPROX', 'DIRECCIONFAMILIAR', 'TELEFONOFAMILIAR', 'ARS', 'PLANES', 'AFILIADO',
    'VIGENCIA', 'NSS', 'CATEGORIA', 'OBSERVACIONES', 'PESOHABITUAL', 'NIVELESCOLAR',
    'PROCEDENCIA', 'RECORDCLINICA', 'SINCRONIZADO'
];

// Filtrar solo campos válidos
$dataFiltrada = [];
foreach ($camposValidos as $campo) {
    $dataFiltrada[$campo] = $data[$campo] ?? '';
}
$data = $dataFiltrada;

// ✅ Preparar la consulta para actualizar datos del paciente
$sql = "UPDATE PACIENTES SET 
    NOMBRES = ?, APELLIDOS = ?, CEDULA = ?, FECHANAC = ?, SEXO = ?, 
    NACIONALIDAD = ?, RH = ?, ECORREO = ?, ESTADOCIVIL = ?, LUGARNAC = ?, 
    OCUPACION = ?, RELIGION = ?, CALLE = ?, PROVINCIA = ?, LOCALIDAD = ?, 
    MUNICIPIO = ?, PAIS = ?, REFERENCIA = ?, CELULAR = ?, TELEFONO = ?, 
    TELTRABAJO = ?, FAX = ?, NOMBRERESP = ?, DIRECCIONRESP = ?, CEDULARESP = ?, 
    TELEFONORESP = ?, FAMILIARPROX = ?, DIRECCIONFAMILIAR = ?, TELEFONOFAMILIAR = ?,  ARS = ?, PLANES = ?, AFILIADO = ?, VIGENCIA = ?, NSS = ?, CATEGORIA = ?, OBSERVACIONES = ?, PESOHABITUAL = ?, NIVELESCOLAR = ?, PROCEDENCIA = ?, RECORDCLINICA = ?, SINCRONIZADO = ? WHERE CLAVE = ?";

$params = [
    $data['NOMBRES'], $data['APELLIDOS'], $data['CEDULA'], $data['FECHANAC'], $data['SEXO'],
    $data['NACIONALIDAD'], $data['RH'], $data['ECORREO'], $data['ESTADOCIVIL'], $data['LUGARNAC'],
    $data['OCUPACION'], $data['RELIGION'], $data['CALLE'], $data['PROVINCIA'], $data['LOCALIDAD'],
    $data['MUNICIPIO'], $data['PAIS'], $data['REFERENCIA'], $data['CELULAR'], $data['TELEFONO'],
    $data['TELTRABAJO'], $data['FAX'], $data['NOMBRERESP'], $data['DIRECCIONRESP'], $data['CEDULARESP'],
    $data['TELEFONORESP'], $data['FAMILIARPROX'], $data['DIRECCIONFAMILIAR'], $data['TELEFONOFAMILIAR'],
    $data['ARS'], $data['PLANES'], $data['AFILIADO'], $data['VIGENCIA'], $data['NSS'], $data['CATEGORIA'],
    $data['OBSERVACIONES'], $data['PESOHABITUAL'], $data['NIVELESCOLAR'], $data['PROCEDENCIA'],
    $data['RECORDCLINICA'], $data['SINCRONIZADO'], $data['CLAVE']
];

try {
    $stmt = $pdo->prepare($sql);
    $success = $stmt->execute($params);

    error_log("Iniciando procesamiento de imagen");

if (!empty($_FILES['nuevaFoto']['name'])) {
    error_log("✅ Iniciando prueba de guardado de imagen");
    
    // ✅ Guardar usando CEDULA como nombre de archivo
    $nombreFoto = $data['CEDULA'] . '.bmp';
    $directorioFotos = __DIR__ . '/fotografias/';

    if (!is_dir($directorioFotos)) {
        mkdir($directorioFotos, 0777, true);
    }

    $rutaFoto = $directorioFotos . $nombreFoto;
    $mimeType = mime_content_type($_FILES['nuevaFoto']['tmp_name']);

    error_log("✅ MIME Type: " . $mimeType);

    // ✅ Permitir también image/x-ms-bmp
    if (in_array($mimeType, ['image/jpeg', 'image/png', 'image/bmp', 'image/x-ms-bmp'])) {
        error_log("✅ Formato de imagen aceptado");

        // ✅ Crear la imagen según el formato
        if ($mimeType === 'image/jpeg') {
            $img = imagecreatefromjpeg($_FILES['nuevaFoto']['tmp_name']);
        } elseif ($mimeType === 'image/png') {
            $img = imagecreatefrompng($_FILES['nuevaFoto']['tmp_name']);
        } elseif ($mimeType === 'image/bmp' || $mimeType === 'image/x-ms-bmp') {
            $img = imagecreatefrombmp($_FILES['nuevaFoto']['tmp_name']);
        }

        if ($img) {
            imagebmp($img, $rutaFoto);
            imagedestroy($img);
            error_log("✅ Imagen guardada correctamente en: " . $rutaFoto);

            // ✅ Obtener el valor de `REGISTRO` desde la tabla PACIENTES
            $stmt = $pdo->prepare("SELECT REGISTRO FROM PACIENTES WHERE CLAVE = ?");
            $stmt->execute([$data['CLAVE']]);
            $row = $stmt->fetch();
            $claveImg = $row ? $row['REGISTRO'] : null;

            error_log("✅ ClaveImg obtenida: " . $claveImg);

            // ✅ Definir ruta completa para base de datos
            $rutaFotoBaseDatos = 'fotografias/' . $nombreFoto;

            // ✅ Verificar si la foto ya existe en la tabla FOTOGRAFIAS
            $stmt = $pdo->prepare("SELECT CLAVE FROM FOTOGRAFIAS WHERE CLAVEPAC = ?");
            $stmt->execute([$data['CLAVE']]);
            $fotoExistente = $stmt->fetch();

            $nombrePaciente = $data['NOMBRES'] . ' ' . $data['APELLIDOS'];
            $tipoFoto = 1;
            $cedula = $data['CEDULA'];
            $fechaCap = date('Y-m-d H:i:s'); // Capturar la fecha actual
           
            // ✅ Definir $archivoFoto correctamente usando $rutaFoto
                $archivoFoto = 'fotografias/' . basename($rutaFoto);
           
            if ($fotoExistente) {
                // ✅ Si ya existe → Actualizar
               
             $stmt = $pdo->prepare("UPDATE FOTOGRAFIAS 
                SET ARCHIVO = ?, FECHA_CAP = ?,  SINCRONIZADO = 0
              WHERE CLAVEPAC = ?");
              $stmt->execute([$archivoFoto, $fechaCap, $data['CLAVE']]);  
                error_log("✅ Foto actualizada correctamente");
            } else {
                // ✅ Si NO existe → Insertar con todos los campos
                $stmt = $pdo->prepare("INSERT INTO FOTOGRAFIAS 
                    (NOMBRE, ARCHIVO, CLAVEPAC, TIPO, CEDULA, CLAVEIMG,  FECHA_CAP, SINCRONIZADO) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, 0)");
                $stmt->execute([$nombrePaciente, $rutaFotoBaseDatos, $data['CLAVE'], $tipoFoto, $cedula, $claveImg, $fechaCap]);
                error_log("✅ Foto insertada correctamente");
            }
        }
    } else {
        error_log("❌ Tipo de archivo no permitido: $mimeType");
    }
}

    echo json_encode([
        'success' => true,
        'message' => 'Paciente actualizado correctamente',
        'clave' => $data['CLAVE']
    ]);
} catch (PDOException $e) {
    error_log("Error de base de datos: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Error al actualizar el paciente en la base de datos'
    ]);
} catch (Exception $e) {
    error_log("Error general: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Error interno del servidor'
    ]);
}
