/**
 * Sistema de Impresión Médica Profesional
 * Maneja la impresión de indicaciones médicas con plantillas profesionales
 * 
 * <AUTHOR> de Consultorio
 * @version 1.0
 */

class ImpresionMedica {
    constructor() {
        this.baseUrl = window.location.origin + window.location.pathname.replace(/[^/]*$/, '');
        this.init();
    }

    init() {
        this.setupPrintButtons();
        this.setupKeyboardShortcuts();
    }

    /**
     * Configura los botones de impresión
     */
    setupPrintButtons() {
        // Reemplazar el botón de imprimir existente
        const printButton = document.querySelector('button[onclick="window.print()"]');
        if (printButton) {
            printButton.removeAttribute('onclick');
            printButton.innerHTML = '<i class="fas fa-print me-2"></i>Imprimir Profesional';
            printButton.className = 'btn btn-primary btn-lg dropdown-toggle';
            printButton.setAttribute('data-bs-toggle', 'dropdown');
            printButton.setAttribute('aria-expanded', 'false');

            // Crear menú dropdown
            const dropdownMenu = this.createPrintDropdown();
            printButton.parentNode.insertBefore(dropdownMenu, printButton.nextSibling);
            
            // Envolver en div dropdown
            const dropdownDiv = document.createElement('div');
            dropdownDiv.className = 'dropdown';
            printButton.parentNode.insertBefore(dropdownDiv, printButton);
            dropdownDiv.appendChild(printButton);
            dropdownDiv.appendChild(dropdownMenu);
        }
    }

    /**
     * Crea el menú dropdown para opciones de impresión
     */
    createPrintDropdown() {
        const dropdown = document.createElement('ul');
        dropdown.className = 'dropdown-menu dropdown-menu-end';
        
        const opciones = [
            {
                icon: 'fas fa-file-alt',
                text: 'Imprimir Este Registro',
                action: 'individual',
                description: 'Imprime solo el registro actual'
            },
            {
                icon: 'fas fa-files-o',
                text: 'Imprimir Todos los Registros',
                action: 'completo',
                description: 'Imprime todos los registros de esta categoría'
            },
            {
                icon: 'fas fa-clipboard-list',
                text: 'Resumen de Indicaciones',
                action: 'resumen',
                description: 'Resumen de todas las indicaciones del paciente'
            },
            {
                type: 'divider'
            },
            {
                icon: 'fas fa-print',
                text: 'Vista Previa Rápida',
                action: 'preview',
                description: 'Vista previa sin plantilla'
            }
        ];

        opciones.forEach(opcion => {
            if (opcion.type === 'divider') {
                const divider = document.createElement('li');
                divider.innerHTML = '<hr class="dropdown-divider">';
                dropdown.appendChild(divider);
                return;
            }

            const li = document.createElement('li');
            const a = document.createElement('a');
            a.className = 'dropdown-item';
            a.href = '#';
            a.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="${opcion.icon} me-3 text-primary" style="width: 20px;"></i>
                    <div>
                        <div class="fw-bold">${opcion.text}</div>
                        <small class="text-muted">${opcion.description}</small>
                    </div>
                </div>
            `;
            
            a.addEventListener('click', (e) => {
                e.preventDefault();
                this.handlePrintAction(opcion.action);
            });
            
            li.appendChild(a);
            dropdown.appendChild(li);
        });

        return dropdown;
    }

    /**
     * Maneja las acciones de impresión
     */
    handlePrintAction(action) {
        const urlParams = new URLSearchParams(window.location.search);
        const CLAVEPAC = urlParams.get('CLAVEPAC');
        const CEDULA = urlParams.get('CEDULA');
        const TABLA = urlParams.get('TABLA');
        const CLAVE = urlParams.get('CLAVE');

        if (!CLAVEPAC || !TABLA) {
            this.showAlert('Error: Faltan parámetros necesarios para la impresión', 'error');
            return;
        }

        switch (action) {
            case 'individual':
                if (!CLAVE) {
                    this.showAlert('Error: No hay registro seleccionado para imprimir', 'warning');
                    return;
                }
                this.openPrintWindow('individual', CLAVEPAC, CEDULA, TABLA, CLAVE);
                break;
                
            case 'completo':
                this.openPrintWindow('completo', CLAVEPAC, CEDULA, TABLA);
                break;
                
            case 'resumen':
                this.openPrintWindow('resumen', CLAVEPAC, CEDULA);
                break;
                
            case 'preview':
                this.printPreview();
                break;
                
            default:
                this.showAlert('Acción de impresión no reconocida', 'error');
        }
    }

    /**
     * Abre ventana de impresión profesional
     */
    openPrintWindow(tipo, CLAVEPAC, CEDULA, TABLA = '', CLAVE = '') {
        let url = `${this.baseUrl}imprimir_indicaciones.php?tipo=${tipo}&CLAVEPAC=${CLAVEPAC}&CEDULA=${CEDULA}`;
        
        if (TABLA) {
            url += `&TABLA=${TABLA}`;
        }
        
        if (CLAVE) {
            url += `&CLAVE=${CLAVE}`;
        }

        // Mostrar loading
        this.showLoading('Preparando documento para impresión...');

        // Abrir ventana
        const printWindow = window.open(url, 'ImpresionMedica', 
            'width=800,height=900,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no');
        
        if (!printWindow) {
            this.hideLoading();
            this.showAlert('Error: No se pudo abrir la ventana de impresión. Verifique que no esté bloqueada por el navegador.', 'error');
            return;
        }

        // Ocultar loading cuando la ventana cargue
        printWindow.onload = () => {
            this.hideLoading();
        };

        // Fallback para ocultar loading
        setTimeout(() => {
            this.hideLoading();
        }, 3000);
    }

    /**
     * Vista previa rápida (método original)
     */
    printPreview() {
        window.print();
    }

    /**
     * Configura atajos de teclado
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+P para imprimir
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.handlePrintAction('individual');
            }
            
            // Ctrl+Shift+P para resumen
            if (e.ctrlKey && e.shiftKey && e.key === 'P') {
                e.preventDefault();
                this.handlePrintAction('resumen');
            }
            
            // Ctrl+Alt+P para todos los registros
            if (e.ctrlKey && e.altKey && e.key === 'p') {
                e.preventDefault();
                this.handlePrintAction('completo');
            }
        });
    }

    /**
     * Muestra alertas
     */
    showAlert(message, type = 'info') {
        // Crear alerta Bootstrap
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        
        const iconMap = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-triangle',
            'warning': 'fas fa-exclamation-circle',
            'info': 'fas fa-info-circle'
        };
        
        alertDiv.innerHTML = `
            <i class="${iconMap[type] || iconMap.info} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // Auto-remover después de 5 segundos
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    /**
     * Muestra indicador de carga
     */
    showLoading(message = 'Cargando...') {
        // Remover loading existente
        this.hideLoading();
        
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'medical-loading';
        loadingDiv.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
        loadingDiv.style.cssText = 'background: rgba(0,0,0,0.7); z-index: 9999;';
        
        loadingDiv.innerHTML = `
            <div class="bg-white rounded p-4 text-center shadow">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <div class="fw-bold">${message}</div>
            </div>
        `;
        
        document.body.appendChild(loadingDiv);
    }

    /**
     * Oculta indicador de carga
     */
    hideLoading() {
        const loading = document.getElementById('medical-loading');
        if (loading) {
            loading.remove();
        }
    }

    /**
     * Obtiene información del registro actual
     */
    getCurrentRecordInfo() {
        const urlParams = new URLSearchParams(window.location.search);
        return {
            CLAVEPAC: urlParams.get('CLAVEPAC'),
            CEDULA: urlParams.get('CEDULA'),
            TABLA: urlParams.get('TABLA'),
            CLAVE: urlParams.get('CLAVE')
        };
    }
}

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    // Verificar si estamos en el módulo de texto simple
    if (window.location.pathname.includes('modulo_texto_simple.php')) {
        new ImpresionMedica();
        
        // Agregar información de atajos
        const shortcutsInfo = document.createElement('div');
        shortcutsInfo.className = 'position-fixed bottom-0 start-0 p-3 text-muted small';
        shortcutsInfo.style.cssText = 'z-index: 1000; opacity: 0.7;';
        shortcutsInfo.innerHTML = `
            <div class="bg-white rounded p-2 shadow-sm border">
                <strong>Atajos de Impresión:</strong><br>
                <small>
                    Ctrl+P: Registro actual<br>
                    Ctrl+Shift+P: Resumen<br>
                    Ctrl+Alt+P: Todos los registros
                </small>
            </div>
        `;
        
        document.body.appendChild(shortcutsInfo);
        
        // Ocultar después de 10 segundos
        setTimeout(() => {
            shortcutsInfo.style.opacity = '0.3';
        }, 10000);
        
        shortcutsInfo.addEventListener('mouseenter', () => {
            shortcutsInfo.style.opacity = '1';
        });
        
        shortcutsInfo.addEventListener('mouseleave', () => {
            shortcutsInfo.style.opacity = '0.3';
        });
    }
});

// Exportar para uso global
window.ImpresionMedica = ImpresionMedica;
