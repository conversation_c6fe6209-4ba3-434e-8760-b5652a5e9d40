<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Prescripción" en Indicaciones
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Actualizar la función getPrescripciones para incluir todos los campos de la tabla PRESCRIPCIONES
function getPrescripciones($pdo, $clavePac) {
    $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
            MEDICAMENTO as medicamento, DOSIS as dosis, FRECUENCIA as frecuencia, 
            DURACION as duracion, INDICACIONES as indicaciones, ACTIVO as activo,
            VIA_ADMINISTRACION as via_administracion, PRESENTACION as presentacion 
            FROM PRESCRIPCIONES WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    // Actualizar la acción savePrescripcion para incluir todos los campos de la tabla PRESCRIPCIONES
    if ($action === 'savePrescripcion') {
        $medicamento = $_POST['medicamento'];
        $dosis = $_POST['dosis'];
        $frecuencia = $_POST['frecuencia'];
        $duracion = $_POST['duracion'];
        $indicaciones = $_POST['indicaciones'];
        $activo = $_POST['activo'];
        $via_administracion = $_POST['via_administracion'] ?? 'Oral';
        $presentacion = $_POST['presentacion'] ?? null;
        
        try {
            $sql = "INSERT INTO PRESCRIPCIONES (CLAVEPAC, MEDICAMENTO, DOSIS, FRECUENCIA, DURACION, INDICACIONES, ACTIVO, VIA_ADMINISTRACION, PRESENTACION) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$clavePac, $medicamento, $dosis, $frecuencia, $duracion, $indicaciones, $activo, $via_administracion, $presentacion]);
            
            $id = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true, 
                'id' => $id,
                'fecha' => date('Y-m-d')
            ]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'updateActivoPrescripcion') {
        $id = intval($_POST['id']);
        $activo = $_POST['activo'];
        
        try {
            $sql = "UPDATE PRESCRIPCIONES SET ACTIVO = ? WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$activo, $id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deletePrescripcion') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "DELETE FROM PRESCRIPCIONES WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Obtener lista de prescripciones
$prescripciones = getPrescripciones($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Prescripción Médica</h4>
    
    <div class="row">
        <div class="col-md-5">
            <div class="card">
                <div class="card-header">
                    Nueva Prescripción
                </div>
                <div class="card-body">
                    <form id="formPrescripcion">
                        <div class="form-group">
                            <label for="medicamento">Medicamento</label>
                            <input type="text" class="form-control" id="medicamento" placeholder="Nombre del medicamento">
                        </div>
                        <div class="form-group">
                            <label for="dosis">Dosis</label>
                            <input type="text" class="form-control" id="dosis" placeholder="Ej: 500mg">
                        </div>
                        <!-- Añadir los nuevos campos al formulario -->
                        <!-- Después del campo de dosis, añadir: -->
                        <div class="form-group">
                            <label for="presentacion">Presentación</label>
                            <input type="text" class="form-control" id="presentacion" placeholder="Ej: Tabletas, Jarabe, Ampolla">
                        </div>

                        <div class="form-group">
                            <label for="via_administracion">Vía de Administración</label>
                            <select class="form-control" id="via_administracion">
                                <option value="Oral">Oral</option>
                                <option value="Intravenosa">Intravenosa</option>
                                <option value="Intramuscular">Intramuscular</option>
                                <option value="Subcutánea">Subcutánea</option>
                                <option value="Tópica">Tópica</option>
                                <option value="Inhalatoria">Inhalatoria</option>
                                <option value="Rectal">Rectal</option>
                                <option value="Otra">Otra</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="frecuencia">Frecuencia</label>
                            <input type="text" class="form-control" id="frecuencia" placeholder="Ej: Cada 8 horas">
                        </div>
                        <div class="form-group">
                            <label for="duracion">Duración</label>
                            <input type="text" class="form-control" id="duracion" placeholder="Ej: 7 días">
                        </div>
                        <div class="form-group">
                            <label for="indicacionesMed">Indicaciones Especiales</label>
                            <textarea class="form-control" id="indicacionesMed" rows="2" placeholder="Ej: Tomar después de las comidas"></textarea>
                        </div>
                        <div class="form-group">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="activoMed" checked>
                                <label class="form-check-label" for="activoMed">
                                    Tratamiento Activo
                                </label>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary" id="btnGuardarPrescripcion">Guardar Prescripción</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-7">
            <div class="card">
                <div class="card-header">
                    Prescripciones
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <!-- Actualizar la tabla para mostrar los nuevos campos -->
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Fecha</th>
                                    <th>Medicamento</th>
                                    <th>Presentación</th>
                                    <th>Dosis</th>
                                    <th>Vía</th>
                                    <th>Frecuencia</th>
                                    <th>Duración</th>
                                    <th>Activo</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody id="tablaPrescripciones">
                                <?php if (count($prescripciones) > 0): ?>
                                    <?php foreach ($prescripciones as $prescripcion): ?>
                                        <tr data-id="<?php echo $prescripcion['id']; ?>">
                                            <td><?php echo $prescripcion['fecha']; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($prescripcion['medicamento']); ?>
                                                <?php if (!empty($prescripcion['indicaciones'])): ?>
                                                    <small class="d-block text-muted"><?php echo htmlspecialchars($prescripcion['indicaciones']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($prescripcion['presentacion'] ?? ''); ?></td>
                                            <td><?php echo htmlspecialchars($prescripcion['dosis']); ?></td>
                                            <td><?php echo htmlspecialchars($prescripcion['via_administracion'] ?? 'Oral'); ?></td>
                                            <td><?php echo htmlspecialchars($prescripcion['frecuencia']); ?></td>
                                            <td><?php echo htmlspecialchars($prescripcion['duracion']); ?></td>
                                            <td>
                                                <div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activo-prescripcion" 
                                                           id="activo<?php echo $prescripcion['id']; ?>" 
                                                           <?php echo ($prescripcion['activo'] == 'S') ? 'checked' : ''; ?>>
                                                    <label class="custom-control-label" for="activo<?php echo $prescripcion['id']; ?>"></label>
                                                </div>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-danger btn-eliminar-prescripcion">Eliminar</button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center">No hay prescripciones</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Actualizar la función JavaScript para incluir los nuevos campos
    $('#btnGuardarPrescripcion').click(function() {
        const medicamento = $('#medicamento').val();
        const dosis = $('#dosis').val();
        const frecuencia = $('#frecuencia').val();
        
        if (!medicamento || !dosis || !frecuencia) {
            alert('Por favor complete al menos los campos de Medicamento, Dosis y Frecuencia');
            return;
        }
        
        const duracion = $('#duracion').val();
        const indicaciones = $('#indicacionesMed').val();
        const activo = $('#activoMed').is(':checked') ? 'S' : 'N';
        const presentacion = $('#presentacion').val();
        const via_administracion = $('#via_administracion').val();
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'savePrescripcion',
                medicamento: medicamento,
                dosis: dosis,
                frecuencia: frecuencia,
                duracion: duracion,
                indicaciones: indicaciones,
                activo: activo,
                presentacion: presentacion,
                via_administracion: via_administracion
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Limpiar formulario
                    $('#formPrescripcion')[0].reset();
                
                    // Agregar a la tabla
                    const newRow = `
                        <tr data-id="${response.id}">
                            <td>${response.fecha}</td>
                            <td>
                                ${medicamento}
                                ${indicaciones ? '<small class="d-block text-muted">' + indicaciones + '</small>' : ''}
                            </td>
                            <td>${presentacion || ''}</td>
                            <td>${dosis}</td>
                            <td>${via_administracion}</td>
                            <td>${frecuencia}</td>
                            <td>${duracion}</td>
                            <td>
                                <div class="custom-control custom-switch">
                                    <input type="checkbox" class="custom-control-input activo-prescripcion" 
                                           id="activo${response.id}" ${activo === 'S' ? 'checked' : ''}>
                                    <label class="custom-control-label" for="activo${response.id}"></label>
                                </div>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-danger btn-eliminar-prescripcion">Eliminar</button>
                            </td>
                        </tr>
                    `;
                
                    // Si la tabla está vacía, eliminar la fila de "No hay prescripciones"
                    if ($('#tablaPrescripciones tr td').length === 1 && $('#tablaPrescripciones tr td').text().includes('No hay prescripciones')) {
                        $('#tablaPrescripciones').empty();
                    }
                
                    $('#tablaPrescripciones').prepend(newRow);
                
                    alert('Prescripción guardada correctamente');
                } else {
                    alert('Error al guardar prescripción: ' + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al guardar prescripción:', error);
                alert('Error al guardar prescripción. Consulte la consola para más detalles.');
            }
        });
    });
    
    // Actualizar estado activo de prescripción
    $(document).on('change', '.activo-prescripcion', function() {
        const id = $(this).closest('tr').data('id');
        const activo = $(this).is(':checked') ? 'S' : 'N';
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'updateActivoPrescripcion',
                id: id,
                activo: activo
            },
            dataType: 'json',
            success: function(response) {
                if (!response.success) {
                    alert('Error al actualizar estado: ' + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al actualizar estado:', error);
                alert('Error al actualizar estado. Consulte la consola para más detalles.');
            }
        });
    });
    
    // Eliminar prescripción
    $(document).on('click', '.btn-eliminar-prescripcion', function() {
        if (confirm('¿Está seguro de eliminar esta prescripción?')) {
            const row = $(this).closest('tr');
            const id = row.data('id');
            
            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: {
                    action: 'deletePrescripcion',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        row.remove();
                        
                        // Si no quedan filas, mostrar mensaje
                        if ($('#tablaPrescripciones tr').length === 0) {
                            $('#tablaPrescripciones').html('<tr><td colspan="7" class="text-center">No hay prescripciones</td></tr>');
                        }
                        
                        alert('Prescripción eliminada correctamente');
                    } else {
                        alert('Error al eliminar prescripción: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error al eliminar prescripción:', error);
                    alert('Error al eliminar prescripción. Consulte la consola para más detalles.');
                }
            });
        }
    });
});
</script>
