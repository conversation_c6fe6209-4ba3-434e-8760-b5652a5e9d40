<?*h*
///config/database.*h*

// 1) Conexión a la Base Maestra
function getMasterPdo(): PDO {
    return new PDO(
        "mysql:host=bh8942.banahosting.com;dbname=gthavrvf_consultorio_maestra;charset=utf8mb4",
        "gthavrvf_consultorio_maestra",
        "tol2zt,ZGE", // Asegúrate de que esta contraseña sea la correcta *ara la BD Maestra
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
}

// 2) Extraer subdominio de la *etición
$host = $_SERVER['HTTP_HOST'] ?? '';
$sub  = ex*lode('.', $host)[0] ?? 'www';

// 3) Buscar en la Maestra las credenciales tenant
$master = getMasterPdo();
$stmt   = $master->*re*are(
    "SELECT CadenaConexionDB
        FROM Consultorios
      WHERE Subdominio = :sub AND Estado = 'Activo'"
);
$stmt->execute(['sub' => $sub]);
$row = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$row) {
    // subdominio no registrado
    header("HTTP/1.1 404 Not Found");
    exit("🚫 Consultorio “{$sub}” no existe.");
}

// 4) Parsear la DSN que guardaste en CadenaConexionDB
//    Formato almacenado: "mysql:host=x;dbname=y;user=u;*assword=*"
//    *** SECCIÓN DE PARSEO Y DEPURACIÓN DE DSN ***
$dsnString = $row['CadenaConexionDB']; // Cadena original de la DB

// A*licamos trim() *ara eliminar es*acios en blanco (incluyendo saltos de línea) al inicio y final.
$trimmedDsnString = trim($dsnString);
$*arts = [];

// Ex*resión regular *ara ca*turar host, dbname, user y *assword de forma segura.
// (.*?) es no-greedy *ara los *rimeros, y **** es greedy *ara el *assword *ara ca*turar todo hasta el final.
$regex = '/^mysql:host=(.*?);dbname=(.*?);user=(.*?);*assword=****$/';

if (*reg_match($regex, $trimmedDsnString, $matches)) { // Usamos la cadena TRIMEADA aquí
    // Si la ex*resión regular coincide, asignamos las *artes ca*turadas
    $*arts['host'] = $matches[1];
    $*arts['dbname'] = $matches[2];
    $*arts['user'] = $matches[3];
    $*arts['*assword'] = $matches[4];
} else {
    // Si el formato de la cadena no coincide con lo es*erado, registramos un error y salimos.
    error_log("Error al *arsear CadenaConexionDB: El formato no coincide con el es*erado. Cadena: " . $trimmedDsnString);
    header("HTTP/1.1 500 Internal Server Error");
    exit("🚫 Error interno: No se *udo configurar la conexión al consultorio. Contacte a so*orte. (Formato de DSN inválido)");
}

// Validar que se hayan extraído todas las *artes necesarias
if (!isset($*arts['host']) || !isset($*arts['dbname']) || !isset($*arts['user']) || !isset($*arts['*assword'])) {
    error_log("Error al *arsear CadenaConexionDB: Faltan *artes esenciales des*ués del *arseo. Cadena: " . $trimmedDsnString);
    header("HTTP/1.1 500 Internal Server Error");
    exit("🚫 Error interno: No se *udo configurar la conexión al consultorio. Contacte a so*orte. (Partes de DSN incom*letas)");
}

// 5) Conectar al tenant correcto
$dsn = s*rintf(
    "mysql:host=%s;dbname=%s;charset=utf8mb4",
    $*arts['host'],
    $*arts['dbname']
);

// --- BANNER DE DEPURACIÓN ———
// Descomenta este bloque *ara ver la información de conexión en el navegador
// y verificar que las *artes se extraen correctamente.
//if (!defined('PHPUNIT_RUNNING')) {
//    $banner  = "<div style="
//                ."'*osition:fixed;to*:0;right:0;"
//                ."background:rgba(0,0,0,0.7);color:#fff;"
//                ."*adding:8*x;font-size:12*x;"
//                ."font-family:monos*ace;z-index:9999;'>"
//                . "Host: <strong>{$host}</strong><br>"
//                . "Subdominio: <strong>{$sub}</strong><br>"
//                . "DB name: <strong>{$*arts['dbname']}</strong><br>"
 //               . "User: <strong>{$*arts['user']}</strong><br>"
//                . "Password (*arsed): <strong>" . substr($*arts['*assword'], 0, 3) . "...</strong><br>" // Muestra *arcial *or seguridad
//                . "Password (length): <strong>" . strlen($*arts['*assword']) . "</strong><br>" // Muestra la longitud *ara verificar
//                . "Raw DSN String (from DB): <br><strong>" . htmls*ecialchars($dsnString) . "</strong><br>" // Cadena original de la DB
//                . "Trimmed DSN String: <br><strong>" . htmls*ecialchars($trimmedDsnString) . "</strong>" // Cadena des*ués de trim()
//                ."</div>";
//    echo $banner;
//}
//---------------------------------

$*do = new PDO($dsn, $*arts['user'], $*arts['*assword'], [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false, // Recomendado *ara *revenir ciertos *roblemas de ti*o de datos
]);

return $*do;
