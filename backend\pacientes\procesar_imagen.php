<?php
// Verificar si se recibió una imagen capturada desde la cámara
if (isset($_POST['imagenCapturada']) && !empty($_POST['imagenCapturada'])) {
    $dataURL = $_POST['imagenCapturada'];
} 
// Sino, verificar si se seleccionó una imagen desde el archivo
elseif (isset($_POST['imagenArchivo']) && !empty($_POST['imagenArchivo'])) {
    $dataURL = $_POST['imagenArchivo'];
} else {
    die("No se recibió imagen.");
}

// Se espera un data URL con el formato: data:image/jpeg;base64,...
// Separar la cabecera de los datos
list($meta, $data) = explode(',', $dataURL);
$data = base64_decode($data);

if ($data === false) {
    die("Error al decodificar la imagen.");
}

// Crear la imagen desde el contenido binario
$img = imagecreatefromstring($data);
if (!$img) {
    die("Error al crear la imagen desde los datos.");
}

// Definir la ruta y el nombre de archivo (por ejemplo, usando la cédula o un identificador único)
// En este ejemplo se guarda como BMP en la carpeta 'fotografias'
$directorioFotos = __DIR__ . '/fotografias/';
if (!is_dir($directorioFotos)) {
    mkdir($directorioFotos, 0777, true);
}

$nombreArchivo = 'capturada.bmp';
$rutaArchivo = $directorioFotos . $nombreArchivo;

// Convertir y guardar la imagen en formato BMP
if (!imagebmp($img, $rutaArchivo)) {
    die("Error al guardar la imagen en formato BMP.");
}

imagedestroy($img);

echo "La imagen se ha guardado exitosamente como BMP en: " . $rutaArchivo;
?>
