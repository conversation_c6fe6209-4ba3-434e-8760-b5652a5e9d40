<?php
session_start();
require_once '../config/database.php';
//$config = include '../config/config.php'; // Ruta al archivo de configuración

// Verificar autenticación
if (!isset($_SESSION['usuario']) || !isset($_SESSION['rol'])) {
    header('Location: ../../login.php');
    exit;
}

// Función para obtener la URL de regreso según el rol
function getBackUrl($rol) {
    switch ($rol) {
        case 'secretaria':
            return '../../secretaria_panel.php';
        case 'doctor':
            return '../../doctor_panel_v2.php';
        case 'admin':
            return '../../index.php';
        default:
            return '../../index.php';
    }
}

// Función para obtener el texto del botón según el rol
function getBackText($rol) {
    switch ($rol) {
        case 'secretaria':
            return 'Panel de Secretaría';
        case 'doctor':
            return 'Panel del Doctor';
        case 'admin':
            return 'Panel de Administración';
        default:
            return 'Inicio';
    }
}

$backUrl = getBackUrl($_SESSION['rol']);
$backText = getBackText($_SESSION['rol']);

// Opcional: Cargar el paciente si se pasa el ID en la URL
$paciente = null;
if (isset($_GET['clave'])) {
    $paciente_id = $_GET['clave'];
    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
    $stmt->execute([$paciente_id]);
    $paciente = $stmt->fetch();
}

$activeTab = 'registrar'; // Valor por defecto
if (isset($_GET['tab'])) {
    $activeTab = $_GET['tab'];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Pacientes</title>
    
    <!-- Favicon médico -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
    <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
    <!-- Bootstrap y Font Awesome -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="../../public/assets/css/custom.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
</head>
<body>

<!-- Botón de regreso inteligente -->
<div class="container mt-3">
    <div class="row">
        <div class="col-12">
            <a href="<?php echo $backUrl; ?>" class="btn btn-outline-primary mb-3">
                <i class="fas fa-arrow-left me-2"></i>
                Regresar al <?php echo $backText; ?>
            </a>
        </div>
    </div>
</div>

<div class="container mt-5">
    <h1 class="text-center">GESTION DE PACIENTES</h1>

    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link <?= $activeTab == 'registrar' ? 'active' : '' ?>" id="registrar-tab" data-toggle="tab" href="#registrar" role="tab" aria-controls="registrar" aria-selected="true">
                <i class="fas fa-user-plus"></i> Registrar/Actualizar
            </a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link <?= $activeTab == 'buscar' ? 'active' : '' ?>" id="buscar-tab" data-toggle="tab" href="#buscar" role="tab" aria-controls="buscar" aria-selected="false">
                <i class="fas fa-search"></i> Buscar
            </a>
        </li>
    </ul>

    <div class="tab-content" id="myTabContent">
        <!-- Pestaña Buscar -->
        <div class="tab-pane fade <?= $activeTab == 'buscar' ? 'show active' : '' ?>" id="buscar" role="tabpanel" aria-labelledby="buscar-tab">
            <?php include 'buscar_pacientes.php'; ?>
        </div>
        <!-- Pestaña Registrar/Actualizar -->
        <div class="tab-pane fade <?= $activeTab == 'registrar' ? 'show active' : '' ?>" id="registrar" role="tabpanel" aria-labelledby="registrar-tab">
            <?php include 'crear_paciente.php'; ?>
        </div>
    </div>
</div>

<!-- jQuery completo, Popper y Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>

<script>
$(document).ready(function() {
    // Función para inicializar DataTable en la tabla de búsqueda
    function initDataTable() {
        if ($('#tablaPacientes').length && !$.fn.DataTable.isDataTable('#tablaPacientes')) {
            $('#tablaPacientes').DataTable({
                  paging: true,
                ordering: true,
                searching: true,
                language: {
                   lengthMenu: "Mostrar _MENU_ entradas",
                    search: "Buscar:",
                    zeroRecords: "No se encontraron resultados",
                    emptyTable: "No hay pacientes disponibles",
                    loadingRecords: "Cargando...",
                   paginate: {
            first: "Primero",
            last: "Último",
            next: "Siguiente",
            previous: "Anterior"
          } 
                    
                    
                    
                }
            });
        }
    }

    // Si la pestaña Buscar está activa al cargar la página, inicializa DataTable
    if ($('#myTabContent .tab-pane.active').attr('id') === 'buscar') {
        initDataTable();
    }
    
    // Cuando se cambie de pestaña, si se activa la pestaña Buscar, inicializa DataTable
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        if ($(e.target).attr('href') === '#buscar') {
            // Esperar un poco a que se renderice el contenido
            setTimeout(initDataTable, 100);
        }
    });
});
</script>

</body>
</html>
