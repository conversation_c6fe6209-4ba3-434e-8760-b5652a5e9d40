
  body {
      font-family: '<PERSON><PERSON>', 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 16px;
      color: #333;
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
  }

  h1, h2, h3 {
      font-weight: 600;
      color: #1d3557;
      margin-bottom: 1rem;
  }

  p, label, td, th {
      font-size: 15px;
      line-height: 1.6;
      color: #444;
  }

  a {
      text-decoration: none;
      color: #0d6efd;
      transition: color 0.2s ease-in-out;
  }

  a:hover {
      text-decoration: underline;
      color: #0a58ca;
  }

  .navbar-brand {
      font-weight: bold;
      font-size: 18px;
      color: #1d3557 !important;
  }

  .card-title {
      font-size: 18px;
      font-weight: 500;
      color: #0d47a1;
  }

  .btn, .btn-primary {
      font-weight: 500;
      letter-spacing: 0.4px;
      padding: 8px 16px;
      border-radius: 6px;
  }

  .btn-primary {
      background-color: #0d6efd;
      border-color: #0d6efd;
  }

  .btn-primary:hover {
      background-color: #0b5ed7;
      border-color: #0a58ca;
  }

  input[type="text"],
  input[type="date"],
  input[type="file"],
  select,
  textarea {
      width: 100%;
      padding: 8px 12px;
      margin-bottom: 12px;
      border: 1px solid #ccc;
      border-radius: 6px;
      font-size: 15px;
      background-color: #fff;
  }

  input:focus,
  select:focus,
  textarea:focus {
      outline: none;
      border-color: #0d6efd;
      box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
  }

  .table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
      background-color: #fff;
  }

  .table th,
  .table td {
      padding: 10px;
      border: 1px solid #dee2e6;
      text-align: center;
      vertical-align: middle;
  }

  .table th {
      background-color: #f1f3f5;
      font-weight: 600;
      color: #495057;
  }

  .status-pendiente-aprobacion {
      background-color: #fff9db !important;
      color: #856404 !important;
  }

  .status-atendido {
      background-color: #d4edda;
      color: #155724;
  }

  .status-cancelo {
      background-color: #f8d7da;
      color: #721c24;
  }

  .status-no-asistio {
      background-color: #fefefe;
      color: #6c757d;
  }

  .status-citado {
      background-color: #e2e3e5;
      color: #383d41;
  }

  .status-esperando {
      background-color: #fff3cd;
      color: #856404;
  }

  .text-danger {
      color: #dc3545 !important;
  }
