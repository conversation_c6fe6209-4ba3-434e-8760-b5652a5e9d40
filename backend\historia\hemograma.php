<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$pdo = require_once __DIR__ . '/../config/database.php';

$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA']   ?? '';
$CLAVE    = $_GET['CLAVE']    ?? null; // CLAVE del registro de hemograma si estamos editando uno específico
$nuevo    = isset($_GET['nuevo']); // Para indicar que se quiere un formulario vacío
$mensaje  = '';
$messageType = '';
$registros = []; // Para almacenar todos los hemogramas del paciente
$seleccionado = null; // Para el hemograma que se muestra en el formulario


// --- 1) Eliminar hemograma por POST seguro ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && !empty($_POST['CLAVE_ELIMINAR'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM HEMOGRAMAGRAL WHERE CLAVE = ?");
        $stmt->execute([$_POST['CLAVE_ELIMINAR']]);
        $mensaje = "✅ Hemograma eliminado exitosamente.";
        $messageType = 'success';
        // Redireccionar para recargar la página y no mostrar el registro eliminado
        header("Location: hemograma.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar hemograma: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// --- 2) Cargar registros históricos de hemograma ---
if ($CLAVEPAC) {
    $stmt = $pdo->prepare("SELECT CLAVE, FECHA_CAP FROM HEMOGRAMAGRAL WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC");
    $stmt->execute([$CLAVEPAC]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// --- 3) Seleccionar registro actual o nuevo ---
// Definir los campos para inicializar el formulario o cargar datos
$campos_hemograma = [
    'CLAVE' => null, // Esto se llenará si se carga un registro existente
    'HEMATIES' => '', 'HEMOGLOBINA' => '', 'HEMATOCRITO' => '', 'LEUCOCITO' => '',
    'NEUTROFILOS' => '', 'BASOFILOS' => '', 'EOSINOFILOS' => '', 'LINFOSITOS' => '',
    'MONOCITOS' => '', 'PLAQUETAS' => '', 'FE' => '', 'TRANS' => '',
    'FERRI' => '', 'VSG' => '', 'TPROTR' => '', 'PCF' => '',
    'VCM' => '', 'VPM' => '', 'CMHC' => '', 'HCM' => '', 'GRAN' => '',
    'VHC' => '', 'VHB' => '', 'HIV' => '', 'VDRL' => '', 'TOXOIG' => '',
    'TESTCOMBS' => '', 'GLICEMIA' => '', 'TIPIFICACION' => '', 'PCR' => '',
    'AMEBAS' => '', 'HPYLORI' => '', 'PSA' => '', 'TOXOM' => '', 'PEMBARAZO' => '',
    // 'UUID' ya NO se inicializa aquí, se asume que DB lo maneja
    'CEDULA' => $CEDULA, // Se toma de la URL inicialmente
    'SINCRONIZADO' => 0 // Valor por defecto
];

// Si se pidió un formulario nuevo
if ($nuevo) {
    $seleccionado = $campos_hemograma; // Inicializa con valores vacíos
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'Ingresando un nuevo hemograma.';
    $messageType = 'info';
} elseif ($CLAVE) { // Si se pidió un examen específico por su CLAVE
    $stmt = $pdo->prepare("SELECT * FROM HEMOGRAMAGRAL WHERE CLAVE = ?");
    $stmt->execute([$CLAVE]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($data_db) {
        // Asegurarse de que el UUID se cargue si existe en la base de datos (para updates)
        $campos_hemograma['UUID'] = ''; // Añadir al array temporal si necesitas cargarla
        $seleccionado = array_merge($campos_hemograma, $data_db); // Combina valores por defecto con los de la DB
        $mensaje = 'Hemograma cargado. Modifique y guarde, o cree uno nuevo.';
        $messageType = 'info';
    } else {
        // Si la CLAVE no existe, carga el más reciente o uno vacío
        $seleccionado = $campos_hemograma;
        $seleccionado['CLAVEPAC'] = $CLAVEPAC;
        $seleccionado['CEDULA'] = $CEDULA;
        $mensaje = 'No se encontró el hemograma solicitado. Creando uno nuevo.';
        $messageType = 'warning';
    }
} elseif (!empty($registros)) { // Si no se pidió nuevo ni específico, carga el más reciente
    $stmt = $pdo->prepare("SELECT * FROM HEMOGRAMAGRAL WHERE CLAVE = ?");
    $stmt->execute([$registros[0]['CLAVE']]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    // Asegurarse de que el UUID se cargue si existe en la base de datos (para updates)
    $campos_hemograma['UUID'] = ''; // Añadir al array temporal si necesitas cargarla
    $seleccionado = array_merge($campos_hemograma, $data_db);
    $mensaje = 'Mostrando el hemograma más reciente.';
    $messageType = 'info';
} else { // Si no hay registros ni se pidió nuevo, inicializa un formulario vacío
    $seleccionado = $campos_hemograma;
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'No se encontraron hemogramas. Ingrese los datos.';
    $messageType = 'secondary';
}


// --- 4) Guardar o actualizar examen ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['eliminar'])) {
    $esActualizacion = !empty($_POST['CLAVE']);
    $data = [];

    // Recopilar datos del POST, asegurando saneamiento básico
    foreach ($campos_hemograma as $campo => $valor_defecto) {
        if ($campo === 'CLAVE' || $campo === 'CLAVEPAC' || $campo === 'CEDULA' || $campo === 'SINCRONIZADO' || $campo === 'UUID') {
            // UUID ya NO se procesa desde $_POST; CLAVE es para el WHERE
            continue;
        }
        $val = $_POST[$campo] ?? '';
        // Saneamiento para números decimales
        if (in_array($campo, ['HEMATIES', 'HEMOGLOBINA', 'HEMATOCRITO', 'LEUCOCITO', 'NEUTROFILOS', 'BASOFILOS', 'EOSINOFILOS', 'LINFOSITOS', 'MONOCITOS', 'PLAQUETAS', 'FE', 'TRANS', 'FERRI', 'VSG', 'TPROTR', 'PCF', 'VCM', 'VPM', 'CMHC', 'HCM', 'GRAN', 'GLICEMIA'])) {
            $data[$campo] = filter_var($val, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) ?: null; // Usar null si está vacío
        } else { // Saneamiento para texto
            $data[$campo] = htmlspecialchars(trim($val)) ?: null; // Usar null si está vacío
        }
    }

    // Campos de control adicionales
    $data['CLAVEPAC'] = $CLAVEPAC; // Siempre usar el CLAVEPAC de la URL
    $data['CEDULA'] = $CEDULA;     // Siempre usar la CEDULA de la URL
    $data['SINCRONIZADO'] = 0;     // Marcar como no sincronizado al guardar

    try {
        if ($esActualizacion) {
            $data['CLAVE'] = $_POST['CLAVE'];
            // Si el UUID se carga desde DB y se necesita para una condición, se añadiría aquí
            // Por ahora, asumimos que CLAVE es suficiente para UPDATE
            $sets = [];
            foreach ($data as $key => $value) {
                if ($key !== 'CLAVE') { // CLAVE no se actualiza a sí misma
                    $sets[] = "$key = :$key";
                }
            }
            $sql = "UPDATE HEMOGRAMAGRAL SET " . implode(', ', $sets) . " WHERE CLAVE = :CLAVE";
        } else {
            // UUID ya NO se genera aquí, se asume que DB lo hace automáticamente
            $cols = implode(', ', array_keys($data));
            $phs  = ':' . implode(', :', array_keys($data));
            $sql  = "INSERT INTO HEMOGRAMAGRAL ($cols) VALUES ($phs)";
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($data);

        $newClave = $esActualizacion ? $_POST['CLAVE'] : $pdo->lastInsertId();
        
        $mensaje = '✅ Hemograma guardado exitosamente.';
        $messageType = 'success';
        // Redireccionar para evitar re-envío y cargar el registro recién guardado/actualizado
        header("Location: hemograma.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$newClave");
        exit;

    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar el hemograma: " . $e->getMessage();
        $messageType = 'danger';
        // Para depuración, puedes descomentar la siguiente línea:
        // error_log("Error saving hemograma: " . $e->getMessage() . " - SQL: " . $sql);
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hemograma General</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .form-container { padding: 20px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 15px rgba(0,0,0,0.1); margin-top: 20px; }
        .form-section { border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin-bottom: 20px; background-color: #fefefe; }
        .form-section h4 { margin-bottom: 15px; color: #0d6efd; border-bottom: 1px solid #0d6efd; padding-bottom: 5px; }
        .list-group-item.active { background-color: #0d6efd !important; border-color: #0d6efd !important; }
        .list-group-item.active a { color: #fff !important; }
        .decimal-input { text-align: right; } /* Para alinear los números a la derecha */
    </style>
</head>
<body class="p-4">
    <div class="row">
        <div class="col-md-3 border-end">
            <h5 class="mb-3">Historial de Hemogramas</h5>
            <ul class="list-group">
                <?php if (empty($registros)): ?>
                    <li class="list-group-item text-muted">No hay registros anteriores.</li>
                <?php endif; ?>
                <?php foreach ($registros as $r): ?>
                    <?php
                    // Formatear FECHA_CAP si es un timestamp
                    $fecha_formateada = (new DateTime($r['FECHA_CAP']))->format('Y-m-d H:i');
                    $active = isset($seleccionado['CLAVE']) && $seleccionado['CLAVE'] == $r['CLAVE'];
                    ?>
                    <li class="list-group-item <?= $active ? 'active' : '' ?>">
                        <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($r['CLAVE']) ?>"
                           class="text-decoration-none <?= $active ? 'text-white' : '' ?>">
                            <?= htmlspecialchars($fecha_formateada) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <div class="col-md-9 form-container">
            <h4 class="mb-3">Hemograma General</h4>
            <p class="text-muted">Paciente: <strong><?= htmlspecialchars($CLAVEPAC) ?></strong> (CI: <strong><?= htmlspecialchars($CEDULA) ?></strong>)</p>

            <?php if ($mensaje): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($mensaje) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form method="post" novalidate>
                <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                <input type="hidden" name="CEDULA" value="<?= htmlspecialchars($CEDULA) ?>">
                <?php if (!empty($seleccionado['CLAVE'])): ?>
                    <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                    <input type="hidden" name="CLAVE_ELIMINAR" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                <?php endif; ?>
               
                <input type="hidden" name="SINCRONIZADO" value="<?= htmlspecialchars($seleccionado['SINCRONIZADO']) ?>">


                <div class="form-section">
                    <h4>Componentes Sanguíneos</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="HEMATIES" class="form-label">Hematíes</label><input type="text" class="form-control decimal-input" id="HEMATIES" name="HEMATIES" value="<?= htmlspecialchars($seleccionado['HEMATIES']) ?>"></div>
                        <div class="col-md-3"><label for="HEMOGLOBINA" class="form-label">Hemoglobina</label><input type="text" class="form-control decimal-input" id="HEMOGLOBINA" name="HEMOGLOBINA" value="<?= htmlspecialchars($seleccionado['HEMOGLOBINA']) ?>"></div>
                        <div class="col-md-3"><label for="HEMATOCRITO" class="form-label">Hematocrito</label><input type="text" class="form-control decimal-input" id="HEMATOCRITO" name="HEMATOCRITO" value="<?= htmlspecialchars($seleccionado['HEMATOCRITO']) ?>"></div>
                        <div class="col-md-3"><label for="LEUCOCITO" class="form-label">Leucocitos</label><input type="text" class="form-control decimal-input" id="LEUCOCITO" name="LEUCOCITO" value="<?= htmlspecialchars($seleccionado['LEUCOCITO']) ?>"></div>
                        <div class="col-md-3"><label for="PLAQUETAS" class="form-label">Plaquetas</label><input type="text" class="form-control decimal-input" id="PLAQUETAS" name="PLAQUETAS" value="<?= htmlspecialchars($seleccionado['PLAQUETAS']) ?>"></div>
                        <div class="col-md-3"><label for="VCM" class="form-label">VCM</label><input type="text" class="form-control decimal-input" id="VCM" name="VCM" value="<?= htmlspecialchars($seleccionado['VCM']) ?>"></div>
                        <div class="col-md-3"><label for="VPM" class="form-label">VPM</label><input type="text" class="form-control decimal-input" id="VPM" name="VPM" value="<?= htmlspecialchars($seleccionado['VPM']) ?>"></div>
                        <div class="col-md-3"><label for="CMHC" class="form-label">CMHC</label><input type="text" class="form-control decimal-input" id="CMHC" name="CMHC" value="<?= htmlspecialchars($seleccionado['CMHC']) ?>"></div>
                        <div class="col-md-3"><label for="HCM" class="form-label">HCM</label><input type="text" class="form-control decimal-input" id="HCM" name="HCM" value="<?= htmlspecialchars($seleccionado['HCM']) ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Recuento Diferencial</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="NEUTROFILOS" class="form-label">Neutrófilos</label><input type="text" class="form-control decimal-input" id="NEUTROFILOS" name="NEUTROFILOS" value="<?= htmlspecialchars($seleccionado['NEUTROFILOS']) ?>"></div>
                        <div class="col-md-3"><label for="BASOFILOS" class="form-label">Basófilos</label><input type="text" class="form-control decimal-input" id="BASOFILOS" name="BASOFILOS" value="<?= htmlspecialchars($seleccionado['BASOFILOS']) ?>"></div>
                        <div class="col-md-3"><label for="EOSINOFILOS" class="form-label">Eosinófilos</label><input type="text" class="form-control decimal-input" id="EOSINOFILOS" name="EOSINOFILOS" value="<?= htmlspecialchars($seleccionado['EOSINOFILOS']) ?>"></div>
                        <div class="col-md-3"><label for="LINFOSITOS" class="form-label">Linfocitos</label><input type="text" class="form-control decimal-input" id="LINFOSITOS" name="LINFOSITOS" value="<?= htmlspecialchars($seleccionado['LINFOSITOS']) ?>"></div>
                        <div class="col-md-3"><label for="MONOCITOS" class="form-label">Monocitos</label><input type="text" class="form-control decimal-input" id="MONOCITOS" name="MONOCITOS" value="<?= htmlspecialchars($seleccionado['MONOCITOS']) ?>"></div>
                        <div class="col-md-3"><label for="GRAN" class="form-label">Granulocitos</label><input type="text" class="form-control decimal-input" id="GRAN" name="GRAN" value="<?= htmlspecialchars($seleccionado['GRAN']) ?>"></div>
                    </div>
                </div>

               <div class="form-section">
  <h4>Coagulación y Otros Bioquímicos</h4>
  <div class="row g-3">
    <div class="col-md-3">
      <label for="FE" class="form-label">Hierro (Fe)</label>
      <input type="text"
             class="form-control decimal-input"
             id="FE" name="FE"
             value="<?= htmlspecialchars($seleccionado['FE']   ?? '', ENT_QUOTES, 'UTF-8') ?>">
    </div>
    <div class="col-md-3">
      <label for="TRANS" class="form-label">Transferrina</label>
      <input type="text"
             class="form-control decimal-input"
             id="TRANS" name="TRANS"
             value="<?= htmlspecialchars($seleccionado['TRANS']?? '', ENT_QUOTES, 'UTF-8') ?>">
    </div>
    <div class="col-md-3">
      <label for="FERRI" class="form-label">Ferritina</label>
      <input type="text"
             class="form-control decimal-input"
             id="FERRI" name="FERRI"
             value="<?= htmlspecialchars($seleccionado['FERRI'] ?? '', ENT_QUOTES, 'UTF-8') ?>">
    </div>
    <!-- …y así con el resto… -->
  </div>
</div>


                <div class="form-section">
                    <h4>Serologías / Infecciones</h4>
                    <div class="row g-3">
                        <div class="col-md-2"><label for="VHC" class="form-label">VHC</label><input type="text" class="form-control" id="VHC" name="VHC" value="<?= htmlspecialchars($seleccionado['VHC']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="VHB" class="form-label">VHB</label><input type="text" class="form-control" id="VHB" name="VHB" value="<?= htmlspecialchars($seleccionado['VHB']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="HIV" class="form-label">HIV</label><input type="text" class="form-control" id="HIV" name="HIV" value="<?= htmlspecialchars($seleccionado['HIV']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="VDRL" class="form-label">VDRL</label><input type="text" class="form-control" id="VDRL" name="VDRL" value="<?= htmlspecialchars($seleccionado['VDRL']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="TOXOIG" class="form-label">TOXOIG</label><input type="text" class="form-control" id="TOXOIG" name="TOXOIG" value="<?= htmlspecialchars($seleccionado['TOXOIG']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="TESTCOMBS" class="form-label">Test Coombs</label><input type="text" class="form-control" id="TESTCOMBS" name="TESTCOMBS" value="<?= htmlspecialchars($seleccionado['TESTCOMBS']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="PCR" class="form-label">PCR</label><input type="text" class="form-control" id="PCR" name="PCR" value="<?= htmlspecialchars($seleccionado['PCR']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="AMEBAS" class="form-label">Amibas</label><input type="text" class="form-control" id="AMEBAS" name="AMEBAS" value="<?= htmlspecialchars($seleccionado['AMEBAS']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="HPYLORI" class="form-label">H. Pylori</label><input type="text" class="form-control" id="HPYLORI" name="HPYLORI" value="<?= htmlspecialchars($seleccionado['HPYLORI']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="PSA" class="form-label">PSA</label><input type="text" class="form-control" id="PSA" name="PSA" value="<?= htmlspecialchars($seleccionado['PSA']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="TOXOM" class="form-label">TOXOM</label><input type="text" class="form-control" id="TOXOM" name="TOXOM" value="<?= htmlspecialchars($seleccionado['TOXOM']) ?>" maxlength="1"></div>
                        <div class="col-md-2"><label for="PEMBARAZO" class="form-label">P. Embarazo</label><input type="text" class="form-control" id="PEMBARAZO" name="PEMBARAZO" value="<?= htmlspecialchars($seleccionado['PEMBARAZO']) ?>" maxlength="1"></div>
                    </div>
                </div>

                <div class="d-flex gap-2 mt-3">
                    <button type="submit" name="accion" value="guardar" class="btn btn-success">💾 Guardar</button>
                    <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&nuevo=1" class="btn btn-secondary">🆕 Nuevo</a>
                    <?php if (!empty($seleccionado['CLAVE'])): ?>
                        <button type="submit" name="eliminar" value="1" class="btn btn-danger" onclick="return confirm('¿Deseas eliminar este hemograma?');">🗑️ Eliminar</button>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">🖨️ Imprimir</button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Función para formatear campos decimales (igual que en examen_fisico.php)
        function formatearDecimal(input) {
            let val = input.value.replace(/[^0-9.]/g, '');
            val = val.replace(/\.(?=.*\.)/g, ''); // Solo un punto decimal
            const endsWithDot = val.endsWith('.');
            const [intRaw = '', decRaw = ''] = val.split('.');
            const intPart = intRaw.slice(0, 8); // Ajustado para más enteros si es necesario
            const decPart = decRaw.slice(0, 2);
            input.value = endsWithDot ? `${intPart}.` : (decPart ? `${intPart}.${decPart}` : intPart);
        }

        // Función para validar campos decimales (igual que en examen_fisico.php)
        function validarFormulario(e) {
            // Ajusta el patrón regex según el `decimal(X,2)` más grande que uses (ej. LEUCOCITO es decimal(8,2))
            const regex = /^\d{1,8}(\.\d{1,2})?$/; 
            let ok = true;
            document.querySelectorAll('.decimal-input').forEach(i => {
                let v = i.value.trim();
                if (v && !regex.test(v)) {
                    i.setCustomValidity('Formato inválido: hasta 8 enteros y 2 decimales.'); // Mensaje más específico
                    i.reportValidity();
                    ok = false;
                } else {
                    i.setCustomValidity('');
                }
            });
            if (!ok) e.preventDefault();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Adjuntar formateadores y validadores a los campos decimales
            document.querySelectorAll('.decimal-input').forEach(i => {
                // Ajusta maxlength según el tamaño de tu columna decimal más grande
                // Ej. decimal(8,2) necesita hasta 8 digitos + 1 punto + 2 decimales = 11
                i.setAttribute('maxlength', '11'); 
                i.setAttribute('pattern', '^\\d{1,8}(\\.\\d{1,2})?$'); // Ajusta el patrón
                i.addEventListener('input', () => formatearDecimal(i));
                formatearDecimal(i); // Formatea el valor inicial si lo hay
            });
            document.querySelector('form').addEventListener('submit', validarFormulario);
        });
    </script>
</body>
</html>