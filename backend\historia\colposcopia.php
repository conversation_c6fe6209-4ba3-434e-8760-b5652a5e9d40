<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA'] ?? '';
$CLAVE    = $_GET['CLAVE'] ?? null;
$nuevo    = isset($_GET['nuevo']);
$mensaje  = '';

// Eliminar registro si se solicita
if (isset($_GET['eliminar'], $_GET['CLAVE']) && $_GET['eliminar'] === '1') {
    $stmt = $pdo->prepare("DELETE FROM COLPOSCOPIA WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $_GET['CLAVE']]);
    header("Location: colposcopia.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
    exit;
}

// Guardar o actualizar
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $esActualizacion = !empty($_POST['CLAVE']);

    // Campos de la tabla (sin CLAVEPAC, CEDULA)
    $campos = [
        'REFERIDOPOR','MOTIVOREF','SINTOMAS',
        'SANGRADOPOSTCOITAL','PAPFRECUENTEMENTE','ANTECEDFAMILIARDECA','EMBARAZADA',
        'COLPOSCOPIACOMPLETA','CITOLOGIA','BIOPSIA','LEC',
        'NORMAL','MOSAICO','HIPERKERATOSIS','PUNTUAL','VASOSATIPICOS',
        'OVOIDESNABOTH','ZTTIPICA','ORIFICIOSGLAND','PERDIDAEPITELIO','ECTROPIUM',
        'ACETOBLANCA','ZTATIPICA',
        'METODOSANTCONCEPTIVOS','TRATAMIENTOPREVIO','APARIENCIADELCERVIX',
        'ESPECULOSCOPIA','SEMANAEMBARAZO','CAUSA','SUGERENCIA',
        'RESULTCITOLOGIA','RESULTBIOPSIA','RESULTLEGRADO','COMENTARIOS'
    ];

    // Construir $data desde POST
    $data = [];
    foreach ($campos as $campo) {
        $data[$campo] = $_POST[$campo] ?? null;
    }

    if ($esActualizacion) {
        // Actualización: incluir solo CLAVE y campos
        $data['CLAVE'] = $_POST['CLAVE'];
        $setParts = [];
        foreach ($campos as $c) {
            $setParts[] = "$c = :$c";
        }
        $setParts[] = "SINCRONIZADO = 0";
        $sql = "UPDATE COLPOSCOPIA SET " . implode(', ', $setParts) . " WHERE CLAVE = :CLAVE";
    } else {
        // Inserción: incluir CLAVEPAC y CEDULA
        $data['CLAVEPAC'] = $CLAVEPAC;
        $data['CEDULA']   = $CEDULA;
        $cols = array_merge(['CLAVEPAC','CEDULA'], $campos);
        $placeholders = [];
        foreach ($cols as $c) {
            $placeholders[] = ':' . $c;
        }
        $sql = sprintf(
            "INSERT INTO COLPOSCOPIA (%s) VALUES (%s)",
            implode(', ', $cols),
            implode(', ', $placeholders)
        );
    }

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($data);
        $id = $esActualizacion ? $_POST['CLAVE'] : $pdo->lastInsertId();
        header("Location: colposcopia.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$id");
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar: " . $e->getMessage();
    }
}

// Obtener registros del paciente
$stmt = $pdo->prepare(
    "SELECT * FROM COLPOSCOPIA WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHA_CAP DESC"
);
$stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
$registros = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Determinar qué datos mostrar
if ($nuevo) {
    $datos = [];
} elseif ($CLAVE) {
    $stmt = $pdo->prepare("SELECT * FROM COLPOSCOPIA WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $datos = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
} elseif (!empty($registros)) {
    $datos = $registros[0];
} else {
    $datos = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Colposcopia</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  
  <style>
  /* Sobrepone el color de los enlaces dentro de un list-group-item activo */
  .list-group-item.active a {
    color: #fff !important;
  }
</style>
  
</head>
<body class="p-4">
  <h4>🩺 Colposcopia</h4>
  <?php if ($mensaje): ?>
    <div class="alert alert-danger"><?= $mensaje ?></div>
  <?php endif; ?>

  
  <form method="post" class="tab-content">
   <!--
    Si $datos['CLAVE'] existe, insertamos un hidden para que POST traiga el valor
    y active la rama de UPDATE en tu PHP
  -->
  <?php if (!empty($datos['CLAVE'])): ?>
    <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($datos['CLAVE']) ?>">
  <?php endif; ?>

  <!-- además, mantenemos CLAVEPAC y CEDULA en POST por si los necesitas -->
  <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
  <input type="hidden" name="CEDULA"   value="<?= htmlspecialchars($CEDULA) ?>">

  <!-- ahora tus inputs normales… -->   
      
    <div class="tab-pane fade show active" id="general">
      <!-- Referido, motivos, síntomas -->
    
    <?php
$referidos = [
  'REFERIDOPOR' => ['Referido por:', 40],
  'MOTIVOREF'   => ['Motivo de referido:', 80],
  'SINTOMAS'    => ['Síntomas:', 80],
];
foreach ($referidos as $campo => list($label, $max)): ?>
  <div class="mb-3">
    <label for="<?= $campo ?>"><?= $label ?></label>
    <input
      type="text"
      name="<?= $campo ?>"
      id="<?= $campo ?>"
      class="form-control"
      maxlength="<?= $max ?>"
      value="<?= htmlspecialchars($datos[$campo] ?? '') ?>"
      <?= $campo !== 'SINTOMAS' ? 'required' : '' ?>
    >
  </div>
<?php endforeach; ?>
    
    
     <fieldset class="mb-3">
  <legend>Lista de Chequeo</legend>
  <div class="row">
    <?php
    $checks = [
      'SANGRADOPOSTCOITAL' => 'Sangrado pos coital',
      'PAPFRECUENTEMENTE' => 'PAP frecuente',
      'ANTECEDFAMILIARDECA' => 'Antecedentes familiares',
      'EMBARAZADA' => 'Embarazada',
      'COLPOSCOPIACOMPLETA' => 'Colposcopia completa',
      'CITOLOGIA' => 'Citología',
      'BIOPSIA' => 'Biopsia',
      'LEC' => 'Legrado'
    ];
    $i = 0;
    foreach ($checks as $chk => $label): ?>
      <div class="col-md-3 col-sm-6">
        <div class="form-check mb-2">
          <input class="form-check-input" type="checkbox"
            name="<?= $chk ?>" id="<?= $chk ?>" value="1"
            <?= !empty($datos[$chk]) && $datos[$chk] == '1' ? 'checked' : '' ?>>
          <label class="form-check-label" for="<?= $chk ?>">
            <?= $label ?>
          </label>
        </div>
      </div>
    <?php endforeach; ?>
  </div>
</fieldset>

<!-- Hallazgos -->
<h5>Hallazgos</h5>
<div class="row">
  <?php
  $hallazgosFields = [
    'NORMAL'           => ['Normal', 2],
    'MOSAICO'          => ['Mosaico', 2],
    'HIPERKERATOSIS'   => ['Hiperkeratosis', 2],
    'PUNTUAL'          => ['Puntual', 2],
    'VASOSATIPICOS'    => ['Vasos Atípico', 2],
    'OVOIDESNABOTH'    => ['Ovoides Naboth', 2],
    'ZTTIPICA'         => ['Z.T. Típica', 2],
    'ORIFICIOSGLAND'   => ['Orificios Gland', 2],
    'PERDIDAEPITELIO'  => ['Pérdida de Epitelio', 2],
    'ACETOBLANCA'      => ['Aceto Blanca', 2],
    'ZTATIPICA'        => ['Z.T. A Típica', 2],
    'ECTROPIUM'        => ['Ectropium', 2]
  ];
  foreach ($hallazgosFields as $campo => list($label, $max)): ?>
    <div class="col-sm-3 mb-2">
      <label for="<?= $campo ?>"><?= $label ?></label>
      <input
        name="<?= $campo ?>"
        id="<?= $campo ?>"
        type="text"
        class="form-control"
        maxlength="<?= $max ?>"
        pattern="\d{1,2}"
        title="Sólo números (0-99)"
        value="<?= htmlspecialchars($datos[$campo] ?? '') ?>"
        onkeypress="
          // 1) no permitir más si ya hay 2 caracteres
          if (this.value.length >= <?= $max ?>) return false;
          // 2) permitir sólo dígitos
          var char = String.fromCharCode(event.which||event.keyCode);
          if (!/[0-9]/.test(char)) return false;
        "
      >
    </div>
  <?php endforeach; ?>
</div>

</div>

<!-- Tabs correctamente ubicadas -->
<ul class="nav nav-tabs mb-3 mt-4" id="tabsColpo" role="tablist">
  <li class="nav-item" role="presentation">
    <button class="nav-link active" id="tab1" data-bs-toggle="tab" data-bs-target="#tab-generales" type="button" role="tab">Datos Generales</button>
  </li>
  <li class="nav-item" role="presentation">
    <button class="nav-link" id="tab2" data-bs-toggle="tab" data-bs-target="#tab-resultados" type="button" role="tab">Resultados / Comentarios</button>
  </li>
</ul>
<div class="tab-content border p-3 mb-4" id="tabContentColpo">
  <div class="tab-pane fade show active" id="tab-generales" role="tabpanel">
  
    <!-- Aquí se incluyen los inputs de Datos Generales -->
    
    <?php
$generalFields = [
  // campo                => [ etiqueta                     , maxlength ]
  'METODOSANTCONCEPTIVOS'  => ['Métodos anti conceptivos', 25],
  'TRATAMIENTOPREVIO'      => ['Tratamiento previo'      , 80],
  'APARIENCIADELCERVIX'    => ['Apariencia del cervix'   , 80],
  'ESPECULOSCOPIA'         => ['Especuloscopia'          , 80],
  'SEMANAEMBARAZO'         => ['Semana de embarazo'      , 80],
  'CAUSA'                  => ['Causa'                   ,100],
  'SUGERENCIA'             => ['Sugerencia'              ,150],
];

foreach ($generalFields as $campo => list($label, $max)) : ?>
  <div class="mb-3">
    <label for="<?= $campo ?>"><?= $label ?></label>
    <input
      type="text"
      name="<?= $campo ?>"
      id="<?= $campo ?>"
      class="form-control"
      maxlength="<?= $max ?>"
      value="<?= htmlspecialchars($datos[$campo] ?? '') ?>"
      <?= $campo !== 'SINTOMAS' ? 'required' : '' ?>
    >
  </div>
<?php endforeach; ?>
    
  </div>
  <div class="tab-pane fade" id="tab-resultados" role="tabpanel">
    <h5 class="mt-2">Resultados</h5>
   
   <?php
$resultFields = [
  'RESULTCITOLOGIA' => ['Resultado citología', 150],
  'RESULTBIOPSIA'   => ['Resultado biopsia'  , 150],
  'RESULTLEGRADO'   => ['Resultado legrado'  , 150],
];

foreach ($resultFields as $campo => list($label, $max)) : ?>
  <div class="mb-3">
    <label for="<?= $campo ?>"><?= $label ?></label>
    <input
      type="text"
      name="<?= $campo ?>"
      id="<?= $campo ?>"
      class="form-control"
      maxlength="<?= $max ?>"
      value="<?= htmlspecialchars($datos[$campo] ?? '') ?>"
    >
  </div>
<?php endforeach; ?>
   
   
    <h5>Comentarios</h5>
    <div class="mb-3">
      <textarea name="COMENTARIOS" class="form-control" rows="4" maxlength="500"><?= htmlspecialchars($datos['COMENTARIOS'] ?? '') ?></textarea>
    </div>
  </div>
</div>

  <!-- botones de acción -->
  <div class="d-flex gap-2 mb-4">
    <!-- Guardar -->
    <button type="submit" class="btn btn-primary">💾 Guardar</button>

    <!-- Nuevo -->
    <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&nuevo=1"
       class="btn btn-secondary">➕ Nuevo</a>

    <!-- Eliminar (solo si existe CLAVE) -->
    <?php if (!empty($datos['CLAVE'])): ?>
      <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&CLAVE=<?= $datos['CLAVE'] ?>&eliminar=1"
         class="btn btn-danger"
         onclick="return confirm('¿Seguro que quieres eliminar este registro?');">
        🗑️ Eliminar
      </a>
    <?php endif; ?>

    <!-- Imprimir (solo si existe CLAVE) -->
    <?php if (!empty($datos['CLAVE'])): ?>
      <button type="button" class="btn btn-info" onclick="window.print();">
        🖨️ Imprimir
      </button>
    <?php endif; ?>
  </div>
 
  </form>

  <hr>
  <h5>Registros previos</h5>
  <ul class="list-group">
    <?php foreach ($registros as $reg): ?>
      <li class="list-group-item <?= $CLAVE==$reg['CLAVE']?'active':'' ?>">
        <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&CLAVE=<?= $reg['CLAVE'] ?>" class="text-decoration-none">
          <?= $reg['FECHA_CAP'] ?> – <?= htmlspecialchars($reg['REFERIDOPOR']) ?>
        </a>
      </li>
    <?php endforeach; ?>
  </ul>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
