<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Enfermedad Actual"
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Función para obtener la enfermedad actual del paciente
function getEnfermedadActual($pdo, $clavePac) {
    $sql = "SELECT * FROM ENFERMEDAD_ACTUAL WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC LIMIT 1";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Manejar la acción de guardar
if (isset($_POST['action']) && $_POST['action'] === 'saveEnfermedadActual') {
    $motivo = $_POST['motivo'] ?? '';
    $inicio = $_POST['inicio'] ?? '';
    $evolucion = $_POST['evolucion'] ?? '';
    $sintomas = $_POST['sintomas'] ?? '';
    $tratamientos = $_POST['tratamientos'] ?? '';
    $factores_agravantes = $_POST['factores_agravantes'] ?? '';
    $factores_atenuantes = $_POST['factores_atenuantes'] ?? '';
    
    try {
        // Verificar si ya existe un registro
        $checkSql = "SELECT CLAVE FROM ENFERMEDAD_ACTUAL WHERE CLAVEPAC = ?";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([$clavePac]);
        $existingRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingRecord) {
            // Actualizar registro existente
            $sql = "UPDATE ENFERMEDAD_ACTUAL SET 
                    MOTIVO_CONSULTA = ?, 
                    INICIO = ?, 
                    EVOLUCION = ?, 
                    SINTOMAS = ?, 
                    TRATAMIENTOS_PREVIOS = ?,
                    FACTORES_AGRAVANTES = ?,
                    FACTORES_ATENUANTES = ?,
                    FECHA_CAP = NOW() 
                    WHERE CLAVEPAC = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$motivo, $inicio, $evolucion, $sintomas, $tratamientos, $factores_agravantes, $factores_atenuantes, $clavePac]);
        } else {
            // Insertar nuevo registro
            $sql = "INSERT INTO ENFERMEDAD_ACTUAL (
                    CLAVEPAC, MOTIVO_CONSULTA, INICIO, EVOLUCION, 
                    SINTOMAS, TRATAMIENTOS_PREVIOS, FACTORES_AGRAVANTES, FACTORES_ATENUANTES) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$clavePac, $motivo, $inicio, $evolucion, $sintomas, $tratamientos, $factores_agravantes, $factores_atenuantes]);
        }
        
        echo json_encode(['success' => true]);
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}

// Obtener datos existentes
$enfermedadActual = getEnfermedadActual($pdo, $clavePac);
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4>Enfermedad Actual</h4>
        <div class="action-buttons">
            <button class="btn btn-primary" id="btnEditEnfermedad">Editar</button>
            <button class="btn btn-success" id="btnSaveEnfermedad" style="display: none;">Guardar</button>
            <button class="btn btn-secondary" id="btnCancelEnfermedad" style="display: none;">Cancelar</button>
        </div>
    </div>
    
    <form id="form-enfermedad-actual">
        <div class="form-group">
            <label for="motivo">Motivo de Consulta</label>
            <textarea class="form-control" id="motivo" name="motivo" rows="3" disabled><?php echo htmlspecialchars($enfermedadActual['MOTIVO_CONSULTA'] ?? ''); ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="inicio">Inicio</label>
            <textarea class="form-control" id="inicio" name="inicio" rows="2" disabled><?php echo htmlspecialchars($enfermedadActual['INICIO'] ?? ''); ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="evolucion">Evolución</label>
            <textarea class="form-control" id="evolucion" name="evolucion" rows="3" disabled><?php echo htmlspecialchars($enfermedadActual['EVOLUCION'] ?? ''); ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="sintomas">Síntomas Asociados</label>
            <textarea class="form-control" id="sintomas" name="sintomas" rows="3" disabled><?php echo htmlspecialchars($enfermedadActual['SINTOMAS'] ?? ''); ?></textarea>
        </div>
        
        <div class="form-group">
            <label for="tratamientos">Tratamientos Previos</label>
            <textarea class="form-control" id="tratamientos" name="tratamientos" rows="3" disabled><?php echo htmlspecialchars($enfermedadActual['TRATAMIENTOS_PREVIOS'] ?? ''); ?></textarea>
        </div>

        <div class="form-group">
            <label for="factores_agravantes">Factores Agravantes</label>
            <textarea class="form-control" id="factores_agravantes" name="factores_agravantes" rows="2" disabled><?php echo htmlspecialchars($enfermedadActual['FACTORES_AGRAVANTES'] ?? ''); ?></textarea>
        </div>

        <div class="form-group">
            <label for="factores_atenuantes">Factores Atenuantes</label>
            <textarea class="form-control" id="factores_atenuantes" name="factores_atenuantes" rows="2" disabled><?php echo htmlspecialchars($enfermedadActual['FACTORES_ATENUANTES'] ?? ''); ?></textarea>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Botones de edición
    $('#btnEditEnfermedad').click(function() {
        // Habilitar campos
        $('#form-enfermedad-actual textarea').prop('disabled', false);
        
        // Cambiar botones
        $(this).hide();
        $('#btnSaveEnfermedad, #btnCancelEnfermedad').show();
    });
    
    $('#btnCancelEnfermedad').click(function() {
        // Recargar formulario
        location.reload();
    });
    
    // Guardar enfermedad actual
    $('#btnSaveEnfermedad').click(function() {
        var formData = {
            action: 'saveEnfermedadActual',
            motivo: $('#motivo').val(),
            inicio: $('#inicio').val(),
            evolucion: $('#evolucion').val(),
            sintomas: $('#sintomas').val(),
            tratamientos: $('#tratamientos').val(),
            factores_agravantes: $('#factores_agravantes').val(),
            factores_atenuantes: $('#factores_atenuantes').val()
        };
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert("Enfermedad actual guardada correctamente.");
                    
                    // Deshabilitar campos
                    $('#form-enfermedad-actual textarea').prop('disabled', true);
                    
                    // Cambiar botones
                    $('#btnSaveEnfermedad, #btnCancelEnfermedad').hide();
                    $('#btnEditEnfermedad').show();
                } else {
                    alert("Error al guardar enfermedad actual: " + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al guardar enfermedad actual:", error);
                alert("Error al guardar enfermedad actual. Consulte la consola para más detalles.");
            }
        });
    });
});
</script>
