<?php
session_start();

// Verificar autenticación y rol
if (!isset($_SESSION['usuario']) || $_SESSION['rol'] !== 'doctor') {
    header('Location: login.php');
    exit;
}

// Configuración de la aplicación
require_once __DIR__ . '/backend/config/database.php';
date_default_timezone_set('America/Santo_Domingo');

// Obtener información del doctor y empresa
$usuario = htmlspecialchars($_SESSION['usuario']);

// Obtener información de la empresa (igual que admin)
try {
    $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
    $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $empresa = null;
}

// Detectar si es doctora para el saludo correcto
if ($empresa && !empty($empresa['NOMBRE'])) {
    $nombreCompleto = $empresa['NOMBRE'];
    if (stripos($nombreCompleto, 'Dra.') === 0) {
        $saludoDoctor = 'Bienvenida';
    } else {
        $saludoDoctor = 'Bienvenido';
    }
} else {
    $saludoDoctor = 'Bienvenido';
}

// Obtener estadísticas básicas
$citas_hoy = 0;
$citas_atendidas = 0;
$citas_pendientes = 0;
$total_pacientes = 0;

try {
    // Citas de hoy
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE()");
    $result = $stmt->fetch();
    $citas_hoy = $result['total'] ?? 0;

    // Citas atendidas hoy
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 0");
    $result = $stmt->fetch();
    $citas_atendidas = $result['total'] ?? 0;

    // Citas pendientes hoy
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS IN (3, 5)");
    $result = $stmt->fetch();
    $citas_pendientes = $result['total'] ?? 0;

    // Total de pacientes
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
    $result = $stmt->fetch();
    $total_pacientes = $result['total'] ?? 0;

    // Facturación del mes actual (si existe tabla de facturas)
    $facturas_mes = 0;
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM FACTURAS WHERE MONTH(FECHA_FACTURA) = MONTH(CURDATE()) AND YEAR(FECHA_FACTURA) = YEAR(CURDATE())");
        $result = $stmt->fetch();
        $facturas_mes = $result['total'] ?? 0;
    } catch (Exception $e) {
        // Si no existe la tabla FACTURAS, mantener en 0
        $facturas_mes = 0;
    }

} catch (Exception $e) {
    // En caso de error, mantener valores por defecto
    error_log("Error en estadísticas del doctor: " . $e->getMessage());
}

// Formatear fecha actual
$fechaActual = date('l, j \d\e F \d\e Y');
$meses = [
    'January' => 'enero', 'February' => 'febrero', 'March' => 'marzo',
    'April' => 'abril', 'May' => 'mayo', 'June' => 'junio',
    'July' => 'julio', 'August' => 'agosto', 'September' => 'septiembre',
    'October' => 'octubre', 'November' => 'noviembre', 'December' => 'diciembre'
];
$dias = [
    'Monday' => 'Lunes', 'Tuesday' => 'Martes', 'Wednesday' => 'Miércoles',
    'Thursday' => 'Jueves', 'Friday' => 'Viernes', 'Saturday' => 'Sábado', 'Sunday' => 'Domingo'
];

$fechaActual = str_replace(array_keys($dias), array_values($dias), $fechaActual);
$fechaActual = str_replace(array_keys($meses), array_values($meses), $fechaActual);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel del Doctor - Consultorio Médico</title>

    <!-- Favicon médico -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
    <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --medical-primary: #1e40af;
            --medical-secondary: #3b82f6;
            --medical-success: #10b981;
            --medical-info: #0ea5e9;
            --medical-warning: #f59e0b;
            --medical-danger: #ef4444;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--medical-primary) !important;
            font-size: 1.3rem;
        }

        .btn-logout {
            background: linear-gradient(135deg, var(--medical-danger) 0%, #dc2626 100%);
            color: white;
            border: none;
            padding: 0.5rem 1.2rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-logout:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
            color: white;
        }

        .main-content {
            padding: 2rem 0;
            max-width: 1400px;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .welcome-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--medical-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .feature-card h5 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--medical-primary);
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #64748b;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .btn-feature {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }

        .btn-feature:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
            color: white;
        }

        .btn-success-custom {
            background: linear-gradient(135deg, var(--medical-success) 0%, #059669 100%);
        }

        .btn-success-custom:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-info-custom {
            background: linear-gradient(135deg, var(--medical-info) 0%, #0284c7 100%);
        }

        .btn-info-custom:hover {
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
        }

        @media (max-width: 768px) {
            .welcome-section h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navegación -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-user-md"></i> Panel del Doctor
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="btn btn-logout" href="logout.php">
                            <i class="bi bi-box-arrow-right"></i> Cerrar Sesión
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenido Principal -->
    <div class="container main-content">
        <!-- Sección de Bienvenida -->
        <div class="welcome-section">
            <h1><?php echo $saludoDoctor; ?>, <span style="color: #fbbf24;"><?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Sistema de Consultorio'; ?></span></h1>
            <p>Panel de control médico para gestión de pacientes y consultas</p>
            <div class="date-display"><?php echo $fechaActual; ?></div>
        </div>

        <!-- Estadísticas Rápidas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-number"><?php echo $citas_hoy; ?></div>
                <div class="stat-label">Citas Hoy</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-success), #059669);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?php echo $citas_atendidas; ?></div>
                <div class="stat-label">Atendidas</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-warning), #d97706);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo $citas_pendientes; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-info), #0284c7);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo $total_pacientes; ?></div>
                <div class="stat-label">Total Pacientes</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <div class="stat-number"><?php echo $facturas_mes; ?></div>
                <div class="stat-label">Facturas Este Mes</div>
            </div>
        </div>

        <!-- Funcionalidades Principales -->
        <div class="feature-grid">
            <!-- Historia Clínica -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));">
                    <i class="fas fa-file-medical"></i>
                </div>
                <h5>Historia Clínica</h5>
                <p>Accede y gestiona las historias clínicas completas de tus pacientes.</p>
                <a href="backend/historia/buscar_paciente.php" class="btn btn-feature">
                    <i class="fas fa-search"></i> Buscar Paciente
                </a>
            </div>

            <!-- Gestión de Citas -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-success), #059669);">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <h5>Gestión de Citas</h5>
                <p>Administra tus citas médicas y organiza tu agenda diaria.</p>
                <div>
                    <a href="backend/citas/gestion_citas.php" class="btn btn-feature btn-success-custom">
                        <i class="fas fa-calendar-alt"></i> Ver Agenda
                    </a>
                    <a href="backend/citas/crear_cita.php" class="btn btn-feature btn-success-custom">
                        <i class="fas fa-plus"></i> Nueva Cita
                    </a>
                </div>
            </div>

            <!-- Pacientes -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-info), #0284c7);">
                    <i class="fas fa-users"></i>
                </div>
                <h5>Gestión de Pacientes</h5>
                <p>Consulta información de pacientes y actualiza datos personales.</p>
                <a href="backend/pacientes/gestion_pacientes.php" class="btn btn-feature btn-info-custom">
                    <i class="fas fa-user-friends"></i> Ver Pacientes
                </a>
            </div>

            <!-- Prescripciones -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-prescription-bottle-alt"></i>
                </div>
                <h5>Prescripciones</h5>
                <p>Genera recetas médicas y certificados médicos profesionales.</p>
                <a href="backend/historia/buscar_paciente.php?seccion=prescripcion" class="btn btn-feature btn-success-custom">
                    <i class="fas fa-pills"></i> Prescribir
                </a>
            </div>

            <!-- Reportes -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-warning), #d97706);">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h5>Reportes Médicos</h5>
                <p>Genera reportes estadísticos y análisis de consultas.</p>
                <a href="reportes.php" class="btn btn-feature" style="background: linear-gradient(135deg, var(--medical-warning), #d97706);">
                    <i class="fas fa-file-chart-line"></i> Ver Reportes
                </a>
            </div>

            <!-- Laboratorio -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-flask"></i>
                </div>
                <h5>Resultados de Laboratorio</h5>
                <p>Revisa y analiza resultados de laboratorio y estudios.</p>
                <a href="backend/historia/buscar_paciente.php?seccion=laboratorio" class="btn btn-feature" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-microscope"></i> Ver Resultados
                </a>
            </div>

            <!-- Facturación -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-file-invoice-dollar"></i>
                </div>
                <h5>Facturación Médica</h5>
                <p>Genera facturas, controla pagos y administra la facturación de consultas médicas.</p>
                <div>
                    <a href="backend/facturas/facturacion.php" class="btn btn-feature" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-plus-circle"></i> Facturar Paciente
                    </a>
                    <a href="backend/facturas/facturacion1.php" class="btn btn-feature" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-list-alt"></i> Ver Facturas
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
