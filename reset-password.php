<?php
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'backend/config/database.php';

$mensaje = '';
$token = $_GET['token'] ?? null;

if ($token) {
    // Buscar usuario con ese token
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE reset_token = ?");
    $stmt->execute([$token]);
    $user = $stmt->fetch();

    if ($user) {
        // Procesar el formulario
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $newPassword = $_POST['newPassword'] ?? '';
            $confirmPassword = $_POST['confirmPassword'] ?? '';

            if (strlen($newPassword) < 6) {
                $mensaje = "La contraseña debe tener al menos 6 caracteres.";
            } elseif ($newPassword !== $confirmPassword) {
                $mensaje = "Las contraseñas no coinciden.";
            } else {
                $hashed = password_hash($newPassword, PASSWORD_BCRYPT);

                $stmt = $pdo->prepare("UPDATE usuarios SET password = ?, reset_token = NULL WHERE reset_token = ?");
                if ($stmt->execute([$hashed, $token])) {
                    $mensaje = "✅ Contraseña actualizada correctamente. Ya puedes <a href='login.php'>iniciar sesión</a>.";
                } else {
                    $mensaje = "❌ Error al actualizar la contraseña.";
                }
            }
        }
    } else {
        $mensaje = "❌ Enlace inválido o expirado.";
    }
} else {
    $mensaje = "❌ No se proporcionó ningún token.";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restablecer Contraseña</title>
    <link rel="stylesheet" href="public/assets/css/styles.css">
</head>
<body>
<div class="login-container">
    <div class="login-card">
        <h2 class="login-title">Restablecer Contraseña</h2>

        <?php if (!empty($mensaje)): ?>
            <p style="color: <?= str_contains($mensaje, '✅') ? 'green' : 'red' ?>"><?= $mensaje ?></p>
        <?php endif; ?>

        <?php if ($user && empty($mensaje) || str_contains($mensaje, 'caracteres') || str_contains($mensaje, 'coinciden')): ?>
        <form method="POST">
            <div class="form-group">
                <label for="newPassword">Nueva Contraseña:</label>
                <input type="password" id="newPassword" name="newPassword" required>
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirmar Contraseña:</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            <button type="submit" class="btn-login">Restablecer Contraseña</button>
        </form>
        <?php endif; ?>
    </div>
</div>
</body>
</html>
