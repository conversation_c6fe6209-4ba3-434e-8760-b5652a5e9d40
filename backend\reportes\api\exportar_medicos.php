<?php
// backend/reportes/api/exportar_medicos.php
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario'])) {
    http_response_code(401);
    die('No autorizado');
}

require_once __DIR__ . '/../../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

// Obtener parámetros
$formato = $_GET['formato'] ?? 'excel';
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$tipo_examen = $_GET['tipo_examen'] ?? '';
$sexo = $_GET['sexo'] ?? '';
$edad_min = $_GET['edad_min'] ?? '';
$edad_max = $_GET['edad_max'] ?? '';

try {
    // Construir consulta base para exámenes físicos
    $where_conditions = ["DATE(e.FECHA_CAP) BETWEEN ? AND ?"];
    $params = [$fecha_inicio, $fecha_fin];

    if ($sexo) {
        $where_conditions[] = "p.SEXO = ?";
        $params[] = $sexo;
    }

    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
    
    // Agregar filtro de edad
    $edad_clause = "";
    if ($edad_min || $edad_max) {
        if ($edad_min && $edad_max) {
            $edad_clause = " AND TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) BETWEEN $edad_min AND $edad_max";
        } elseif ($edad_min) {
            $edad_clause = " AND TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) >= $edad_min";
        } elseif ($edad_max) {
            $edad_clause = " AND TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) <= $edad_max";
        }
    }

    // Obtener datos de exámenes físicos
    $sql = "SELECT e.FECHA_CAP, p.NOMBREAPELLIDO, p.CEDULA, p.SEXO,
                  TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) as edad,
                  e.PESO, e.TALLA, e.TAS, e.TAD, e.FC, e.TEMP, e.IMC,
                  e.HALLAZGOS, e.OTROS
           FROM EXAMENFISICO e 
           JOIN PACIENTES p ON e.CLAVEPAC = p.CLAVE 
           $where_clause $edad_clause 
           ORDER BY e.FECHA_CAP DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $examenes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($formato === 'excel') {
        // Exportar a Excel (CSV)
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_medicos_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // BOM para UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Encabezados
        fputcsv($output, [
            'Fecha',
            'Paciente',
            'Cédula',
            'Sexo',
            'Edad',
            'Peso (kg)',
            'Talla (cm)',
            'TAS',
            'TAD',
            'FC',
            'Temp (°C)',
            'IMC',
            'Hallazgos',
            'Otros'
        ], ';');
        
        // Datos
        foreach ($examenes as $examen) {
            fputcsv($output, [
                date('d/m/Y', strtotime($examen['FECHA_CAP'])),
                $examen['NOMBREAPELLIDO'],
                $examen['CEDULA'],
                $examen['SEXO'] == 'M' ? 'Masculino' : 'Femenino',
                $examen['edad'] ?? 'N/A',
                $examen['PESO'] ?? '',
                $examen['TALLA'] ?? '',
                $examen['TAS'] ?? '',
                $examen['TAD'] ?? '',
                $examen['FC'] ?? '',
                $examen['TEMP'] ?? '',
                $examen['IMC'] ?? '',
                $examen['HALLAZGOS'] ?? '',
                $examen['OTROS'] ?? ''
            ], ';');
        }
        
        fclose($output);
        
    } else {
        // Exportar a PDF (HTML por ahora)
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_medicos_' . date('Y-m-d') . '.html"');
        
        // Obtener información de la empresa
        $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
        $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
        $nombre_empresa = $empresa ? $empresa['NOMBRE'] : 'Consultorio Médico';
        
        // Estadísticas
        $total_examenes = count($examenes);
        
        // Calcular promedios de signos vitales
        $pesos = array_filter(array_column($examenes, 'PESO'), function($p) { return $p !== null && $p > 0; });
        $tallas = array_filter(array_column($examenes, 'TALLA'), function($t) { return $t !== null && $t > 0; });
        $tas = array_filter(array_column($examenes, 'TAS'), function($t) { return $t !== null && $t > 0; });
        $tad = array_filter(array_column($examenes, 'TAD'), function($t) { return $t !== null && $t > 0; });
        $fc = array_filter(array_column($examenes, 'FC'), function($f) { return $f !== null && $f > 0; });
        $temp = array_filter(array_column($examenes, 'TEMP'), function($t) { return $t !== null && $t > 0; });
        $imc = array_filter(array_column($examenes, 'IMC'), function($i) { return $i !== null && $i > 0; });
        
        $peso_promedio = !empty($pesos) ? round(array_sum($pesos) / count($pesos), 1) : 0;
        $talla_promedio = !empty($tallas) ? round(array_sum($tallas) / count($tallas), 1) : 0;
        $tas_promedio = !empty($tas) ? round(array_sum($tas) / count($tas), 1) : 0;
        $tad_promedio = !empty($tad) ? round(array_sum($tad) / count($tad), 1) : 0;
        $fc_promedio = !empty($fc) ? round(array_sum($fc) / count($fc), 1) : 0;
        $temp_promedio = !empty($temp) ? round(array_sum($temp) / count($temp), 1) : 0;
        $imc_promedio = !empty($imc) ? round(array_sum($imc) / count($imc), 1) : 0;
        
        echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Reporte Médico</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .info { margin-bottom: 20px; }
        .stats { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center; }
        .stat-item { background: white; padding: 10px; border-radius: 3px; border: 1px solid #dee2e6; }
        .vitals-grid { display: grid; grid-template-columns: repeat(7, 1fr); gap: 10px; margin: 15px 0; }
        .vital-item { background: #e9ecef; padding: 8px; border-radius: 3px; text-align: center; font-size: 11px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 10px; }
        th, td { border: 1px solid #ddd; padding: 4px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .imc-normal { color: #28a745; font-weight: bold; }
        .imc-sobrepeso { color: #ffc107; font-weight: bold; }
        .imc-obesidad { color: #dc3545; font-weight: bold; }
        .footer { margin-top: 30px; text-align: center; font-size: 10px; color: #666; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>$nombre_empresa</h1>
        <h2>Reporte de Exámenes Médicos</h2>
        <p>Período: " . date('d/m/Y', strtotime($fecha_inicio)) . " - " . date('d/m/Y', strtotime($fecha_fin)) . "</p>
    </div>
    
    <div class='info'>
        <p><strong>Generado por:</strong> " . htmlspecialchars($_SESSION['usuario']) . "</p>
        <p><strong>Fecha de generación:</strong> " . date('d/m/Y H:i:s') . "</p>";
        
        if ($sexo) echo "<p><strong>Filtro por sexo:</strong> " . ($sexo == 'M' ? 'Masculino' : 'Femenino') . "</p>";
        if ($edad_min || $edad_max) {
            echo "<p><strong>Filtro por edad:</strong> ";
            if ($edad_min && $edad_max) echo "$edad_min - $edad_max años";
            elseif ($edad_min) echo "Desde $edad_min años";
            elseif ($edad_max) echo "Hasta $edad_max años";
            echo "</p>";
        }
        
        echo "</div>
    
    <div class='stats'>
        <h3>Resumen Estadístico</h3>
        <div class='stats-grid'>
            <div class='stat-item'>
                <h4>$total_examenes</h4>
                <p>Total Exámenes</p>
            </div>
            <div class='stat-item'>
                <h4>" . count($pesos) . "</h4>
                <p>Con Peso Registrado</p>
            </div>
            <div class='stat-item'>
                <h4>" . count($imc) . "</h4>
                <p>Con IMC Calculado</p>
            </div>
            <div class='stat-item'>
                <h4>" . count($tas) . "</h4>
                <p>Con Presión Arterial</p>
            </div>
        </div>
        
        <h4>Promedios de Signos Vitales</h4>
        <div class='vitals-grid'>
            <div class='vital-item'>
                <strong>Peso</strong><br>
                {$peso_promedio} kg
            </div>
            <div class='vital-item'>
                <strong>Talla</strong><br>
                {$talla_promedio} cm
            </div>
            <div class='vital-item'>
                <strong>TAS</strong><br>
                {$tas_promedio}
            </div>
            <div class='vital-item'>
                <strong>TAD</strong><br>
                {$tad_promedio}
            </div>
            <div class='vital-item'>
                <strong>FC</strong><br>
                {$fc_promedio}
            </div>
            <div class='vital-item'>
                <strong>Temp</strong><br>
                {$temp_promedio}°C
            </div>
            <div class='vital-item'>
                <strong>IMC</strong><br>
                {$imc_promedio}
            </div>
        </div>
    </div>
    
    <h3>Detalle de Exámenes Físicos</h3>
    <table>
        <thead>
            <tr>
                <th>Fecha</th>
                <th>Paciente</th>
                <th>Sexo</th>
                <th>Edad</th>
                <th>Peso</th>
                <th>Talla</th>
                <th>TAS/TAD</th>
                <th>FC</th>
                <th>Temp</th>
                <th>IMC</th>
                <th>Hallazgos</th>
            </tr>
        </thead>
        <tbody>";
        
        foreach ($examenes as $examen) {
            $imc_clase = '';
            if ($examen['IMC']) {
                $imc_val = $examen['IMC'];
                if ($imc_val < 18.5) $imc_clase = '';
                elseif ($imc_val < 25) $imc_clase = 'imc-normal';
                elseif ($imc_val < 30) $imc_clase = 'imc-sobrepeso';
                else $imc_clase = 'imc-obesidad';
            }
            
            echo "<tr>
                <td>" . date('d/m/Y', strtotime($examen['FECHA_CAP'])) . "</td>
                <td>" . htmlspecialchars($examen['NOMBREAPELLIDO']) . "</td>
                <td>" . ($examen['SEXO'] == 'M' ? 'M' : 'F') . "</td>
                <td>" . ($examen['edad'] ?? 'N/A') . "</td>
                <td>" . ($examen['PESO'] ? $examen['PESO'] . ' kg' : 'N/A') . "</td>
                <td>" . ($examen['TALLA'] ? $examen['TALLA'] . ' cm' : 'N/A') . "</td>
                <td>" . (($examen['TAS'] && $examen['TAD']) ? $examen['TAS'] . '/' . $examen['TAD'] : 'N/A') . "</td>
                <td>" . ($examen['FC'] ?? 'N/A') . "</td>
                <td>" . ($examen['TEMP'] ? $examen['TEMP'] . '°C' : 'N/A') . "</td>
                <td class='$imc_clase'>" . ($examen['IMC'] ? round($examen['IMC'], 1) : 'N/A') . "</td>
                <td>" . htmlspecialchars(substr($examen['HALLAZGOS'] ?? '', 0, 50)) . "</td>
            </tr>";
        }
        
        echo "</tbody>
    </table>
    
    <div class='footer'>
        <p>Reporte generado automáticamente por el Sistema de Gestión del Consultorio</p>
        <p>Total de registros: $total_examenes</p>
    </div>
</body>
</html>";
    }

} catch (PDOException $e) {
    http_response_code(500);
    die('Error en la base de datos: ' . $e->getMessage());
} catch (Exception $e) {
    http_response_code(500);
    die('Error interno: ' . $e->getMessage());
}
?>
