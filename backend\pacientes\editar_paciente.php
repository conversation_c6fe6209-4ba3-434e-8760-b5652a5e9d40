<?php
session_start();
require_once '../config/database.php';
$config = include '../config/config.php'; // Ruta al archivo de configuración

// Obtener el paciente según la CLAVE pasada por GET
$paciente = null;
if (isset($_GET['CLAVE'])) {
    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
    $stmt->execute([$_GET['CLAVE']]);
    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);
}
if (!$paciente) {
    echo "Paciente no encontrado.";
    exit;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Editar Paciente</title>
  <!-- Bootstrap CSS -->
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <style>
    /* Estilos opcionales para resaltar las pestañas */
    .nav-tabs .nav-link.active {
      background-color: #007bff;
      color: #fff;
      font-weight: bold;
      border-bottom: 3px solid #0056b3;
    }
  </style>
  <script>
    function validarNumeros(event) {
      var input = event.target;
      var valor = input.value;
      input.value = valor.replace(/[^0-9]/g, '');
      if (input.value.length > 11) {
          input.value = input.value.slice(0, 11);
      }
    }
    function validarFechaNacimiento(event) {
      var input = event.target;
      var fechaNacimiento = new Date(input.value);
      var fechaHoy = new Date();
      if (fechaNacimiento > fechaHoy) {
          input.setCustomValidity("La fecha de nacimiento no puede ser en el futuro.");
      } else {
          input.setCustomValidity("");
      }
    }
    // Actualizar NSS: campo de solo lectura que se iguala al valor de la cédula
    document.addEventListener("DOMContentLoaded", function () {
      const cedulaInput = document.getElementById('CEDULA');
      const nssInput = document.getElementById('NSS');
      if (cedulaInput && nssInput) {
          cedulaInput.addEventListener('input', function () {
              nssInput.value = this.value;
          });
      }
    });
  </script>
</head>
<body>
<div class="container mt-5">
  <h1>Editar Paciente</h1>
  <form action="actualizar_paciente.php" method="POST" id="editarPacienteForm">
    <!-- Campo oculto para la CLAVE -->
    <input type="hidden" name="CLAVE" value="<?php echo htmlspecialchars($paciente['CLAVE']); ?>">
    <!-- Campo oculto para SINCRONIZADO (valor 0 por defecto) -->
    <input type="hidden" name="SINCRONIZADO" value="0">

    <!-- Datos principales -->
    <div class="form-group">
      <label for="CEDULA">Cédula:</label>
      <input type="text" class="form-control" id="CEDULA" name="CEDULA" value="<?php echo htmlspecialchars($paciente['CEDULA']); ?>" maxlength="11" required oninput="validarNumeros(event)">
    </div>
    <div class="form-group">
      <label for="NOMBRES">Nombres:</label>
      <input type="text" class="form-control" id="NOMBRES" name="NOMBRES" value="<?php echo htmlspecialchars($paciente['NOMBRES']); ?>" required>
    </div>
    <div class="form-group">
      <label for="APELLIDOS">Apellidos:</label>
      <input type="text" class="form-control" id="APELLIDOS" name="APELLIDOS" value="<?php echo htmlspecialchars($paciente['APELLIDOS']); ?>" required>
    </div>
    <div class="form-group">
      <label for="FECHANAC">Fecha de Nacimiento:</label>
      <input type="date" class="form-control" id="FECHANAC" name="FECHANAC" value="<?php echo htmlspecialchars($paciente['FECHANAC']); ?>" required oninput="validarFechaNacimiento(event)">
    </div>
    <div class="form-group">
      <label for="SEXO">Sexo:</label>
      <select class="form-control" id="SEXO" name="SEXO" required>
        <option value="">Seleccionar</option>
        <option value="Masculino" <?php echo ($paciente['SEXO'] === 'Masculino') ? 'selected' : ''; ?>>Masculino</option>
        <option value="Femenino" <?php echo ($paciente['SEXO'] === 'Femenino') ? 'selected' : ''; ?>>Femenino</option>
        <option value="Indefinido" <?php echo ($paciente['SEXO'] === 'Indefinido') ? 'selected' : ''; ?>>Indefinido</option>
      </select>
    </div>
   
   <div class="form-group">
  <label for="RH">Tipo de Sangre (RH):</label>
  <select class="form-control" id="RH" name="RH" required>
    <option value="" disabled>Seleccione una opción</option>
    <option value="O+" <?php echo (trim($paciente['RH']) === 'O+' ? 'selected' : ''); ?>>O+</option>
    <option value="O-" <?php echo (trim($paciente['RH']) === 'O-' ? 'selected' : ''); ?>>O-</option>
    <option value="A+" <?php echo (trim($paciente['RH']) === 'A+' ? 'selected' : ''); ?>>A+</option>
    <option value="A-" <?php echo (trim($paciente['RH']) === 'A-' ? 'selected' : ''); ?>>A-</option>
    <option value="B+" <?php echo (trim($paciente['RH']) === 'B+' ? 'selected' : ''); ?>>B+</option>
    <option value="B-" <?php echo (trim($paciente['RH']) === 'B-' ? 'selected' : ''); ?>>B-</option>
    <option value="AB+" <?php echo (trim($paciente['RH']) === 'AB+' ? 'selected' : ''); ?>>AB+</option>
    <option value="AB-" <?php echo (trim($paciente['RH']) === 'AB-' ? 'selected' : ''); ?>>AB-</option>
  </select>
</div>
   
  
    <div class="form-group">
      <label for="NACIONALIDAD">Nacionalidad:</label>
      <input type="text" class="form-control" id="NACIONALIDAD" name="NACIONALIDAD" value="<?php echo htmlspecialchars($paciente['NACIONALIDAD']); ?>" required>
    </div>

    <hr>
    <h4>Información Adicional</h4>
    <!-- Información adicional (fuera de pestañas) -->
    <div class="form-group">
      <label for="ESTADOCIVIL">Estado Civil:</label>
      <select class="form-control" id="ESTADOCIVIL" name="ESTADOCIVIL">
        <option value="">Seleccionar</option>
        <option value="Soltero(a)" <?php echo ($paciente['ESTADOCIVIL'] == "Soltero(a)") ? 'selected' : ''; ?>>Soltero(a)</option>
        <option value="Casado(a)" <?php echo ($paciente['ESTADOCIVIL'] == "Casado(a)") ? 'selected' : ''; ?>>Casado(a)</option>
        <option value="Viudo(a)" <?php echo ($paciente['ESTADOCIVIL'] == "Viudo(a)") ? 'selected' : ''; ?>>Viudo(a)</option>
        <option value="Divorciado(a)" <?php echo ($paciente['ESTADOCIVIL'] == "Divorciado(a)") ? 'selected' : ''; ?>>Divorciado(a)</option>
        <option value="Separado(a)" <?php echo ($paciente['ESTADOCIVIL'] == "Separado(a)") ? 'selected' : ''; ?>>Separado(a)</option>
        <option value="Unión Libre" <?php echo ($paciente['ESTADOCIVIL'] == "Unión Libre") ? 'selected' : ''; ?>>Unión Libre</option>
      </select>
    </div>
    <div class="form-group">
      <label for="LUGARNAC">Lugar de Nacimiento:</label>
      <input type="text" class="form-control" id="LUGARNAC" name="LUGARNAC" value="<?php echo htmlspecialchars($paciente['LUGARNAC']); ?>">
    </div>
    <div class="form-group">
      <label for="OCUPACION">Ocupación:</label>
      <input type="text" class="form-control" id="OCUPACION" name="OCUPACION" value="<?php echo htmlspecialchars($paciente['OCUPACION']); ?>">
    </div>
    <div class="form-group">
      <label for="RELIGION">Religión:</label>
      <select class="form-control" id="RELIGION" name="RELIGION">
        <option value="">Seleccionar</option>
        <option value="Católico" <?php echo ($paciente['RELIGION'] == "Católico") ? 'selected' : ''; ?>>Católico</option>
        <option value="Cristiano Evangelice" <?php echo ($paciente['RELIGION'] == "Cristiano Evangelice") ? 'selected' : ''; ?>>Cristiano Evangelice</option>
        <option value="Testigo" <?php echo ($paciente['RELIGION'] == "Testigo") ? 'selected' : ''; ?>>Testigo</option>
        <option value="Ninguna" <?php echo ($paciente['RELIGION'] == "Ninguna") ? 'selected' : ''; ?>>Ninguna</option>
        <option value="Otra" <?php echo ($paciente['RELIGION'] == "Otra") ? 'selected' : ''; ?>>Otra</option>
      </select>
    </div>

    <!-- Pestañas -->
    <ul class="nav nav-tabs" id="infoTab" role="tablist">
      <li class="nav-item">
        <a class="nav-link active" id="domicilio-tab" data-toggle="tab" href="#domicilio" role="tab" aria-controls="domicilio" aria-selected="true">Domicilio</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="contacto-tab" data-toggle="tab" href="#contacto" role="tab" aria-controls="contacto" aria-selected="false">Contacto</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="poliza-tab" data-toggle="tab" href="#poliza" role="tab" aria-controls="poliza" aria-selected="false">Póliza</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="responsable-tab" data-toggle="tab" href="#responsable" role="tab" aria-controls="responsable" aria-selected="false">Responsable</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="familiar-tab" data-toggle="tab" href="#familiar" role="tab" aria-controls="familiar" aria-selected="false">Familiar</a>
      </li>
    </ul>
    
    <div class="tab-content pt-3" id="infoTabContent">
      <!-- Pestaña DOMICILIO -->
      <div class="tab-pane fade show active" id="domicilio" role="tabpanel" aria-labelledby="domicilio-tab">
        <div class="form-group">
          <label for="CALLE">Calle:</label>
          <input type="text" class="form-control" id="CALLE" name="CALLE" value="<?php echo htmlspecialchars($paciente['CALLE']); ?>">
        </div>
        <div class="form-group">
          <label for="PROVINCIA">Provincia:</label>
          <input type="text" class="form-control" id="PROVINCIA" name="PROVINCIA" value="<?php echo htmlspecialchars($paciente['PROVINCIA']); ?>">
        </div>
        <div class="form-group">
          <label for="LOCALIDAD">Localidad:</label>
          <input type="text" class="form-control" id="LOCALIDAD" name="LOCALIDAD" value="<?php echo htmlspecialchars($paciente['LOCALIDAD']); ?>">
        </div>
        <div class="form-group">
          <label for="MUNICIPIO">Municipio:</label>
          <input type="text" class="form-control" id="MUNICIPIO" name="MUNICIPIO" value="<?php echo htmlspecialchars($paciente['MUNICIPIO']); ?>">
        </div>
        <div class="form-group">
          <label for="PAIS">País:</label>
          <input type="text" class="form-control" id="PAIS" name="PAIS" value="<?php echo htmlspecialchars($paciente['PAIS']); ?>">
        </div>
        <div class="form-group">
          <label for="REFERENCIA">Referencia:</label>
          <input type="text" class="form-control" id="REFERENCIA" name="REFERENCIA" value="<?php echo htmlspecialchars($paciente['REFERENCIA']); ?>">
        </div>
      </div>
      
      <!-- Pestaña CONTACTO -->
      <div class="tab-pane fade" id="contacto" role="tabpanel" aria-labelledby="contacto-tab">
        <div class="form-group">
          <label for="CELULAR">Teléfono Celular:</label>
          <input type="text" class="form-control" id="CELULAR" name="CELULAR" value="<?php echo htmlspecialchars($paciente['CELULAR']); ?>">
        </div>
        <div class="form-group">
          <label for="TELEFONO">Teléfono de Casa:</label>
          <input type="text" class="form-control" id="TELEFONO" name="TELEFONO" value="<?php echo htmlspecialchars($paciente['TELEFONO']); ?>">
        </div>
        <div class="form-group">
          <label for="TELTRABAJO">Teléfono del Trabajo:</label>
          <input type="text" class="form-control" id="TELTRABAJO" name="TELTRABAJO" value="<?php echo htmlspecialchars($paciente['TELTRABAJO']); ?>">
        </div>
        <div class="form-group">
          <label for="FAX">Fax:</label>
          <input type="text" class="form-control" id="FAX" name="FAX" value="<?php echo htmlspecialchars($paciente['FAX']); ?>">
        </div>
        <div class="form-group">
          <label for="ECORREO">Correo Electrónico:</label>
          <input type="email" class="form-control" id="ECORREO" name="ECORREO" value="<?php echo htmlspecialchars($paciente['ECORREO']); ?>">
        </div>
      </div>
      
      <!-- Pestaña PÓLIZA -->
      <div class="tab-pane fade" id="poliza" role="tabpanel" aria-labelledby="poliza-tab">
        <div class="form-group">
          <label for="ARS">ARS:</label>
          <input type="text" class="form-control" id="ARS" name="ARS" value="<?php echo htmlspecialchars($paciente['ARS']); ?>">
        </div>
        <div class="form-group">
          <label for="PLANES">Planes:</label>
          <input type="text" class="form-control" id="PLANES" name="PLANES" value="<?php echo htmlspecialchars($paciente['PLANES']); ?>">
        </div>
        <div class="form-group">
          <label for="AFILIADO">Afiliado:</label>
          <input type="text" class="form-control" id="AFILIADO" name="AFILIADO" value="<?php echo htmlspecialchars($paciente['AFILIADO']); ?>">
        </div>
        <div class="form-group">
          <label for="VIGENCIA">Vigencia:</label>
          <input type="date" class="form-control" id="VIGENCIA" name="VIGENCIA" value="<?php echo !empty($paciente['VIGENCIA']) ? htmlspecialchars(date('Y-m-d', strtotime($paciente['VIGENCIA']))) : ''; ?>">
        </div>
        <!-- NSS es de solo lectura y se iguala a la cédula -->
        <div class="form-group">
          <label for="NSS">Número de Seguro Social (NSS):</label>
          <input type="text" class="form-control" id="NSS" name="NSS" readonly value="<?php echo htmlspecialchars($paciente['CEDULA']); ?>">
        </div>
        <div class="form-group">
          <label for="CATEGORIA">Categoría:</label>
          <input type="text" class="form-control" id="CATEGORIA" name="CATEGORIA" value="<?php echo htmlspecialchars($paciente['CATEGORIA']); ?>">
        </div>
      </div>
      
      <!-- Pestaña RESPONSABLE -->
      <div class="tab-pane fade" id="responsable" role="tabpanel" aria-labelledby="responsable-tab">
        <div class="form-group">
          <label for="NOMBRERESP">Nombre del Responsable:</label>
          <input type="text" class="form-control" id="NOMBRERESP" name="NOMBRERESP" value="<?php echo htmlspecialchars($paciente['NOMBRERESP']); ?>">
        </div>
        <div class="form-group">
          <label for="DIRECCIONRESP">Dirección del Responsable:</label>
          <input type="text" class="form-control" id="DIRECCIONRESP" name="DIRECCIONRESP" value="<?php echo htmlspecialchars($paciente['DIRECCIONRESP']); ?>">
        </div>
        <div class="form-group">
          <label for="CEDULARESP">Cédula del Responsable:</label>
          <input type="text" class="form-control" id="CEDULARESP" name="CEDULARESP" value="<?php echo htmlspecialchars($paciente['CEDULARESP']); ?>">
        </div>
        <div class="form-group">
          <label for="TELEFONORESP">Teléfono del Responsable:</label>
          <input type="text" class="form-control" id="TELEFONORESP" name="TELEFONORESP" value="<?php echo htmlspecialchars($paciente['TELEFONORESP']); ?>">
        </div>
      </div>
      
      <!-- Pestaña FAMILIAR -->
      <div class="tab-pane fade" id="familiar" role="tabpanel" aria-labelledby="familiar-tab">
        <div class="form-group">
          <label for="FAMILIARPROX">Nombre del Familiar Próximo:</label>
          <input type="text" class="form-control" id="FAMILIARPROX" name="FAMILIARPROX" value="<?php echo htmlspecialchars($paciente['FAMILIARPROX']); ?>">
        </div>
        <div class="form-group">
          <label for="DIRECCIONFAMILIAR">Dirección del Familiar Próximo:</label>
          <input type="text" class="form-control" id="DIRECCIONFAMILIAR" name="DIRECCIONFAMILIAR" value="<?php echo htmlspecialchars($paciente['DIRECCIONFAMILIAR']); ?>">
        </div>
        <div class="form-group">
          <label for="TELEFONOFAMILIAR">Teléfono del Familiar Próximo:</label>
          <input type="text" class="form-control" id="TELEFONOFAMILIAR" name="TELEFONOFAMILIAR" value="<?php echo htmlspecialchars($paciente['TELEFONOFAMILIAR']); ?>">
        </div>
      </div>
    </div>
    
    <!-- Otros campos fuera de pestañas -->
    <div class="form-group mt-3">
      <label for="OBSERVACIONES">Observaciones (por la secretaria):</label>
       <textarea class="form-control" id="OBSERVACIONES" name="OBSERVACIONES" maxlength="60">${paciente.OBSERVACIONES ? paciente.OBSERVACIONES : ''}</textarea>   
          
    </div>
    <div class="form-group">
      <label for="PESOHABITUAL">Peso Habitual:</label>
      <input type="number" step="0.01" class="form-control" id="PESOHABITUAL" name="PESOHABITUAL" value="<?php echo htmlspecialchars($paciente['PESOHABITUAL']); ?>">
    </div>
    <!-- Botones de radio para Nivel Escolar (valores 0,1,2,3) -->
    <div class="form-group">
      <label>Nivel Escolar:</label><br>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelPrimario" value="0" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="0" ? 'checked' : ''; ?>>
        <label class="form-check-label" for="nivelPrimario">Primario</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelSecundario" value="1" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="1" ? 'checked' : ''; ?>>
        <label class="form-check-label" for="nivelSecundario">Secundario</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelUniversitario" value="2" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="2" ? 'checked' : ''; ?>>
        <label class="form-check-label" for="nivelUniversitario">Universitario</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelNinguno" value="3" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="3" ? 'checked' : ''; ?>>
        <label class="form-check-label" for="nivelNinguno">Ninguno</label>
      </div>
    </div>
    <!-- Botones de radio para Procedencia -->
    <div class="form-group">
      <label>Procedencia:</label><br>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaUrbano" value="Urbano" <?php echo $paciente && htmlspecialchars($paciente['PROCEDENCIA'])=="Urbano" ? 'checked' : ''; ?>>
        <label class="form-check-label" for="procedenciaUrbano">Urbano</label>
      </div>
      <div class="form-check form-check-inline">
        <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaRural" value="Rural" <?php echo $paciente && htmlspecialchars($paciente['PROCEDENCIA'])=="Rural" ? 'checked' : ''; ?>>
        <label class="form-check-label" for="procedenciaRural">Rural</label>
      </div>
    </div>
    <div class="form-group">
      <label for="RECORDCLINICA">Record Clínico:</label>
      <input type="text" class="form-control" id="RECORDCLINICA" name="RECORDCLINICA" value="<?php echo htmlspecialchars($paciente['RECORDCLINICA']); ?>">
    </div>
    
    <button type="submit" class="btn btn-primary">Guardar Cambios</button>
  </form>
</div>

<!-- jQuery y Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>

