/* Estilos personalizados para la página de error - no-autorizado.css */

/* Variables de color y fuentes para consistencia */
:root {
    --primary-color: #285499; /* Azul principal de tu tema */
    --danger-color: #DC3545; /* Rojo para el error */
    --light-bg: #f8f9fa; /* Fondo claro general */
    --dark-text: #343a40; /* Texto oscuro */
    --gray-text: #6c757d; /* Texto gris */
    --card-shadow: 0 8px 25px rgba(0, 0, 0, 0.15); /* Sombra más dramática */
    --border-radius-lg: 1rem; /* Bordes más redondeados */
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--light-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh; /* Asegura que ocupe toda la altura de la ventana */
    margin: 0;
    padding: 20px; /* Pequeño padding para móviles */
    text-align: center;
}

.error-container {
    background-color: #ffffff;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    padding: 3rem;
    max-width: 550px; /* Limita el ancho para mejor legibilidad */
    width: 100%;
    animation: fadeInScale 0.6s ease-out; /* Animación de entrada */
}

.error-icon {
    font-size: 5rem; /* Icono grande */
    color: var(--danger-color);
    margin-bottom: 1.5rem;
    animation: bounceIn 0.8s ease-out; /* Animación para el icono */
    display: block; /* Para centrar el icono */
    margin-left: auto;
    margin-right: auto;
}

h1 {
    font-size: 2.5rem; /* Tamaño de título más grande */
    color: var(--dark-text);
    margin-bottom: 1rem;
    font-weight: 700;
}

p {
    font-size: 1.15rem; /* Tamaño de párrafo más legible */
    color: var(--gray-text);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-return {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    padding: 0.85rem 2rem;
    font-size: 1.1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.btn-return:hover {
    background-color: #204176;
    border-color: #1a3c6d;
    transform: translateY(-2px); /* Pequeño efecto hover */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* Animaciones */
@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% { transform: scale(0.1); opacity: 0; }
    60% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1); }
}

/* Responsividad básica para pantallas muy pequeñas */
@media (max-width: 576px) {
    .error-container {
        padding: 2rem;
    }
    h1 {
        font-size: 2rem;
    }
    p {
        font-size: 1rem;
    }
    .error-icon {
        font-size: 4rem;
    }
}