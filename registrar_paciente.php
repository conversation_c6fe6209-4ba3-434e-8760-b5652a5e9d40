<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();
require_once 'backend/config/database.php';

$mensaje = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $cedula = $_POST['cedula'] ?? '';
    $usuario = $_POST['usuario'] ?? '';
    $clave = $_POST['clave'] ?? '';
    $confirmar = $_POST['confirmar'] ?? '';

    if ($clave !== $confirmar) {
        $mensaje = "❌ Las contraseñas no coinciden.";
    } else {
        // Verificar si la cédula existe en PACIENTES
        $stmt = $pdo->prepare("SELECT NOMBREAPELLIDO, ECORREO FROM PACIENTES WHERE CEDULA = ?");
        $stmt->execute([$cedula]);
        $paciente = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$paciente) {
            $mensaje = "❌ No se encontró un paciente registrado con esa cédula.";
        } else {
            // Verificar si la cédula ya está registrada como usuario
            $stmtCheckCedula = $pdo->prepare("SELECT * FROM usuarios WHERE cedula = ?");
            $stmtCheckCedula->execute([$cedula]);

            if ($stmtCheckCedula->fetch()) {
                $mensaje = "⚠️ Esta cédula ya fue registrada como usuario.";
            } else {
                // Verificar si el nombre de usuario ya existe
                $stmt2 = $pdo->prepare("SELECT * FROM usuarios WHERE usuario = ?");
                $stmt2->execute([$usuario]);

                if ($stmt2->fetch()) {
                    $mensaje = "❌ Este nombre de usuario ya está en uso.";
                } else {
                    // Registrar el nuevo usuario
                    $hash = password_hash($clave, PASSWORD_DEFAULT);
                    $correoPaciente = $paciente['ECORREO'] ?? '';

                    $stmt3 = $pdo->prepare("INSERT INTO usuarios (usuario, cedula, password, email, role) VALUES (?, ?, ?, ?, 'paciente')");
                    $stmt3->execute([$usuario, $cedula, $hash, $correoPaciente]);

                    // Enviar correo de bienvenida (opcional)
                    if ($correoPaciente && filter_var($correoPaciente, FILTER_VALIDATE_EMAIL)) {
                        $asunto = "Bienvenido a Mi Consultorio";
                        $cuerpo = "Hola {$paciente['NOMBREAPELLIDO']},\n\nGracias por registrarte.\n¡Ya puedes solicitar tus citas médicas!";
                        mail($correoPaciente, $asunto, $cuerpo, "From: <EMAIL>");
                    }

                    // Notificación a la secretaria (opcional)
                    mail("<EMAIL>", "📢 Nuevo paciente registrado",
                         "Nuevo registro:\n\nNombre: {$paciente['NOMBREAPELLIDO']}\nCédula: $cedula\nUsuario: $usuario\nFecha: " . date("Y-m-d H:i:s"),
                         "From: <EMAIL>");

                    // Iniciar sesión automática
                    $_SESSION['usuario'] = $usuario;
                    $_SESSION['rol'] = 'paciente';
                    $_SESSION['cedula'] = $cedula;
                    $_SESSION['nombre_paciente'] = $paciente['NOMBREAPELLIDO'];

                    header("Location: paciente_panel.php");
                    exit;
                }
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Registro de Paciente</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
    <h2>Registro de Paciente</h2>

    <?php if ($mensaje): ?>
        <div class="alert alert-warning"><?php echo $mensaje; ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="mb-3">
            <label>Cédula (11 dígitos):</label>
            <input type="text" name="cedula" class="form-control" required maxlength="11" pattern="\d{11}">
        </div>
        <div class="mb-3">
            <label>Nombre de Usuario:</label>
            <input type="text" name="usuario" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>Contraseña:</label>
            <input type="password" name="clave" class="form-control" required>
        </div>
        <div class="mb-3">
            <label>Confirmar Contraseña:</label>
            <input type="password" name="confirmar" class="form-control" required>
        </div>
        <button type="submit" class="btn btn-primary">Registrarse</button>
        <a href="login.php" class="btn btn-secondary">Volver al Login</a>
    </form>
</div>
</body>
</html>
