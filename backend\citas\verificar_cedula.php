<?php
include('../config/database.php');

// Verificar si se recibió una cédula mediante el método GET
if (isset($_GET['cedula'])) {
    $cedula = $_GET['cedula'];

    try {
        // Consulta para verificar si la cédula existe
        $sql = "SELECT * FROM PACIENTES WHERE CEDULA = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$cedula]);

        if ($stmt->rowCount() > 0) {
            // La cédula existe en la base de datos
            $paciente = $stmt->fetch(PDO::FETCH_ASSOC);
            echo json_encode(['existe' => true, 'paciente' => $paciente]);
        } else {
            // La cédula no existe en la base de datos
            echo json_encode(['existe' => false]);
        }
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Error al verificar la cédula']);
    }
} else {
    echo json_encode(['error' => 'No se proporcionó una cédula']);
}
?>