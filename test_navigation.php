<?php
// Archivo de prueba para verificar navegación
echo "<h2>Test de Navegación</h2>";
echo "<p><strong>URL actual:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Servidor:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Protocolo:</strong> " . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "</p>";
echo "<p><strong>Directorio actual:</strong> " . __DIR__ . "</p>";

// Verificar si historia_menu.php existe
$historia_menu_path = __DIR__ . '/backend/historia/historia_menu.php';
echo "<p><strong>Archivo historia_menu.php existe:</strong> " . (file_exists($historia_menu_path) ? 'SÍ' : 'NO') . "</p>";
echo "<p><strong>Ruta completa:</strong> " . $historia_menu_path . "</p>";

// Enlaces de prueba
echo "<h3>Enlaces de Prueba:</h3>";
echo "<p><a href='backend/historia/historia_menu.php?CLAVEPAC=123&CEDULA=12345&SEXO=M'>Enlace directo a historia_menu.php</a></p>";
echo "<p><a href='backend/historia/buscar_paciente.php'>Enlace a buscar_paciente.php</a></p>";
?>

<script>
console.log('URL actual:', window.location.href);
console.log('Pathname:', window.location.pathname);
console.log('Host:', window.location.host);
console.log('Protocol:', window.location.protocol);

// Función de prueba para simular la navegación
function testNavigation() {
    const currentUrl = window.location.href;
    const currentPath = window.location.pathname;
    
    console.log('=== TEST DE NAVEGACIÓN ===');
    console.log('Current URL:', currentUrl);
    console.log('Current Path:', currentPath);
    
    let targetUrl;
    
    if (currentUrl.includes('backend/historia')) {
        targetUrl = `historia_menu.php?CLAVEPAC=123&CEDULA=12345&SEXO=M`;
    } else {
        const baseUrl = currentUrl.split('/').slice(0, -1).join('/');
        targetUrl = `${baseUrl}/backend/historia/historia_menu.php?CLAVEPAC=123&CEDULA=12345&SEXO=M`;
    }
    
    console.log('Target URL:', targetUrl);
    alert('Target URL: ' + targetUrl);
}
</script>

<button onclick="testNavigation()">Probar Navegación</button>
