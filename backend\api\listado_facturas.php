require_once __DIR__ . '/../config/database.php';  // $pdo ya apunta al tenant actual

// 1) Obtener lista de consultorios activos
$stmt = $masterPdo->query("SELECT Subdominio, NombreConsultorio FROM Consultorios WHERE Estado='Activo'");
$consultorios = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 2) Obtener subdominio seleccionado
$subdominio = $_GET['subdominio'] ?? '';
$pdo = null; $empresa = null; $error = null;
if ($subdominio) {
    // Recuperar cadena conexión tenant
    $stmt = $masterPdo->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio=?");
    $stmt->execute([$subdominio]);
    $cfg = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($cfg) {
        parse_str(str_replace(['mysql:', ';'], ['', '&'], $cfg['CadenaConexionDB']), $params);
        $dsn = "mysql:host={$params['host']};dbname={$params['dbname']};charset=utf8";
        try {
            $pdo = new PDO($dsn, $params['user'], $params['password'], [
                PDO::ATTR_ERRMODE=>PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE=>PDO::FETCH_ASSOC
            ]);
            // Cargar datos consultorio y facturas
            $empresaStmt = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
            $empresa = $empresaStmt->fetch();
            $facturasStmt = $pdo->query("SELECT CLAVE, NUMERO_FACTURA, FECHA_FACTURA, FECHA_VENCIMIENTO, PRECIO, MONEDA, ESTATUS
                FROM FACTURAS_SOFTWARE ORDER BY FECHA_FACTURA DESC");
            $facturas = $facturasStmt->fetchAll();
        } catch (PDOException $e) {
            $error = "Error conexión tenant: " . htmlspecialchars($e->getMessage());
        }
    } else {
        $error = "Subdominio inválido";
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Histórico de Facturas</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
  <h2>📃 Histórico de Facturas</h2>
  <?php if ($error): ?>
    <div class="alert alert-danger"><?= $error ?></div>
  <?php endif; ?>
  <!-- Seleccionar consultorio -->
  <form method="GET" class="mb-4">
    <label>Consultorio:</label>
    <select name="subdominio" onchange="this.form.submit()" class="form-select w-auto d-inline-block ms-2">
      <option value="">-- Elegir --</option>
      <?php foreach ($consultorios as $c): ?>
        <option value="<?= htmlspecialchars($c['Subdominio']) ?>" <?= $subdominio===$c['Subdominio']?'selected':'' ?>>
          <?= htmlspecialchars($c['NombreConsultorio']) ?>
        </option>
      <?php endforeach; ?>
    </select>
    <?php if ($subdominio): ?>
      <a href="crear_factura.php?subdominio=<?= urlencode($subdominio) ?>" class="btn btn-success ms-3">➕ Nueva Factura</a>
    <?php endif; ?>
  </form>

  <?php if ($pdo && $empresa): ?>
    <div class="card mb-4">
      <div class="card-header bg-info text-white">Datos del Consultorio</div>
      <div class="card-body">
        <p><strong>Nombre:</strong> <?= htmlspecialchars($empresa['NombreConsultorio']) ?></p>
        <p><strong>RNC:</strong> <?= htmlspecialchars($empresa['RNC']) ?></p>
      </div>
    </div>
    <?php if (empty($facturas)): ?>
      <p>No hay facturas registradas.</p>
    <?php else: ?>
      <table class="table table-striped">
        <thead>
          <tr>
            <th>#</th>
            <th>Fecha</th>
            <th>Vence</th>
            <th>Monto</th>
            <th>Estado</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($facturas as $f): ?>
            <tr>
              <td><?= htmlspecialchars($f['NUMERO_FACTURA']) ?></td>
              <td><?= htmlspecialchars($f['FECHA_FACTURA']) ?></td>
              <td><?= htmlspecialchars($f['FECHA_VENCIMIENTO']) ?></td>
              <td><?= htmlspecialchars($f['MONEDA']) ?> <?= number_format($f['PRECIO'],2) ?></td>
              <td><?= htmlspecialchars($f['ESTATUS']) ?></td>
              <td>
                <a href="imprimir_factura.php?id=<?= $f['CLAVE'] ?>" target="_blank" class="btn btn-sm btn-primary">🖨️ Imprimir</a>
                <a href="editar_factura.php?id=<?= $f['CLAVE'] ?>" class="btn btn-sm btn-secondary ms-1">✏️ Editar</a>
              </td>
            </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    <?php endif; ?>
  <?php endif; ?>
</div>
</body>
</html>