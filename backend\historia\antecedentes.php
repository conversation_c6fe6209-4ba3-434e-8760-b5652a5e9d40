<?php
// antecedentes.php
$pdo = require_once __DIR__ . '/../config/database.php';
$mensaje = "";
$CLAVEPAC = $_GET['CLAVEPAC'] ?? null;
$CEDULA = $_GET['CEDULA'] ?? '';
$datos = [];

if (!$CLAVEPAC) {
    die("Paciente no especificado.");
}

// --- Lógica de Robustez: Resolver CLAVEPAC usando CEDULA ---
// Siempre buscar la CLAVEPAC correcta del paciente en la tabla PACIENTES
// usando la CEDULA enviada, para asegurar la integridad referencial.
$clav_pac_real = $CLAVEPAC; // Valor por defecto si no se encuentra o no hay CEDULA

if (!empty($CEDULA)) {
    try {
        $stmtPaciente = $pdo->prepare("SELECT CLAVE FROM PACIENTES WHERE CEDULA = :cedula");
        $stmtPaciente->execute(['cedula' => $CEDULA]);
        $rowPaciente = $stmtPaciente->fetch(PDO::FETCH_ASSOC);

        if ($rowPaciente) {
            $clav_pac_real = $rowPaciente['CLAVE']; // Asigna la CLAVEPAC encontrada en remoto
        } else {
            // Si el paciente no se encuentra, CLAVEPAC_real será el CLAVEPAC original (del GET)
            // o se podría manejar un error más explícito.
            $mensaje = "⚠️ Advertencia: Paciente con CÉDULA: " . htmlspecialchars($CEDULA) . " no encontrado en PACIENTES. Usando CLAVEPAC original.";
        }
    } catch (PDOException $e) {
        $mensaje = "❌ Error al buscar paciente en PACIENTES: " . $e->getMessage();
    }
}
// --- FIN Lógica de Robustez CLAVEPAC ---

// Verifica si existen antecedentes para el CLAVEPAC real
$stmt = $pdo->prepare("SELECT * FROM ANTECEDENTES WHERE CLAVEPAC = :CLAVEPAC LIMIT 1");
$stmt->execute(['CLAVEPAC' => $clav_pac_real]);
$datos = $stmt->fetch(PDO::FETCH_ASSOC); // Usar FETCH_ASSOC para consistencia

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $campos = [
        'FAMILIARES', 'PERSONALES', 'OTROS', 'PATOLOGICOFAMILIARES', 'HABITOS',
        'PATOLOGICOPERSONALES', 'VACUNAS', 'ALCOHOL', 'CANTALCOHOL', 'FUMA', 'CANTFUMA',
        'DROGA', 'CANTDROGA', 'DESDEALCOHOL', 'DESDEFUMA', 'DESDEDROGA', 'TIPODROGA',
        'ALTORIESGO', 'CAUSAALTORIESGO'
    ];

    $valores = [];
    foreach ($campos as $campo) {
        $valores[$campo] = $_POST[$campo] ?? null;
    }

    // Asegurarse de que CLAVEPAC y CEDULA están en $valores ANTES de construir la consulta
    $valores['CLAVEPAC'] = $clav_pac_real; // Siempre usar la CLAVEPAC real
    $valores['CEDULA'] = $CEDULA; // La CEDULA se mantiene desde el parámetro de GET

    if ($datos) {
        // UPDATE
        $sets = [];
        $updateValues = [];
        foreach ($campos as $campo) {
            $sets[] = "$campo = :$campo";
            $updateValues[$campo] = $valores[$campo];
        }
        $updateValues['CLAVEPAC_WHERE'] = $clav_pac_real; // Parámetro separado para WHERE
        $sql = "UPDATE ANTECEDENTES SET " . implode(", ", $sets) . ", SINCRONIZADO = 0 WHERE CLAVEPAC = :CLAVEPAC_WHERE";
        $valores = $updateValues; // Usar solo los valores necesarios para UPDATE
    } else {
        // INSERT
        // array_keys($valores) ahora incluye CLAVEPAC y CEDULA
        $columnas = implode(", ", array_keys($valores));
        $placeholders = implode(", ", array_map(function($c) {
            return ":$c";
        }, array_keys($valores)));
        $sql = "INSERT INTO ANTECEDENTES ($columnas, SINCRONIZADO) VALUES ($placeholders, 0)";
    }

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);
        $mensaje = $datos ? "✅ Antecedentes actualizados correctamente." : "✅ Antecedentes insertados correctamente.";
        // Vuelve a cargar los datos para asegurar que se muestren los valores actuales (incluyendo fecha_cap, etc.)
        $stmt = $pdo->prepare("SELECT * FROM ANTECEDENTES WHERE CLAVEPAC = :CLAVEPAC LIMIT 1");
        $stmt->execute(['CLAVEPAC' => $clav_pac_real]);
        $datos = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar antecedentes: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Antecedentes</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .form-label {
            font-weight: bold;
        }
        .card-header h5 {
            margin-bottom: 0;
        }
        /* Espacio adicional entre grupos para mejor legibilidad */
        .card:not(:last-of-type) {
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body class="bg-light">
<div class="container mt-4">
    <h3 class="mb-4 text-center">Antecedentes del Paciente: <?= htmlspecialchars($CEDULA) ?> (ID: <?= htmlspecialchars($CLAVEPAC) ?>)</h3>
    <?php if ($mensaje): ?><div class="alert alert-info text-center"><?= htmlspecialchars($mensaje) ?></div><?php endif; ?>

    <form method="post" novalidate>
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Información General de Antecedentes</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="FAMILIARES" class="form-label">Antecedentes Familiares:</label>
                        <textarea name="FAMILIARES" id="FAMILIARES" class="form-control" rows="4" maxlength="600"><?= htmlspecialchars($datos['FAMILIARES'] ?? '') ?></textarea>
                    </div>
                    <div class="col-md-6">
                        <label for="PERSONALES" class="form-label">Antecedentes Personales:</label>
                        <textarea name="PERSONALES" id="PERSONALES" class="form-control" rows="4" maxlength="600"><?= htmlspecialchars($datos['PERSONALES'] ?? '') ?></textarea>
                    </div>
                    <div class="col-12">
                        <label for="OTROS" class="form-label">Otros Antecedentes Relevantes:</label>
                        <input type="text" name="OTROS" id="OTROS" class="form-control" maxlength="100" value="<?= htmlspecialchars($datos['OTROS'] ?? '') ?>">
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Historial Patológico y Hábitos</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="PATOLOGICOFAMILIARES" class="form-label">Patológicos Familiares:</label>
                        <textarea name="PATOLOGICOFAMILIARES" id="PATOLOGICOFAMILIARES" class="form-control" rows="4" maxlength="600"><?= htmlspecialchars($datos['PATOLOGICOFAMILIARES'] ?? '') ?></textarea>
                    </div>
                    <div class="col-md-6">
                        <label for="PATOLOGICOPERSONALES" class="form-label">Patológicos Personales:</label>
                        <textarea name="PATOLOGICOPERSONALES" id="PATOLOGICOPERSONALES" class="form-control" rows="4" maxlength="600"><?= htmlspecialchars($datos['PATOLOGICOPERSONALES'] ?? '') ?></textarea>
                    </div>
                    <div class="col-12">
                        <label for="VACUNAS" class="form-label">Historial de Vacunas:</label>
                        <textarea name="VACUNAS" id="VACUNAS" class="form-control" rows="3" maxlength="600"><?= htmlspecialchars($datos['VACUNAS'] ?? '') ?></textarea>
                    </div>

                    <hr class="my-4"> <div class="col-md-4">
                        <label for="ALCOHOL" class="form-label">¿Consume Alcohol?</label>
                        <select name="ALCOHOL" id="ALCOHOL" class="form-select">
                            <option value="">-- Seleccionar --</option>
                            <option value="S" <?= ($datos['ALCOHOL'] ?? '') === 'S' ? 'selected' : '' ?>>Sí</option>
                            <option value="N" <?= ($datos['ALCOHOL'] ?? '') === 'N' ? 'selected' : '' ?>>No</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="CANTALCOHOL" class="form-label">Cantidad de Alcohol:</label>
                        <input type="text" name="CANTALCOHOL" id="CANTALCOHOL" class="form-control" maxlength="20" value="<?= htmlspecialchars($datos['CANTALCOHOL'] ?? '') ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="DESDEALCOHOL" class="form-label">Desde cuándo bebe:</label>
                        <input type="text" name="DESDEALCOHOL" id="DESDEALCOHOL" class="form-control" maxlength="20" value="<?= htmlspecialchars($datos['DESDEALCOHOL'] ?? '') ?>">
                    </div>

                    <div class="col-md-4">
                        <label for="FUMA" class="form-label">¿Fuma?</label>
                        <select name="FUMA" id="FUMA" class="form-select">
                            <option value="">-- Seleccionar --</option>
                            <option value="S" <?= ($datos['FUMA'] ?? '') === 'S' ? 'selected' : '' ?>>Sí</option>
                            <option value="N" <?= ($datos['FUMA'] ?? '') === 'N' ? 'selected' : '' ?>>No</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="CANTFUMA" class="form-label">Cantidad de Cigarros:</label>
                        <input type="text" name="CANTFUMA" id="CANTFUMA" class="form-control" maxlength="20" value="<?= htmlspecialchars($datos['CANTFUMA'] ?? '') ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="DESDEFUMA" class="form-label">Desde cuándo fuma:</label>
                        <input type="text" name="DESDEFUMA" id="DESDEFUMA" class="form-control" maxlength="20" value="<?= htmlspecialchars($datos['DESDEFUMA'] ?? '') ?>">
                    </div>

                    <div class="col-md-4">
                        <label for="DROGA" class="form-label">¿Usa Drogas?</label>
                        <select name="DROGA" id="DROGA" class="form-select">
                            <option value="">-- Seleccionar --</option>
                            <option value="S" <?= ($datos['DROGA'] ?? '') === 'S' ? 'selected' : '' ?>>Sí</option>
                            <option value="N" <?= ($datos['DROGA'] ?? '') === 'N' ? 'selected' : '' ?>>No</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="TIPODROGA" class="form-label">Tipo de Droga:</label>
                        <input type="text" name="TIPODROGA" id="TIPODROGA" class="form-control" maxlength="20" value="<?= htmlspecialchars($datos['TIPODROGA'] ?? '') ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="CANTDROGA" class="form-label">Cantidad de Droga:</label>
                        <input type="text" name="CANTDROGA" id="CANTDROGA" class="form-control" maxlength="20" value="<?= htmlspecialchars($datos['CANTDROGA'] ?? '') ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="DESDEDROGA" class="form-label">Desde cuándo usa drogas:</label>
                        <input type="text" name="DESDEDROGA" id="DESDEDROGA" class="form-control" maxlength="20" value="<?= htmlspecialchars($datos['DESDEDROGA'] ?? '') ?>">
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Evaluación de Alto Riesgo</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="ALTORIESGO" class="form-label">¿Alto Riesgo?</label>
                        <select name="ALTORIESGO" id="ALTORIESGO" class="form-select">
                            <option value="">-- Seleccionar --</option>
                            <option value="S" <?= ($datos['ALTORIESGO'] ?? '') === 'S' ? 'selected' : '' ?>>Sí</option>
                            <option value="N" <?= ($datos['ALTORIESGO'] ?? '') === 'N' ? 'selected' : '' ?>>No</option>
                        </select>
                    </div>
                    <div class="col-md-8">
                        <label for="CAUSAALTORIESGO" class="form-label">Causa del Alto Riesgo:</label>
                        <input type="text" name="CAUSAALTORIESGO" id="CAUSAALTORIESGO" class="form-control" maxlength="255" value="<?= htmlspecialchars($datos['CAUSAALTORIESGO'] ?? '') ?>">
                    </div>
                </div>
            </div>
        </div>

        <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
            <button class="btn btn-primary btn-lg" type="submit">💾 Guardar Antecedentes</button>
            <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg">Volver</a>
        </div>
    </form>
</div>
</body>
</html>