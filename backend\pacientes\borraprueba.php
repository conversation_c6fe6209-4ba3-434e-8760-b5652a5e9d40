<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Formulario Pacientes - Proyecto Completo</title>
  <!-- Bootstrap 4/5 -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">

  <style>
    .foto-paciente {
      width: 100%;
      height: 200px;
      background-color: #f0f0f0;
      border: 1px solid #ccc;
      display: flex;
      align-items: center;
      justify-content: center;
      font-style: italic;
      color: #999;
    }
    .botones-accion .btn {
      width: 100%;
      margin-bottom: 5px;
    }
  </style>
</head>
<body>

<div class="container mt-4">
  <!-- Pestañas Principales -->
  <ul class="nav nav-tabs" id="mainTab" role="tablist">
    <li class="nav-item">
      <a class="nav-link active" id="datos-tab" data-toggle="tab" href="#datos" role="tab" aria-controls="datos" aria-selected="true">
        DATOS GENERALES PACIENTE
      </a>
    </li>
    <li class="nav-item">
      <a class="nav-link" id="buscar-tab" data-toggle="tab" href="#buscar" role="tab" aria-controls="buscar" aria-selected="false">
        BÚSQUEDA DE PACIENTES
      </a>
    </li>
  </ul>

  <div class="tab-content border p-3" id="mainTabContent">
    <!-- Pestaña Principal: DATOS GENERALES PACIENTE -->
    <div class="tab-pane fade show active" id="datos" role="tabpanel" aria-labelledby="datos-tab">
      <!-- Fila superior: datos personales y foto/botones -->
      <div class="row">
        <!-- Columna Izquierda: Campos principales -->
        <div class="col-md-8">
          <h5 class="mb-3">Datos Personales del Paciente</h5>
          <form id="formPaciente">
            <div class="form-row">
              <div class="form-group col-md-3">
                <label for="CEDULA">Cédula:</label>
                <input type="text" class="form-control" id="CEDULA" name="CEDULA" maxlength="11">
              </div>
              <div class="form-group col-md-3">
                <label for="NOMBRES">Nombres:</label>
                <input type="text" class="form-control" id="NOMBRES" name="NOMBRES" maxlength="35">
              </div>
              <div class="form-group col-md-3">
                <label for="APELLIDOS">Apellidos:</label>
                <input type="text" class="form-control" id="APELLIDOS" name="APELLIDOS" maxlength="35">
              </div>
              <div class="form-group col-md-3">
                <label for="EDAD">Edad Actual:</label>
                <input type="number" class="form-control" id="EDAD" name="EDAD" min="0" max="120">
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-3">
                <label for="ESTADOCIVIL">Estado Civil:</label>
                <select class="form-control" id="ESTADOCIVIL" name="ESTADOCIVIL">
                  <option value="">Seleccionar</option>
                  <option value="Soltero(a)">Soltero(a)</option>
                  <option value="Casado(a)">Casado(a)</option>
                  <option value="Viudo(a)">Viudo(a)</option>
                  <option value="Divorciado(a)">Divorciado(a)</option>
                  <option value="Separado(a)">Separado(a)</option>
                  <option value="Unión Libre">Unión Libre</option>
                </select>
              </div>
              <div class="form-group col-md-3">
                <label for="FECHANAC">Fecha Nac.:</label>
                <input type="date" class="form-control" id="FECHANAC" name="FECHANAC">
              </div>
              <div class="form-group col-md-3">
                <label for="NACIONALIDAD">Nacionalidad:</label>
                <input type="text" class="form-control" id="NACIONALIDAD" name="NACIONALIDAD" maxlength="35">
              </div>
              <div class="form-group col-md-3">
                <label for="PESOHABITUAL">Peso Hab. (kg):</label>
                <input type="number" step="0.01" class="form-control" id="PESOHABITUAL" name="PESOHABITUAL">
              </div>
            </div>
            
            <div class="form-row">
              <div class="form-group col-md-3">
                <label for="SEXO">Sexo:</label>
                <select class="form-control" id="SEXO" name="SEXO">
                  <option value="">Seleccionar</option>
                  <option value="M">Masculino</option>
                  <option value="F">Femenino</option>
                  <option value="I">Indefinido</option>
                </select>
              </div>
              <div class="form-group col-md-3">
                <label for="FECHAINGRESO">Fecha de Ingreso:</label>
                <input type="date" class="form-control" id="FECHAINGRESO" name="FECHAINGRESO">
              </div>
              <div class="form-group col-md-3">
                <label for="RH">Tipo de Sangre (RH):</label>
                <select class="form-control" id="RH" name="RH">
                  <option value="">Seleccionar</option>
                  <option value="O+">O+</option>
                  <option value="O-">O-</option>
                  <option value="A+">A+</option>
                  <option value="A-">A-</option>
                  <option value="B+">B+</option>
                  <option value="B-">B-</option>
                  <option value="AB+">AB+</option>
                  <option value="AB-">AB-</option>
                </select>
              </div>
              <div class="form-group col-md-3">
                <label for="RELIGION">Religión:</label>
                <select class="form-control" id="RELIGION" name="RELIGION">
                  <option value="">Seleccionar</option>
                  <option value="Católico">Católico</option>
                  <option value="Cristiano Evangelice">Cristiano Evangelice</option>
                  <option value="Testigo">Testigo</option>
                  <option value="Ninguna">Ninguna</option>
                  <option value="Otra">Otra</option>
                </select>
              </div>
            </div>
            
            <!-- Nivel Escolar y Procedencia en la misma línea -->
            <div class="form-row">
              <div class="form-group col-md-6">
                <label>Nivel Escolar:</label><br>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="primario" value="0">
                  <label class="form-check-label" for="primario">Primario</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="secundario" value="1">
                  <label class="form-check-label" for="secundario">Secundario</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="universitario" value="2">
                  <label class="form-check-label" for="universitario">Universitario</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="ninguno" value="3">
                  <label class="form-check-label" for="ninguno">Ninguno</label>
                </div>
              </div>
              <div class="form-group col-md-6">
                <label>Procedencia:</label><br>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="PROCEDENCIA" id="urbano" value="1">
                  <label class="form-check-label" for="urbano">Urbano</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" name="PROCEDENCIA" id="rural" value="0">
                  <label class="form-check-label" for="rural">Rural</label>
                </div>
              </div>
            </div>
          </form>
        </div>

        <!-- Columna Derecha: Foto + Botones -->
        <div class="col-md-4">
          <div class="foto-paciente mb-3">
            FOTO PACIENTE
          </div>
          <div class="botones-accion">
            <button class="btn btn-success">Insertar</button>
            <button class="btn btn-primary">Modificar</button>
            <button class="btn btn-info">Guardar</button>
            <button class="btn btn-warning">Eliminar</button>
            <button class="btn btn-secondary">Cancelar</button>
            <button class="btn btn-dark">Imprimir</button>
          </div>
        </div>
      </div><!-- Fin row principal -->

      <!-- Subpestañas (Domicilio, Contacto, Póliza, Responsable, Otros) -->
      <ul class="nav nav-tabs mt-4" id="subTab" role="tablist">
        <li class="nav-item">
          <a class="nav-link active" id="domicilio-subtab" data-toggle="tab" href="#domicilio" role="tab" aria-controls="domicilio" aria-selected="true">
            Domicilio
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="contacto-subtab" data-toggle="tab" href="#contacto" role="tab" aria-controls="contacto" aria-selected="false">
            Contacto
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="poliza-subtab" data-toggle="tab" href="#poliza" role="tab" aria-controls="poliza" aria-selected="false">
            Póliza
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="responsable-subtab" data-toggle="tab" href="#responsable" role="tab" aria-controls="responsable" aria-selected="false">
            Responsable
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link" id="otros-subtab" data-toggle="tab" href="#otros" role="tab" aria-controls="otros" aria-selected="false">
            Otros
          </a>
        </li>
      </ul>

      <div class="tab-content border p-3" id="subTabContent">
        <!-- Domicilio -->
        <div class="tab-pane fade show active" id="domicilio" role="tabpanel" aria-labelledby="domicilio-subtab">
          <form>
            <div class="form-row">
              <div class="form-group col-md-6">
                <label for="CALLE">Calle</label>
                <input type="text" class="form-control" id="CALLE">
              </div>
              <div class="form-group col-md-6">
                <label for="PROVINCIA">Provincia/Ciudad</label>
                <input type="text" class="form-control" id="PROVINCIA">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group col-md-6">
                <label for="MUNICIPIO">Municipio</label>
                <input type="text" class="form-control" id="MUNICIPIO">
              </div>
              <div class="form-group col-md-6">
                <label for="SECTOR">Sector/Barrio</label>
                <input type="text" class="form-control" id="SECTOR">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group col-md-6">
                <label for="PAIS">País</label>
                <input type="text" class="form-control" id="PAIS" value="República Dominicana">
              </div>
              <div class="form-group col-md-6">
                <label for="REFERENCIA">Referencias para llegar</label>
                <input type="text" class="form-control" id="REFERENCIA">
              </div>
            </div>
          </form>
        </div>

        <!-- Contacto -->
        <div class="tab-pane fade" id="contacto" role="tabpanel" aria-labelledby="contacto-subtab">
          <form>
            <div class="form-row">
              <div class="form-group col-md-4">
                <label for="TELCASA">Teléfono Casa</label>
                <input type="text" class="form-control" id="TELCASA">
              </div>
              <div class="form-group col-md-4">
                <label for="TELEFONOMOVIL">Teléfono Móvil</label>
                <input type="text" class="form-control" id="TELEFONOMOVIL">
              </div>
              <div class="form-group col-md-4">
                <label for="TELTRABAJO">Teléfono Trabajo</label>
                <input type="text" class="form-control" id="TELTRABAJO">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group col-md-4">
                <label for="FAX">Fax</label>
                <input type="text" class="form-control" id="FAX">
              </div>
              <div class="form-group col-md-8">
                <label for="CORREO">Correo Electrónico</label>
                <input type="email" class="form-control" id="CORREO">
              </div>
            </div>
          </form>
        </div>

        <!-- Póliza -->
        <div class="tab-pane fade" id="poliza" role="tabpanel" aria-labelledby="poliza-subtab">
          <form>
            <div class="form-row">
              <div class="form-group col-md-4">
                <label for="ARS">ARS</label>
                <select class="form-control" id="ARS">
                  <option value="">No Asegurado</option>
                  <option value="1">ARS 1</option>
                  <option value="2">ARS 2</option>
                </select>
              </div>
              <div class="form-group col-md-4">
                <label for="PLAN">Plan</label>
                <input type="text" class="form-control" id="PLAN">
              </div>
              <div class="form-group col-md-4">
                <label for="AFILIADO">No. Afiliado</label>
                <input type="text" class="form-control" id="AFILIADO">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group col-md-4">
                <label for="VIGENCIA">Vigencia</label>
                <input type="date" class="form-control" id="VIGENCIA">
              </div>
              <div class="form-group col-md-4">
                <label for="NSS">No. Seg. Social</label>
                <input type="text" class="form-control" id="NSS">
              </div>
              <div class="form-group col-md-4">
                <label for="CATEGORIA">Categoría</label>
                <input type="text" class="form-control" id="CATEGORIA">
              </div>
            </div>
          </form>
        </div>

        <!-- Responsable -->
        <div class="tab-pane fade" id="responsable" role="tabpanel" aria-labelledby="responsable-subtab">
          <form>
            <h6>Datos Responsable</h6>
            <div class="form-row">
              <div class="form-group col-md-6">
                <label for="NOMBRERESP">Nombres y Apellidos Responsable de Pago</label>
                <input type="text" class="form-control" id="NOMBRERESP">
              </div>
              <div class="form-group col-md-6">
                <label for="CEDULARESP">No. Cédula Responsable</label>
                <input type="text" class="form-control" id="CEDULARESP">
              </div>
            </div>
            <div class="form-row">
              <div class="form-group col-md-6">
                <label for="DIRRESP">Dirección Responsable</label>
                <input type="text" class="form-control" id="DIRRESP">
              </div>
              <div class="form-group col-md-6">
                <label for="TELRESP">Teléfono Responsable</label>
                <input type="text" class="form-control" id="TELRESP">
              </div>
            </div>
            
            <h6>Familiar Más Próximo</h6>
            <div class="form-row">
              <div class="form-group col-md-4">
                <label for="FAMILIARPROX">Familiar Más Próximo</label>
                <input type="text" class="form-control" id="FAMILIARPROX">
              </div>
              <div class="form-group col-md-4">
                <label for="DIRFAMILIAR">Dirección Familiar Más Próximo</label>
                <input type="text" class="form-control" id="DIRFAMILIAR">
              </div>
              <div class="form-group col-md-4">
                <label for="TELFAMILIAR">Teléfono Familiar Más Próximo</label>
                <input type="text" class="form-control" id="TELFAMILIAR">
              </div>
            </div>

            <div class="form-row">
              <div class="form-group col-md-6">
                <label for="REMITIDO">Remitido Por</label>
                <input type="text" class="form-control" id="REMITIDO">
              </div>
              <div class="form-group col-md-6">
                <label for="OBSERVACIONESRESP">Observaciones</label>
                <input type="text" class="form-control" id="OBSERVACIONESRESP">
              </div>
            </div>
          </form>
        </div>

        <!-- Otros -->
        <div class="tab-pane fade" id="otros" role="tabpanel" aria-labelledby="otros-subtab">
          <form>
            <div class="form-group">
              <label for="RECORDCLINICA">Record en Clínica</label>
              <input type="text" class="form-control" id="RECORDCLINICA">
            </div>
          </form>
        </div>
      </div><!-- Fin subTabContent -->
    </div><!-- Fin Pestaña DATOS GENERALES PACIENTE -->

    <!-- Pestaña Principal: BÚSQUEDA DE PACIENTES -->
    <div class="tab-pane fade" id="buscar" role="tabpanel" aria-labelledby="buscar-tab">
      <div class="row">
        <div class="col-md-4">
          <h5>Buscar Paciente</h5>
          <div class="form-group">
            <label>Cédula</label>
            <input type="text" class="form-control" placeholder="Cédula...">
          </div>
          <div class="form-group">
            <label>Nombres</label>
            <input type="text" class="form-control" placeholder="Nombres...">
          </div>
          <div class="form-group">
            <label>Apellidos</label>
            <input type="text" class="form-control" placeholder="Apellidos...">
          </div>
          <div class="form-check mb-2">
            <input class="form-check-input" type="checkbox" id="pacInternos">
            <label class="form-check-label" for="pacInternos">Pac. Internos</label>
          </div>
          <button class="btn btn-primary">Buscar</button>
          <button class="btn btn-secondary">Limpiar</button>
          <button class="btn btn-danger">Salir</button>
        </div>

        <div class="col-md-8">
          <h5>Resultados</h5>
          <div class="border" style="height: 200px;">
            <!-- Aquí una tabla o lista con los resultados de búsqueda -->
          </div>
        </div>
      </div>
    </div><!-- Fin Pestaña BÚSQUEDA DE PACIENTES -->

  </div><!-- Fin mainTabContent -->
</div><!-- Fin container -->

<!-- Scripts Bootstrap -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

</body>
</html>
