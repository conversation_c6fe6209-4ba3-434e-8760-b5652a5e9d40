<?php
// eliminar_factura.php
header('Content-Type: application/json');

require_once '../config/database.php';
$pdo = include '../config/database.php';

$clave = $_GET['clave'] ?? null;

if (!$clave) {
    echo json_encode(["status" => "error", "message" => "Falta el parámetro clave"]);
    exit;
}

try {
    // Verifica si existe primero
    $stmt = $pdo->prepare("SELECT * FROM FACTURAS WHERE CLAVE = ?");
    $stmt->execute([$clave]);
    if (!$stmt->fetch()) {
        echo json_encode(["status" => "error", "message" => "Factura no encontrada"]);
        exit;
    }

    // Elimina
    $del = $pdo->prepare("DELETE FROM FACTURAS WHERE CLAVE = ?");
    $del->execute([$clave]);

    echo json_encode(["status" => "success"]);
} catch (PDOException $e) {
    echo json_encode(["status" => "error", "message" => $e->getMessage()]);
}
