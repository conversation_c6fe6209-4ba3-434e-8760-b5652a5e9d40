<?php
/**
 * Script para crear la tabla SUGERENCIAS_REPORTES en la base de datos correcta
 */

session_start();
require_once __DIR__ . '/../config/database.php';

echo "<h1>🔧 Crear Tabla de Sugerencias</h1>";

try {
    // Mostrar información de la base de datos
    $stmt = $pdo->query("SELECT DATABASE() as db_name");
    $db_info = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p><strong>Base de datos actual:</strong> " . $db_info['db_name'] . "</p>";
    
    // Verificar si la tabla ya existe
    $stmt = $pdo->prepare("SHOW TABLES LIKE 'SUGERENCIAS_REPORTES'");
    $stmt->execute();
    $table_exists = $stmt->rowCount() > 0;
    
    if ($table_exists) {
        echo "<p style='color: orange;'>⚠️ La tabla SUGERENCIAS_REPORTES ya existe</p>";
        
        // Mostrar estructura actual
        $stmt = $pdo->query("DESCRIBE SUGERENCIAS_REPORTES");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>📊 Estructura actual:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>" . $col['Field'] . "</td>";
            echo "<td>" . $col['Type'] . "</td>";
            echo "<td>" . $col['Null'] . "</td>";
            echo "<td>" . $col['Key'] . "</td>";
            echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Contar registros
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM SUGERENCIAS_REPORTES");
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Registros existentes:</strong> " . $count['total'] . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ La tabla SUGERENCIAS_REPORTES NO existe</p>";
        
        // Crear la tabla
        $createTableSQL = "
            CREATE TABLE SUGERENCIAS_REPORTES (
                CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                SUGERENCIA TEXT NOT NULL,
                USUARIO VARCHAR(100) DEFAULT 'Anónimo',
                IP_ADDRESS VARCHAR(45),
                USER_AGENT TEXT,
                FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ESTADO ENUM('PENDIENTE', 'REVISADA', 'IMPLEMENTADA', 'RECHAZADA') DEFAULT 'PENDIENTE',
                PRIORIDAD ENUM('BAJA', 'MEDIA', 'ALTA') DEFAULT 'MEDIA',
                COMENTARIOS_ADMIN TEXT,
                FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createTableSQL);
        echo "<p style='color: green;'>✅ Tabla SUGERENCIAS_REPORTES creada exitosamente</p>";
        
        // Verificar creación
        $stmt = $pdo->query("DESCRIBE SUGERENCIAS_REPORTES");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>📊 Estructura creada:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>" . $col['Field'] . "</td>";
            echo "<td>" . $col['Type'] . "</td>";
            echo "<td>" . $col['Null'] . "</td>";
            echo "<td>" . $col['Key'] . "</td>";
            echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Insertar una sugerencia de prueba si no hay registros
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM SUGERENCIAS_REPORTES");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($count['total'] == 0) {
        echo "<h3>➕ Insertando sugerencia de prueba...</h3>";
        
        $stmt = $pdo->prepare("
            INSERT INTO SUGERENCIAS_REPORTES 
            (SUGERENCIA, USUARIO, IP_ADDRESS, USER_AGENT) 
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Sugerencia de prueba inicial - Tabla creada el ' . date('Y-m-d H:i:s'),
            $_SESSION['usuario'] ?? 'Sistema',
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'Script de creación de tabla'
        ]);
        
        echo "<p style='color: green;'>✅ Sugerencia de prueba insertada (ID: " . $pdo->lastInsertId() . ")</p>";
    }
    
    echo "<h3>🔗 Enlaces de prueba:</h3>";
    echo "<ul>";
    echo "<li><a href='admin_sugerencias.php'>👨‍💼 Ir al Panel de Admin</a></li>";
    echo "<li><a href='personalizados_reportes.php'>📄 Ir a Reportes Personalizados</a></li>";
    echo "<li><a href='test_sugerencias.php'>🧪 Ir al Test de Sugerencias</a></li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p><strong>Código de error:</strong> " . $e->getCode() . "</p>";
    echo "<p><strong>Información adicional:</strong> Verifica que tengas permisos para crear tablas en la base de datos.</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error general: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><small>🕒 Ejecutado el: " . date('Y-m-d H:i:s') . "</small></p>";
?>
