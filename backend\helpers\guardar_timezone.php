<?php
session_start(); // Iniciar sesión para almacenar la zona horaria

// Leer los datos enviados por el usuario
$data = json_decode(file_get_contents("php://input"), true);

if (isset($data['timezone'])) {
    $_SESSION['user_timezone'] = $data['timezone']; // Guardar zona horaria en sesión
    echo json_encode(["status" => "success", "timezone" => $_SESSION['user_timezone']]);
} else {
    echo json_encode(["status" => "error", "message" => "No se recibió la zona horaria"]);
}
?>
