<?php

// secretaria_panel.php
session_start();
header("Cache-Control: no-cache, must-revalidate");
header("Expires: Sat, 1 Jan 2000 00:00:00 GMT");
header("Pragma: no-cache");

header('Content-Type: text/html; charset=UTF-8');

// Incluir la conexión a la base de datos si es necesario para obtener datos adicionales
// require_once __DIR__ . '/backend/config/database.php';


// Mostrar errores solo en desarrollo
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// --- Control de Acceso y Redirección ---
// Solo permite el acceso a usuarios con rol 'secretaria'
if (!isset($_SESSION['usuario']) || !isset($_SESSION['rol']) || trim($_SESSION['rol']) !== 'secretaria') {
    header('Location: no_autorizado.php'); // Asegúrate de que esta ruta sea correcta
    exit;
}

// Obtener el nombre del usuario (secretaria) de la sesión
$nombreSecretaria = $_SESSION['nombre_usuario'] ?? 'Secretaria'; // Asume que guardas el nombre en 'nombre_usuario'
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Secretaria - Mi Consultorio</title>

    <!-- Favicon médico -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
    <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Fuente Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Variables de color y fuentes para consistencia con el tema médico */
        :root {
            --primary-color: #285499; /* Azul más oscuro y profesional */
            --secondary-color: #4CAF50; /* Verde más vibrante para acciones de éxito */
            --accent-color: #FFC107; /* Amarillo para advertencias */
            --danger-color: #DC3545; /* Rojo estándar de Bootstrap */
            --light-bg: #f0f2f5; /* Fondo general más suave */
            --dark-text: #343a40; /* Texto oscuro */
            --gray-text: #6c757d; /* Texto gris */
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Sombra suave para tarjetas */
            --border-radius-lg: 0.75rem; /* Bordes redondeados */
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background-color: #ffffff !important; /* Fondo blanco para la barra de navegación */
            border-bottom: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            display: flex;
            align-items: center;
        }
        .navbar-brand .bi {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }

        .btn-logout {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .btn-logout:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-2px);
        }

        .main-content {
            flex-grow: 1; /* Permite que el contenido principal ocupe el espacio restante */
            padding: 2rem 0;
            display: flex;
            align-items: center; /* Centra verticalmente el contenido si hay espacio */
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeIn 0.8s ease-out;
        }
        .welcome-section h1 {
            font-size: 2.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        .welcome-section p {
            font-size: 1.15rem;
            color: var(--gray-text);
        }

        .feature-card {
            background-color: #ffffff;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow);
            padding: 2.5rem;
            margin-bottom: 1.5rem; /* Espacio entre tarjetas en móvil */
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
            height: 100%; /* Asegura que las tarjetas tengan la misma altura en la fila */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
        .feature-card .icon-wrapper {
            font-size: 4.5rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            display: block;
            line-height: 1; /* Evita espacio extra en el icono */
        }
        .feature-card h5 {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 1rem;
        }
        .feature-card p {
            color: var(--gray-text);
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            flex-grow: 1; /* Permite que el párrafo ocupe espacio y empuje el botón hacia abajo */
        }
        .feature-card .btn-feature {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            padding: 0.8rem 1.8rem;
            font-size: 1.05rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex; /* Para alinear icono y texto */
            align-items: center;
            gap: 0.75rem;
        }
        .btn-feature:hover {
            background-color: #204176;
            border-color: #1a3c6d;
            transform: translateY(-2px);
        }
        /* Estilo para el contenedor de dos botones */
        .btn-group-vertical-custom {
            display: flex;
            flex-direction: column;
            gap: 1rem; /* Espacio entre los botones */
            width: 100%; /* Ocupa el ancho completo de la tarjeta */
        }
        .btn-group-vertical-custom .btn {
            width: 100%; /* Asegura que los botones ocupen el ancho completo */
        }


        /* Animaciones */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .welcome-section h1 {
                font-size: 2.2rem;
            }
            .welcome-section p {
                font-size: 1rem;
            }
            .feature-card {
                padding: 2rem;
            }
            .feature-card .icon-wrapper {
                font-size: 3.5rem;
            }
            .feature-card h5 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-clipboard-data-fill"></i>Panel de Secretaria
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                       <a class="btn btn-logout" href="logout.php">
                            <i class="bi bi-box-arrow-right"></i>Cerrar Sesión
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container main-content">
        <div class="row w-100 justify-content-center">
            <div class="col-12 welcome-section">
                <h1>Hola, <span style="color: var(--secondary-color);"><?php echo htmlspecialchars($nombreSecretaria); ?></span></h1>
                <p>Bienvenida al panel de gestión. Aquí puedes administrar pacientes y citas de tu consultorio.</p>
            </div>

            <!-- Tarjeta de Gestión de Pacientes -->
            <div class="col-md-6 col-lg-6 d-flex">
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <h5>Gestión de Pacientes</h5>
                    <p>Accede y administra la información de todos los pacientes, incluyendo datos personales, historial y facturación.</p>
                    <div class="btn-group-vertical-custom">
                        <a href="backend/pacientes/gestion_pacientes.php" class="btn btn-feature">
                            <i class="bi bi-person-lines-fill"></i>Gestionar Pacientes
                        </a>
                        <a href="backend/facturas/facturacion.php" class="btn btn-feature">
                            <i class="bi bi-receipt"></i>Facturar Paciente
                        </a>
                    </div>
                </div>
            </div>

            <!-- Tarjeta de Gestión de Citas -->
            <div class="col-md-6 col-lg-6 d-flex">
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="bi bi-calendar-event-fill"></i>
                    </div>
                    <h5>Gestión de Citas</h5>
                    <p>Organiza, programa y verifica el estado de todas las citas médicas del consultorio.</p>
                    <div class="btn-group-vertical-custom">
                        <a href="backend/citas/crear_cita.php" class="btn btn-feature">
                            <i class="bi bi-plus-circle-fill"></i>Crear Cita
                        </a>
                        <a href="backend/citas/gestion_citas.php" class="btn btn-feature">
                            <i class="bi bi-calendar-check"></i>Gestionar Citas
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle (popper.js incluido) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
