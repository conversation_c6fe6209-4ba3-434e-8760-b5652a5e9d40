<?php
// /public_html/mi_consultorio/test_config.php
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 1) Cargamos nuestro config multi‐tenant que define getDBConnection()
require_once __DIR__ . '/config/config.php';

// 2) Abrimos la conexión MySQL remota
$conn = getDBConnection();
$conn->set_charset('utf8mb4');

// 3) Si hay error de conexión, lo mostramos
if ($conn->connect_error) {
    die("❌ Error de conexión: " . $conn->connect_error);
}

// 4) Hacemos un “SELECT DATABASE()” para saber en qué BD estamos
if ($res = $conn->query("SELECT DATABASE() AS dbname")) {
    $row = $res->fetch_assoc();
    echo "✅ Conectado correctamente a la base de datos: <strong>{$row['dbname']}</strong>";
    $res->free();
} else {
    echo "❌ Falló la consulta: " . $conn->error;
}

$conn->close();
