<?php
// backend/reportes/financieros_reportes.php
session_start();
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: ../../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

$usuario = htmlspecialchars($_SESSION['usuario']);

// Procesar filtros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$estado_pago = $_GET['estado_pago'] ?? '';
$metodo_pago = $_GET['metodo_pago'] ?? '';
$ars = $_GET['ars'] ?? '';
$reporte_tipo = $_GET['tipo'] ?? 'ingresos';
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reportes Financieros - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background-color: #f8f9fa; }
        .header-section {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(45deg, #ffc107, #ff8c00);
            color: white;
            border-radius: 15px;
        }
        .money-positive { color: #28a745; font-weight: bold; }
        .money-negative { color: #dc3545; font-weight: bold; }
        .money-pending { color: #ffc107; font-weight: bold; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-chart-pie me-3"></i>Reportes Financieros</h1>
                    <p class="mb-0">Análisis de ingresos, facturación y estados de pago</p>
                </div>
                <div class="col-md-4 text-end">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-end">
                            <li class="breadcrumb-item"><a href="../../../index.php" class="text-white">Inicio</a></li>
                            <li class="breadcrumb-item"><a href="../../reportes.php" class="text-white">Reportes</a></li>
                            <li class="breadcrumb-item active text-white-50">Financieros</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filtros -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-filter me-2"></i>Filtros de Reporte</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label for="fecha_inicio" class="form-label">Fecha Inicio</label>
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" value="<?php echo $fecha_inicio; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="fecha_fin" class="form-label">Fecha Fin</label>
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" value="<?php echo $fecha_fin; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="estado_pago" class="form-label">Estado de Pago</label>
                        <select class="form-select" id="estado_pago" name="estado_pago">
                            <option value="">Todos</option>
                            <option value="pagado" <?php echo $estado_pago == 'pagado' ? 'selected' : ''; ?>>Pagado</option>
                            <option value="pendiente" <?php echo $estado_pago == 'pendiente' ? 'selected' : ''; ?>>Pendiente</option>
                            <option value="parcial" <?php echo $estado_pago == 'parcial' ? 'selected' : ''; ?>>Pago Parcial</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="metodo_pago" class="form-label">Método de Pago</label>
                        <select class="form-select" id="metodo_pago" name="metodo_pago">
                            <option value="">Todos</option>
                            <option value="efectivo" <?php echo $metodo_pago == 'efectivo' ? 'selected' : ''; ?>>Efectivo</option>
                            <option value="tarjeta" <?php echo $metodo_pago == 'tarjeta' ? 'selected' : ''; ?>>Tarjeta</option>
                            <option value="transferencia" <?php echo $metodo_pago == 'transferencia' ? 'selected' : ''; ?>>Transferencia</option>
                            <option value="cheque" <?php echo $metodo_pago == 'cheque' ? 'selected' : ''; ?>>Cheque</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="ars" class="form-label">ARS</label>
                        <select class="form-select" id="ars" name="ars">
                            <option value="">Todas</option>
                            <option value="1" <?php echo $ars == '1' ? 'selected' : ''; ?>>ARS Principal</option>
                            <option value="0" <?php echo $ars == '0' ? 'selected' : ''; ?>>Particular</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="tipo" class="form-label">Tipo de Reporte</label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="ingresos" <?php echo $reporte_tipo == 'ingresos' ? 'selected' : ''; ?>>Ingresos</option>
                            <option value="pagos" <?php echo $reporte_tipo == 'pagos' ? 'selected' : ''; ?>>Estado de Pagos</option>
                            <option value="ars" <?php echo $reporte_tipo == 'ars' ? 'selected' : ''; ?>>Análisis ARS</option>
                            <option value="detallado" <?php echo $reporte_tipo == 'detallado' ? 'selected' : ''; ?>>Detallado</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-search me-2"></i>Generar Reporte
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportarExcel()">
                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportarPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Estadísticas Financieras -->
        <?php
        // Construir filtros
        $where_conditions = ["DATE(FECHA) BETWEEN ? AND ?"];
        $params = [$fecha_inicio, $fecha_fin];

        if ($estado_pago == 'pagado') {
            $where_conditions[] = "NOPAGO = 0";
        } elseif ($estado_pago == 'pendiente') {
            $where_conditions[] = "NOPAGO > 0";
        } elseif ($estado_pago == 'parcial') {
            $where_conditions[] = "PAGADO > 0 AND NOPAGO > 0";
        }

        if ($metodo_pago) {
            $where_conditions[] = "MODOPGO = ?";
            $params[] = $metodo_pago;
        }

        if ($ars !== '') {
            $where_conditions[] = "FARS = ?";
            $params[] = $ars;
        }

        $where_clause = "WHERE " . implode(" AND ", $where_conditions) . " AND ESTATUS = 'A'";

        // Total facturado
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(PRECIO), 0) as total FROM FACTURAS $where_clause");
        $stmt->execute($params);
        $total_facturado = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Total pagado
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(PAGADO), 0) as total FROM FACTURAS $where_clause");
        $stmt->execute($params);
        $total_pagado = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Total pendiente
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(NOPAGO), 0) as total FROM FACTURAS $where_clause");
        $stmt->execute($params);
        $total_pendiente = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Número de facturas
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM FACTURAS $where_clause");
        $stmt->execute($params);
        $num_facturas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Promedio por factura
        $promedio_factura = $num_facturas > 0 ? $total_facturado / $num_facturas : 0;
        ?>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                        <h4>RD$<?php echo number_format($total_facturado, 2); ?></h4>
                        <p class="mb-0">Total Facturado</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4>RD$<?php echo number_format($total_pagado, 2); ?></h4>
                        <p class="mb-0">Total Pagado</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <h4>RD$<?php echo number_format($total_pendiente, 2); ?></h4>
                        <p class="mb-0">Total Pendiente</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice fa-2x mb-2"></i>
                        <h4><?php echo $num_facturas; ?></h4>
                        <p class="mb-0">Facturas</p>
                        <small>Promedio: RD$<?php echo number_format($promedio_factura, 2); ?></small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>Estado de Pagos</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="estadoPagosChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>Ingresos Diarios</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="ingresosDiariosChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-credit-card me-2"></i>Métodos de Pago</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="metodosPagoChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Ingresos Mensuales</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="ingresosMensualesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Análisis de ARS -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-hospital me-2"></i>Análisis por ARS</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php
                    $stmt = $pdo->prepare("
                        SELECT 
                            CASE WHEN FARS = 0 THEN 'Particular' ELSE 'ARS' END as tipo_pago,
                            COUNT(*) as cantidad_facturas,
                            COALESCE(SUM(PRECIO), 0) as total_facturado,
                            COALESCE(SUM(PAGADO), 0) as total_pagado,
                            COALESCE(SUM(NOPAGO), 0) as total_pendiente
                        FROM FACTURAS 
                        $where_clause 
                        GROUP BY FARS
                    ");
                    $stmt->execute($params);
                    $datos_ars = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    foreach ($datos_ars as $ars_data):
                    ?>
                    <div class="col-md-6">
                        <div class="card border-<?php echo $ars_data['tipo_pago'] == 'Particular' ? 'primary' : 'success'; ?>">
                            <div class="card-header bg-<?php echo $ars_data['tipo_pago'] == 'Particular' ? 'primary' : 'success'; ?> text-white">
                                <h6 class="mb-0"><?php echo $ars_data['tipo_pago']; ?></h6>
                            </div>
                            <div class="card-body">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <h6>Facturas</h6>
                                        <h5><?php echo $ars_data['cantidad_facturas']; ?></h5>
                                    </div>
                                    <div class="col-4">
                                        <h6>Facturado</h6>
                                        <h5 class="money-positive">RD$<?php echo number_format($ars_data['total_facturado'], 2); ?></h5>
                                    </div>
                                    <div class="col-4">
                                        <h6>Pendiente</h6>
                                        <h5 class="money-pending">RD$<?php echo number_format($ars_data['total_pendiente'], 2); ?></h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Tabla Detallada -->
        <?php if ($reporte_tipo == 'detallado'): ?>
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>Detalle de Facturas</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="tablaFacturas">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>No. Factura</th>
                                <th>Paciente</th>
                                <th>Concepto</th>
                                <th>Total</th>
                                <th>Pagado</th>
                                <th>Pendiente</th>
                                <th>Método</th>
                                <th>Estado</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "SELECT f.FECHA, f.NUMFACT, p.NOMBREAPELLIDO, f.CONCEPTO, f.PRECIO, f.PAGADO, f.NOPAGO, f.MODOPGO,
                                          CASE WHEN f.NOPAGO = 0 THEN 'Pagado'
                                               WHEN f.PAGADO = 0 THEN 'Pendiente'
                                               ELSE 'Parcial' END as estado_pago
                                   FROM FACTURAS f
                                   LEFT JOIN PACIENTES p ON f.CLAVEPAC = p.CLAVE
                                   $where_clause
                                   ORDER BY f.FECHA DESC, f.NUMFACT DESC
                                   LIMIT 100";

                            $stmt = $pdo->prepare($sql);
                            $stmt->execute($params);
                            $facturas = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            foreach ($facturas as $factura):
                            ?>
                            <tr>
                                <td><?php echo date('d/m/Y', strtotime($factura['FECHA'])); ?></td>
                                <td><?php echo htmlspecialchars($factura['NUMFACT']); ?></td>
                                <td><?php echo htmlspecialchars($factura['NOMBREAPELLIDO'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($factura['CONCEPTO']); ?></td>
                                <td class="money-positive">RD$<?php echo number_format($factura['PRECIO'], 2); ?></td>
                                <td class="money-positive">RD$<?php echo number_format($factura['PAGADO'], 2); ?></td>
                                <td class="money-pending">RD$<?php echo number_format($factura['NOPAGO'], 2); ?></td>
                                <td><?php echo ucfirst($factura['MODOPGO'] ?? 'N/A'); ?></td>
                                <td>
                                    <span class="badge bg-<?php
                                        echo $factura['estado_pago'] == 'Pagado' ? 'success' :
                                             ($factura['estado_pago'] == 'Pendiente' ? 'danger' : 'warning');
                                    ?>">
                                        <?php echo $factura['estado_pago']; ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Top Servicios -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-star me-2"></i>Servicios Más Facturados</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Concepto</th>
                                <th>Cantidad</th>
                                <th>Total Facturado</th>
                                <th>Promedio</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $stmt = $pdo->prepare("
                                SELECT CONCEPTO, COUNT(*) as cantidad,
                                       COALESCE(SUM(PRECIO), 0) as total,
                                       COALESCE(AVG(PRECIO), 0) as promedio
                                FROM FACTURAS
                                $where_clause
                                GROUP BY CONCEPTO
                                ORDER BY total DESC
                                LIMIT 10
                            ");
                            $stmt->execute($params);
                            $servicios = $stmt->fetchAll(PDO::FETCH_ASSOC);

                            foreach ($servicios as $servicio):
                            ?>
                            <tr>
                                <td><?php echo htmlspecialchars($servicio['CONCEPTO']); ?></td>
                                <td><?php echo $servicio['cantidad']; ?></td>
                                <td class="money-positive">RD$<?php echo number_format($servicio['total'], 2); ?></td>
                                <td>RD$<?php echo number_format($servicio['promedio'], 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Datos para gráficos
        <?php
        // Datos para gráfico de estado de pagos
        $pagado_total = $total_pagado;
        $pendiente_total = $total_pendiente;

        // Datos para ingresos diarios
        $stmt = $pdo->prepare("
            SELECT DATE(FECHA) as fecha, COALESCE(SUM(PAGADO), 0) as ingresos
            FROM FACTURAS
            $where_clause
            GROUP BY DATE(FECHA)
            ORDER BY fecha DESC
            LIMIT 30
        ");
        $stmt->execute($params);
        $datos_diarios = array_reverse($stmt->fetchAll(PDO::FETCH_ASSOC));

        $labels_diarios = [];
        $data_diarios = [];
        foreach ($datos_diarios as $dato) {
            $labels_diarios[] = date('d/m', strtotime($dato['fecha']));
            $data_diarios[] = $dato['ingresos'];
        }

        // Datos para métodos de pago
        $stmt = $pdo->prepare("
            SELECT MODOPGO, COALESCE(SUM(PAGADO), 0) as total
            FROM FACTURAS
            $where_clause AND PAGADO > 0
            GROUP BY MODOPGO
        ");
        $stmt->execute($params);
        $datos_metodos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $labels_metodos = [];
        $data_metodos = [];
        foreach ($datos_metodos as $metodo) {
            $labels_metodos[] = ucfirst($metodo['MODOPGO'] ?? 'No especificado');
            $data_metodos[] = $metodo['total'];
        }

        // Datos para ingresos mensuales
        $stmt = $pdo->prepare("
            SELECT DATE_FORMAT(FECHA, '%Y-%m') as mes, COALESCE(SUM(PAGADO), 0) as ingresos
            FROM FACTURAS
            WHERE FECHA >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH) AND ESTATUS = 'A'
            GROUP BY mes
            ORDER BY mes
        ");
        $stmt->execute();
        $datos_mensuales = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $labels_mensuales = [];
        $data_mensuales = [];
        foreach ($datos_mensuales as $mes) {
            $labels_mensuales[] = date('M Y', strtotime($mes['mes'] . '-01'));
            $data_mensuales[] = $mes['ingresos'];
        }
        ?>

        // Gráfico de estado de pagos
        const ctx1 = document.getElementById('estadoPagosChart').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: ['Pagado', 'Pendiente'],
                datasets: [{
                    data: [<?php echo $pagado_total; ?>, <?php echo $pendiente_total; ?>],
                    backgroundColor: ['#28a745', '#ffc107']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Gráfico de ingresos diarios
        const ctx2 = document.getElementById('ingresosDiariosChart').getContext('2d');
        new Chart(ctx2, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($labels_diarios); ?>,
                datasets: [{
                    label: 'Ingresos diarios',
                    data: <?php echo json_encode($data_diarios); ?>,
                    borderColor: '#ffc107',
                    backgroundColor: 'rgba(255, 193, 7, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Gráfico de métodos de pago
        const ctx3 = document.getElementById('metodosPagoChart').getContext('2d');
        new Chart(ctx3, {
            type: 'pie',
            data: {
                labels: <?php echo json_encode($labels_metodos); ?>,
                datasets: [{
                    data: <?php echo json_encode($data_metodos); ?>,
                    backgroundColor: ['#007bff', '#28a745', '#dc3545', '#17a2b8', '#6f42c1']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Gráfico de ingresos mensuales
        const ctx4 = document.getElementById('ingresosMensualesChart').getContext('2d');
        new Chart(ctx4, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($labels_mensuales); ?>,
                datasets: [{
                    label: 'Ingresos mensuales',
                    data: <?php echo json_encode($data_mensuales); ?>,
                    backgroundColor: '#ffc107'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        function exportarExcel() {
            window.location.href = 'api/exportar_financieros.php?formato=excel&' + new URLSearchParams(window.location.search);
        }

        function exportarPDF() {
            window.location.href = 'api/exportar_financieros.php?formato=pdf&' + new URLSearchParams(window.location.search);
        }
    </script>
</body>
</html>
