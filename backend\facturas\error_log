[04-Apr-2025 13:28:42 America/New_York] PHP Fatal error:  Uncaught PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'FROM PACIENTES 
        WHERE NOMBRES LIKE '%40211327826%' OR APELLIDOS LIKE...' at line 1 in /home/<USER>/public_html/mi_consultorio/backend/facturas/buscar_paciente.php:26
Stack trace:
#0 /home/<USER>/public_html/mi_consultorio/backend/facturas/buscar_paciente.php(26): PDOStatement->execute()
#1 {main}
  thrown in /home/<USER>/public_html/mi_consultorio/backend/facturas/buscar_paciente.php on line 26
[04-Apr-2025 13:29:47 America/New_York] PHP Fatal error:  Uncaught PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near 'FROM PACIENTES 
        WHERE NOMBRES LIKE '%40211327826%' OR APELLIDOS LIKE...' at line 1 in /home/<USER>/public_html/mi_consultorio/backend/facturas/buscar_paciente.php:26
Stack trace:
#0 /home/<USER>/public_html/mi_consultorio/backend/facturas/buscar_paciente.php(26): PDOStatement->execute()
#1 {main}
  thrown in /home/<USER>/public_html/mi_consultorio/backend/facturas/buscar_paciente.php on line 26
