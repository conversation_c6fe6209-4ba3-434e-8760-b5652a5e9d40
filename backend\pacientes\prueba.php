<?php
// main.php: Página principal de gestión de pacientes
require_once '../config/database.php';
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Gestión de Pacientes</title>
  <!-- Bootstrap CSS -->
  <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
  <!-- DataTables CSS -->
  <link href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-5">
  <h1 class="text-center">Gestión de Pacientes</h1>
  <!-- Botón para abrir el modal de creación -->
  <button class="btn btn-primary mb-3" data-toggle="modal" data-target="#crearPacienteModal">Agregar <PERSON></button>
  
  <!-- Tabla de pacientes -->
  <table id="pacientesTable" class="table table-striped table-bordered">
    <thead>
      <tr>
        <th>Cédula</th>
        <th>Registro</th>
        <th>Nombres</th>
        <th>Apellidos</th>
        <th>Edad</th>
        <th>Acciones</th>
      </tr>
    </thead>
    <tbody>
      <?php
      // Consulta para obtener todos los pacientes
      $stmt = $pdo->query("SELECT * FROM PACIENTES");
      while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
          echo "<tr>";
          echo "<td>" . htmlspecialchars($row['CEDULA']) . "</td>";
          echo "<td>" . htmlspecialchars($row['REGISTRO']) . "</td>";
          echo "<td>" . htmlspecialchars($row['NOMBRES']) . "</td>";
          echo "<td>" . htmlspecialchars($row['APELLIDOS']) . "</td>";
          echo "<td>" . htmlspecialchars($row['EDAD'] ?? '') . "</td>";
          echo "<td>
                  <button class='btn btn-sm btn-warning editarPaciente' data-clave='" . $row['CLAVE'] . "'>Editar</button>
                  <button class='btn btn-sm btn-danger eliminarPaciente' data-clave='" . $row['CLAVE'] . "'>Eliminar</button>
                </td>";
          echo "</tr>";
      }
      ?>
    </tbody>
  </table>
</div>

<!-- Modal para Agregar Paciente (puedes incluir tu formulario de creación) -->
<div class="modal fade" id="crearPacienteModal" tabindex="-1" role="dialog" aria-labelledby="crearPacienteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Agregar Paciente</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <?php include 'crear_pacientes.php'; ?>
      </div>
    </div>
  </div>
</div>

<!-- Modal para Editar Paciente -->
<div class="modal fade" id="editarPacienteModal" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="editarPacienteModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
       <div class="modal-header">
          <h5 class="modal-title">Editar Paciente</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Cerrar">
             <span aria-hidden="true">&times;</span>
          </button>
       </div>
       <div class="modal-body">
          <!-- El formulario de edición se cargará dinámicamente aquí -->
       </div>
    </div>
  </div>
</div>

<!-- jQuery, Popper.js, Bootstrap JS y DataTables JS -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<script src="https://cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
$(document).ready(function(){
    // Inicializa DataTable
    var table = $('#pacientesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.21/i18n/Spanish.json"
        }
    });
    
    // Eliminación de paciente vía AJAX
    $('#pacientesTable').on('click', '.eliminarPaciente', function(){
        var clave = $(this).data('clave');
        if(confirm("¿Estás seguro de eliminar este paciente?")){
            $.ajax({
                url: 'eliminar_paciente.php',
                type: 'POST',
                data: { CLAVE: clave },
                success: function(response) {
                    alert('Paciente eliminado con éxito');
                    location.reload();
                },
                error: function() {
                    alert('Error al eliminar paciente.');
                }
            });
        }
    });
    
    // Carga del formulario de edición con AJAX (ver código anterior)
    $('#pacientesTable').on('click', '.editarPaciente', function(){
        var clave = $(this).data('clave');
        $.ajax({
            url: 'obtener_paciente.php',
            type: 'GET',
            data: { CLAVE: clave },
            dataType: 'json',
            success: function(paciente) {
             // Ejemplo de generación dinámica del formulario de edición con tabs
var form = `
  <form id="editarPacienteForm">
    <input type="hidden" name="CLAVE" value="${paciente.CLAVE}">
    
    <!-- Navegación de Tabs -->
    <ul class="nav nav-tabs" id="editPacienteTabs" role="tablist">
      <li class="nav-item">
        <a class="nav-link active" id="datos-principales-tab" data-toggle="tab" href="#datos-principales" role="tab" aria-controls="datos-principales" aria-selected="true">Datos Principales</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="domicilio-tab" data-toggle="tab" href="#domicilio" role="tab" aria-controls="domicilio" aria-selected="false">Domicilio</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="contacto-tab" data-toggle="tab" href="#contacto" role="tab" aria-controls="contacto" aria-selected="false">Contacto</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="poliza-tab" data-toggle="tab" href="#poliza" role="tab" aria-controls="poliza" aria-selected="false">Póliza</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="responsable-tab" data-toggle="tab" href="#responsable" role="tab" aria-controls="responsable" aria-selected="false">Responsable</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="familiar-tab" data-toggle="tab" href="#familiar" role="tab" aria-controls="familiar" aria-selected="false">Familiar</a>
      </li>
      <li class="nav-item">
        <a class="nav-link" id="otros-tab" data-toggle="tab" href="#otros" role="tab" aria-controls="otros" aria-selected="false">Otros</a>
      </li>
    </ul>
    
    <!-- Contenido de los Tabs -->
    <div class="tab-content pt-3" id="editPacienteTabsContent">
      <!-- Datos Principales -->
      <div class="tab-pane fade show active" id="datos-principales" role="tabpanel" aria-labelledby="datos-principales-tab">
        <div class="form-group">
          <label for="CEDULA">Cédula:</label>
          <input type="text" class="form-control" name="CEDULA" id="CEDULA" value="${paciente.CEDULA}" maxlength="11" required>
        </div>
        <div class="form-group">
          <label for="NOMBRES">Nombres:</label>
          <input type="text" class="form-control" name="NOMBRES" id="NOMBRES" value="${paciente.NOMBRES}" required maxlength="35">
        </div>
        <div class="form-group">
          <label for="APELLIDOS">Apellidos:</label>
          <input type="text" class="form-control" name="APELLIDOS" id="APELLIDOS" value="${paciente.APELLIDOS}" required maxlength="35">
        </div>
        <div class="form-group">
          <label for="FECHANAC">Fecha de Nacimiento:</label>
          <input type="date" class="form-control" name="FECHANAC" id="FECHANAC" value="${paciente.FECHANAC}" required>
        </div>
        <div class="form-group">
          <label for="SEXO">Sexo:</label>
          <select class="form-control" name="SEXO" id="SEXO" required>
            <option value="">Seleccionar</option>
            <option value="Masculino" ${paciente.SEXO === 'Masculino' ? 'selected' : ''}>Masculino</option>
            <option value="Femenino" ${paciente.SEXO === 'Femenino' ? 'selected' : ''}>Femenino</option>
            <option value="Indefinido" ${paciente.SEXO === 'Indefinido' ? 'selected' : ''}>Indefinido</option>
          </select>
        </div>
        <div class="form-group">
          <label for="RH">Tipo de Sangre (RH):</label>
          <select class="form-control" name="RH" id="RH">
            <option value="">Seleccionar</option>
            <option value="O+" ${paciente.RH === 'O+' ? 'selected' : ''}>O+</option>
            <option value="O-" ${paciente.RH === 'O-' ? 'selected' : ''}>O-</option>
            <option value="A+" ${paciente.RH === 'A+' ? 'selected' : ''}>A+</option>
            <option value="A-" ${paciente.RH === 'A-' ? 'selected' : ''}>A-</option>
            <option value="B+" ${paciente.RH === 'B+' ? 'selected' : ''}>B+</option>
            <option value="B-" ${paciente.RH === 'B-' ? 'selected' : ''}>B-</option>
            <option value="AB+" ${paciente.RH === 'AB+' ? 'selected' : ''}>AB+</option>
            <option value="AB-" ${paciente.RH === 'AB-' ? 'selected' : ''}>AB-</option>
          </select>
        </div>
        <div class="form-group">
          <label for="PESOHABITUAL">Peso Habitual:</label>
          <input type="number" step="0.01" class="form-control" name="PESOHABITUAL" id="PESOHABITUAL" value="${paciente.PESOHABITUAL}">
        </div>
        <div class="form-group">
          <label for="NACIONALIDAD">Nacionalidad:</label>
          <input type="text" class="form-control" name="NACIONALIDAD" id="NACIONALIDAD" value="${paciente.NACIONALIDAD}" maxlength="35">
        </div>
        <div class="form-group">
          <label for="ESTADOCIVIL">Estado Civil:</label>
          <input type="text" class="form-control" name="ESTADOCIVIL" id="ESTADOCIVIL" value="${paciente.ESTADOCIVIL}">
        </div>
        <div class="form-group">
          <label for="LUGARNAC">Lugar de Nacimiento:</label>
          <input type="text" class="form-control" name="LUGARNAC" id="LUGARNAC" value="${paciente.LUGARNAC}" maxlength="35">
        </div>
        <div class="form-group">
          <label for="OCUPACION">Ocupación:</label>
          <input type="text" class="form-control" name="OCUPACION" id="OCUPACION" value="${paciente.OCUPACION}" maxlength="25">
        </div>
        <div class="form-group">
          <label for="RELIGION">Religión:</label>
          <select class="form-control" name="RELIGION" id="RELIGION">
            <option value="">Seleccionar</option>
            <option value="Católico" ${paciente.RELIGION === 'Católico' ? 'selected' : ''}>Católico</option>
            <option value="Cristiano Evangelice" ${paciente.RELIGION === 'Cristiano Evangelice' ? 'selected' : ''}>Cristiano Evangelice</option>
            <option value="Testigo" ${paciente.RELIGION === 'Testigo' ? 'selected' : ''}>Testigo</option>
            <option value="Ninguna" ${paciente.RELIGION === 'Ninguna' ? 'selected' : ''}>Ninguna</option>
            <option value="Otra" ${paciente.RELIGION === 'Otra' ? 'selected' : ''}>Otra</option>
          </select>
        </div>
      </div>
      
      <!-- Domicilio -->
      <div class="tab-pane fade" id="domicilio" role="tabpanel" aria-labelledby="domicilio-tab">
        <div class="form-group">
          <label for="CALLE">Calle:</label>
          <input type="text" class="form-control" name="CALLE" id="CALLE" value="${paciente.CALLE}" maxlength="80">
        </div>
        <div class="form-group">
          <label for="PROVINCIA">Provincia:</label>
          <input type="text" class="form-control" name="PROVINCIA" id="PROVINCIA" value="${paciente.PROVINCIA}" maxlength="30">
        </div>
        <div class="form-group">
          <label for="LOCALIDAD">Localidad:</label>
          <input type="text" class="form-control" name="LOCALIDAD" id="LOCALIDAD" value="${paciente.LOCALIDAD}" maxlength="35">
        </div>
        <div class="form-group">
          <label for="MUNICIPIO">Municipio:</label>
          <input type="text" class="form-control" name="MUNICIPIO" id="MUNICIPIO" value="${paciente.MUNICIPIO}" maxlength="35">
        </div>
        <div class="form-group">
          <label for="PAIS">País:</label>
          <input type="text" class="form-control" name="PAIS" id="PAIS" value="${paciente.PAIS}" maxlength="35">
        </div>
        <div class="form-group">
          <label for="REFERENCIA">Referencia:</label>
          <input type="text" class="form-control" name="REFERENCIA" id="REFERENCIA" value="${paciente.REFERENCIA}" maxlength="30">
        </div>
      </div>
      
      <!-- Contacto -->
      <div class="tab-pane fade" id="contacto" role="tabpanel" aria-labelledby="contacto-tab">
        <div class="form-group">
          <label for="CELULAR">Teléfono Celular:</label>
          <input type="text" class="form-control" name="CELULAR" id="CELULAR" value="${paciente.CELULAR}" maxlength="10">
        </div>
        <div class="form-group">
          <label for="TELEFONO">Teléfono de Casa:</label>
          <input type="text" class="form-control" name="TELEFONO" id="TELEFONO" value="${paciente.TELEFONO}" maxlength="10">
        </div>
        <div class="form-group">
          <label for="TELTRABAJO">Teléfono del Trabajo:</label>
          <input type="text" class="form-control" name="TELTRABAJO" id="TELTRABAJO" value="${paciente.TELTRABAJO}" maxlength="10">
        </div>
        <div class="form-group">
          <label for="FAX">Fax:</label>
          <input type="text" class="form-control" name="FAX" id="FAX" value="${paciente.FAX}" maxlength="10">
        </div>
        <div class="form-group">
          <label for="ECORREO">Correo Electrónico:</label>
          <input type="email" class="form-control" name="ECORREO" id="ECORREO" value="${paciente.ECORREO}" maxlength="50">
        </div>
      </div>
      
      <!-- Póliza -->
      <div class="tab-pane fade" id="poliza" role="tabpanel" aria-labelledby="poliza-tab">
        <div class="form-group">
          <label for="ARS">ARS:</label>
          <select class="form-control" name="ARS" id="ARS" required>
            <option value="">Seleccionar ARS</option>
            <option value="1" ${paciente.ARS == 1 ? 'selected' : ''}>Aseguradora 1</option>
            <option value="2" ${paciente.ARS == 2 ? 'selected' : ''}>Aseguradora 2</option>
            <!-- Agregar más opciones según corresponda -->
          </select>
        </div>
        <div class="form-group">
          <label for="PLANES">Planes:</label>
          <input type="text" class="form-control" name="PLANES" id="PLANES" value="${paciente.PLANES}" maxlength="20">
        </div>
        <div class="form-group">
          <label for="AFILIADO">Afiliado:</label>
          <input type="text" class="form-control" name="AFILIADO" id="AFILIADO" value="${paciente.AFILIADO}" maxlength="18">
        </div>
        <div class="form-group">
          <label for="VIGENCIA">Vigencia:</label>
          <input type="date" class="form-control" name="VIGENCIA" id="VIGENCIA" value="${paciente.VIGENCIA ? paciente.VIGENCIA.substr(0,10) : ''}">
        </div>
        <div class="form-group">
          <label for="NSS">Número de Seguro Social (NSS):</label>
          <input type="text" class="form-control" name="NSS" id="NSS" value="${paciente.NSS}" readonly>
        </div>
        <div class="form-group">
          <label for="CATEGORIA">Categoría:</label>
          <select class="form-control" name="CATEGORIA" id="CATEGORIA" required>
            <option value="">Seleccionar Categoría</option>
            <option value="1" ${paciente.CATEGORIA == 1 ? 'selected' : ''}>Categoría 1</option>
            <option value="2" ${paciente.CATEGORIA == 2 ? 'selected' : ''}>Categoría 2</option>
            <!-- Agregar más opciones según corresponda -->
          </select>
        </div>
      </div>
      
      <!-- Responsable -->
      <div class="tab-pane fade" id="responsable" role="tabpanel" aria-labelledby="responsable-tab">
        <div class="form-group">
          <label for="NOMBRERESP">Nombre del Responsable:</label>
          <input type="text" class="form-control" name="NOMBRERESP" id="NOMBRERESP" value="${paciente.NOMBRERESP}" maxlength="50">
        </div>
        <div class="form-group">
          <label for="DIRECCIONRESP">Dirección del Responsable:</label>
          <input type="text" class="form-control" name="DIRECCIONRESP" id="DIRECCIONRESP" value="${paciente.DIRECCIONRESP}" maxlength="50">
        </div>
        <div class="form-group">
          <label for="CEDULARESP">Cédula del Responsable:</label>
          <input type="text" class="form-control" name="CEDULARESP" id="CEDULARESP" value="${paciente.CEDULARESP}" maxlength="10">
        </div>
        <div class="form-group">
          <label for="TELEFONORESP">Teléfono del Responsable:</label>
          <input type="text" class="form-control" name="TELEFONORESP" id="TELEFONORESP" value="${paciente.TELEFONORESP}" maxlength="10">
        </div>
      </div>
      
      <!-- Familiar -->
      <div class="tab-pane fade" id="familiar" role="tabpanel" aria-labelledby="familiar-tab">
        <div class="form-group">
          <label for="FAMILIARPROX">Nombre del Familiar Próximo:</label>
          <input type="text" class="form-control" name="FAMILIARPROX" id="FAMILIARPROX" value="${paciente.FAMILIARPROX}" maxlength="35">
        </div>
        <div class="form-group">
          <label for="DIRECCIONFAMILIAR">Dirección del Familiar Próximo:</label>
          <input type="text" class="form-control" name="DIRECCIONFAMILIAR" id="DIRECCIONFAMILIAR" value="${paciente.DIRECCIONFAMILIAR}" maxlength="35">
        </div>
        <div class="form-group">
          <label for="TELEFONOFAMILIAR">Teléfono del Familiar Próximo:</label>
          <input type="text" class="form-control" name="TELEFONOFAMILIAR" id="TELEFONOFAMILIAR" value="${paciente.TELEFONOFAMILIAR}" maxlength="10">
        </div>
      </div>
      
      <!-- Otros -->
      <div class="tab-pane fade" id="otros" role="tabpanel" aria-labelledby="otros-tab">
        <div class="form-group">
          <label for="OBSERVACIONES">Observaciones:</label>
          <textarea class="form-control" name="OBSERVACIONES" id="OBSERVACIONES" maxlength="60">${paciente.OBSERVACIONES}</textarea>
        </div>
        <div class="form-group">
          <label>Nivel Escolar:</label><br>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelPrimario" value="0" ${paciente.NIVELESCOLAR == "0" ? 'checked' : ''}>
            <label class="form-check-label" for="nivelPrimario">Primario</label>
          </div>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelSecundario" value="1" ${paciente.NIVELESCOLAR == "1" ? 'checked' : ''}>
            <label class="form-check-label" for="nivelSecundario">Secundario</label>
          </div>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelUniversitario" value="2" ${paciente.NIVELESCOLAR == "2" ? 'checked' : ''}>
            <label class="form-check-label" for="nivelUniversitario">Universitario</label>
          </div>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelNinguno" value="3" ${paciente.NIVELESCOLAR == "3" ? 'checked' : ''}>
            <label class="form-check-label" for="nivelNinguno">Ninguno</label>
          </div>
        </div>
        <div class="form-group">
          <label>Procedencia:</label><br>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaUrbano" value="1" ${paciente.PROCEDENCIA == "1" ? 'checked' : ''}>
            <label class="form-check-label" for="procedenciaUrbano">Urbano</label>
          </div>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaRural" value="0" ${paciente.PROCEDENCIA == "0" ? 'checked' : ''}>
            <label class="form-check-label" for="procedenciaRural">Rural</label>
          </div>
        </div>
        <div class="form-group">
          <label for="RECORDCLINICA">Record Clínico:</label>
          <input type="text" class="form-control" name="RECORDCLINICA" id="RECORDCLINICA" value="${paciente.RECORDCLINICA}">
        </div>
      </div>
    </div>
    
    <button type="submit" class="btn btn-primary mt-3">Guardar Cambios</button>
  </form>
`;

// Inserta el formulario en el modal de edición y muestra el modal
$('#editarPacienteModal .modal-body').html(form);
$('#editarPacienteModal').modal('show');

            },
            error: function() {
                alert('Error al obtener datos del paciente.');
            }
        });
    });
    
    // Envío del formulario de edición vía AJAX
    $(document).on('submit', '#editarPacienteForm', function(e){
        e.preventDefault();
        var formData = $(this).serialize();
        $.ajax({
            url: 'actualizar_paciente.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response){
                if(response.success){
                    alert('Paciente actualizado con éxito');
                    $('#editarPacienteModal').modal('hide');
                    location.reload();
                } else {
                    alert('Error al actualizar paciente');
                }
            },
            error: function(){
                alert('Error al actualizar paciente');
            }
        });
    });
});
</script>
</body>
</html>
