<?php
// backend/reportes/api/exportar_citas.php
session_start();

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../lib/ValidadorReportes.php';
date_default_timezone_set('America/Santo_Domingo');

// Crear validador
$validador = new ValidadorReportes();

// Obtener parámetros
$formato = $_GET['formato'] ?? 'excel';
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-t');
$doctor = $validador->sanitizarTexto($_GET['doctor'] ?? '');
$estatus = $_GET['estatus'] ?? '';

// Validar permisos
if (!$validador->validarPermisos()) {
    echo $validador->respuestaErrorHTML('Error de Autenticación');
    exit;
}

// Validar conexión a base de datos
if (!$validador->validarConexionBD($pdo)) {
    echo $validador->respuestaErrorHTML('Error de Conexión');
    exit;
}

// Validar parámetros de entrada
if (!$validador->validarFormato($formato)) {
    echo $validador->respuestaErrorHTML('Formato Inválido');
    exit;
}

if (!$validador->validarFechas($fecha_inicio, $fecha_fin)) {
    echo $validador->respuestaErrorHTML('Error en Fechas');
    exit;
}

if (!$validador->validarEstadoCita($estatus)) {
    echo $validador->respuestaErrorHTML('Estado de Cita Inválido');
    exit;
}

try {
    // Validar que existan datos en el rango especificado
    $condiciones_extra = [];
    if ($doctor) $condiciones_extra['NUMDOCTOR'] = $doctor;
    if ($estatus !== '') $condiciones_extra['ESTATUS'] = $estatus;

    $validador->validarExistenciaDatos($pdo, 'CITAMEDIC', 'FECHACON', $fecha_inicio, $fecha_fin, $condiciones_extra);

    // Mostrar advertencias si las hay
    if ($validador->tieneAdvertencias()) {
        // Para formato HTML, mostrar advertencias
        if ($formato === 'pdf') {
            $advertencias_html = "<div style='background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
            $advertencias_html .= "<strong>Advertencias:</strong><ul>";
            foreach ($validador->obtenerAdvertencias() as $advertencia) {
                $advertencias_html .= "<li>" . htmlspecialchars($advertencia) . "</li>";
            }
            $advertencias_html .= "</ul></div>";
        }
    }

    // Construir consulta
    $where_conditions = ["FECHACON BETWEEN ? AND ?"];
    $params = [$fecha_inicio, $fecha_fin];

    if ($doctor) {
        $where_conditions[] = "NUMDOCTOR = ?";
        $params[] = $doctor;
    }
    if ($estatus !== '') {
        $where_conditions[] = "ESTATUS = ?";
        $params[] = $estatus;
    }

    $where_clause = "WHERE " . implode(" AND ", $where_conditions);

    // Obtener datos
    $sql = "SELECT c.FECHACON, c.HORACON, p.NOMBREAPELLIDO, p.CEDULA, c.ESTATUS, c.NUMDOCTOR, c.OBSERVACION,
                   CASE c.ESTATUS
                       WHEN 0 THEN 'Atendido'
                       WHEN 1 THEN 'Canceló'
                       WHEN 2 THEN 'No asistió'
                       WHEN 3 THEN 'Citado'
                       WHEN 4 THEN 'Llegó tarde'
                       WHEN 5 THEN 'Esperando'
                       WHEN 6 THEN 'Pendiente aprobación'
                       ELSE 'Desconocido'
                   END as estado_texto
           FROM CITAMEDIC c
           JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
           $where_clause
           ORDER BY c.FECHACON, c.HORACON";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $citas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Verificar si se encontraron datos
    if (empty($citas)) {
        $validador->limpiar();
        $validador->obtenerErrores()[] = "No se encontraron citas para los criterios especificados";
        $validador->obtenerAdvertencias()[] = "Intente ampliar el rango de fechas o modificar los filtros";
        echo $validador->respuestaErrorHTML('Sin Datos Disponibles');
        exit;
    }

    if ($formato === 'excel') {
        // Exportar a Excel (CSV)
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_citas_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // BOM para UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Encabezados
        fputcsv($output, [
            'Fecha',
            'Hora', 
            'Paciente',
            'Cédula',
            'Estado',
            'Doctor',
            'Observaciones'
        ], ';');
        
        // Datos
        foreach ($citas as $cita) {
            fputcsv($output, [
                date('d/m/Y', strtotime($cita['FECHACON'])),
                date('H:i', strtotime($cita['HORACON'])),
                $cita['NOMBREAPELLIDO'],
                $cita['CEDULA'],
                $cita['estado_texto'],
                'Dr. ' . $cita['NUMDOCTOR'],
                $cita['OBSERVACION'] ?? ''
            ], ';');
        }
        
        fclose($output);
        
    } else {
        // Exportar a PDF usando la clase ReportePDF
        require_once __DIR__ . '/../../lib/ReportePDF.php';

        // Obtener información de la empresa
        $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
        $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
        $nombre_empresa = $empresa ? $empresa['NOMBRE'] : 'Consultorio Médico';

        // Estadísticas
        $total_citas = count($citas);
        $atendidas = count(array_filter($citas, function($c) { return $c['ESTATUS'] == 0; }));
        $no_asistieron = count(array_filter($citas, function($c) { return $c['ESTATUS'] == 2; }));
        $canceladas = count(array_filter($citas, function($c) { return $c['ESTATUS'] == 1; }));
        $pendientes = count(array_filter($citas, function($c) { return $c['ESTATUS'] == 6; }));
        $tasa_asistencia = $total_citas > 0 ? round(($atendidas / $total_citas) * 100, 1) : 0;

        // Crear el reporte PDF
        $pdf = new ReportePDF('Reporte de Citas Médicas', $nombre_empresa);

        // Construir período
        $periodo = ReportePDF::formatearFecha($fecha_inicio) . ' - ' . ReportePDF::formatearFecha($fecha_fin);

        // Filtros aplicados
        $filtros = [];
        if ($doctor) $filtros[] = "Doctor: Dr. $doctor";
        if ($estatus !== '') $filtros[] = "Estado: " . ReportePDF::formatearEstadoCita($estatus);
        $subtitulo = !empty($filtros) ? 'Filtros: ' . implode(' | ', $filtros) : '';

        $pdf->iniciarHTML()
           ->agregarEncabezado($subtitulo, $periodo);

        // Estadísticas
        $estadisticas = [
            ['valor' => $total_citas, 'etiqueta' => 'Total Citas'],
            ['valor' => $atendidas, 'etiqueta' => 'Atendidas'],
            ['valor' => $no_asistieron, 'etiqueta' => 'No Asistieron'],
            ['valor' => $tasa_asistencia . '%', 'etiqueta' => 'Tasa Asistencia']
        ];

        $pdf->agregarSeccionEstadisticas('Resumen Estadístico', $estadisticas);

        // Preparar datos para la tabla
        $encabezados = ['Fecha', 'Hora', 'Paciente', 'Cédula', 'Estado', 'Doctor', 'Observaciones'];
        $datos_tabla = [];

        foreach ($citas as $cita) {
            $clase_estado = '';
            switch ($cita['ESTATUS']) {
                case 0: $clase_estado = 'estado-atendido'; break;
                case 2: $clase_estado = 'estado-no-asistio'; break;
                case 3: $clase_estado = 'estado-citado'; break;
                case 6: $clase_estado = 'estado-pendiente'; break;
            }

            $datos_tabla[] = [
                ReportePDF::formatearFecha($cita['FECHACON']),
                ReportePDF::formatearHora($cita['HORACON']),
                htmlspecialchars($cita['NOMBREAPELLIDO']),
                htmlspecialchars($cita['CEDULA']),
                "<span class='$clase_estado'>" . $cita['estado_texto'] . "</span>",
                'Dr. ' . $cita['NUMDOCTOR'],
                htmlspecialchars(substr($cita['OBSERVACION'] ?? '', 0, 30) . (strlen($cita['OBSERVACION'] ?? '') > 30 ? '...' : ''))
            ];
        }

        $pdf->agregarTabla('Detalle de Citas', $encabezados, $datos_tabla);

        // Agregar estadísticas adicionales si hay datos
        if ($total_citas > 0) {
            $contenido_adicional = "<div class='stats-section'>
                <h3 style='margin: 0 0 10px 0; color: #2c3e50;'>Distribución por Estado</h3>
                <table style='width: 60%; margin: 0 auto;'>
                    <tr><td><strong>Atendidas:</strong></td><td class='estado-atendido'>$atendidas (" . round(($atendidas/$total_citas)*100, 1) . "%)</td></tr>
                    <tr><td><strong>No Asistieron:</strong></td><td class='estado-no-asistio'>$no_asistieron (" . round(($no_asistieron/$total_citas)*100, 1) . "%)</td></tr>
                    <tr><td><strong>Canceladas:</strong></td><td class='estado-pendiente'>$canceladas (" . round(($canceladas/$total_citas)*100, 1) . "%)</td></tr>
                    <tr><td><strong>Pendientes:</strong></td><td class='estado-citado'>$pendientes (" . round(($pendientes/$total_citas)*100, 1) . "%)</td></tr>
                </table>
            </div>";

            $pdf->agregarSeccionPersonalizada($contenido_adicional);
        }

        $pdf->finalizarHTML();

        echo $pdf->generarPDF('reporte_citas_' . date('Y-m-d'));
    }

} catch (PDOException $e) {
    $validador->limpiar();
    $validador->obtenerErrores()[] = "Error en la base de datos: " . $e->getMessage();
    $validador->obtenerAdvertencias()[] = "Verifique la conexión a la base de datos y contacte al administrador";
    echo $validador->respuestaErrorHTML('Error de Base de Datos');

    // Log del error para el administrador
    error_log("Error PDO en exportar_citas.php: " . $e->getMessage() . " - Usuario: " . ($_SESSION['usuario'] ?? 'desconocido'));

} catch (Exception $e) {
    $validador->limpiar();
    $validador->obtenerErrores()[] = "Error interno del sistema: " . $e->getMessage();
    $validador->obtenerAdvertencias()[] = "Si el problema persiste, contacte al soporte técnico";
    echo $validador->respuestaErrorHTML('Error del Sistema');

    // Log del error para el administrador
    error_log("Error general en exportar_citas.php: " . $e->getMessage() . " - Usuario: " . ($_SESSION['usuario'] ?? 'desconocido'));
}
?>
