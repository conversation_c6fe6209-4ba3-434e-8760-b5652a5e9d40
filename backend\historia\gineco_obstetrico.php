<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? null;
$CEDULA = $_GET['CEDULA'] ?? '';
$mensaje = "";
$tipo_alerta = "info"; // Por defecto, el mensaje es informativo

if (!$CLAVEPAC) die("Paciente no especificado.");

$campos = [
    // Datos Menstruales y de Inicio
    'MENARQUIA', 'RITMO', 'CANTIDAD', 'DIMENORREA', 'TELARQUIA', 'PUBARQUIA', 'VSA', 'NOESPONJOSO',
    // Historial Gineco-Obstétrico
    'GESTAS', 'PARAS', 'ABORTOS', 'ESPONTANEOS', 'PROVOCADOS', 'CAUSAS', 'LEGRADOS', 'HIJOSVIVOS',
    'HIJOSMUERTOS', 'PPRODUCTO', 'LACTANCIA', 'PLANIFICACION', 'FUM', 'FUPAGO', 'FUP', 'FUA', 'FUC', 'FPP',
    // Otros Datos Relevantes
    'TIPOSANGREESPOSO', 'CESARREAS', 'MOLA', 'ECTOPICO', 'DISPAREUNIA', 'SPC', 'EVOLUCION'
];

$datos = [];
$stmt = $pdo->prepare("SELECT * FROM ANTGINECOBSTETRICO WHERE CLAVEPAC = :clavepac LIMIT 1");
$stmt->execute(['clavepac' => $CLAVEPAC]);
$datos = $stmt->fetch(PDO::FETCH_ASSOC); // Usar FETCH_ASSOC para que las claves sean nombres de columna

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $valores = [];
    foreach ($campos as $campo) {
        $valores[$campo] = $_POST[$campo] ?? null;
    }

    // Manejo especial para TIPOSANGREESPOSO si es nulo desde el POST (lo que select enviaría)
    if (isset($_POST['TIPOSANGREESPOSO']) && $_POST['TIPOSANGREESPOSO'] === "") {
        $valores['TIPOSANGREESPOSO'] = null;
    }

    if ($datos) {
        // UPDATE
        $sets = implode(", ", array_map(function($c) { return "$c = :$c"; }, array_keys($valores)));
        $sql = "UPDATE ANTGINECOBSTETRICO SET $sets, SINCRONIZADO = 0 WHERE CLAVEPAC = :CLAVEPAC";
        $valores['CLAVEPAC'] = $CLAVEPAC;
    } else {
        // INSERT
        // Asegúrate de que CEDULA se incluya solo si no es nulo o vacío
        $insert_columns = ['CLAVEPAC', 'SINCRONIZADO'];
        $insert_placeholders = [':CLAVEPAC', ':SINCRONIZADO'];
        $insert_values = ['CLAVEPAC' => $CLAVEPAC, 'SINCRONIZADO' => 0];

        if (!empty($CEDULA)) {
            $insert_columns[] = 'CEDULA';
            $insert_placeholders[] = ':CEDULA';
            $insert_values['CEDULA'] = $CEDULA;
        }

        foreach ($valores as $col => $val) {
            $insert_columns[] = $col;
            $insert_placeholders[] = ":$col";
            $insert_values[$col] = $val;
        }

        $columnas = implode(", ", $insert_columns);
        $placeholders = implode(", ", $insert_placeholders);
        $sql = "INSERT INTO ANTGINECOBSTETRICO ($columnas) VALUES ($placeholders)";
        $valores = $insert_values;
    }

    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);
        $mensaje = $datos ? "✅ Actualizado correctamente." : "✅ Insertado correctamente.";
        $tipo_alerta = "success";
        // Recargar los datos después de guardar para reflejar los cambios
        $stmt = $pdo->prepare("SELECT * FROM ANTGINECOBSTETRICO WHERE CLAVEPAC = :clavepac LIMIT 1");
        $stmt->execute(['clavepac' => $CLAVEPAC]);
        $datos = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $mensaje = "❌ Error: " . $e->getMessage();
        $tipo_alerta = "danger";
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Ant. Gineco-Obstétrico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Estilos opcionales para mejorar el espaciado y la apariencia de los títulos de sección */
        .section-title {
            background-color: #e9ecef; /* Un gris claro */
            padding: 0.5rem 1rem;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            border-radius: 0.3rem;
            color: #495057;
            font-weight: bold;
        }
    </style>
</head>
<body class="p-4 bg-light">
    <div class="container bg-white p-4 rounded shadow-sm">
        <h4 class="mb-4 text-primary">🩺 Antecedentes Gineco-Obstétricos</h4>
        <?php if ($mensaje): ?>
            <div class="alert alert-<?= $tipo_alerta ?> alert-dismissible fade show" role="alert">
                <?= $mensaje ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <form method="post">
            <div class="section-title">Datos Menstruales y de Inicio</div>
            <div class="row g-3">
                <?php
                $campos_menstruales = ['MENARQUIA', 'RITMO', 'CANTIDAD', 'DIMENORREA', 'TELARQUIA', 'PUBARQUIA', 'VSA', 'NOESPONJOSO'];
                foreach ($campos_menstruales as $campo):
                    // Reiniciar valores para cada campo en el loop
                    $tipoCampo = 'text'; $maxlen = 30; $pattern = ''; $placeholder = ''; $title = ''; $dataAttr = '';
                    $label_text = ucfirst(strtolower(str_replace('_', ' ', $campo))); // Reemplazar guiones bajos y capitalizar

                    // Ajustes específicos para los campos
                    switch ($campo) {
                        case 'MENARQUIA': $label_text = 'Menarquia (Edad)'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 12'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'RITMO': $label_text = 'Ritmo (días)'; $maxlen = 10; $pattern = '[A-Za-z0-9/\-]{1,10}'; $title = 'Ej: 28/5'; $dataAttr = 'data-alphanumeric-dash'; break; // Nuevo data-attr para / y -
                        case 'CANTIDAD': $label_text = 'Cantidad Flujo'; $maxlen = 4; $pattern = '[A-Za-z0-9]{1,4}'; $title = 'Ej: Leve, Mod, Abund'; $dataAttr = 'data-alphanumeric'; break;
                        case 'DIMENORREA': $label_text = 'Dismenorrea'; $maxlen = 4; $pattern = '[A-Za-z0-9]{1,4}'; $title = 'Ej: Si, No'; $dataAttr = 'data-alphanumeric'; break;
                        case 'TELARQUIA': $label_text = 'Telarquia (Edad)'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 10'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'PUBARQUIA': $label_text = 'Pubarquia (Edad)'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 11'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'VSA': $label_text = 'Inicio VSA (Edad)'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 16'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'NOESPONJOSO': $label_text = 'No. Esposos'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 1'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                    }
                ?>
                <div class="col-md-3">
                    <label for="<?= $campo ?>" class="form-label"><?= $label_text ?></label>
                    <input
                        type="<?= $tipoCampo ?>"
                        name="<?= $campo ?>"
                        id="<?= $campo ?>"
                        class="form-control"
                        maxlength="<?= $maxlen ?>"
                        value="<?= htmlspecialchars($datos[$campo] ?? '') ?>"
                        <?= $pattern ? "pattern=\"$pattern\"" : '' ?>
                        <?= $placeholder ? "placeholder=\"$placeholder\"" : '' ?>
                        <?= $title ? "title=\"$title\"" : '' ?>
                        <?= $dataAttr ?>
                    >
                </div>
                <?php endforeach; ?>
            </div>

            <div class="section-title">Historial Gineco-Obstétrico</div>
            <div class="row g-3">
                <?php
                $campos_historial = ['GESTAS', 'PARAS', 'ABORTOS', 'ESPONTANEOS', 'PROVOCADOS', 'CAUSAS', 'LEGRADOS', 'HIJOSVIVOS', 'HIJOSMUERTOS', 'PPRODUCTO', 'LACTANCIA', 'PLANIFICACION', 'FUM', 'FUPAGO', 'FUP', 'FUA', 'FUC', 'FPP'];
                foreach ($campos_historial as $campo):
                    // Reiniciar valores para cada campo en el loop
                    $tipoCampo = 'text'; $maxlen = 30; $pattern = ''; $placeholder = ''; $title = ''; $dataAttr = '';
                    $label_text = ucfirst(strtolower(str_replace('_', ' ', $campo)));

                    switch ($campo) {
                        case 'GESTAS': $label_text = 'Gestas'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 3'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'PARAS': $label_text = 'Paras'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 2'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'ABORTOS': $label_text = 'Abortos'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 1'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'ESPONTANEOS': $label_text = 'Abortos Espontáneos'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 1'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'PROVOCADOS': $label_text = 'Abortos Provocados'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 0'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'CAUSAS': $label_text = 'Causas (Abortos)'; $maxlen = 30; $pattern = '[A-Za-z0-9\s]{1,30}'; $title = 'Texto simple (máx 30)'; break;
                        case 'LEGRADOS': $label_text = 'Legrados'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 1'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'HIJOSVIVOS': $label_text = 'Hijos Vivos'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 2'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'HIJOSMUERTOS': $label_text = 'Hijos Muertos'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 0'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'PPRODUCTO': $label_text = 'Peso Prom. Producto'; $maxlen = 30; $pattern = '[A-Za-z0-9\s.]{1,30}'; $title = 'Ej: 7.5 lbs / 3400g'; break; // Permite . para pesos
                        case 'LACTANCIA': $label_text = 'Lactancia'; $maxlen = 30; $pattern = '[A-Za-z0-9\s]{1,30}'; $title = 'Texto simple (máx 30)'; break;
                        case 'PLANIFICACION': $label_text = 'Planificación Familiar'; $maxlen = 350; $tipoCampo = 'textarea'; break;
                        case 'FUM': $label_text = 'FUM (Fecha Última Menstruación)'; $tipoCampo = 'date'; $maxlen = ''; break;
                        case 'FUPAGO': $label_text = 'FUPAGO (Fecha Último Parto)'; $tipoCampo = 'date'; $maxlen = ''; break;
                        case 'FUP': $label_text = 'FUP (Fecha Último Puerperio)'; $tipoCampo = 'date'; $maxlen = ''; break;
                        case 'FUA': $label_text = 'FUA (Fecha Último Aborto)'; $tipoCampo = 'date'; $maxlen = ''; break;
                        case 'FUC': $label_text = 'FUC (Fecha Última Cesárea)'; $tipoCampo = 'date'; $maxlen = ''; break;
                        case 'FPP': $label_text = 'FPP (Fecha Probable de Parto)'; $tipoCampo = 'date'; $maxlen = ''; break;
                    }
                ?>
                <?php if ($tipoCampo === 'textarea'): ?>
                    <div class="col-md-12 mb-3"> <label for="<?= $campo ?>" class="form-label"><?= $label_text ?></label>
                        <textarea
                            name="<?= $campo ?>"
                            id="<?= $campo ?>"
                            class="form-control"
                            maxlength="<?= $maxlen ?>"
                            rows="3"
                        ><?= htmlspecialchars($datos[$campo] ?? '') ?></textarea>
                    </div>
                <?php else: ?>
                    <div class="col-md-3 mb-3">
                        <label for="<?= $campo ?>" class="form-label"><?= $label_text ?></label>
                        <input
                            type="<?= $tipoCampo ?>"
                            name="<?= $campo ?>"
                            id="<?= $campo ?>"
                            class="form-control"
                            maxlength="<?= $maxlen ?>"
                            value="<?= htmlspecialchars($datos[$campo] ?? '') ?>"
                            <?= $pattern ? "pattern=\"$pattern\"" : '' ?>
                            <?= $placeholder ? "placeholder=\"$placeholder\"" : '' ?>
                            <?= $title ? "title=\"$title\"" : '' ?>
                            <?= $dataAttr ?>
                        >
                    </div>
                <?php endif; ?>
                <?php endforeach; ?>
            </div>

            <div class="section-title">Otros Datos Relevantes</div>
            <div class="row g-3">
                <div class="col-md-3 mb-3">
                    <label for="TIPOSANGREESPOSO" class="form-label">Tipo de Sangre (Esposo)</label>
                    <select name="TIPOSANGREESPOSO" id="TIPOSANGREESPOSO" class="form-select">
                        <option value="">Seleccione</option>
                        <?php
                            $tiposSangre = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
                            foreach ($tiposSangre as $tipo):
                        ?>
                            <option value="<?= $tipo ?>" <?= ($datos['TIPOSANGREESPOSO'] ?? '') === $tipo ? 'selected' : '' ?>>
                                <?= $tipo ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <?php
                $otros_campos = ['CESARREAS', 'MOLA', 'ECTOPICO', 'DISPAREUNIA', 'SPC'];
                foreach ($otros_campos as $campo):
                    // Reiniciar valores para cada campo en el loop
                    $tipoCampo = 'text'; $maxlen = 30; $pattern = ''; $placeholder = ''; $title = ''; $dataAttr = '';
                    $label_text = ucfirst(strtolower(str_replace('_', ' ', $campo)));

                    switch ($campo) {
                        case 'CESARREAS': $label_text = 'Cesáreas'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 1'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'MOLA': $label_text = 'Mola'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 0'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'ECTOPICO': $label_text = 'Ectópico'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 0'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'DISPAREUNIA': $label_text = 'Dispareunia'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 0'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                        case 'SPC': $label_text = 'Síndrome PreMenstrual'; $maxlen = 2; $pattern = '\d{1,2}'; $placeholder = 'Ej: 0'; $title = 'Solo números (0-99)'; $dataAttr = 'data-only-digits'; break;
                    }
                ?>
                <div class="col-md-3 mb-3">
                    <label for="<?= $campo ?>" class="form-label"><?= $label_text ?></label>
                    <input
                        type="<?= $tipoCampo ?>"
                        name="<?= $campo ?>"
                        id="<?= $campo ?>"
                        class="form-control"
                        maxlength="<?= $maxlen ?>"
                        value="<?= htmlspecialchars($datos[$campo] ?? '') ?>"
                        <?= $pattern ? "pattern=\"$pattern\"" : '' ?>
                        <?= $placeholder ? "placeholder=\"$placeholder\"" : '' ?>
                        <?= $title ? "title=\"$title\"" : '' ?>
                        <?= $dataAttr ?>
                    >
                </div>
                <?php endforeach; ?>
            </div>

            <div class="section-title">Evolución</div>
            <div class="row g-3">
                <div class="col-md-12 mb-3">
                    <label for="EVOLUCION" class="form-label">Evolución</label>
                    <textarea
                        name="EVOLUCION"
                        id="EVOLUCION"
                        class="form-control"
                        maxlength="350"
                        rows="5"
                    ><?= htmlspecialchars($datos['EVOLUCION'] ?? '') ?></textarea>
                </div>
            </div>

            <hr class="my-4">

            <div class="d-flex justify-content-start gap-2">
                <button type="submit" class="btn btn-success btn-lg">💾 Guardar</button>
                <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>" class="btn btn-secondary btn-lg">Cancelar</a>
                <a href="imprimir_ginecoobstetrico.php?CLAVEPAC=<?= $CLAVEPAC ?>" target="_blank" class="btn btn-outline-primary btn-lg">🖨️ Imprimir</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Validar solo números (0-9)
            document.querySelectorAll('input[data-only-digits]').forEach(input => {
                input.addEventListener('keypress', function (e) {
                    if (!/[0-9]/.test(e.key)) {
                        e.preventDefault();
                    }
                });
            });

            // Validar alfanumérico (letras y números)
            document.querySelectorAll('input[data-alphanumeric]').forEach(input => {
                input.addEventListener('keypress', function (e) {
                    if (!/[0-9a-zA-Z]/.test(e.key)) {
                        e.preventDefault();
                    }
                });
            });

            // Validar alfanumérico con guiones y slash (letras, números, / y -)
            document.querySelectorAll('input[data-alphanumeric-dash]').forEach(input => {
                input.addEventListener('keypress', function (e) {
                    if (!/[0-9a-zA-Z\/\-]/.test(e.key)) { // Agregado / y -
                        e.preventDefault();
                    }
                });
            });
            // La validación original de 'data-letters-only' fue reemplazada por 'data-alphanumeric-dash' o eliminada si no aplica
            // Si necesitas una validación estricta de solo letras y espacios, avisame para añadirla
        });
    </script>
</body>
</html>