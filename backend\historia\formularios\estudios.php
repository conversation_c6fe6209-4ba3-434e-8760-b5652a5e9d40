<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Estudios" en Indicaciones
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Función para obtener los estudios indicados al paciente
function getEstudios($pdo, $clavePac) {
    $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
            DESCRIPCION as descripcion, ESTADO as estado, 
            TIPO as tipo, PRIORIDAD as prioridad, NOTAS as notas 
            FROM ESTUDIOS_INDICADOS WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    if ($action === 'saveEstudio') {
        $descripcion = $_POST['descripcion'];
        $estado = $_POST['estado'];
        $tipo = $_POST['tipo'] ?? null;
        $prioridad = $_POST['prioridad'] ?? 'Normal';
        $notas = $_POST['notas'] ?? null;
        
        try {
            $sql = "INSERT INTO ESTUDIOS_INDICADOS (CLAVEPAC, DESCRIPCION, ESTADO, TIPO, PRIORIDAD, NOTAS) 
                VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$clavePac, $descripcion, $estado, $tipo, $prioridad, $notas]);
            
            $id = $pdo->lastInsertId();
            
            echo json_encode([
                'success' => true, 
                'id' => $id,
                'fecha' => date('Y-m-d')
            ]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'updateEstadoEstudio') {
        $id = intval($_POST['id']);
        $estado = $_POST['estado'];
        $notas = $_POST['notas'] ?? null;
        
        try {
            $sql = "UPDATE ESTUDIOS_INDICADOS SET ESTADO = ?, NOTAS = ? WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$estado, $notas, $id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteEstudio') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "DELETE FROM ESTUDIOS_INDICADOS WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Obtener lista de estudios
$estudios = getEstudios($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Estudios Indicados</h4>
    
    <div class="row">
        <div class="col-md-5">
            <div class="card">
                <div class="card-header">
                    Nuevo Estudio
                </div>
                <div class="card-body">
                    <form id="formEstudio">
                        <div class="form-group">
                            <label for="descripcionEstudio">Descripción del Estudio</label>
                            <textarea class="form-control" id="descripcionEstudio" rows="3" placeholder="Describa el estudio a realizar"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="tipoEstudio">Tipo de Estudio</label>
                            <select class="form-control" id="tipoEstudio" name="tipo">
                                <option value="Laboratorio">Laboratorio</option>
                                <option value="Imagen">Imagen</option>
                                <option value="Procedimiento">Procedimiento</option>
                                <option value="Otro">Otro</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Prioridad</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="prioridadEstudio" id="prioridadNormal" value="Normal" checked>
                                <label class="form-check-label" for="prioridadNormal">Normal</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="prioridadEstudio" id="prioridadUrgente" value="Urgente">
                                <label class="form-check-label" for="prioridadUrgente">Urgente</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notasEstudio">Notas Adicionales</label>
                            <textarea class="form-control" id="notasEstudio" rows="2"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Estado</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="estadoEstudio" id="estadoPendiente" value="Pendiente" checked>
                                <label class="form-check-label" for="estadoPendiente">Pendiente</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="estadoEstudio" id="estadoRealizado" value="Realizado">
                                <label class="form-check-label" for="estadoRealizado">Realizado</label>
                            </div>
                        </div>
                        <button type="button" class="btn btn-primary" id="btnGuardarEstudio">Guardar Estudio</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-7">
            <div class="card">
                <div class="card-header">
                    Estudios Indicados
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Fecha</th>
                                    <th>Descripción</th>
                                    <th>Tipo</th>
                                    <th>Prioridad</th>
                                    <th>Estado</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody id="tablaEstudios">
                                <?php if (count($estudios) > 0): ?>
                                    <?php foreach ($estudios as $estudio): ?>
                                        <tr data-id="<?php echo $estudio['id']; ?>">
                                            <td><?php echo $estudio['fecha']; ?></td>
                                            <td>
                                                <?php echo htmlspecialchars($estudio['descripcion']); ?>
                                                <?php if (!empty($estudio['notas'])): ?>
                                                    <small class="d-block text-muted"><?php echo htmlspecialchars($estudio['notas']); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($estudio['tipo'] ?? 'No especificado'); ?></td>
                                            <td><?php echo htmlspecialchars($estudio['prioridad'] ?? 'Normal'); ?></td>
                                            <td>
                                                <select class="form-control estado-estudio">
                                                    <option value="Pendiente" <?php echo ($estudio['estado'] == 'Pendiente') ? 'selected' : ''; ?>>Pendiente</option>
                                                    <option value="Realizado" <?php echo ($estudio['estado'] == 'Realizado') ? 'selected' : ''; ?>>Realizado</option>
                                                </select>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-danger btn-eliminar-estudio">Eliminar</button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center">No hay estudios indicados</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Guardar nuevo estudio
    $('#btnGuardarEstudio').click(function() {
        const descripcion = $('#descripcionEstudio').val();
        if (!descripcion) {
            alert('Por favor ingrese la descripción del estudio');
            return;
        }
        
        const estado = $('input[name="estadoEstudio"]:checked').val();
        const tipo = $('#tipoEstudio').val();
        const prioridad = $('input[name="prioridadEstudio"]:checked').val();
        const notas = $('#notasEstudio').val();
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'saveEstudio',
                descripcion: descripcion,
                estado: estado,
                tipo: tipo,
                prioridad: prioridad,
                notas: notas
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Limpiar formulario
                    $('#descripcionEstudio').val('');
                    $('#estadoPendiente').prop('checked', true);
                    $('#tipoEstudio').val('Laboratorio');
                    $('#prioridadNormal').prop('checked', true);
                    $('#notasEstudio').val('');
                
                    // Agregar a la tabla
                    const notasHtml = notas ? `<small class="d-block text-muted">${notas}</small>` : '';
                    
                    const newRow = `
                        <tr data-id="${response.id}">
                            <td>${response.fecha}</td>
                            <td>
                                ${descripcion}
                                ${notasHtml}
                            </td>
                            <td>${tipo}</td>
                            <td>${prioridad}</td>
                            <td>
                                <select class="form-control estado-estudio">
                                    <option value="Pendiente" ${estado === 'Pendiente' ? 'selected' : ''}>Pendiente</option>
                                    <option value="Realizado" ${estado === 'Realizado' ? 'selected' : ''}>Realizado</option>
                                </select>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-danger btn-eliminar-estudio">Eliminar</button>
                            </td>
                        </tr>
                    `;
                    
                    // Si la tabla está vacía, eliminar la fila de "No hay estudios"
                    if ($('#tablaEstudios tr td').length === 1 && $('#tablaEstudios tr td').text().includes('No hay estudios')) {
                        $('#tablaEstudios').empty();
                    }
                    
                    $('#tablaEstudios').prepend(newRow);
                    
                    alert('Estudio guardado correctamente');
                } else {
                    alert('Error al guardar estudio: ' + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al guardar estudio:', error);
                alert('Error al guardar estudio. Consulte la consola para más detalles.');
            }
        });
    });
    
    // Actualizar estado de estudio
    $(document).on('change', '.estado-estudio', function() {
        const id = $(this).closest('tr').data('id');
        const estado = $(this).val();
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'updateEstadoEstudio',
                id: id,
                estado: estado
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // No es necesario hacer nada, la interfaz ya se actualizó
                } else {
                    alert('Error al actualizar estado: ' + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al actualizar estado:', error);
                alert('Error al actualizar estado. Consulte la consola para más detalles.');
            }
        });
    });
    
    // Eliminar estudio
    $(document).on('click', '.btn-eliminar-estudio', function() {
        if (confirm('¿Está seguro de eliminar este estudio?')) {
            const row = $(this).closest('tr');
            const id = row.data('id');
            
            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: {
                    action: 'deleteEstudio',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        row.remove();
                        
                        // Si no quedan filas, mostrar mensaje
                        if ($('#tablaEstudios tr').length === 0) {
                            $('#tablaEstudios').html('<tr><td colspan="6" class="text-center">No hay estudios indicados</td></tr>');
                        }
                        
                        alert('Estudio eliminado correctamente');
                    } else {
                        alert('Error al eliminar estudio: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error al eliminar estudio:', error);
                    alert('Error al eliminar estudio. Consulte la consola para más detalles.');
                }
            });
        }
    });
});
</script>
