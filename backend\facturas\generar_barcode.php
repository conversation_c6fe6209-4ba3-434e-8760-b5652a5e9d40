<?php
require_once __DIR__ . '/barcode/BarcodeGenerator.php';
require_once __DIR__ . '/barcode/BarcodeGeneratorPNG.php';

use Picqer\Barcode\BarcodeGeneratorPNG;

try {
    $valor = $_GET['valor'] ?? '000000';

    header('Content-Type: image/png');

    $generator = new BarcodeGeneratorPNG();
    echo $generator->getBarcode($valor, $generator::TYPE_CODE_128, 2, 60);
} catch (Exception $e) {
    echo "Error generando código: " . $e->getMessage();
}
