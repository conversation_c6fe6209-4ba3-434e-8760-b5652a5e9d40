<?php
require_once '../config/database.php';

$pdo = include '../config/database.php';

$query = trim($_GET['q'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

if (strlen($query) < 3) {
    echo json_encode([]);
    exit;
}

$sql = "SELECT CLAVE, NOMBRES, APELLIDOS, CEDULA  FROM PACIENTES 
        WHERE NOMBRES LIKE :q OR APELLIDOS LIKE :q 
        OR CEDULA = :cedula
        LIMIT :limit OFFSET :offset";

$stmt = $pdo->prepare($sql);
$stmt->bindValue(':q', "%$query%", PDO::PARAM_STR);
$stmt->bindValue(':cedula', $query, PDO::PARAM_STR);
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();

$pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode(array_map(function($p) {
    return [
        'clave' => $p['CLAVE'],
        'nombre' => $p['NOMBRES'] . ' ' . $p['APELLIDOS'],
        'cedula' => $p['CEDULA']
    ];
}, $pacientes));