<?php
require_once '../config/database.php';
$pdo = include '../config/database.php';

header('Content-Type: application/json');

$clave = $_GET['clave'] ?? null;

if (!$clave) {
  echo json_encode(['status' => 'error', 'message' => 'Falta la clave de la factura']);
  exit;
}

try {
  $stmt = $pdo->prepare("SELECT * FROM FACTURAS WHERE CLAVE = ?");
  $stmt->execute([$clave]);
  $factura = $stmt->fetch();

  if ($factura) {
    echo json_encode(['status' => 'success', 'factura' => $factura]);
  } else {
    echo json_encode(['status' => 'error', 'message' => 'Factura no encontrada']);
  }
} catch (PDOException $e) {
  echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
