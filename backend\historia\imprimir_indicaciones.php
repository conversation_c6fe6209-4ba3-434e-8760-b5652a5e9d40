<?php
/**
 * Sistema de Impresión Profesional para Indicaciones Médicas
 * Genera plantillas médicas profesionales para Estudios, Informes, Prescripción y Certificados
 * 
 * <AUTHOR> de Consultorio
 * @version 1.0
 */

session_start();
require_once __DIR__ . '/../config/database.php';

// Obtener parámetros
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA = $_GET['CEDULA'] ?? '';
$CLAVE = $_GET['CLAVE'] ?? '';
$TABLA = strtolower($_GET['TABLA'] ?? '');
$tipo_impresion = $_GET['tipo'] ?? 'individual'; // individual, completo, resumen

// Configuración de tablas
$tablas_config = [
    'estudios' => [
        'tabla_real' => 'ESTUDIOS',
        'campo' => 'DESCRIPCION',
        'titulo' => 'Estudios Médicos',
        'icono' => 'fas fa-microscope',
        'color' => '#1e40af'
    ],
    'informes' => [
        'tabla_real' => 'INFORMES',
        'campo' => 'INFORME',
        'titulo' => 'Informes Médicos',
        'icono' => 'fas fa-file-medical-alt',
        'color' => '#059669'
    ],
    'prescripcion' => [
        'tabla_real' => 'PRESCRIPCION',
        'campo' => 'DESCRIPCION',
        'titulo' => 'Prescripción Médica',
        'icono' => 'fas fa-prescription-bottle-alt',
        'color' => '#dc2626'
    ],
    'seguimiento' => [
        'tabla_real' => 'SEGUIMIENTO',
        'campo' => 'SEGUIMIENTO',
        'titulo' => 'Seguimiento del Paciente',
        'icono' => 'fas fa-chart-line',
        'color' => '#8b5cf6'
    ],
    'diagnostico' => [
        'tabla_real' => 'DIAGNOSTICO',
        'campo' => 'TEXTO',
        'titulo' => 'Diagnóstico',
        'icono' => 'fas fa-stethoscope',
        'color' => '#f59e0b'
    ],
    'tratamientos' => [
        'tabla_real' => 'TRATAMIENTOS',
        'campo' => 'TRATAMIENTO',
        'titulo' => 'Tratamientos',
        'icono' => 'fas fa-pills',
        'color' => '#10b981'
    ],
    'complementarias' => [
        'tabla_real' => 'COMPLEMENTARIAS',
        'campo' => 'COMPLEMENTARIOS',
        'titulo' => 'Información Complementaria',
        'icono' => 'fas fa-info-circle',
        'color' => '#06b6d4'
    ],
    'enfermedad_actual' => [
        'tabla_real' => 'ENFERMEDAD_ACTUAL',
        'campo' => 'TEXTO',
        'titulo' => 'Enfermedad Actual',
        'icono' => 'fas fa-heartbeat',
        'color' => '#ef4444'
    ],
    'certificado' => [
        'tabla_real' => 'CERTIFICADO',
        'campo' => 'DIAGCERT',
        'titulo' => 'Certificado Médico',
        'icono' => 'fas fa-certificate',
        'color' => '#1e40af'
    ]
];

// Validar tabla
if (!isset($tablas_config[$TABLA])) {
    die("Error: Tabla no válida");
}

$config = $tablas_config[$TABLA];

// Obtener información del paciente
$stmt_paciente = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
$stmt_paciente->execute([$CLAVEPAC]);
$paciente = $stmt_paciente->fetch(PDO::FETCH_ASSOC);

if (!$paciente) {
    die("Error: Paciente no encontrado");
}

// Calcular edad
$edad = '';
if (!empty($paciente['FECHANAC'])) {
    $nac = new DateTime($paciente['FECHANAC']);
    $hoy = new DateTime();
    $edad = $hoy->diff($nac)->y . ' años';
}

// Obtener información de la empresa/consultorio
$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

// Obtener datos según el tipo de impresión
$datos = [];
if ($tipo_impresion === 'individual' && $CLAVE) {
    // Imprimir un registro específico
    $stmt = $pdo->prepare("SELECT * FROM {$config['tabla_real']} WHERE CLAVE = ?");
    $stmt->execute([$CLAVE]);
    $registro = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($registro) {
        $datos[] = $registro;
    }
} elseif ($tipo_impresion === 'completo') {
    // Imprimir todos los registros del paciente para esta tabla
    $stmt = $pdo->prepare("SELECT * FROM {$config['tabla_real']} WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC");
    $stmt->execute([$CLAVEPAC]);
    $datos = $stmt->fetchAll(PDO::FETCH_ASSOC);
} elseif ($tipo_impresion === 'resumen') {
    // Imprimir resumen de todas las indicaciones
    foreach ($tablas_config as $tabla_key => $tabla_config) {
        $stmt = $pdo->prepare("SELECT * FROM {$tabla_config['tabla_real']} WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC LIMIT 3");
        $stmt->execute([$CLAVEPAC]);
        $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($registros)) {
            $datos[$tabla_key] = [
                'config' => $tabla_config,
                'registros' => $registros
            ];
        }
    }
}

// Función para formatear fecha
function formatearFecha($fecha) {
    if (empty($fecha)) return '';
    $dt = new DateTime($fecha);
    return $dt->format('d/m/Y H:i');
}

// Función para formatear fecha solo día
function formatearFechaSolo($fecha) {
    if (empty($fecha)) return '';
    $dt = new DateTime($fecha);
    return $dt->format('d/m/Y');
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $config['titulo']; ?> - <?php echo htmlspecialchars($paciente['NOMBREAPELLIDO']); ?></title>
    
    <!-- Bootstrap para impresión -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Estilos para impresión médica profesional */
        @page {
            size: A4;
            margin: 1.5cm;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #000;
        }
        
        .medical-header {
            border-bottom: 3px solid <?php echo $config['color']; ?>;
            padding-bottom: 1rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .clinic-info {
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .clinic-name {
            font-size: 18pt;
            font-weight: bold;
            color: <?php echo $config['color']; ?>;
            margin-bottom: 0.5rem;
        }
        
        .clinic-details {
            font-size: 10pt;
            color: #666;
        }
        
        .document-title {
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            margin: 1.5rem 0;
            color: <?php echo $config['color']; ?>;
            text-transform: uppercase;
        }
        
        .patient-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .patient-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .info-item {
            display: flex;
            align-items: center;
        }
        
        .info-label {
            font-weight: bold;
            margin-right: 0.5rem;
            min-width: 80px;
        }
        
        .content-section {
            margin-bottom: 2rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .content-header {
            background: <?php echo $config['color']; ?>;
            color: white;
            padding: 0.75rem 1rem;
            font-weight: bold;
            font-size: 11pt;
        }
        
        .content-body {
            padding: 1.5rem;
            min-height: 100px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .footer-info {
            margin-top: 3rem;
            padding-top: 1rem;
            border-top: 1px solid #ccc;
            font-size: 10pt;
            color: #666;
        }
        
        .signature-section {
            margin-top: 4rem;
            text-align: center;
        }
        
        .signature-line {
            border-top: 1px solid #000;
            width: 300px;
            margin: 3rem auto 0.5rem;
        }
        
        .doctor-info {
            font-size: 11pt;
            font-weight: bold;
        }
        
        /* Estilos para pantalla */
        @media screen {
            body {
                background: #f5f5f5;
                padding: 2rem;
            }
            
            .print-container {
                background: white;
                max-width: 21cm;
                margin: 0 auto;
                padding: 2cm;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                border-radius: 8px;
            }
            
            .no-print {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
            }
        }
        
        /* Ocultar elementos en impresión */
        @media print {
            .no-print {
                display: none !important;
            }
            
            .print-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
        }
        
        /* Estilos para resumen */
        .summary-section {
            margin-bottom: 1.5rem;
            page-break-inside: avoid;
        }
        
        .summary-header {
            background: linear-gradient(135deg, <?php echo $config['color']; ?>, #6c757d);
            color: white;
            padding: 0.5rem 1rem;
            font-weight: bold;
            font-size: 10pt;
            border-radius: 4px 4px 0 0;
        }
        
        .summary-content {
            border: 1px solid #dee2e6;
            border-top: none;
            padding: 1rem;
            font-size: 10pt;
            border-radius: 0 0 4px 4px;
        }
    </style>
</head>
<body>
    <!-- Botones de control (solo en pantalla) -->
    <div class="no-print">
        <div class="btn-group">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> Imprimir
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cerrar
            </button>
        </div>
    </div>

    <div class="print-container">
        <!-- Header médico -->
        <div class="medical-header">
            <div class="clinic-info">
                <?php if ($empresa): ?>
                    <div class="clinic-name"><?php echo htmlspecialchars($empresa['NOMBRE']); ?></div>

                    <!-- Especialidad del médico -->
                    <?php if (!empty($empresa['ESPECIALIDAD'])): ?>
                        <div style="font-size: 14pt; font-weight: bold; color: <?php echo $config['color']; ?>; margin: 0.5rem 0;">
                            <?php echo htmlspecialchars($empresa['ESPECIALIDAD']); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Exequatur -->
                    <?php if (!empty($empresa['EXECQUATUR'])): ?>
                        <div style="font-size: 12pt; color: #333; margin: 0.3rem 0;">
                            EXEQUATUR <?php echo htmlspecialchars($empresa['EXECQUATUR']); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($empresa['CALLE'])): ?>
                        <div class="clinic-details">
                            <?php echo htmlspecialchars($empresa['CALLE']); ?>
                            <?php if (!empty($empresa['TELEFONO'])): ?>
                                | Tel: <?php echo htmlspecialchars($empresa['TELEFONO']); ?>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="clinic-name">CONSULTORIO MÉDICO</div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Título del documento -->
        <div class="document-title">
            <i class="<?php echo $config['icono']; ?>"></i>
            <?php echo $config['titulo']; ?>
        </div>

        <!-- Información del paciente -->
        <div class="patient-info">
            <div class="patient-info-grid">
                <div class="info-item">
                    <span class="info-label">Paciente:</span>
                    <span><?php echo htmlspecialchars($paciente['NOMBREAPELLIDO']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Cédula:</span>
                    <span><?php echo htmlspecialchars($paciente['CEDULA']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Edad:</span>
                    <span><?php echo $edad; ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Fecha:</span>
                    <span><?php echo date('d/m/Y'); ?></span>
                </div>
            </div>
        </div>

        <!-- Contenido según tipo de impresión -->
        <?php if ($tipo_impresion === 'resumen'): ?>
            <!-- Resumen de todas las indicaciones -->
            <h4 style="text-align: center; margin-bottom: 2rem; color: <?php echo $config['color']; ?>;">
                RESUMEN DE INDICACIONES MÉDICAS
            </h4>
            
            <?php foreach ($datos as $tabla_key => $tabla_data): ?>
                <div class="summary-section">
                    <div class="summary-header">
                        <i class="<?php echo $tabla_data['config']['icono']; ?>"></i>
                        <?php echo $tabla_data['config']['titulo']; ?>
                    </div>
                    <div class="summary-content">
                        <?php foreach ($tabla_data['registros'] as $registro): ?>
                            <div style="margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid #eee;">
                                <strong>Fecha:</strong> <?php echo formatearFecha($registro['FECHA_CAP']); ?><br>
                                <div style="margin-top: 0.5rem;">
                                    <?php echo nl2br(htmlspecialchars(substr($registro[$tabla_data['config']['campo']], 0, 200))); ?>
                                    <?php if (strlen($registro[$tabla_data['config']['campo']]) > 200): ?>
                                        <em>... (continúa)</em>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
            
        <?php else: ?>
            <!-- Impresión individual o completa -->
            <?php foreach ($datos as $registro): ?>
                <div class="content-section">
                    <div class="content-header">
                        <i class="<?php echo $config['icono']; ?>"></i>
                        <?php echo $config['titulo']; ?>
                        <?php if (!empty($registro['FECHA_CAP'])): ?>
                            - <?php echo formatearFecha($registro['FECHA_CAP']); ?>
                        <?php endif; ?>
                    </div>
                    <div class="content-body">
                        <?php echo nl2br(htmlspecialchars($registro[$config['campo']])); ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <!-- Información del pie -->
        <div class="footer-info">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                <div>
                    <strong>Fecha de emisión:</strong> <?php echo date('d/m/Y H:i'); ?><br>
                    <strong>Documento generado por:</strong> Sistema de Historia Clínica
                </div>
                <div style="text-align: right;">
                    <strong>Paciente:</strong> <?php echo htmlspecialchars($paciente['NOMBREAPELLIDO']); ?><br>
                    <strong>CI:</strong> <?php echo htmlspecialchars($paciente['CEDULA']); ?>
                </div>
            </div>
        </div>

        <!-- Sección de firma -->
        <div class="signature-section">
            <div class="signature-line"></div>
            <div class="doctor-info">
                Dr. <?php echo htmlspecialchars($_SESSION['usuario'] ?? 'Médico Tratante'); ?><br>
                <small>Firma y Sello</small>
            </div>
        </div>
    </div>

    <script>
        // Auto-imprimir si se especifica en la URL
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            window.onload = function() {
                setTimeout(() => {
                    window.print();
                }, 500);
            };
        }
    </script>
</body>
</html>
