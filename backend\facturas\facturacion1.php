<?php
// Conexión a la base de datos
$pdo = require_once '../config/database.php';
$config = include '../config/config.php';

$mensaje = null;





$pdo = include '../config/database.php';

try {
    $stmt = $pdo->query("SELECT NUMFACT, FECHA, CONCEPTO, PRECIO, PAGADO, VALORRECLAMADO, ESTATUS FROM FACTURAS ORDER BY NUMFACT DESC LIMIT 20");
    $facturas = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $facturas = [];
}




if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Captura de datos del formulario
        $concepto        = $_POST['concepto'] ?? '';
        $modopago        = $_POST['modopago'] ?? '';
        $fechapago       = $_POST['fechapago'] ?? null;
        $precio          = $_POST['precio'] ?? 0;
        $pagado          = $_POST['pagado'] ?? 0;
        $pendiente       = $_POST['pendiente'] ?? 0;
        $fars            = $_POST['FARS'] ?? null;
        $noautorizacion  = $_POST['noautorizacion'] ?? '';
        $procedimiento   = $_POST['procedimiento'] ?? '';
        $valorreclamado  = $_POST['valorreclamado'] ?? 0;
        $credito         = isset($_POST['credito']) ? '1' : '0';

        // Datos fijos o simulados por ahora
        $clavepac        = 6;
        $numfact         = 1001;
        $estatus         = 'A';
        $streclama       = 'N';
        $fecha           = date('Y-m-d');
        $hora            = date('H:i:s');
        $usuario         = 1;
        $consultorio     = 1;
        $usuario = 1; 
        // Inserción
        $sql = "INSERT INTO FACTURAS (
            CLAVEPAC, NUMFACT, FARS, PRECIO, PAGADO, NOPAGO, CONCEPTO,
            FECHAPAGO, MODOPGO, ESTATUS, NOAUTORIZACION,
            VALORRECLAMADO, PROCEDIMIENTOS, STRECLAMA, FECHA, HORA, USUARIO, CONSULTORIO
        ) VALUES (
            :clavepac, :numfact, :fars, :precio, :pagado, :pendiente, :concepto,
            :fechapago, :modopago, :estatus, :noautorizacion,
            :valorreclamado, :procedimiento, :streclama, :fecha, :hora, :usuario, :consultorio
        )";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':clavepac'        => $clavepac,
            ':numfact'         => $numfact,
            ':fars'            => $fars,
            ':precio'          => $precio,
            ':pagado'          => $pagado,
            ':pendiente'       => $pendiente,
            ':concepto'        => $concepto,
            ':fechapago'       => $fechapago,
            ':modopago'        => $modopago,
            ':estatus'         => $estatus,
            ':noautorizacion'  => $noautorizacion,
            ':valorreclamado'  => $valorreclamado,
            ':procedimiento'   => $procedimiento,
            ':streclama'       => $streclama,
            ':fecha'           => $fecha,
            ':hora'            => $hora,
            ':usuario'         => $usuario,
            ':consultorio'     => $consultorio
        ]);

        $mensaje = "✅ Factura guardada correctamente.";

    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar factura: " . $e->getMessage();
    }
}

// Obtener ARS
$stmt_Ars = $pdo->query("SELECT CLAVE, NOMBRE FROM ASEGURADORA ORDER BY NOMBRE");
$arsList = $stmt_Ars->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Factura de Paciente</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>


<body>
<div class="container mt-4">
    <h4 class="mb-3">Factura de Paciente</h4>

    <?php if ($mensaje): ?>
        <div class="alert alert-info"><?= htmlspecialchars($mensaje) ?></div>
    <?php endif; ?>

 <!-- Tabla de Facturas -->
    <table class="table table-bordered table-sm mb-4">
        <thead class="table-light">
            <tr>
                <th>NUMFact</th>
                <th>Fecha</th>
                <th>Concepto</th>
                <th>Precio</th>
                <th>Pagado</th>
                <th>Reclam. ARS</th>
                <th>ST</th>
            </tr>
        </thead>
         <tbody>
<?php if (empty($facturas)): ?>
  <tr>
    <td colspan="7" class="text-center text-muted">No hay facturas registradas.</td>
  </tr>
<?php else: ?>
  <?php foreach ($facturas as $fact): ?>
    <tr>
      <td><?= htmlspecialchars($fact['NUMFACT']) ?></td>
      <td><?= htmlspecialchars(date('d/m/Y', strtotime($fact['FECHAPAGO'] ?? $fact['FECHA'] ?? ''))) ?></td>
      <td><?= htmlspecialchars($fact['CONCEPTO']) ?></td>
      <td class="text-end"><?= number_format($fact['PRECIO'], 2) ?></td>
      <td class="text-end"><?= number_format($fact['PAGADO'], 2) ?></td>
      <td class="text-end"><?= number_format($fact['VALORRECLAMADO'], 2) ?></td>
      <td><?= $fact['ESTATUS'] === 'A' ? '✔️' : '🕓' ?></td>
    </tr>
  <?php endforeach; ?>
<?php endif; ?>
</tbody>
    </table>


    <form method="post">
        <div class="row mb-3">
            <div class="col-md-6">
                
 
 <!-- 🔍 Buscador de pacientes con sugerencias -->
<div class="mb-3 position-relative">
  <label for="busquedaPaciente" class="form-label">Buscar Paciente (Nombre, Cédula o NSS)</label>
  <input type="text" class="form-control" id="busquedaPaciente" autocomplete="off">
  <div id="sugerencias" class="list-group position-absolute w-100" style="z-index: 1000;"></div>
</div>


<div id="datosPaciente" class="mb-4" style="display:none;">
  <h5>Datos del Paciente</h5>
  <p><strong>Nombre:</strong> <span id="nombrePaciente"></span></p>
  <p><strong>Cédula:</strong> <span id="cedulaPaciente"></span></p>
  <p><strong>Teléfono:</strong> <span id="telefonoPaciente"></span></p>
  <p><strong>ARS:</strong> <span id="arsPaciente"></span></p>
</div> 
 

<!-- Campos ocultos necesarios -->
<input type="hidden" name="CLAVEPAC" id="clavepac"> 
<input type="hidden" name="NUMFACT" id="numfact">
<p><strong>Factura No:</strong> <span id="facturaNo">Cargando...</span></p>         
        
                
                <label class="form-label">Concepto</label>
                <input type="text" class="form-control" name="concepto" required>
            </div>
            <div class="col-md-3">
                <label class="form-label">Modo de pago</label>
                <select class="form-select" name="modopago" required>
                    <option value="Efectivo">Efectivo</option>
                    <option value="Tarjeta">Tarjeta</option>
                    <option value="Transferencia">Transferencia</option>
                    <option value="Otro">Otro</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Fecha Pago</label>
                <input type="date" class="form-control" name="fechapago" required>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col">
                <label class="form-check-label">
                    <input type="checkbox" class="form-check-input" name="credito"> A Crédito
                </label>
            </div>
            <div class="col">
                <label class="form-label">Precio</label>
                <input type="number" class="form-control" name="precio" step="0.01" required>
            </div>
            <div class="col">
                <label class="form-label">Pagado</label>
                <input type="number" class="form-control" name="pagado" step="0.01" required>
            </div>
            <div class="col">
                <label class="form-label">Pendiente</label>
                <input type="number" class="form-control" name="pendiente" step="0.01" required>
            </div>
        </div>

        <!-- ARS -->
        <fieldset class="border p-3 mb-3">
            <legend class="float-none w-auto px-2">ASEGURADORA ARS</legend>
            <div class="row mb-3">
                <div class="col-md-6">
                    <label class="form-label">ARS</label>
                    <select class="form-select" name="FARS" required>
                        <option value="">Seleccione una ARS</option>
                        <?php foreach ($arsList as $ars): ?>
                            <option value="<?= $ars['CLAVE'] ?>"><?= htmlspecialchars($ars['NOMBRE']) ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <label class="form-label">No. Autorización</label>
                    <input type="text" class="form-control" name="noautorizacion">
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <label class="form-label">Procedimiento Aplicado</label>
                    <input type="text" class="form-control" name="procedimiento">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Valor Reclamado</label>
                    <input type="number" class="form-control" name="valorreclamado" step="0.01">
                </div>
            </div>
        </fieldset>


<div class="mt-4" id="datosPaciente" style="display: none;">
  <p><strong>Paciente No:</strong> <span id="pacienteNo"></span></p>
  <p><strong>Cédula ID:</strong> <span id="cedula"></span></p>
  <p><strong>Nombre Paciente:</strong> <span id="nombre"></span></p>
  <p><strong>NSS:</strong> <span id="nss"></span></p>
  <p><strong>No. Afiliado:</strong> <span id="afiliado"></span></p>
</div>


        <div class="d-flex justify-content-end gap-2">
            <button type="submit" class="btn btn-primary">Guardar</button>
            <button type="reset" class="btn btn-secondary">Cancelar</button>
        </div>

        
    </form>
</div>

<script>
document.getElementById('busquedaPaciente').addEventListener('input', async function () {
  const query = this.value.trim();
  const sugerencias = document.getElementById('sugerencias');
  sugerencias.innerHTML = '';

  if (query.length < 2) return;

  const res = await fetch('buscar_paciente.php?q=' + encodeURIComponent(query));
  const data = await res.json();

  data.forEach(p => {
    const item = document.createElement('a');
    item.href = '#';
    item.className = 'list-group-item list-group-item-action';
    item.textContent = `${p.nombre} (${p.cedula})`;
    item.onclick = () => {
      // Asignar valores al formulario
      document.getElementById('busquedaPaciente').value = p.nombre;
      document.getElementById('clavepac').value = p.clave;
      document.getElementById('pacienteNo').textContent = p.clave;
      document.getElementById('cedula').textContent = p.cedula;
      document.getElementById('nombre').textContent = p.nombre;
      document.getElementById('nss').textContent = p.nss;
      document.getElementById('afiliado').textContent = p.afiliado;
      document.getElementById('datosPaciente').style.display = 'block';
      sugerencias.innerHTML = '';
    };
    sugerencias.appendChild(item);
  });
});



// Autogenerar número de factura
async function cargarNumeroFactura() {
  const res = await fetch('obtener_ultimo_numfact.php');
  const data = await res.json();
  const proximo = data.ultimo ? parseInt(data.ultimo) + 1 : 1;

  document.getElementById('numfact').value = proximo;
  const mostrar = document.getElementById('facturaNo');
  if (mostrar) mostrar.textContent = proximo;
}

cargarNumeroFactura();



function buscarPaciente() {
  const cedula = document.getElementById('buscarPaciente').value.trim();
  if (!cedula) return alert('Ingrese una cédula o nombre');

  fetch(`/backend/api/get_paciente_info.php?cedula=${cedula}`)
    .then(res => res.json())
    .then(data => {
      if (data.status !== 'success') {
        alert('Paciente no encontrado');
        return;
      }

      const p = data.paciente;
      document.getElementById('infoPaciente').classList.remove('d-none');
      document.getElementById('nombrePaciente').textContent = p.nombre;
      document.getElementById('cedulaPaciente').textContent = p.cedula;
      document.getElementById('telefonoPaciente').textContent = p.telefono || 'N/D';
      document.getElementById('arsPaciente').textContent = p.ars?.nombre || 'N/D';

      // Setear valores ocultos del form
      document.querySelector('[name="CLAVEPAC"]').value = p.clave;

      // Limpiar y renderizar facturas
      const tbody = document.querySelector('#tablaFacturas tbody');
      tbody.innerHTML = '';
      data.facturas.forEach(f => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
          <td>${f.NUMFACT}</td>
          <td>${f.FECHA}</td>
          <td>${f.CONCEPTO}</td>
          <td>${f.PRECIO}</td>
          <td>${f.PAGADO}</td>
          <td>${f.VALORRECLAMADO}</td>
          <td>${f.ESTATUS}</td>
        `;
        tbody.appendChild(tr);
      });
    })
    .catch(err => {
      console.error('Error:', err);
      alert('Error al buscar paciente');
    });
}
</script>




</body>
</html>

