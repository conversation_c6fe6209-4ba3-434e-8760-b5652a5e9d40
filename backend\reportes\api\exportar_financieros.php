<?php
// backend/reportes/api/exportar_financieros.php
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario'])) {
    http_response_code(401);
    die('No autorizado');
}

require_once __DIR__ . '/../../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

// Obtener parámetros
$formato = $_GET['formato'] ?? 'excel';
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$estado_pago = $_GET['estado_pago'] ?? '';
$metodo_pago = $_GET['metodo_pago'] ?? '';
$ars = $_GET['ars'] ?? '';

try {
    // Construir consulta
    $where_conditions = ["DATE(FECHA) BETWEEN ? AND ?"];
    $params = [$fecha_inicio, $fecha_fin];

    if ($estado_pago == 'pagado') {
        $where_conditions[] = "NOPAGO = 0";
    } elseif ($estado_pago == 'pendiente') {
        $where_conditions[] = "NOPAGO > 0";
    } elseif ($estado_pago == 'parcial') {
        $where_conditions[] = "PAGADO > 0 AND NOPAGO > 0";
    }

    if ($metodo_pago) {
        $where_conditions[] = "MODOPGO = ?";
        $params[] = $metodo_pago;
    }

    if ($ars !== '') {
        $where_conditions[] = "FARS = ?";
        $params[] = $ars;
    }

    $where_clause = "WHERE " . implode(" AND ", $where_conditions) . " AND ESTATUS = 'A'";

    // Obtener datos
    $sql = "SELECT f.FECHA, f.NUMFACT, p.NOMBREAPELLIDO, p.CEDULA, f.CONCEPTO, 
                  f.PRECIO, f.PAGADO, f.NOPAGO, f.MODOPGO, f.FARS,
                  CASE WHEN f.NOPAGO = 0 THEN 'Pagado' 
                       WHEN f.PAGADO = 0 THEN 'Pendiente' 
                       ELSE 'Parcial' END as estado_pago,
                  CASE WHEN f.FARS = 0 THEN 'Particular' ELSE 'ARS' END as tipo_pago
           FROM FACTURAS f 
           LEFT JOIN PACIENTES p ON f.CLAVEPAC = p.CLAVE 
           $where_clause 
           ORDER BY f.FECHA DESC, f.NUMFACT DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $facturas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($formato === 'excel') {
        // Exportar a Excel (CSV)
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_financiero_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // BOM para UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Encabezados
        fputcsv($output, [
            'Fecha',
            'No. Factura',
            'Paciente',
            'Cédula',
            'Concepto',
            'Total',
            'Pagado',
            'Pendiente',
            'Método Pago',
            'Tipo Pago',
            'Estado'
        ], ';');
        
        // Datos
        foreach ($facturas as $factura) {
            fputcsv($output, [
                date('d/m/Y', strtotime($factura['FECHA'])),
                $factura['NUMFACT'],
                $factura['NOMBREAPELLIDO'] ?? 'N/A',
                $factura['CEDULA'] ?? 'N/A',
                $factura['CONCEPTO'],
                number_format($factura['PRECIO'], 2),
                number_format($factura['PAGADO'], 2),
                number_format($factura['NOPAGO'], 2),
                ucfirst($factura['MODOPGO'] ?? 'N/A'),
                $factura['tipo_pago'],
                $factura['estado_pago']
            ], ';');
        }
        
        fclose($output);
        
    } else {
        // Exportar a PDF (HTML por ahora)
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_financiero_' . date('Y-m-d') . '.html"');
        
        // Obtener información de la empresa
        $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
        $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
        $nombre_empresa = $empresa ? $empresa['NOMBRE'] : 'Consultorio Médico';
        
        // Estadísticas
        $total_facturas = count($facturas);
        $total_facturado = array_sum(array_column($facturas, 'PRECIO'));
        $total_pagado = array_sum(array_column($facturas, 'PAGADO'));
        $total_pendiente = array_sum(array_column($facturas, 'NOPAGO'));
        
        $facturas_pagadas = count(array_filter($facturas, function($f) { return $f['NOPAGO'] == 0; }));
        $facturas_pendientes = count(array_filter($facturas, function($f) { return $f['PAGADO'] == 0; }));
        $facturas_parciales = count(array_filter($facturas, function($f) { return $f['PAGADO'] > 0 && $f['NOPAGO'] > 0; }));
        
        $promedio_factura = $total_facturas > 0 ? $total_facturado / $total_facturas : 0;
        
        echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Reporte Financiero</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .info { margin-bottom: 20px; }
        .stats { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center; }
        .stat-item { background: white; padding: 10px; border-radius: 3px; border: 1px solid #dee2e6; }
        .financial-summary { background-color: #e9ecef; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .summary-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 10px; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .money-positive { color: #28a745; font-weight: bold; }
        .money-negative { color: #dc3545; font-weight: bold; }
        .money-pending { color: #ffc107; font-weight: bold; }
        .estado-pagado { color: #28a745; font-weight: bold; }
        .estado-pendiente { color: #dc3545; font-weight: bold; }
        .estado-parcial { color: #ffc107; font-weight: bold; }
        .footer { margin-top: 30px; text-align: center; font-size: 10px; color: #666; }
        .totals-row { background-color: #f8f9fa; font-weight: bold; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>$nombre_empresa</h1>
        <h2>Reporte Financiero</h2>
        <p>Período: " . date('d/m/Y', strtotime($fecha_inicio)) . " - " . date('d/m/Y', strtotime($fecha_fin)) . "</p>
    </div>
    
    <div class='info'>
        <p><strong>Generado por:</strong> " . htmlspecialchars($_SESSION['usuario']) . "</p>
        <p><strong>Fecha de generación:</strong> " . date('d/m/Y H:i:s') . "</p>";
        
        if ($estado_pago) echo "<p><strong>Filtro por estado:</strong> " . ucfirst($estado_pago) . "</p>";
        if ($metodo_pago) echo "<p><strong>Filtro por método:</strong> " . ucfirst($metodo_pago) . "</p>";
        if ($ars !== '') echo "<p><strong>Filtro por tipo:</strong> " . ($ars == '0' ? 'Particular' : 'ARS') . "</p>";
        
        echo "</div>
    
    <div class='financial-summary'>
        <h3>Resumen Financiero</h3>
        <div class='summary-grid'>
            <div class='stat-item'>
                <h4 class='money-positive'>RD$" . number_format($total_facturado, 2) . "</h4>
                <p>Total Facturado</p>
            </div>
            <div class='stat-item'>
                <h4 class='money-positive'>RD$" . number_format($total_pagado, 2) . "</h4>
                <p>Total Pagado</p>
            </div>
            <div class='stat-item'>
                <h4 class='money-pending'>RD$" . number_format($total_pendiente, 2) . "</h4>
                <p>Total Pendiente</p>
            </div>
        </div>
    </div>
    
    <div class='stats'>
        <h3>Estadísticas de Facturación</h3>
        <div class='stats-grid'>
            <div class='stat-item'>
                <h4>$total_facturas</h4>
                <p>Total Facturas</p>
            </div>
            <div class='stat-item'>
                <h4>$facturas_pagadas</h4>
                <p>Pagadas</p>
            </div>
            <div class='stat-item'>
                <h4>$facturas_pendientes</h4>
                <p>Pendientes</p>
            </div>
            <div class='stat-item'>
                <h4>RD$" . number_format($promedio_factura, 2) . "</h4>
                <p>Promedio por Factura</p>
            </div>
        </div>
    </div>
    
    <h3>Detalle de Facturas</h3>
    <table>
        <thead>
            <tr>
                <th>Fecha</th>
                <th>No. Factura</th>
                <th>Paciente</th>
                <th>Concepto</th>
                <th>Total</th>
                <th>Pagado</th>
                <th>Pendiente</th>
                <th>Método</th>
                <th>Estado</th>
            </tr>
        </thead>
        <tbody>";
        
        foreach ($facturas as $factura) {
            $clase_estado = '';
            switch ($factura['estado_pago']) {
                case 'Pagado': $clase_estado = 'estado-pagado'; break;
                case 'Pendiente': $clase_estado = 'estado-pendiente'; break;
                case 'Parcial': $clase_estado = 'estado-parcial'; break;
            }
            
            echo "<tr>
                <td>" . date('d/m/Y', strtotime($factura['FECHA'])) . "</td>
                <td>" . htmlspecialchars($factura['NUMFACT']) . "</td>
                <td>" . htmlspecialchars($factura['NOMBREAPELLIDO'] ?? 'N/A') . "</td>
                <td>" . htmlspecialchars($factura['CONCEPTO']) . "</td>
                <td class='money-positive'>RD$" . number_format($factura['PRECIO'], 2) . "</td>
                <td class='money-positive'>RD$" . number_format($factura['PAGADO'], 2) . "</td>
                <td class='money-pending'>RD$" . number_format($factura['NOPAGO'], 2) . "</td>
                <td>" . ucfirst($factura['MODOPGO'] ?? 'N/A') . "</td>
                <td class='$clase_estado'>" . $factura['estado_pago'] . "</td>
            </tr>";
        }
        
        echo "<tr class='totals-row'>
                <td colspan='4'><strong>TOTALES</strong></td>
                <td class='money-positive'><strong>RD$" . number_format($total_facturado, 2) . "</strong></td>
                <td class='money-positive'><strong>RD$" . number_format($total_pagado, 2) . "</strong></td>
                <td class='money-pending'><strong>RD$" . number_format($total_pendiente, 2) . "</strong></td>
                <td colspan='2'></td>
            </tr>";
        
        echo "</tbody>
    </table>
    
    <div class='footer'>
        <p>Reporte generado automáticamente por el Sistema de Gestión del Consultorio</p>
        <p>Total de registros: $total_facturas | Efectividad de cobro: " . ($total_facturado > 0 ? round(($total_pagado / $total_facturado) * 100, 1) : 0) . "%</p>
    </div>
</body>
</html>";
    }

} catch (PDOException $e) {
    http_response_code(500);
    die('Error en la base de datos: ' . $e->getMessage());
} catch (Exception $e) {
    http_response_code(500);
    die('Error interno: ' . $e->getMessage());
}
?>
