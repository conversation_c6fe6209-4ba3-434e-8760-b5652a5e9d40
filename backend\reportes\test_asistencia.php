<?php
// backend/reportes/test_asistencia.php
// Script para verificar el campo ASISTENCIA en la base de datos

session_start();
require_once __DIR__ . '/../config/database.php';

// Simular sesión para prueba
$_SESSION['usuario'] = 'admin';
$_SESSION['rol'] = 'admin';

echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Prueba Campo ASISTENCIA</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🔍 Prueba del Campo ASISTENCIA</h1>";

try {
    // 1. Verificar estructura de la tabla
    echo "<div class='section'>";
    echo "<h2>1. Estructura de la Tabla CITAMEDIC</h2>";
    
    $stmt = $pdo->query("DESCRIBE CITAMEDIC");
    $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $tiene_asistencia = false;
    $tiene_estatus = false;
    
    echo "<table>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
    
    foreach ($columnas as $columna) {
        echo "<tr>";
        echo "<td>{$columna['Field']}</td>";
        echo "<td>{$columna['Type']}</td>";
        echo "<td>{$columna['Null']}</td>";
        echo "<td>{$columna['Key']}</td>";
        echo "<td>{$columna['Default']}</td>";
        echo "</tr>";
        
        if ($columna['Field'] == 'ASISTENCIA') $tiene_asistencia = true;
        if ($columna['Field'] == 'ESTATUS') $tiene_estatus = true;
    }
    echo "</table>";
    
    echo "<p class='" . ($tiene_asistencia ? 'success' : 'error') . "'>";
    echo ($tiene_asistencia ? '✅' : '❌') . " Campo ASISTENCIA: " . ($tiene_asistencia ? 'Encontrado' : 'NO ENCONTRADO');
    echo "</p>";
    
    echo "<p class='" . ($tiene_estatus ? 'success' : 'warning') . "'>";
    echo ($tiene_estatus ? '✅' : '⚠️') . " Campo ESTATUS: " . ($tiene_estatus ? 'Encontrado' : 'NO ENCONTRADO');
    echo "</p>";
    
    echo "</div>";
    
    // 2. Verificar datos en el campo ASISTENCIA
    if ($tiene_asistencia) {
        echo "<div class='section'>";
        echo "<h2>2. Datos en el Campo ASISTENCIA</h2>";
        
        $stmt = $pdo->query("
            SELECT ASISTENCIA, 
                   CASE ASISTENCIA 
                       WHEN 0 THEN 'Atendido'
                       WHEN 1 THEN 'Canceló'
                       WHEN 2 THEN 'No asistió'
                       WHEN 3 THEN 'Citado'
                       WHEN 4 THEN 'Llegó tarde'
                       WHEN 5 THEN 'Esperando'
                       WHEN 6 THEN 'Pendiente aprobación'
                       ELSE 'Desconocido'
                   END as estado_texto,
                   COUNT(*) as cantidad
            FROM CITAMEDIC 
            GROUP BY ASISTENCIA 
            ORDER BY ASISTENCIA
        ");
        
        $datos_asistencia = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($datos_asistencia)) {
            echo "<p class='warning'>⚠️ No hay datos en la tabla CITAMEDIC</p>";
        } else {
            echo "<table>";
            echo "<tr><th>Código ASISTENCIA</th><th>Estado</th><th>Cantidad</th></tr>";
            foreach ($datos_asistencia as $dato) {
                echo "<tr>";
                echo "<td>{$dato['ASISTENCIA']}</td>";
                echo "<td>{$dato['estado_texto']}</td>";
                echo "<td>{$dato['cantidad']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
    }
    
    // 3. Verificar datos en el campo ESTATUS (para comparación)
    if ($tiene_estatus) {
        echo "<div class='section'>";
        echo "<h2>3. Datos en el Campo ESTATUS (para comparación)</h2>";
        
        $stmt = $pdo->query("
            SELECT ESTATUS, COUNT(*) as cantidad
            FROM CITAMEDIC 
            GROUP BY ESTATUS 
            ORDER BY ESTATUS
        ");
        
        $datos_estatus = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($datos_estatus)) {
            echo "<p class='warning'>⚠️ No hay datos en el campo ESTATUS</p>";
        } else {
            echo "<table>";
            echo "<tr><th>Código ESTATUS</th><th>Cantidad</th></tr>";
            foreach ($datos_estatus as $dato) {
                echo "<tr>";
                echo "<td>{$dato['ESTATUS']}</td>";
                echo "<td>{$dato['cantidad']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
    }
    
    // 4. Prueba de consulta con filtros
    echo "<div class='section'>";
    echo "<h2>4. Prueba de Consulta con Filtros</h2>";
    
    if ($tiene_asistencia) {
        $fecha_inicio = date('Y-m-01');
        $fecha_fin = date('Y-m-t');
        
        echo "<h4>Consulta por rango de fechas (mes actual):</h4>";
        
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM CITAMEDIC 
            WHERE FECHACON BETWEEN ? AND ?
        ");
        $stmt->execute([$fecha_inicio, $fecha_fin]);
        $total_mes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<p>Total de citas en el mes actual: <strong>$total_mes</strong></p>";
        
        if ($total_mes > 0) {
            echo "<h4>Distribución por ASISTENCIA en el mes actual:</h4>";
            
            $stmt = $pdo->prepare("
                SELECT ASISTENCIA, 
                       CASE ASISTENCIA 
                           WHEN 0 THEN 'Atendido'
                           WHEN 1 THEN 'Canceló'
                           WHEN 2 THEN 'No asistió'
                           WHEN 3 THEN 'Citado'
                           WHEN 4 THEN 'Llegó tarde'
                           WHEN 5 THEN 'Esperando'
                           WHEN 6 THEN 'Pendiente aprobación'
                           ELSE 'Desconocido'
                       END as estado_texto,
                       COUNT(*) as cantidad
                FROM CITAMEDIC 
                WHERE FECHACON BETWEEN ? AND ?
                GROUP BY ASISTENCIA 
                ORDER BY ASISTENCIA
            ");
            $stmt->execute([$fecha_inicio, $fecha_fin]);
            $distribucion = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>Código</th><th>Estado</th><th>Cantidad</th><th>Porcentaje</th></tr>";
            foreach ($distribucion as $dato) {
                $porcentaje = round(($dato['cantidad'] / $total_mes) * 100, 1);
                echo "<tr>";
                echo "<td>{$dato['ASISTENCIA']}</td>";
                echo "<td>{$dato['estado_texto']}</td>";
                echo "<td>{$dato['cantidad']}</td>";
                echo "<td>{$porcentaje}%</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p class='error'>❌ No se puede realizar la prueba sin el campo ASISTENCIA</p>";
    }
    
    echo "</div>";
    
    // 5. Recomendaciones
    echo "<div class='section'>";
    echo "<h2>5. Recomendaciones</h2>";
    
    if ($tiene_asistencia) {
        echo "<p class='success'>✅ El campo ASISTENCIA está disponible y se puede usar para los reportes</p>";
        echo "<p>✅ Todas las consultas han sido actualizadas para usar ASISTENCIA en lugar de ESTATUS</p>";
    } else {
        echo "<p class='error'>❌ El campo ASISTENCIA no existe en la tabla CITAMEDIC</p>";
        echo "<p class='warning'>⚠️ Será necesario crear el campo o usar un campo alternativo</p>";
        
        if ($tiene_estatus) {
            echo "<p class='warning'>⚠️ Se podría usar el campo ESTATUS como alternativa</p>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ Error durante la prueba: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Archivo: " . $e->getFile() . "</p>";
    echo "<p>Línea: " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>✅ Prueba Completada</h2>";
echo "<p><a href='citas_reportes.php'>🔗 Ir a Reportes de Citas</a></p>";
echo "<p><a href='diagnostico.php'>🔗 Ejecutar Diagnóstico Completo</a></p>";
echo "</div>";

echo "</body></html>";
?>
