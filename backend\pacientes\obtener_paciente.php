<?php
session_start();
require_once '../config/database.php';

// Configurar headers para JSON
header('Content-Type: application/json');

// Configuración de errores
ini_set('display_errors', 0);
error_reporting(E_ALL);

/**
 * Función para normalizar rutas de fotografías
 */
function normalizarRutaFoto($ruta) {
    if (empty($ruta)) {
        return 'fotografias/NOFOTO.BMP';
    }

    $ruta = trim($ruta);

    // Corregir rutas mal formadas comunes
    if ($ruta === 'FotografiasNOFOTO.BMP' || $ruta === 'NOFOTO.BMP') {
        return 'fotografias/NOFOTO.BMP';
    }

    // Convertir barras invertidas a barras normales
    $ruta = str_replace('\\', '/', $ruta);

    // Corregir doble directorio: fotografias/Fotografias/archivo.bmp
    if (strpos($ruta, 'fotografias/Fotografias/') === 0) {
        $ruta = str_replace('fotografias/Fotografias/', 'fotografias/', $ruta);
    }

    // Corregir directorio con mayúscula al inicio
    if (strpos($ruta, 'Fotografias/') === 0) {
        $ruta = str_replace('Fotografias/', 'fotografias/', $ruta);
    }

    // Corregir casos donde hay Fotografias en el medio
    $ruta = str_replace('/Fotografias/', '/fotografias/', $ruta);

    // Si no tiene directorio, agregarlo
    if (!strpos($ruta, '/') && $ruta !== 'fotografias/NOFOTO.BMP') {
        $ruta = 'fotografias/' . $ruta;
    }

    // Limpiar caracteres especiales que pueden causar problemas de URL
    $ruta = preg_replace('/[^\x20-\x7E]/', '', $ruta); // Remover caracteres no ASCII

    return $ruta;
}

try {
    // Obtener la CLAVE del paciente PARA BUSCARLO
    $clave = $_GET['CLAVE'] ?? null;

    if (!$clave || empty(trim($clave))) {
        echo json_encode([
            "error" => "No se proporcionó una CLAVE válida",
            "success" => false
        ]);
        exit;
    }

    // Validar que la CLAVE sea numérica
    if (!is_numeric($clave)) {
        echo json_encode([
            "error" => "La CLAVE debe ser un número válido",
            "success" => false
        ]);
        exit;
    }

    $stmt = $pdo->prepare("
        SELECT p.*, f.ARCHIVO
        FROM PACIENTES p
        LEFT JOIN FOTOGRAFIAS f ON p.CLAVE = f.CLAVEPAC
        WHERE p.CLAVE = ?
    ");

    $stmt->execute([$clave]);
    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$paciente) {
        echo json_encode([
            "error" => "No se encontró el paciente con CLAVE: " . $clave,
            "success" => false
        ]);
        exit;
    }

    // Normalizar la ruta de la foto antes de enviar
    if (!empty($paciente['ARCHIVO'])) {
        $paciente['ARCHIVO'] = normalizarRutaFoto($paciente['ARCHIVO']);
    } else {
        $paciente['ARCHIVO'] = normalizarRutaFoto('fotografias/NOFOTO.BMP');
    }

    // Agregar información de éxito
    $paciente['success'] = true;

    echo json_encode($paciente);

} catch (PDOException $e) {
    error_log("Error de base de datos en obtener_paciente.php: " . $e->getMessage());
    echo json_encode([
        "error" => "Error al consultar la base de datos",
        "success" => false
    ]);
} catch (Exception $e) {
    error_log("Error general en obtener_paciente.php: " . $e->getMessage());
    echo json_encode([
        "error" => "Error interno del servidor",
        "success" => false
    ]);
}
?>