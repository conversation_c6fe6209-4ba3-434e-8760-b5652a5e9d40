<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Hemograma General"
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Función para obtener los hemogramas del paciente
function getHemogramas($pdo, $clavePac) {
    $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha 
            FROM HEMOGRAMA WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Función para obtener un hemograma específico
function getHemograma($pdo, $id) {
    $sql = "SELECT * FROM HEMOGRAMA WHERE CLAVE = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$id]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    if ($action === 'getHemogramas') {
        echo json_encode(getHemogramas($pdo, $clavePac));
        exit;
    }
    else if ($action === 'getHemograma') {
        $id = intval($_POST['id']);
        echo json_encode(getHemograma($pdo, $id));
        exit;
    }
    else if ($action === 'saveHemograma') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $fecha = $_POST['fecha'];
        $hemoglobina = $_POST['hemoglobina'];
        $hematocrito = $_POST['hematocrito'];
        $globulosRojos = $_POST['globulosRojos'];
        $globulosBlancos = $_POST['globulosBlancos'];
        $plaquetas = $_POST['plaquetas'];
        $neutrofilos = $_POST['neutrofilos'];
        $linfocitos = $_POST['linfocitos'];
        $monocitos = $_POST['monocitos'];
        $eosinofilos = $_POST['eosinofilos'];
        $basofilos = $_POST['basofilos'];
        $observaciones = $_POST['observaciones'];
        $vcm = $_POST['vcm'] ?? null;
        $hcm = $_POST['hcm'] ?? null;
        $chcm = $_POST['chcm'] ?? null;
        $rdw = $_POST['rdw'] ?? null;
        
        try {
            if ($id > 0) {
                // Actualizar registro existente
                $sql = "UPDATE HEMOGRAMA SET 
                        FECHA_CAP = ?, HEMOGLOBINA = ?, HEMATOCRITO = ?, 
                        GLOBULOS_ROJOS = ?, GLOBULOS_BLANCOS = ?, PLAQUETAS = ?, 
                        NEUTROFILOS = ?, LINFOCITOS = ?, MONOCITOS = ?, 
                        EOSINOFILOS = ?, BASOFILOS = ?, OBSERVACIONES = ?,
                        VCM = ?, HCM = ?, CHCM = ?, RDW = ?
                        WHERE CLAVE = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $fecha, $hemoglobina, $hematocrito, $globulosRojos, 
                    $globulosBlancos, $plaquetas, $neutrofilos, $linfocitos, 
                    $monocitos, $eosinofilos, $basofilos, $observaciones,
                    $vcm, $hcm, $chcm, $rdw, $id
                ]);
            } else {
                // Insertar nuevo registro
                $sql = "INSERT INTO HEMOGRAMA (
                    CLAVEPAC, FECHA_CAP, HEMOGLOBINA, HEMATOCRITO, 
                    GLOBULOS_ROJOS, GLOBULOS_BLANCOS, PLAQUETAS, 
                    NEUTROFILOS, LINFOCITOS, MONOCITOS, 
                    EOSINOFILOS, BASOFILOS, OBSERVACIONES,
                    VCM, HCM, CHCM, RDW) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $clavePac, $fecha, $hemoglobina, $hematocrito, 
                    $globulosRojos, $globulosBlancos, $plaquetas, 
                    $neutrofilos, $linfocitos, $monocitos, 
                    $eosinofilos, $basofilos, $observaciones,
                    $vcm, $hcm, $chcm, $rdw
                ]);
                
                $id = $pdo->lastInsertId();
            }
            
            echo json_encode(['success' => true, 'id' => $id]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteHemograma') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "DELETE FROM HEMOGRAMA WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Obtener lista de hemogramas
$hemogramas = getHemogramas($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Hemograma General</h4>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    Registros
                </div>
                <div class="card-body">
                    <div class="lista-registros" id="hemogramasList">
                        <?php if (count($hemogramas) > 0): ?>
                            <ul class="list-unstyled m-2">
                                <?php foreach ($hemogramas as $item): ?>
                                    <li><a href="#" class="hemograma-item" data-id="<?php echo $item['id']; ?>"><?php echo $item['fecha']; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-center">No hay registros</p>
                        <?php endif; ?>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" id="btnNuevoHemograma">Nuevo</button>
                        <button class="btn btn-sm btn-info" id="btnEditarHemograma">Editar</button>
                        <button class="btn btn-sm btn-danger" id="btnEliminarHemograma">Eliminar</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    Datos del Hemograma
                </div>
                <div class="card-body">
                    <form id="formHemograma">
                        <input type="hidden" id="hemogramaId" name="id" value="0">
                        
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="fechaHemograma">Fecha</label>
                                <input type="date" class="form-control" id="fechaHemograma" name="fecha" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                <label for="hemoglobina">Hemoglobina (g/dL)</label>
                                <input type="text" class="form-control" id="hemoglobina" name="hemoglobina">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="hematocrito">Hematocrito (%)</label>
                                <input type="text" class="form-control" id="hematocrito" name="hematocrito">
                            </div>
                            <div class="form-group col-md-4">
                                <label for="globulosRojos">Glóbulos Rojos (x10^6/μL)</label>
                                <input type="text" class="form-control" id="globulosRojos" name="globulosRojos">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="vcm">VCM (fL)</label>
                                <input type="text" class="form-control" id="vcm" name="vcm">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="hcm">HCM (pg)</label>
                                <input type="text" class="form-control" id="hcm" name="hcm">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="chcm">CHCM (g/dL)</label>
                                <input type="text" class="form-control" id="chcm" name="chcm">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="rdw">RDW (%)</label>
                                <input type="text" class="form-control" id="rdw" name="rdw">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="globulosBlancos">Glóbulos Blancos (x10^3/μL)</label>
                                <input type="text" class="form-control" id="globulosBlancos" name="globulosBlancos">
                            </div>
                            <div class="form-group col-md-6">
                                <label for="plaquetas">Plaquetas (x10^3/μL)</label>
                                <input type="text" class="form-control" id="plaquetas" name="plaquetas">
                            </div>
                        </div>
                        
                        <h5 class="mt-3">Fórmula Leucocitaria</h5>
                        <div class="form-row">
                            <div class="form-group col-md-2">
                                <label for="neutrofilos">Neutrófilos (%)</label>
                                <input type="text" class="form-control" id="neutrofilos" name="neutrofilos">
                            </div>
                            <div class="form-group col-md-2">
                                <label for="linfocitos">Linfocitos (%)</label>
                                <input type="text" class="form-control" id="linfocitos" name="linfocitos">
                            </div>
                            <div class="form-group col-md-2">
                                <label for="monocitos">Monocitos (%)</label>
                                <input type="text" class="form-control" id="monocitos" name="monocitos">
                            </div>
                            <div class="form-group col-md-2">
                                <label for="eosinofilos">Eosinófilos (%)</label>
                                <input type="text" class="form-control" id="eosinofilos" name="eosinofilos">
                            </div>
                            <div class="form-group col-md-2">
                                <label for="basofilos">Basófilos (%)</label>
                                <input type="text" class="form-control" id="basofilos" name="basofilos">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="observaciones">Observaciones</label>
                            <textarea class="form-control" id="observaciones" name="observaciones" rows="3"></textarea>
                        </div>
                        
                        <div class="text-right">
                            <button type="button" class="btn btn-secondary" id="btnCancelarHemograma">Cancelar</button>
                            <button type="button" class="btn btn-primary" id="btnGuardarHemograma">Guardar</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentHemogramaId = 0;
    let editMode = false;
    
    // Inicializar formulario
    disableForm(true);
    $('#btnEditarHemograma').prop('disabled', true);
    $('#btnEliminarHemograma').prop('disabled', true);
    $('#btnCancelarHemograma').hide();
    $('#btnGuardarHemograma').hide();
    
    // Si hay registros, seleccionar el primero
    if ($('.hemograma-item').length > 0) {
        $('.hemograma-item:first').click();
    }
    
    // Cargar hemograma al hacer clic en un elemento de la lista
    $(document).on('click', '.hemograma-item', function(e) {
        e.preventDefault();
        
        $('.hemograma-item').removeClass('font-weight-bold');
        $(this).addClass('font-weight-bold');
        
        const id = $(this).data('id');
        currentHemogramaId = id;
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'getHemograma',
                id: id
            },
            dataType: 'json',
            success: function(data) {
                if (data) {
                    $('#hemogramaId').val(data.CLAVE);
                    $('#fechaHemograma').val(data.FECHA_CAP ? data.FECHA_CAP.split(' ')[0] : '');
                    $('#hemoglobina').val(data.HEMOGLOBINA);
                    $('#hematocrito').val(data.HEMATOCRITO);
                    $('#globulosRojos').val(data.GLOBULOS_ROJOS);
                    $('#globulosBlancos').val(data.GLOBULOS_BLANCOS);
                    $('#plaquetas').val(data.PLAQUETAS);
                    $('#neutrofilos').val(data.NEUTROFILOS);
                    $('#linfocitos').val(data.LINFOCITOS);
                    $('#monocitos').val(data.MONOCITOS);
                    $('#eosinofilos').val(data.EOSINOFILOS);
                    $('#basofilos').val(data.BASOFILOS);
                    $('#observaciones').val(data.OBSERVACIONES);
                    $('#vcm').val(data.VCM);
                    $('#hcm').val(data.HCM);
                    $('#chcm').val(data.CHCM);
                    $('#rdw').val(data.RDW);
                    
                    disableForm(true);
                    $('#btnEditarHemograma').prop('disabled', false);
                    $('#btnEliminarHemograma').prop('disabled', false);
                    $('#btnCancelarHemograma').hide();
                    $('#btnGuardarHemograma').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al cargar hemograma:", error);
                alert("Error al cargar hemograma. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Nuevo hemograma
    $('#btnNuevoHemograma').click(function() {
        clearForm();
        disableForm(false);
        editMode = false;
        
        $('#btnEditarHemograma').prop('disabled', true);
        $('#btnEliminarHemograma').prop('disabled', true);
        $('#btnCancelarHemograma').show();
        $('#btnGuardarHemograma').show();
    });
    
    // Editar hemograma
    $('#btnEditarHemograma').click(function() {
        if (currentHemogramaId > 0) {
            disableForm(false);
            editMode = true;
            
            $('#btnNuevoHemograma').prop('disabled', true);
            $('#btnEliminarHemograma').prop('disabled', true);
            $('#btnCancelarHemograma').show();
            $('#btnGuardarHemograma').show();
        }
    });
    
    // Eliminar hemograma
    $('#btnEliminarHemograma').click(function() {
        if (currentHemogramaId > 0) {
            if (confirm("¿Está seguro de eliminar este hemograma?")) {
                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: {
                        action: 'deleteHemograma',
                        id: currentHemogramaId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert("Hemograma eliminado correctamente.");
                            location.reload(); // Recargar para actualizar la lista
                        } else {
                            alert("Error al eliminar hemograma: " + response.error);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error al eliminar hemograma:", error);
                        alert("Error al eliminar hemograma. Consulte la consola para más detalles.");
                    }
                });
            }
        }
    });
    
    // Cancelar
    $('#btnCancelarHemograma').click(function() {
        if (editMode && currentHemogramaId > 0) {
            // Volver a cargar el hemograma actual
            $('.hemograma-item[data-id="' + currentHemogramaId + '"]').click();
        } else {
            clearForm();
        }
        
        disableForm(true);
        $('#btnNuevoHemograma').prop('disabled', false);
        $('#btnEditarHemograma').prop('disabled', currentHemogramaId === 0);
        $('#btnEliminarHemograma').prop('disabled', currentHemogramaId === 0);
        $('#btnCancelarHemograma').hide();
        $('#btnGuardarHemograma').hide();
    });
    
    // Guardar hemograma
    $('#btnGuardarHemograma').click(function() {
        const formData = {
            action: 'saveHemograma',
            id: editMode ? currentHemogramaId : 0,
            fecha: $('#fechaHemograma').val(),
            hemoglobina: $('#hemoglobina').val(),
            hematocrito: $('#hematocrito').val(),
            globulosRojos: $('#globulosRojos').val(),
            globulosBlancos: $('#globulosBlancos').val(),
            plaquetas: $('#plaquetas').val(),
            neutrofilos: $('#neutrofilos').val(),
            linfocitos: $('#linfocitos').val(),
            monocitos: $('#monocitos').val(),
            eosinofilos: $('#eosinofilos').val(),
            basofilos: $('#basofilos').val(),
            observaciones: $('#observaciones').val(),
            vcm: $('#vcm').val(),
            hcm: $('#hcm').val(),
            chcm: $('#chcm').val(),
            rdw: $('#rdw').val()
        };
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert("Hemograma guardado correctamente.");
                    location.reload(); // Recargar para actualizar la lista
                } else {
                    alert("Error al guardar hemograma: " + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al guardar hemograma:", error);
                alert("Error al guardar hemograma. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Funciones auxiliares
    function clearForm() {
        $('#formHemograma')[0].reset();
        $('#hemogramaId').val(0);
        $('#fechaHemograma').val(new Date().toISOString().split('T')[0]);
        currentHemogramaId = 0;
    }
    
    function disableForm(disabled) {
        $('#formHemograma input, #formHemograma textarea').prop('disabled', disabled);
    }
});
</script>
