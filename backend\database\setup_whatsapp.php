<?php
/**
 * <PERSON><PERSON>t para configurar las tablas de WhatsApp
 * Ejecutar una sola vez para crear las tablas necesarias
 */

require_once __DIR__ . '/../config/database.php';

try {
    echo "🚀 Configurando tablas de WhatsApp...\n\n";
    
    // Leer el archivo SQL
    $sqlFile = __DIR__ . '/create_whatsapp_tables.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Archivo SQL no encontrado: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    
    // Dividir en statements individuales
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !str_starts_with($stmt, '--');
        }
    );
    
    $success = 0;
    $errors = 0;
    
    foreach ($statements as $statement) {
        try {
            $pdo->exec($statement);
            
            // Determinar qué se creó
            if (str_contains(strtoupper($statement), 'CREATE TABLE')) {
                preg_match('/CREATE TABLE.*?`?(\w+)`?/i', $statement, $matches);
                $tableName = $matches[1] ?? 'tabla';
                echo "✅ Tabla '$tableName' creada correctamente\n";
            } elseif (str_contains(strtoupper($statement), 'INSERT INTO')) {
                preg_match('/INSERT INTO.*?`?(\w+)`?/i', $statement, $matches);
                $tableName = $matches[1] ?? 'tabla';
                echo "📝 Datos insertados en '$tableName'\n";
            }
            
            $success++;
        } catch (PDOException $e) {
            echo "❌ Error ejecutando statement: " . $e->getMessage() . "\n";
            echo "Statement: " . substr($statement, 0, 100) . "...\n\n";
            $errors++;
        }
    }
    
    echo "\n🎉 Configuración completada!\n";
    echo "✅ Operaciones exitosas: $success\n";
    echo "❌ Errores: $errors\n\n";
    
    // Verificar que las tablas se crearon
    echo "🔍 Verificando tablas creadas:\n";
    $tables = ['whatsapp_messages', 'whatsapp_config', 'whatsapp_templates', 'patient_whatsapp', 'whatsapp_conversations'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                echo "✅ $table - OK\n";
            } else {
                echo "❌ $table - NO ENCONTRADA\n";
            }
        } catch (Exception $e) {
            echo "❌ $table - ERROR: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n📋 Próximos pasos:\n";
    echo "1. Configura tu cuenta de Twilio en backend/config/config.php\n";
    echo "2. Actualiza 'whatsapp' => 'enabled' => true en config.php\n";
    echo "3. Configura el webhook en Twilio apuntando a tu servidor\n";
    echo "4. Prueba enviando un mensaje al sandbox de Twilio\n\n";
    
} catch (Exception $e) {
    echo "💥 Error fatal: " . $e->getMessage() . "\n";
    exit(1);
}
?>
