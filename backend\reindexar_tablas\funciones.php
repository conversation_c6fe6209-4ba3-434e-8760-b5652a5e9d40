<?php
// funciones.php

// 1) Conexión PDO a MySQL (sin hint de retorno)
function getPDO() {
    $host = 'bh8942.banahosting.com';
    $db   = 'gthavrvf_consultorio';
    $user = 'gthavrvf_test';
    $pass = 'w{IR_(Rx,BC#';
    $dsn  = "mysql:host=$host;dbname=$db;charset=utf8mb4";
    $opts = array(PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION);
    return new PDO($dsn, $user, $pass, $opts);
}

// 2) Lista tablas de usuario
function listarTablas($pdo) {
    $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_Type='BASE TABLE'");
    return $stmt->fetchAll(PDO::FETCH_COLUMN);
}

// 3) Valida cédula RD (sin type hints)
function validaCedula($ced) {
    $ced = trim($ced);
    if (strlen($ced) !== 11 || !ctype_digit($ced) || intval(substr($ced,0,3)) === 0) {
        return false;
    }
    $sImp = 0; $sPar = 0;
    for ($i = 0; $i < 10; $i++) {
        $d = intval($ced[$i]);
        if ((($i+1) % 2) !== 0) {
            $sImp += $d;
        } else {
            $prod = $d * 2;
            $sPar += ($prod > 9) ? ($prod - 9) : $prod;
        }
    }
    $suma = $sImp + $sPar;
    $ult  = intval(substr((string)$suma, -1));
    $ctrl = ($ult === 0) ? 0 : 10 - $ult;
    return ($ctrl === intval($ced[10]));
}

// 4) Descubre PK de cualquier tabla
function getPrimaryKey($pdo, $tabla) {
    $stmt = $pdo->prepare("SHOW KEYS FROM `$tabla` WHERE Key_name = 'PRIMARY'");
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return isset($row['Column_name']) ? $row['Column_name'] : null;
}

/* **
 * Reindexa la PK de $tabla y actualiza en cascada todas las FKs.
 */
/**
 * Reindexa la PK de $tabla y actualiza todas las FKs que la referencian,
 * sin usar transacciones (compatible con MyISAM).
 */
function reindexTabla($pdo, $tabla) {
    // 1) Detectar la PK
    $stmt = $pdo->prepare("SHOW KEYS FROM `$tabla` WHERE Key_name = 'PRIMARY'");
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if (! $row) {
        return ['status'=>'omitida', 'msg'=>"tabla $tabla sin PK"];
    }
    $pk = $row['Column_name'];

    // 2) Leer OLD → NEW
    $ids = $pdo
      ->query("SELECT `$pk` FROM `$tabla` ORDER BY `$pk`")
      ->fetchAll(PDO::FETCH_COLUMN);
    $total = count($ids);
    if ($total === 0) {
        return ['status'=>'vacia', 'msg'=>'sin registros'];
    }

    // 3) Encontrar las tablas hijas (FKs)
    $db = $pdo->query("SELECT DATABASE()")->fetchColumn();
    $fkStmt = $pdo->prepare("
      SELECT TABLE_NAME, COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE
        REFERENCED_TABLE_SCHEMA = ?
        AND REFERENCED_TABLE_NAME   = ?
        AND REFERENCED_COLUMN_NAME  = ?
    ");
    $fkStmt->execute([$db, $tabla, $pk]);
    $fks = $fkStmt->fetchAll(PDO::FETCH_ASSOC);

    // 4) Deshabilitar las comprobaciones de FK
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

    // 5) Hacer todos los UPDATES en autocommit
    try {
        // Preparo el UPDATE para la tabla principal
        $updMain = $pdo->prepare("
          UPDATE `$tabla` SET `$pk` = :new WHERE `$pk` = :old
        ");
        foreach ($ids as $i => $old) {
            $new = $i + 1;
            // 5a) Actualizar la PK principal
            $updMain->execute([':new'=>$new,':old'=>$old]);

            // 5b) Actualizar cada FK en cada tabla hija
            foreach ($fks as $fk) {
                $ct = $fk['TABLE_NAME'];
                $cc = $fk['COLUMN_NAME'];
                $pdo->exec("
                  UPDATE `$ct`
                  SET `$cc` = $new
                  WHERE `$cc` = $old
                ");
            }
        }

        // 6) Ajustar AUTO_INCREMENT
        $next = $total + 1;
        $pdo->exec("ALTER TABLE `$tabla` AUTO_INCREMENT = $next");

        $status = ['status'=>'ok', 'msg'=>"renumerada 1..$total"];
    } catch (Exception $e) {
        $status = ['status'=>'error', 'msg'=>$e->getMessage()];
    }

    // 7) Reactivar comprobaciones de FK
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

    return $status;
}