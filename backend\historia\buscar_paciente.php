<?php
// Incluir la conexión a la base de datos
// La ruta es relativa desde backend/historia/ a backend/config/database.php
$pdo = require_once __DIR__ . '/../config/database.php';

// Mostrar errores de PHP para depuración (¡DESHABILITAR EN PRODUCCIÓN!)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Sección AJAX para la búsqueda en tiempo real
if (isset($_GET['ajax']) && $_GET['ajax'] == '1') {
    $busqueda = $_GET['q'] ?? '';
    // Usamos LIKE para búsquedas parciales
    // CAMBIO CLAVE EN LA CONSULTA SQL (Línea 9 si cuentas desde <?php):
    // Se usan dos signos de interrogación '?' porque hay dos condiciones LIKE
    $sql = "SELECT CLAVE, CEDULA, NOMBREAPELLIDO AS NOMBRE_COMPLETO_PACIENTE, SEXO FROM PACIENTES WHERE CEDULA LIKE ? OR NOMBREAPELLIDO LIKE ? LIMIT 20";
    $stmt = $pdo->prepare($sql);
    // CAMBIO CLAVE EN LA EJECUCIÓN (Línea 11 si cuentas desde <?php):
    // Se pasa el valor de $busqueda dos veces en el array, una por cada '?'
    $stmt->execute(["%$busqueda%", "%$busqueda%"]);
    echo json_encode($stmt->fetchAll(PDO::FETCH_ASSOC));
    exit; // Importante para que no se imprima el resto del HTML
}

// Lógica para carga inicial (no aplica mucho con la búsqueda AJAX, pero se mantiene por estructura)
$busqueda = $_GET['q'] ?? '';
$resultados = [];
// El $sql de la sección AJAX ya fue definido, pero se puede redefinir aquí si la lógica fuera distinta
// En este caso, la búsqueda inicial se hará vía JS al cargar la página si el campo no está vacío.
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Buscar Paciente - Sistema Médico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- CORRECCIÓN DE RUTA: Asegúrate de que esta ruta sea correcta para tu archivo CSS -->
    <link href="../../assets/css/medical-theme.css" rel="stylesheet">
    <style>
        /* Variables y base del tema ya definidos en medical-theme.css */
        :root {
            --medical-primary: #2563eb; /* Azul oscuro */
            --medical-secondary: #0ea5e9; /* Azul claro */
            --medical-accent: #db2777; /* Rosa fuerte, ideal para femenino */
            --medical-gray-50: #f8fafc;
            --medical-gray-100: #f1f5f9;
            --medical-gray-200: #e2e8f0;
            --medical-gray-400: #94a3b8;
            --medical-gray-500: #64748b;
            --medical-gray-800: #1e293b;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            display: flex; /* Usar flexbox para centrar contenido */
            align-items: center; /* Centrar verticalmente */
            justify-content: center; /* Centrar horizontalmente */
            padding: 2rem 0; /* Padding superior e inferior */
        }
        
        .search-container {
            max-width: 800px;
            width: 90%; /* Ajuste para responsividad */
            margin: 0 auto; /* Asegura el centrado */
        }
        
        .search-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .search-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2); /* Sombra para el texto */
        }
        
        .search-header p {
            font-size: 1.125rem;
            opacity: 0.9;
        }
        
        .search-card {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            padding: 2rem;
            margin-bottom: 2rem;
            animation: fadeIn 0.5s ease-out; /* Animación de entrada */
        }
        
        .search-input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        
        .search-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--medical-gray-200);
            border-radius: var(--radius-lg);
            font-size: 1.125rem;
            transition: all 0.2s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--medical-primary);
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--medical-gray-400);
            font-size: 1.25rem;
            pointer-events: none; /* Para que no interfiera con el input click */
        }
        
        .search-results {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            margin-top: 1rem;
            animation: slideInUp 0.3s ease-out; /* Animación para los resultados */
        }
        
        .search-result-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid var(--medical-gray-100);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .search-result-item:hover {
            background: var(--medical-gray-50);
            transform: translateX(4px);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .patient-avatar-small {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            /* background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%); */
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.125rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* Sombra suave para el avatar */
        }
        
        .patient-info {
            flex: 1;
        }
        
        .patient-name {
            font-weight: 600;
            color: var(--medical-gray-800);
            margin-bottom: 0.25rem;
        }
        
        .patient-details {
            color: var(--medical-gray-500);
            font-size: 0.875rem;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        /* Estilo del spinner (puedes moverlo a medical-theme.css si es genérico) */
        .medical-spinner {
            border: 4px solid var(--medical-gray-200);
            border-top: 4px solid var(--medical-primary);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto; /* Centrar spinner */
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .no-results {
            text-align: center;
            padding: 3rem 2rem;
            color: var(--medical-gray-500);
        }
        
        .no-results-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .search-tips {
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            color: white;
            margin-top: 2rem;
        }
        
        .search-tips h6 {
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .search-tips ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .search-tips li {
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }

        /* Animaciones */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .search-header h1 {
                font-size: 2rem;
            }
            .search-header p {
                font-size: 1rem;
            }
            .search-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="search-container">
        <div class="search-header">
            <h1><i class="bi bi-person-fill-gear"></i> Sistema Médico</h1>
            <p>Buscar paciente para acceder a su historia clínica</p>
        </div>

        <div class="search-card">
            <div class="search-input-group">
                <span class="search-icon"><i class="bi bi-search"></i></span>
                <input 
                    type="text" 
                    id="buscar" 
                    class="search-input" 
                    placeholder="Ingrese nombre completo o número de cédula..."
                    autocomplete="off"
                >
            </div>
            
            <div class="loading-spinner" id="loading">
                <div class="medical-spinner"></div>
                <p class="mt-2 text-muted">Buscando pacientes...</p>
            </div>
            
            <div id="resultados-container" style="display: none;">
                <div id="resultados" class="search-results"></div>
            </div>
        </div>

        <div class="search-tips">
            <h6><i class="bi bi-lightbulb-fill"></i> Consejos de búsqueda</h6>
            <ul>
                <li>Puede buscar por nombre completo o parcial</li>
                <li>También puede buscar por número de cédula</li>
                <li>La búsqueda es automática mientras escribe</li>
                <li>Haga clic en cualquier resultado para acceder a la historia clínica</li>
            </ul>
        </div>
    </div>

    <script>
        let searchTimeout;
        const searchInput = document.getElementById('buscar');
        const resultsContainer = document.getElementById('resultados-container');
        const resultsList = document.getElementById('resultados');
        const loading = document.getElementById('loading');

        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            
            clearTimeout(searchTimeout); // Clear previous timeout
            
            if (query.length < 2) { // Disparar búsqueda solo con 2+ caracteres
                hideResults();
                return;
            }

            showLoading(); // Show loading spinner
            
            // Debounce search to reduce API calls
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300); // 300ms debounce
        });

        function showLoading() {
            loading.style.display = 'block';
            resultsContainer.style.display = 'none';
        }

        function hideLoading() {
            loading.style.display = 'none';
        }

        function showResults() {
            hideLoading();
            resultsContainer.style.display = 'block';
        }

        function hideResults() {
            hideLoading();
            resultsContainer.style.display = 'none';
            resultsList.innerHTML = ''; // Limpiar resultados cuando se ocultan
        }

        function performSearch(query) {
            // Usa la ruta relativa correcta si buscar_paciente.php está en el mismo directorio
            fetch(`buscar_paciente.php?ajax=1&q=${encodeURIComponent(query)}`)
                .then(response => {
                    if (!response.ok) { // Check for HTTP errors
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    hideLoading();
                    displayResults(data);
                })
                .catch(error => {
                    hideLoading();
                    console.error('Error en la búsqueda:', error);
                    showError('Error al realizar la búsqueda. Intente nuevamente. Detalles: ' + error.message);
                });
        }

        function displayResults(patients) {
            if (patients.length === 0) {
                showNoResults();
                return;
            }

            let html = '';
            patients.forEach(patient => {
                const sexoDB = patient.SEXO; // Valor de la base de datos (ej. 'Masculino', 'Femenino', 'Indefinido')
                
                let sexIcon = 'bi-question-circle'; // Icono por defecto (Bootstrap Icon)
                let sexText = 'Desconocido'; // Texto por defecto
                let sexBgColor = 'var(--medical-gray-500)'; // Color gris por defecto para el fondo del avatar
                
                // Lógica precisa para determinar el icono, texto y color basados en el valor completo de 'SEXO'
                if (sexoDB === 'Femenino') {
                    sexIcon = 'bi-gender-female'; // Icono femenino
                    sexText = 'Femenino';
                    sexBgColor = 'var(--medical-accent)'; // Color para Femenino (rosa)
                } else if (sexoDB === 'Masculino') {
                    sexIcon = 'bi-gender-male'; // Icono masculino
                    sexText = 'Masculino';
                    sexBgColor = 'var(--medical-primary)'; // Color para Masculino (azul)
                } else if (sexoDB === 'Indefinido') {
                    sexIcon = 'bi-gender-trans'; // Icono trans/indefinido
                    sexText = 'Indefinido';
                    sexBgColor = 'var(--medical-gray-400)'; // Otro color para Indefinido
                }

                html += `
                    <div class="search-result-item" onclick="selectPatient('${patient.CLAVE}', '${escapeHtml(patient.CEDULA)}', '${escapeHtml(sexoDB)}')">
                        <div class="patient-avatar-small" style="background: ${sexBgColor};">
                            <i class="bi ${sexIcon}"></i>
                        </div>
                        <div class="patient-info">
                            <div class="patient-name">${escapeHtml(patient.NOMBRE_COMPLETO_PACIENTE)}</div>
                            <div class="patient-details">
                                <span>CI: ${escapeHtml(patient.CEDULA)}</span>
                                <span class="mx-2">•</span>
                                <span>${sexText}</span> 
                            </div>
                        </div>
                        <div class="text-muted">
                            <i class="bi bi-arrow-right-circle-fill" style="font-size: 1.5rem; color: var(--medical-primary);"></i>
                        </div>
                    </div>
                `;
            });

            resultsList.innerHTML = html;
            showResults();
        }

        function showNoResults() {
            resultsList.innerHTML = `
                <div class="no-results">
                    <i class="bi bi-search no-results-icon"></i>
                    <h6>No se encontraron pacientes</h6>
                    <p>Intente con un término de búsqueda diferente</p>
                </div>
            `;
            showResults();
        }

        function showError(message) {
            resultsList.innerHTML = `
                <div class="no-results">
                    <i class="bi bi-exclamation-triangle-fill no-results-icon text-danger"></i>
                    <h6>Error en la búsqueda</h6>
                    <p>${message}</p>
                </div>
            `;
            showResults();
        }

        function selectPatient(clave, cedula, sexo) {
            // Obtener el elemento clickeado y aplicar el estilo de "seleccionado"
            const selectedItem = event.currentTarget;
            selectedItem.style.background = 'var(--medical-primary)';
            selectedItem.style.color = 'white';
            selectedItem.querySelector('.patient-name').style.color = 'white';
            selectedItem.querySelector('.patient-details').style.color = 'white';
            selectedItem.querySelector('.patient-avatar-small').style.background = 'white';
            selectedItem.querySelector('.patient-avatar-small i').style.color = 'var(--medical-primary)';
            selectedItem.querySelector('.bi-arrow-right-circle-fill').style.color = 'white';
            
            // Añadir un spinner o icono de carga al final
            selectedItem.innerHTML += '<div class="ms-auto"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span></div>';
            
            // Navegar a la historia del paciente después de un breve retardo
            setTimeout(() => {
                // Solución directa y absoluta
                const protocol = window.location.protocol;
                const host = window.location.host;

                console.log('=== DEBUG NAVEGACIÓN ===');
                console.log('Current URL:', window.location.href);
                console.log('Protocol:', protocol);
                console.log('Host:', host);

                // Construir URL absoluta completa
                const targetUrl = `${protocol}//${host}/backend/historia/historia_menu.php?CLAVEPAC=${clave}&CEDULA=${cedula}&SEXO=${sexo}`;

                console.log('Target URL (absolute):', targetUrl);

                // Navegar usando URL absoluta
                window.location.href = targetUrl;
            }, 500); // Retardo de 500ms para que la animación se vea
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Focus search input on load
        window.addEventListener('load', () => {
            searchInput.focus();
        });

        // Handle keyboard navigation (escape key to clear/hide results)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                searchInput.value = ''; // Clear search input
                hideResults();
                searchInput.blur();
            }
        });

        // Initialize search if there's a pre-filled query (e.g., from browser back button)
        if (searchInput.value.trim().length >= 2) {
            performSearch(searchInput.value.trim());
        }
    </script>
</body>
</html>