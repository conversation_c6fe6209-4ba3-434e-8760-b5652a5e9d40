<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Antecedentes Familiares"
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Función para obtener los antecedentes del paciente
function getAntecedentes($pdo, $clavePac) {
    $sql = "SELECT * FROM ANTECEDENTES WHERE CLAVEPAC = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Manejar la acción de guardar
if (isset($_POST['action']) && $_POST['action'] === 'saveAntecedentesFamiliares') {
    $familiares = $_POST['familiares'] ?? '';
    $patologicoFamiliares = $_POST['patologicoFamiliares'] ?? '';
    
    try {
        // Verificar si ya existe un registro
        $checkSql = "SELECT CLAVE FROM ANTECEDENTES WHERE CLAVEPAC = ?";
        $checkStmt = $pdo->prepare($checkSql);
        $checkStmt->execute([$clavePac]);
        $existingRecord = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingRecord) {
            // Actualizar registro existente
            $sql = "UPDATE ANTECEDENTES SET 
                    FAMILIARES = ?, 
                    PATOLOGICOFAMILIARES = ? 
                    WHERE CLAVEPAC = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$familiares, $patologicoFamiliares, $clavePac]);
        } else {
            // Insertar nuevo registro
            $sql = "INSERT INTO ANTECEDENTES (
                    CLAVEPAC, FAMILIARES, PATOLOGICOFAMILIARES) 
                    VALUES (?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$clavePac, $familiares, $patologicoFamiliares]);
        }
        
        echo json_encode(['success' => true]);
        exit;
    } catch (PDOException $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}

// Obtener datos existentes
$antecedentes = getAntecedentes($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Antecedentes Familiares</h4>
    
    <form id="form-antecedentes-familiares">
        <div class="form-group">
            <label for="familiares">Antecedentes Familiares</label>
            <textarea class="form-control" id="familiares" name="familiares" rows="4"><?php echo htmlspecialchars($antecedentes['FAMILIARES'] ?? ''); ?></textarea>
            <small class="form-text text-muted">Registre los antecedentes familiares relevantes del paciente.</small>
        </div>
        
        <div class="form-group">
            <label for="patologicoFamiliares">Antecedentes Patológicos Familiares</label>
            <textarea class="form-control" id="patologicoFamiliares" name="patologicoFamiliares" rows="4"><?php echo htmlspecialchars($antecedentes['PATOLOGICOFAMILIARES'] ?? ''); ?></textarea>
            <small class="form-text text-muted">Registre las enfermedades hereditarias o patologías presentes en la familia del paciente.</small>
        </div>
        
        <div class="text-right">
            <button type="button" class="btn btn-primary" id="btnSaveAntecedentesFamiliares">Guardar</button>
        </div>
    </form>
</div>

<script>
$(document).ready(function() {
    // Guardar antecedentes familiares
    $('#btnSaveAntecedentesFamiliares').click(function() {
        var formData = {
            action: 'saveAntecedentesFamiliares',
            familiares: $('#familiares').val(),
            patologicoFamiliares: $('#patologicoFamiliares').val()
        };
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert("Antecedentes familiares guardados correctamente.");
                } else {
                    alert("Error al guardar antecedentes familiares: " + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al guardar antecedentes familiares:", error);
                alert("Error al guardar antecedentes familiares. Consulte la consola para más detalles.");
            }
        });
    });
});
</script>
