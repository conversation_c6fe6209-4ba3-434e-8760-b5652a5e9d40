<?php
require_once '../config/database.php';

// Obtener la clave del paciente a editar
$clave = isset($_GET['clave']) ? $_GET['clave'] : 0;

// Consultar los datos del paciente
$stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
$stmt->execute([$clave]);
$paciente = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$paciente) {
    echo "Paciente no encontrado.";
    exit;
}

// Obtener los criterios de búsqueda para mantenerlos al regresar
$cedula = isset($_GET['cedula']) ? $_GET['cedula'] : '';
$registro = isset($_GET['registro']) ? $_GET['registro'] : '';
$nombres = isset($_GET['nombres']) ? $_GET['nombres'] : '';
$apellidos = isset($_GET['apellidos']) ? $_GET['apellidos'] : '';
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Paciente</title>
    <style>
        form {
            width: 400px;
            margin: auto;
        }
        label {
            display: block;
            margin: 10px 0 5px;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            margin-bottom: 15px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h2>Editar Paciente</h2>
    <form action="actualizar_paciente.php" method="POST">
        <input type="hidden" name="clave" value="<?php echo $paciente['CLAVE']; ?>">

    <!-- Campo oculto sincronizado con valor igual a 0 -->
    <input type="hidden" name="sincronizado" value="0">

        <label for="nombre">Nombres:</label>
        <input type="text" id="nombre" name="nombres" value="<?php echo htmlspecialchars($paciente['NOMBRES']); ?>" required>

        <label for="apellidos">Apellidos:</label>
        <input type="text" id="apellidos" name="apellidos" value="<?php echo htmlspecialchars($paciente['APELLIDOS']); ?>" required>

        <label for="cedula">Cédula:</label>
        <input type="text" id="cedula" name="cedula" value="<?php echo htmlspecialchars($paciente['CEDULA']); ?>" required>

        <label for="telefono">Teléfono:</label>
        <input type="text" id="telefono" name="telefono" value="<?php echo htmlspecialchars($paciente['TELEFONO']); ?>">

        <label for="sexo">Sexo:</label>
        <select id="sexo" name="sexo" required>
            <option value="M" <?php echo $paciente['SEXO'] == 'M' ? 'selected' : ''; ?>>Masculino</option>
            <option value="F" <?php echo $paciente['SEXO'] == 'F' ? 'selected' : ''; ?>>Femenino</option>
        </select>

        <label for="rh">RH:</label>
        <input type="text" id="rh" name="rh" value="<?php echo htmlspecialchars($paciente['RH']); ?>">

        <label for="correo">Correo Electrónico:</label>
        <input type="email" id="correo" name="correo" value="<?php echo htmlspecialchars($paciente['CORREO']); ?>">

        <button type="submit">Actualizar Paciente</button>
    </form>

    <br>
    <a href="buscar_pacientes.php?cedula=<?php echo urlencode($cedula); ?>&registro=<?php echo urlencode($registro); ?>&nombres=<?php echo urlencode($nombres); ?>&apellidos=<?php echo urlencode($apellidos); ?>">Volver a la búsqueda</a>
</body>
</html>
