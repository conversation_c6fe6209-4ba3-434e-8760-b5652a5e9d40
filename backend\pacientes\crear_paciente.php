<?php

session_start();
require_once '../config/database.php';
$config = include '../config/config.php'; // Ruta al archivo de configuración

ini_set('display_errors', 1);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

/**
 * Función para normalizar rutas de fotografías
 * Corrige rutas inconsistentes en la base de datos
 */
function normalizarRutaFoto($ruta) {
    if (empty($ruta)) {
        return 'fotografias/NOFOTO.BMP';
    }

    $ruta = trim($ruta);

    // Corregir rutas mal formadas comunes
    if ($ruta === 'FotografiasNOFOTO.BMP' || $ruta === 'NOFOTO.BMP') {
        return 'fotografias/NOFOTO.BMP';
    }

    // Convertir barras invertidas a barras normales
    $ruta = str_replace('\\', '/', $ruta);

    // Corregir doble directorio: fotografias/Fotografias/archivo.bmp
    if (strpos($ruta, 'fotografias/Fotografias/') === 0) {
        $ruta = str_replace('fotografias/Fotografias/', 'fotografias/', $ruta);
    }

    // Corregir directorio con mayúscula al inicio
    if (strpos($ruta, 'Fotografias/') === 0) {
        $ruta = str_replace('Fotografias/', 'fotografias/', $ruta);
    }

    // Corregir casos donde hay Fotografias en el medio
    $ruta = str_replace('/Fotografias/', '/fotografias/', $ruta);

    // Si no tiene directorio, agregarlo
    if (!strpos($ruta, '/') && $ruta !== 'fotografias/NOFOTO.BMP') {
        $ruta = 'fotografias/' . $ruta;
    }

    // Limpiar caracteres especiales que pueden causar problemas de URL
    $ruta = preg_replace('/[^\x20-\x7E]/', '', $ruta); // Remover caracteres no ASCII

    return $ruta;
}

// Cargar listado de categorías (tipo 'C')
$stmt_categ = $pdo->query("SELECT CLAVE, NOMBRE FROM CATEGORIAS WHERE TIPO = 'C' ORDER BY CLAVE");
$categList = $stmt_categ->fetchAll(PDO::FETCH_ASSOC);

$mensaje_error = '';
$mensaje_exito = '';

/**
 * Calcula la edad en años a partir de la fecha de nacimiento.
 *
 * @param string $fechaNacimiento La fecha de nacimiento en formato 'YYYY-MM-DD'.
 * @return int La edad calculada.
 */
function calcularEdad($fechaNacimiento) {
    $fechaNac = new DateTime($fechaNacimiento);
    $fechaActual = new DateTime();
    $diferencia = $fechaActual->diff($fechaNac);
    return $diferencia->y;
}

/**
 * Determina si la persona es menor de edad.
 *
 * @param string $fechaNacimiento La fecha de nacimiento en formato 'YYYY-MM-DD'.
 * @return bool Retorna true si la edad es menor a 18 años, false en caso contrario.
 */
function esMenorDeEdad($fechaNacimiento) {
    return calcularEdad($fechaNacimiento) < 18;
}


function validarCedulaRD($cedula) {
    if (strlen($cedula) != 11 || !ctype_digit($cedula)) {
        return false;
    }
    $suma = 0;
    $multiplicador = [1, 2];
    for ($i = 0; $i < 10; $i++) {
        $num = $cedula[$i] * $multiplicador[$i % 2];
        $suma += ($num > 9) ? $num - 9 : $num;
    }
    $digitoVerificador = (10 - ($suma % 10)) % 10;
    return $digitoVerificador == $cedula[10];
}

$paciente = null;
if (isset($_GET['CLAVE'])) {
    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
    $stmt->execute([$_GET['CLAVE']]);
    $paciente = $stmt->fetch();
}



// Si estamos en modo edición, recuperar la ruta de la foto:
if ($paciente) {
    $stmtFoto = $pdo->prepare("SELECT ARCHIVO FROM FOTOGRAFIAS WHERE CLAVEPAC = ?");
    $stmtFoto->execute([$paciente['CLAVE']]);
    $fotoPaciente = $stmtFoto->fetch(PDO::FETCH_ASSOC);
    $rutaFoto = (!empty($fotoPaciente['ARCHIVO'])) ? normalizarRutaFoto($fotoPaciente['ARCHIVO']) : normalizarRutaFoto("fotografias/NOFOTO.BMP");
} else {
    $rutaFoto = normalizarRutaFoto("fotografias/NOFOTO.BMP");
}

// Procesar los datos del formulario
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Recoger datos del formulario
    $NOMBRES       = $_POST['NOMBRES'];
    $APELLIDOS     = $_POST['APELLIDOS'];
    $CEDULA        = $_POST['CEDULA'];
    $FECHANAC      = $_POST['FECHANAC'];
    $SEXO          = $_POST['SEXO'];
    $NACIONALIDAD  = $_POST['NACIONALIDAD'];
    $RH            = $_POST['RH'];
    
    // Datos adicionales (fuera de pestañas)
    $ESTADOCIVIL   = $_POST['ESTADOCIVIL'];
    $LUGARNAC      = $_POST['LUGARNAC'];
    $OCUPACION     = $_POST['OCUPACION'];
    $RELIGION      = $_POST['RELIGION'];
    
    // DOMICILIO
    $CALLE      = $_POST['CALLE'];
    $PROVINCIA  = $_POST['PROVINCIA'];
    $LOCALIDAD  = $_POST['LOCALIDAD'];
    $MUNICIPIO  = $_POST['MUNICIPIO'];
    $PAIS       = $_POST['PAIS'];
    $REFERENCIA = $_POST['REFERENCIA'];
   
    // CONTACTO
    $TELEFONO   = $_POST['TELEFONO'];
    $CELULAR    = $_POST['CELULAR'];
    $TELTRABAJO = $_POST['TELTRABAJO'];
    $FAX        = $_POST['FAX'];
    $ECORREO    = $_POST['ECORREO'];
    
    // RESPONSABLE
    $NOMBRERESP    = $_POST['NOMBRERESP'];
    $DIRECCIONRESP = $_POST['DIRECCIONRESP'];
    $CEDULARESP    = $_POST['CEDULARESP'];
    $TELEFONORESP  = $_POST['TELEFONORESP'];
    
    // FAMILIAR
    $FAMILIARPROX      = $_POST['FAMILIARPROX'];
    $DIRECCIONFAMILIAR = $_POST['DIRECCIONFAMILIAR'];
    $TELEFONOFAMILIAR  = $_POST['TELEFONOFAMILIAR'];
    
    // PÓLIZA (se usa NSS en vez de POLIZA)
    $ARS       = $_POST['ARS'];
    $PLANES    = $_POST['PLANES'];
    $AFILIADO  = $_POST['AFILIADO'];
    $VIGENCIA  = $_POST['VIGENCIA'];
    
    $NSS              = $CEDULA;
    $CATEGORIA        = $_POST['CATEGORIA'];
    
    // Otros campos
    $OBSERVACIONES = $_POST['OBSERVACIONES'];
    $PESOHABITUAL  = $_POST['PESOHABITUAL'];
    $NIVELESCOLAR  = $_POST['NIVELESCOLAR'] ?? '0';
    $PROCEDENCIA   = $_POST['PROCEDENCIA'] ?? '0';
    $RECORDCLINICA = $_POST['RECORDCLINICA'] ;
    
    $nombrePaciente = $NOMBRES . " " . $APELLIDOS;
    
    // Procesar Estado Civil según Sexo
    $sexoInicial = strtoupper(trim(substr($SEXO, 0, 1)));
    if ($sexoInicial == "M") {
        $ESTADOCIVIL = trim(str_replace("(a)", "", $ESTADOCIVIL));
    } elseif ($sexoInicial == "F") {
        if (stripos($ESTADOCIVIL, "Soltero") !== false) {
            $ESTADOCIVIL = "Soltera";
        } elseif (stripos($ESTADOCIVIL, "Casado") !== false) {
            $ESTADOCIVIL = "Casada";
        } elseif (stripos($ESTADOCIVIL, "Viudo") !== false) {
            $ESTADOCIVIL = "Viuda";
        } elseif (stripos($ESTADOCIVIL, "Divorciado") !== false) {
            $ESTADOCIVIL = "Divorciada";
        } elseif (stripos($ESTADOCIVIL, "Separado") !== false) {
            $ESTADOCIVIL = "Separada";
        }
    }
    
    // Campos no están en el formulario
    $SINCEDULA = validarCedulaRD($CEDULA) ? 1 : 0;
    $VISITA = 1; // Primera visita

    // Recoger y mapear el campo PROCEDENCIA (si está vacío, se le asigna valor por defecto)
        $procedencia_input = $_POST['PROCEDENCIA'] ?? '';
        $mapProcedencia = [ 'Urbano' => '1', 'Rural' => '0' ];
        $procedencia_val = isset($mapProcedencia[$procedencia_input]) ? $mapProcedencia[$procedencia_input] : '0';

    
    // Verificar si la cédula ya existe en otro paciente
    if ($paciente) {
        $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CEDULA = ? AND CLAVE != ?");
        $stmt->execute([$CEDULA, $paciente['CLAVE']]);
    } else {
        $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CEDULA = ?");
        $stmt->execute([$CEDULA]);
    }
   
   
    $paciente_existente = $stmt->fetch();
    
    if ($paciente_existente) {
        $mensaje_error = "⚠️ ¡Atención! La cédula " . htmlspecialchars($paciente_existente['CEDULA']) . " ya está registrada con el nombre: " . htmlspecialchars($paciente_existente['NOMBREAPELLIDO']) . ".";

        // Si es una actualización del mismo paciente, permitir continuar
        if (isset($_GET['CLAVE']) && $_GET['CLAVE'] == $paciente_existente['CLAVE']) {
            // Es el mismo paciente, continuar con la actualización
        } else {
            // Es un paciente diferente, mostrar error y redirigir
            header("Location: crear_paciente.php?CLAVE=" . $paciente_existente['CLAVE'] . "&cedula_duplicada=1&error=" . urlencode($mensaje_error));
            exit;
        }
    } else {
        if ($paciente) {
            // Actualizar paciente existente
            $stmt = $pdo->prepare("UPDATE PACIENTES SET NOMBRES = ?, APELLIDOS = ?, CEDULA = ?, FECHANAC = ?, SEXO = ?, NACIONALIDAD = ?, TELEFONO = ?, RH = ?, ECORREO = ?,
            ESTADOCIVIL = ?, LUGARNAC = ?, OCUPACION = ?, RELIGION = ?, CALLE = ?, PROVINCIA = ?, LOCALIDAD = ?, MUNICIPIO = ?, PAIS = ?, REFERENCIA = ?, CELULAR = ?, TELTRABAJO = ?, FAX = ?,
            ARS = ?, PLANES = ?, AFILIADO = ?, VIGENCIA = ?, NSS = ?, CATEGORIA = ?, NOMBRERESP = ?, DIRECCIONRESP = ?, CEDULARESP = ?, TELEFONORESP = ?, FAMILIARPROX = ?, DIRECCIONFAMILIAR = ?, 
            TELEFONOFAMILIAR = ?, OBSERVACIONES = ?, PESOHABITUAL = ?, NIVELESCOLAR = ?, PROCEDENCIA = ?, RECORDCLINICA = ? WHERE CLAVE = ?");
            $stmt->execute([$NOMBRES, $APELLIDOS, $CEDULA, $FECHANAC, $SEXO, $NACIONALIDAD, $TELEFONO, $RH, $ECORREO, $ESTADOCIVIL, $LUGARNAC, $OCUPACION, $RELIGION,
            $CALLE, $PROVINCIA, $LOCALIDAD, $MUNICIPIO, $PAIS, $REFERENCIA, $CELULAR, $TELTRABAJO, $FAX, $ARS, $PLANES, $AFILIADO, $VIGENCIA, $NSS, $CATEGORIA,
            $NOMBRERESP, $DIRECCIONRESP, $CEDULARESP, $TELEFONORESP, $FAMILIARPROX, $DIRECCIONFAMILIAR, $TELEFONOFAMILIAR, $OBSERVACIONES, $PESOHABITUAL, $NIVELESCOLAR, $PROCEDENCIA,
            $RECORDCLINICA, $paciente['CLAVE']]);
            $mensaje_exito = "Paciente actualizado con éxito.";
            $idPaciente = $paciente['CLAVE'];
        } else {
            // Calcular el nuevo REGISTRO
            $stmt = $pdo->query("SELECT MAX(REGISTRO) AS max_registro FROM PACIENTES");
            $result = $stmt->fetch();
            $nuevo_registro = $result['max_registro'] ? $result['max_registro'] + 1 : 1;
            if ($CEDULA === "00000000000") {
                $CEDULA = str_pad($nuevo_registro, 11, "0", STR_PAD_LEFT);
                
               $SINCEDULA = esMenorDeEdad($FECHANAC) ? 2 : 0;
            }
            
            $NSS = $CEDULA;
            // Insertar nuevo paciente
            $stmt = $pdo->prepare("INSERT INTO PACIENTES (REGISTRO, VISITA, NOMBRES, APELLIDOS, CEDULA, FECHANAC, SEXO, NACIONALIDAD, TELEFONO, RH, ECORREO, ESTADOCIVIL, LUGARNAC, OCUPACION, RELIGION,
            CALLE, PROVINCIA, LOCALIDAD, MUNICIPIO, PAIS, REFERENCIA, CELULAR, TELTRABAJO, FAX, ARS, PLANES, AFILIADO, VIGENCIA, NSS, CATEGORIA, NOMBRERESP, DIRECCIONRESP, CEDULARESP,
            TELEFONORESP, FAMILIARPROX, DIRECCIONFAMILIAR, TELEFONOFAMILIAR, OBSERVACIONES, PESOHABITUAL, NIVELESCOLAR, SINCEDULA, PROCEDENCIA, RECORDCLINICA, ESTATUS, STEMBARAZO, SINCRONIZADO)  
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'A', 0, 0)");
            $stmt->execute([$nuevo_registro, $VISITA, $NOMBRES, $APELLIDOS, $CEDULA, $FECHANAC, $SEXO, $NACIONALIDAD, $TELEFONO, $RH, $ECORREO, $ESTADOCIVIL, $LUGARNAC, $OCUPACION, $RELIGION,
            $CALLE, $PROVINCIA, $LOCALIDAD, $MUNICIPIO, $PAIS, $REFERENCIA, $CELULAR, $TELTRABAJO, $FAX, $ARS, $PLANES, $AFILIADO, $VIGENCIA, $NSS, $CATEGORIA, $NOMBRERESP, $DIRECCIONRESP,
            $CEDULARESP, $TELEFONORESP, $FAMILIARPROX, $DIRECCIONFAMILIAR, $TELEFONOFAMILIAR, $OBSERVACIONES, $PESOHABITUAL, $NIVELESCOLAR, $SINCEDULA, $PROCEDENCIA, $RECORDCLINICA]);
            $mensaje_exito = "Paciente registrado con éxito.";
           
            $idPaciente = $pdo->lastInsertId(); // Para usar en la fotografia 
        }
        

// Procesar la fotografía: se da prioridad a la imagen capturada desde la cámara
if (isset($_POST['imagenCapturada']) && !empty($_POST['imagenCapturada'])) {
    $dataURL = $_POST['imagenCapturada'];
    // Asegurarse de que el Data URL tenga el formato correcto.
    if (strpos($dataURL, 'data:image') !== 0) {
        echo "El formato de la imagen capturada no es correcto.";
        exit;
    }
    
    // Separar la cabecera de los datos
    $parts = explode(',', $dataURL);
    if (count($parts) < 2) {
        echo "Datos de imagen incompletos.";
        exit;
    }
    $data = $parts[1];
    
    // Decodificar la imagen desde Base64
    $decodedData = base64_decode($data);
    if ($decodedData === false) {
        $mensaje_error = "❌ Error al decodificar la imagen capturada. Por favor, intente nuevamente.";
        error_log("Error al decodificar imagen Base64 para paciente: " . $CEDULA);
        // No hacer exit, continuar sin imagen
    } else {
        // Crear una imagen desde los datos decodificados
        $img = imagecreatefromstring($decodedData);
        if (!$img) {
            $mensaje_error = "❌ Error al procesar la imagen capturada. Verifique el formato de la imagen.";
            error_log("Error al crear imagen desde string para paciente: " . $CEDULA);
            // No hacer exit, continuar sin imagen
        }
    }
    
    // Construir el nombre del archivo basado en la cédula
    $nombreFoto = $CEDULA . '.bmp';
    $directorioFotos = __DIR__ . '/fotografias/';
    if (!is_dir($directorioFotos)) {
        mkdir($directorioFotos, 0777, true);
    }
    $rutaFotoCompleta = $directorioFotos . $nombreFoto;
    
    // Guardar la imagen en formato BMP
    if (!imagebmp($img, $rutaFotoCompleta)) {
        echo "Error al guardar la imagen capturada en formato BMP.";
        exit;
    }
    imagedestroy($img);
    
    // Definir la ruta relativa para guardar en la base de datos
    $rutaFoto = normalizarRutaFoto('fotografias/' . $nombreFoto);
    
} elseif (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
    // Procesar la imagen subida desde el archivo
    $directorioFotos = __DIR__ . '/fotografias/';
    if (!is_dir($directorioFotos)) {
        mkdir($directorioFotos, 0777, true);
    }
    
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $_FILES['foto']['tmp_name']);
    finfo_close($finfo);
    
    if (in_array($mimeType, ['image/jpeg', 'image/png', 'image/bmp', 'image/x-ms-bmp'])) {
        if ($mimeType === 'image/jpeg') {
            $img = imagecreatefromjpeg($_FILES['foto']['tmp_name']);
        } elseif ($mimeType === 'image/png') {
            $img = imagecreatefrompng($_FILES['foto']['tmp_name']);
        } else {
            $img = imagecreatefrombmp($_FILES['foto']['tmp_name']);
        }
        
        if (!$img) {
            echo "Error al procesar la imagen subida.";
            exit;
        }
        
        $nombreFoto = $CEDULA . '.bmp';
        $rutaFotoCompleta = $directorioFotos . $nombreFoto;
        
        if (!imagebmp($img, $rutaFotoCompleta)) {
            echo "Error al guardar la imagen subida en formato BMP.";
            exit;
        }
        imagedestroy($img);
        
        $rutaFoto = normalizarRutaFoto('fotografias/' . $nombreFoto);
    } else {
        echo "La imagen debe ser de tipo JPEG, PNG o BMP.";
        exit;
    }
} else {
    $rutaFoto = normalizarRutaFoto("fotografias/NOFOTO.BMP");
}
// =====================================================================
// 4. Procesar el registro de la fotografía en la BD (INSERT/UPDATE)
// =====================================================================
$fechaCap = date('Y-m-d H:i:s'); // Capturar la fecha actual

if ($paciente) {
    // Modo actualización: ya existe el paciente
    $claveImg = $paciente['REGISTRO']; // Se asume que REGISTRO es el identificador que se usa para la foto
    // Verificar si ya existe una foto para este paciente
    $stmtFoto = $pdo->prepare("SELECT CLAVE FROM FOTOGRAFIAS WHERE CLAVEPAC = ?");
    $stmtFoto->execute([$paciente['CLAVE']]);
    $fotoExistente = $stmtFoto->fetch();
    if ($fotoExistente) {
    // ✅ Actualizar la fotografía
    $stmtUpdateFoto = $pdo->prepare("UPDATE FOTOGRAFIAS 
        SET NOMBRE = ?, ARCHIVO = ?, TIPO = ?, CEDULA = ?, CLAVEIMG = ?,  FECHA_CAP = ?, SINCRONIZADO = 0
        WHERE CLAVEPAC = ?");
    $stmtUpdateFoto->execute([$nombrePaciente, $rutaFoto, 1, $CEDULA, $claveImg, $fechaCap, $paciente['CLAVE']]);
} else {
    // ✅ Insertar la fotografía nueva
    $stmtInsertFoto = $pdo->prepare("INSERT INTO FOTOGRAFIAS 
        (NOMBRE, ARCHIVO, CLAVEPAC, TIPO, CEDULA, CLAVEIMG,FECHA_CAP, SINCRONIZADO ) 
        VALUES (?, ?, ?, ?, ?, ?, ?, 0)");
    $stmtInsertFoto->execute([$nombrePaciente, $rutaFoto, $paciente['CLAVE'], 1, $CEDULA, $claveImg, $fechaCap]);
}
} else {
    // Modo inserción: el paciente se acaba de insertar
    $idPaciente = $pdo->lastInsertId();
    $stmtReg = $pdo->prepare("SELECT REGISTRO FROM PACIENTES WHERE CLAVE = ?");
    $stmtReg->execute([$idPaciente]);
    $row = $stmtReg->fetch();
    $claveImg = $row ? $row['REGISTRO'] : $idPaciente;
    
    $stmtInsertFoto = $pdo->prepare("INSERT INTO FOTOGRAFIAS 
    (NOMBRE, ARCHIVO, CLAVEPAC, TIPO, CEDULA, CLAVEIMG, FECHA_CAP,SINCRONIZADO) VALUES (?, ?, ?, ?, ?, ?, ?, 0)");
        $stmtInsertFoto->execute([$nombrePaciente, $rutaFoto, $idPaciente, 1, $CEDULA, $claveImg, $fechaCap]);
}
header("Location: gestion_pacientes.php?tab=registrar");
exit;

    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Registrar Paciente - Sistema Médico</title>

  <!-- Favicon médico -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
  <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">

  <!-- Los estilos se heredan del archivo principal gestion_pacientes.php -->
  <script>
      function validarFormulario() {
          var nombres   = document.getElementById("NOMBRES").value;
          var apellidos = document.getElementById("APELLIDOS").value;
          var cedula    = document.getElementById("CEDULA").value;
          var fechanac  = document.getElementById("FECHANAC").value;
          var correo    = document.getElementById("ECORREO").value;
         
          if (nombres.length < 2 || nombres.length > 35) {
              alert("El nombre debe tener entre 2 y 35 caracteres.");
              return false;
          }
          if (apellidos.length < 2 || apellidos.length > 35) {
              alert("El apellido debe tener entre 2 y 35 caracteres.");
              return false;
          }
          if (cedula.length !== 11 || !/^\d+$/.test(cedula)) {
              alert("La cédula debe tener exactamente 11 caracteres numéricos.");
              return false;
          }
          if (correo && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(correo)) {
              alert("Por favor ingrese un correo electrónico válido.");
              return false;
          }
          return true;
      }
      
      function cancelarYEnfocarCedula() {
          window.history.back();
          setTimeout(() => {
              const campoCedula = document.getElementById('CEDULA');
              if (campoCedula) {
                  campoCedula.focus();
                  campoCedula.select();
              }
          }, 100);
      }
      
      // Actualiza el campo NSS para que siempre contenga el valor de la cédula.
      document.addEventListener("DOMContentLoaded", function () {
          const cedulaInput = document.getElementById('CEDULA');
          const nssInput = document.getElementById('NSS');
          if (cedulaInput && nssInput) {
              cedulaInput.addEventListener('input', function () {
                  nssInput.value = this.value;
              });
          }
      });
      
      document.addEventListener("DOMContentLoaded", function() {
          const sexoSelect = document.getElementById("SEXO");
          const estadoSelect = document.getElementById("ESTADOCIVIL");
          const estadosMasculinos = ["Soltero", "Casado", "Viudo", "Divorciado", "Separado", "Unión Libre"];
          const estadosFemeninos   = ["Soltera", "Casada", "Viuda", "Divorciada", "Separada", "Unión Libre"];
          const estadosNeutros      = ["Soltero(a)", "Casado(a)", "Viudo(a)", "Divorciado(a)", "Separado(a)", "Unión Libre"];
      
          function actualizarEstadoCivil() {
              let sexo = sexoSelect.value;
              let opcionesHTML = "<option value=''>Seleccionar</option>";
              if (sexo === "Masculino") {
                  estadosMasculinos.forEach(function(opcion) {
                      opcionesHTML += `<option value="${opcion}">${opcion}</option>`;
                  });
              } else if (sexo === "Femenino") {
                  estadosFemeninos.forEach(function(opcion) {
                      opcionesHTML += `<option value="${opcion}">${opcion}</option>`;
                  });
              } else {
                  estadosNeutros.forEach(function(opcion) {
                      opcionesHTML += `<option value="${opcion}">${opcion}</option>`;
                  });
              }
              estadoSelect.innerHTML = opcionesHTML;
          }
      
          sexoSelect.addEventListener("change", actualizarEstadoCivil);
          actualizarEstadoCivil();
      });
  </script>
  

</head>
<!-- El contenido se incluye dentro del archivo principal gestion_pacientes.php -->

<!-- Header de la sección de registro -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="text-primary mb-1">
            <i class="fas fa-user-plus me-2"></i>
            Registrar/Actualizar Paciente
        </h4>
        <p class="text-muted mb-0">
            <i class="fas fa-info-circle me-1"></i>
            Complete la información médica del paciente
        </p>
    </div>
    <div class="badge bg-success fs-6">
        <i class="fas fa-stethoscope me-1"></i>
        Sistema Médico
    </div>
</div>

<!-- Alertas médicas -->
<?php
if (isset($_GET['cedula_duplicada']) && $_GET['cedula_duplicada'] == 1) {
    echo '<div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Paciente Existente:</strong> La cédula ingresada ya existe. Se han cargado los datos del paciente existente para su visualización/edición.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
          </div>';
}
?>
    
    <!-- Formulario con enctype para subir archivos -->
   
   <form action="crear_paciente.php<?php echo isset($paciente) ? '?CLAVE=' . htmlspecialchars($paciente['CLAVE']) : ''; ?>" method="POST" enctype="multipart/form-data" novalidate onsubmit="return validarFormulario();">
       
      
  <!-- Sección de Captura de Imagen Médica Moderna -->
      <div class="medical-image-capture-section mb-5">
        <div class="row">
          <div class="col-12">
            <div class="medical-image-header text-center mb-4">
              <h5 class="text-primary mb-2">
                <i class="fas fa-camera-retro me-2"></i>
                Fotografía del Paciente
              </h5>
              <p class="text-muted small mb-0">
                <i class="fas fa-info-circle me-1"></i>
                Capture o suba una fotografía para el expediente médico
              </p>
            </div>
          </div>
        </div>

        <div class="row g-4">
          <!-- Sección de Captura en Vivo -->
          <div class="col-lg-6">
            <div class="medical-camera-container">
              <div class="camera-header">
                <h6 class="text-secondary mb-3">
                  <i class="fas fa-video me-2"></i>
                  Captura en Vivo
                </h6>
              </div>

              <div class="camera-viewport">
                <video id="video" autoplay muted playsinline class="medical-video"></video>
                <div class="camera-overlay">
                  <div class="camera-frame"></div>
                  <div class="camera-corners">
                    <div class="corner top-left"></div>
                    <div class="corner top-right"></div>
                    <div class="corner bottom-left"></div>
                    <div class="corner bottom-right"></div>
                  </div>
                </div>
              </div>

              <div class="camera-controls mt-3 text-center">
                <button type="button" id="capturar" class="btn btn-medical-primary btn-lg">
                  <i class="fas fa-camera me-2"></i>
                  Capturar Fotografía
                </button>
                <button type="button" id="iniciarCamera" class="btn btn-medical-success btn-lg ms-2" style="display:none;">
                  <i class="fas fa-play me-2"></i>
                  Iniciar Cámara
                </button>
              </div>
            </div>
          </div>

          <!-- Sección de Vista Previa y Subida -->
          <div class="col-lg-6">
            <div class="medical-preview-container">
              <div class="preview-header">
                <h6 class="text-secondary mb-3">
                  <i class="fas fa-image me-2"></i>
                  Vista Previa y Subida
                </h6>
              </div>

              <!-- Vista previa de la imagen -->
              <div class="image-preview-area">
                <div id="previewContainer" class="preview-placeholder">
                  <i class="fas fa-user-circle preview-icon"></i>
                  <p class="preview-text">Vista previa de la fotografía</p>
                </div>
                <img id="previewImage" src="" alt="Vista previa" class="preview-image" style="display:none;">
              </div>

              <!-- Subir desde archivo -->
              <div class="file-upload-area mt-3">
                <label for="foto" class="file-upload-label">
                  <i class="fas fa-cloud-upload-alt me-2"></i>
                  Subir desde Archivo
                </label>
                <input type="file" class="form-control file-input" id="foto" name="foto" accept="image/*" capture="camera">
                <small class="text-muted">
                  <i class="fas fa-info-circle me-1"></i>
                  Formatos: JPG, PNG, BMP (máx. 5MB)
                </small>
              </div>
            </div>
          </div>
        </div>

        <!-- Canvas oculto y campo para datos -->
        <canvas id="canvas" style="display:none;"></canvas>
        <input type="hidden" id="imagenCapturada" name="imagenCapturada">
      </div>

      <style>
        /* Estilos para la sección de captura de imagen */
        .medical-image-capture-section {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 252, 0.9) 100%);
          border-radius: 20px;
          padding: 2rem;
          border: 2px solid rgba(30, 64, 175, 0.1);
          box-shadow: 0 8px 25px rgba(30, 64, 175, 0.1);
          position: relative;
          overflow: hidden;
        }

        .medical-image-capture-section::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg,
            var(--medical-primary) 0%,
            var(--medical-accent) 50%,
            var(--success-medical) 100%);
        }

        .medical-image-header h5 {
          font-weight: 700;
          font-size: 1.3rem;
        }

        .medical-camera-container,
        .medical-preview-container {
          background: rgba(255, 255, 255, 0.8);
          border-radius: 16px;
          padding: 1.5rem;
          border: 1px solid rgba(30, 64, 175, 0.1);
          box-shadow: 0 4px 15px rgba(30, 64, 175, 0.05);
          height: 100%;
        }

        .camera-viewport {
          position: relative;
          border-radius: 12px;
          overflow: hidden;
          background: #000;
          aspect-ratio: 4/3;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .medical-video {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px;
        }

        .camera-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          pointer-events: none;
        }

        .camera-frame {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 200px;
          height: 200px;
          border: 2px dashed rgba(255, 255, 255, 0.8);
          border-radius: 50%;
          animation: pulse-frame 2s infinite;
        }

        .camera-corners {
          position: absolute;
          top: 20px;
          left: 20px;
          right: 20px;
          bottom: 20px;
        }

        .corner {
          position: absolute;
          width: 20px;
          height: 20px;
          border: 3px solid var(--medical-accent);
        }

        .corner.top-left {
          top: 0;
          left: 0;
          border-right: none;
          border-bottom: none;
        }

        .corner.top-right {
          top: 0;
          right: 0;
          border-left: none;
          border-bottom: none;
        }

        .corner.bottom-left {
          bottom: 0;
          left: 0;
          border-right: none;
          border-top: none;
        }

        .corner.bottom-right {
          bottom: 0;
          right: 0;
          border-left: none;
          border-top: none;
        }

        .image-preview-area {
          position: relative;
          aspect-ratio: 4/3;
          border-radius: 12px;
          overflow: hidden;
          background: linear-gradient(135deg,
            var(--medical-gray-100) 0%,
            var(--medical-gray-50) 100%);
          border: 2px dashed var(--medical-gray-300);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .preview-placeholder {
          text-align: center;
          color: var(--medical-gray-400);
        }

        .preview-icon {
          font-size: 4rem;
          margin-bottom: 1rem;
          color: var(--medical-gray-300);
        }

        .preview-text {
          font-size: 0.9rem;
          margin: 0;
        }

        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 10px;
        }

        .file-upload-area {
          text-align: center;
        }

        .file-upload-label {
          display: inline-block;
          padding: 0.75rem 1.5rem;
          background: linear-gradient(135deg,
            var(--info-medical) 0%,
            var(--medical-accent) 100%);
          color: white;
          border-radius: 12px;
          cursor: pointer;
          transition: var(--transition);
          font-weight: 500;
          margin-bottom: 0.5rem;
        }

        .file-upload-label:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
        }

        .file-input {
          display: none;
        }

        .camera-controls .btn {
          border-radius: 12px;
          padding: 0.75rem 1.5rem;
          font-weight: 600;
          box-shadow: 0 4px 15px rgba(30, 64, 175, 0.2);
          transition: var(--transition);
        }

        .camera-controls .btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
        }

        @keyframes pulse-frame {
          0%, 100% {
            opacity: 0.6;
            transform: translate(-50%, -50%) scale(1);
          }
          50% {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.05);
          }
        }

        /* Responsive */
        @media (max-width: 768px) {
          .medical-image-capture-section {
            padding: 1.5rem;
          }

          .camera-controls .btn {
            width: 100%;
            margin: 0.25rem 0;
          }

          .camera-controls .btn.ms-2 {
            margin-left: 0 !important;
          }
        }

        /* Estilos para los tabs de información del paciente */
        .medical-info-tabs-container {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 252, 0.9) 100%);
          border-radius: 20px;
          padding: 2rem;
          border: 2px solid rgba(30, 64, 175, 0.1);
          box-shadow: 0 8px 25px rgba(30, 64, 175, 0.1);
          position: relative;
          overflow: hidden;
          margin: 2rem 0;
        }

        .medical-info-tabs-container::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(90deg,
            #1e40af 0%,
            #3b82f6 20%,
            #0ea5e9 40%,
            #06b6d4 60%,
            #10b981 80%,
            #f59e0b 100%);
        }

        .info-tabs-header h6 {
          font-weight: 700;
          font-size: 1.2rem;
        }

        .medical-info-nav {
          background: linear-gradient(135deg,
            rgba(30, 64, 175, 0.05) 0%,
            rgba(59, 130, 246, 0.03) 50%,
            rgba(14, 165, 233, 0.05) 100%);
          border-radius: 16px;
          padding: 0.75rem;
          border: 1px solid rgba(30, 64, 175, 0.1);
          box-shadow: inset 0 2px 8px rgba(30, 64, 175, 0.05);
        }

        .medical-info-tab {
          border: none !important;
          border-radius: 12px !important;
          padding: 1rem 1.5rem !important;
          font-weight: 600 !important;
          color: var(--medical-gray-600) !important;
          background: rgba(255, 255, 255, 0.8) !important;
          backdrop-filter: blur(10px);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
          position: relative;
          overflow: hidden;
          margin: 0.25rem !important;
          border: 1px solid rgba(255, 255, 255, 0.3) !important;
          box-shadow: 0 4px 12px rgba(30, 64, 175, 0.08);
          display: flex !important;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
          min-height: 70px;
          text-decoration: none;
        }

        .info-tab-icon {
          width: 35px;
          height: 35px;
          border-radius: 10px;
          background: linear-gradient(135deg,
            rgba(30, 64, 175, 0.1) 0%,
            rgba(59, 130, 246, 0.05) 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          flex-shrink: 0;
        }

        .info-tab-icon i {
          font-size: 1.1rem;
          color: var(--medical-gray-500);
          transition: all 0.3s ease;
        }

        .info-tab-text {
          font-size: 0.9rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          text-align: center;
          line-height: 1.2;
        }

        /* Efectos hover para tabs de información */
        .medical-info-tab:hover {
          color: var(--medical-primary) !important;
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(219, 234, 254, 0.8) 100%) !important;
          transform: translateY(-3px) scale(1.02);
          border-color: rgba(30, 64, 175, 0.2) !important;
          box-shadow: 0 8px 20px rgba(30, 64, 175, 0.15);
        }

        .medical-info-tab:hover .info-tab-icon {
          background: linear-gradient(135deg,
            rgba(30, 64, 175, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 100%);
          transform: scale(1.1) rotate(5deg);
        }

        .medical-info-tab:hover .info-tab-icon i {
          color: var(--medical-primary);
          transform: scale(1.2);
        }

        /* Estado activo para tabs de información */
        .medical-info-tab.active {
          color: var(--medical-white) !important;
          background: linear-gradient(135deg,
            var(--medical-primary) 0%,
            var(--medical-secondary) 100%) !important;
          transform: translateY(-4px) scale(1.05);
          border-color: var(--medical-primary) !important;
          box-shadow: 0 12px 30px rgba(30, 64, 175, 0.3);
          position: relative;
          z-index: 10;
        }

        .medical-info-tab.active .info-tab-icon {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.2) 0%,
            rgba(255, 255, 255, 0.1) 100%);
          transform: scale(1.15);
          box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
        }

        .medical-info-tab.active .info-tab-icon i {
          color: var(--medical-white);
          transform: scale(1.3);
          text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
        }

        .medical-info-tab.active .info-tab-text {
          color: var(--medical-white);
          text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        /* Animación de resplandor para tab activo */
        .medical-info-tab.active {
          animation: info-tab-glow 2s ease-in-out infinite alternate;
        }

        @keyframes info-tab-glow {
          from {
            box-shadow: 0 12px 30px rgba(30, 64, 175, 0.3);
          }
          to {
            box-shadow: 0 12px 30px rgba(30, 64, 175, 0.5),
                       0 0 15px rgba(30, 64, 175, 0.2);
          }
        }

        /* Colores específicos para cada tab */
        #domicilio-tab.active {
          background: linear-gradient(135deg, #10b981 0%, #34d399 100%) !important;
        }

        #contacto-tab.active {
          background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%) !important;
        }

        #poliza-tab.active {
          background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%) !important;
        }

        #responsable-tab.active {
          background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%) !important;
        }

        #familiar-tab.active {
          background: linear-gradient(135deg, #ef4444 0%, #f87171 100%) !important;
        }

        /* Responsive para tabs de información */
        @media (max-width: 992px) {
          .medical-info-tab {
            padding: 0.75rem 1rem !important;
            min-height: 60px;
          }

          .info-tab-icon {
            width: 30px;
            height: 30px;
            border-radius: 8px;
          }

          .info-tab-icon i {
            font-size: 1rem;
          }

          .info-tab-text {
            font-size: 0.8rem;
          }
        }

        @media (max-width: 768px) {
          .medical-info-tabs-container {
            padding: 1.5rem;
            margin: 1rem 0;
          }

          .medical-info-nav {
            padding: 0.5rem;
          }

          .medical-info-tab {
            padding: 0.5rem 0.75rem !important;
            min-height: 55px;
            margin: 0.15rem !important;
          }

          .info-tab-icon {
            width: 28px;
            height: 28px;
            border-radius: 7px;
          }

          .info-tab-icon i {
            font-size: 0.9rem;
          }

          .info-tab-text {
            font-size: 0.75rem;
          }
        }

        @media (max-width: 576px) {
          .medical-info-tab {
            padding: 0.4rem 0.5rem !important;
            min-height: 50px;
          }

          .info-tab-text {
            font-size: 0.7rem;
            letter-spacing: 0.3px;
          }
        }

        /* Estilos para los paneles de contenido de las pestañas */
        .medical-info-tab-content {
          margin-top: 1.5rem;
        }

        .medical-info-panel {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.95) 0%,
            rgba(248, 250, 252, 0.9) 100%);
          border-radius: 16px;
          padding: 2rem;
          border: 1px solid rgba(30, 64, 175, 0.1);
          box-shadow: 0 4px 15px rgba(30, 64, 175, 0.05);
          position: relative;
          overflow: hidden;
        }

        .medical-info-panel::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: linear-gradient(90deg,
            currentColor 0%,
            transparent 100%);
          opacity: 0.3;
        }

        .panel-header {
          border-bottom: 1px solid rgba(30, 64, 175, 0.1);
          padding-bottom: 1rem;
          margin-bottom: 1.5rem;
        }

        .panel-header h6 {
          font-weight: 700;
          font-size: 1.1rem;
          margin-bottom: 0.5rem;
        }

        .panel-header p {
          margin-bottom: 0;
          font-size: 0.9rem;
        }

        /* Animación de entrada para los paneles */
        .tab-pane.show .medical-info-panel {
          animation: slideInPanel 0.4s ease-out;
        }

        @keyframes slideInPanel {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Responsive para paneles */
        @media (max-width: 768px) {
          .medical-info-panel {
            padding: 1.5rem;
          }

          .panel-header {
            padding-bottom: 0.75rem;
            margin-bottom: 1rem;
          }
        }
      </style>
  
      <!-- Campos principales -->
      <div class="form-group">
          <label for="CEDULA">Cédula:</label>
          <input type="text" class="form-control" id="CEDULA" name="CEDULA" value="<?php echo $paciente ? htmlspecialchars($paciente['CEDULA']) : ''; ?>" required oninput="validarNumeros(event)" maxlength="11">
      </div>
      
      <div class="form-group">
          <label for="NOMBRES">Nombre:</label>
          <input type="text" class="form-control" id="NOMBRES" name="NOMBRES" value="<?php echo $paciente ? htmlspecialchars($paciente['NOMBRES']) : ''; ?>" required maxlength="35">
      </div>
      
      <div class="form-group">
          <label for="APELLIDOS">Apellido:</label>
          <input type="text" class="form-control" id="APELLIDOS" name="APELLIDOS" value="<?php echo $paciente ? htmlspecialchars($paciente['APELLIDOS']) : ''; ?>" required maxlength="35">
      </div>
      
      <div class="form-group">
          <label for="FECHANAC">Fecha de Nacimiento:</label>
          <input type="date" class="form-control" id="FECHANAC" name="FECHANAC" value="<?php echo $paciente ? htmlspecialchars($paciente['FECHANAC']) : ''; ?>" required oninput="validarFechaNacimiento(event)">
      </div>
      
      <div class="form-group">
          <label for="SEXO">Sexo:</label>
          <select class="form-control" id="SEXO" name="SEXO">
              <option value="" <?php echo !$paciente || htmlspecialchars($paciente['SEXO']) == '' ? 'selected' : ''; ?>>Seleccionar</option>
              <option value="Masculino" <?php echo $paciente && htmlspecialchars($paciente['SEXO']) == 'Masculino' ? 'selected' : ''; ?>>Masculino</option>
              <option value="Femenino" <?php echo $paciente && htmlspecialchars($paciente['SEXO']) == 'Femenino' ? 'selected' : ''; ?>>Femenino</option>
              <option value="Indefinido" <?php echo $paciente && htmlspecialchars($paciente['SEXO']) == 'Indefinido' ? 'selected' : ''; ?>>Indefinido</option>
          </select>
      </div>
     
      <div class="form-group">
          <label for="RH">Tipo de Sangre:</label>
          <select class="form-control" id="RH" name="RH">
              <option value="" <?= !$paciente || htmlspecialchars($paciente['RH']) == '' ? 'selected' : '' ?>>Seleccionar</option>
              <option value="O+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'O+' ? 'selected' : '' ?>>O+</option>
              <option value="A+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'A+' ? 'selected' : '' ?>>A+</option>
              <option value="B+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'B+' ? 'selected' : '' ?>>B+</option>
              <option value="AB+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'AB+' ? 'selected' : '' ?>>AB+</option>
              <option value="O-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'O-' ? 'selected' : '' ?>>O-</option>
              <option value="A-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'A-' ? 'selected' : '' ?>>A-</option>
              <option value="B-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'B-' ? 'selected' : '' ?>>B-</option>
              <option value="AB-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'AB-' ? 'selected' : '' ?>>AB-</option>
          </select>
      
      </div>
      <div class="form-group">
          <label for="PESOHABITUAL">Peso Habitual:</label>
          <input type="number" step="0.01" class="form-control" id="PESOHABITUAL" name="PESOHABITUAL" value="<?php echo $paciente ? htmlspecialchars($paciente['PESOHABITUAL']) : ''; ?>">
      </div>
      
      <div class="form-group">
          <label for="NACIONALIDAD">Nacionalidad:</label>
          <input type="text" class="form-control" id="NACIONALIDAD" name="NACIONALIDAD" value="<?php echo $paciente ? htmlspecialchars($paciente['NACIONALIDAD']) : ''; ?>" maxlength="35">
      </div>
      
      <hr>
      <h4>Información Adicional</h4>
      
      <div class="form-group">
          <label for="ESTADOCIVIL">Estado Civil:</label>
          <select id="ESTADOCIVIL" name="ESTADOCIVIL" class="form-control">
              <option value="">Seleccionar</option>
          </select>
      
      </div>
      <div class="form-group">
          <label for="LUGARNAC">Lugar de Nacimiento:</label>
          <input type="text" class="form-control" id="LUGARNAC" name="LUGARNAC" value="<?php echo $paciente ? htmlspecialchars($paciente['LUGARNAC']) : ''; ?>" maxlength="35">
      </div>
      
      <div class="form-group">
          <label for="OCUPACION">Ocupación:</label>
          <input type="text" class="form-control" id="OCUPACION" name="OCUPACION" value="<?php echo $paciente ? htmlspecialchars($paciente['OCUPACION']) : ''; ?>" maxlength="25">
      </div>
      
      <div class="form-group">
          <label for="RELIGION">Religión:</label>
          <select class="form-control" id="RELIGION" name="RELIGION">
              <option value="">Seleccionar</option>
              <option value="Católico" <?php echo $paciente && htmlspecialchars($paciente['RELIGION'])=="Católico" ? 'selected' : ''; ?>>Católico</option>
              <option value="Cristiano Evangelice" <?php echo $paciente && htmlspecialchars($paciente['RELIGION'])=="Cristiano Evangelice" ? 'selected' : ''; ?>>Cristiano Evangelice</option>
              <option value="Testigo" <?php echo $paciente && htmlspecialchars($paciente['RELIGION'])=="Testigo" ? 'selected' : ''; ?>>Testigo</option>
              <option value="Ninguna" <?php echo $paciente && htmlspecialchars($paciente['RELIGION'])=="Ninguna" ? 'selected' : ''; ?>>Ninguna</option>
              <option value="Otra" <?php echo $paciente && htmlspecialchars($paciente['RELIGION'])=="Otra" ? 'selected' : ''; ?>>Otra</option>
          </select>
      
      </div>
      
      <!-- Pestañas médicas modernas para información del paciente -->
      <div class="medical-info-tabs-container mb-4">
          <div class="info-tabs-header text-center mb-3">
              <h6 class="text-primary mb-1">
                  <i class="fas fa-folder-open me-2"></i>
                  Información Detallada del Paciente
              </h6>
              <p class="text-muted small mb-0">
                  <i class="fas fa-info-circle me-1"></i>
                  Complete los datos organizados por categorías
              </p>
          </div>

          <ul class="nav nav-pills nav-justified medical-info-nav" id="infoTab" role="tablist">
              <li class="nav-item" role="presentation">
                  <button class="nav-link medical-info-tab active"
                          id="domicilio-tab"
                          data-bs-toggle="pill"
                          data-bs-target="#domicilio"
                          type="button"
                          role="tab"
                          aria-controls="domicilio"
                          aria-selected="true">
                      <div class="info-tab-icon">
                          <i class="fas fa-home"></i>
                      </div>
                      <span class="info-tab-text">Domicilio</span>
                  </button>
              </li>
              <li class="nav-item" role="presentation">
                  <button class="nav-link medical-info-tab"
                          id="contacto-tab"
                          data-bs-toggle="pill"
                          data-bs-target="#contacto"
                          type="button"
                          role="tab"
                          aria-controls="contacto"
                          aria-selected="false">
                      <div class="info-tab-icon">
                          <i class="fas fa-phone"></i>
                      </div>
                      <span class="info-tab-text">Contacto</span>
                  </button>
              </li>
              <li class="nav-item" role="presentation">
                  <button class="nav-link medical-info-tab"
                          id="poliza-tab"
                          data-bs-toggle="pill"
                          data-bs-target="#poliza"
                          type="button"
                          role="tab"
                          aria-controls="poliza"
                          aria-selected="false">
                      <div class="info-tab-icon">
                          <i class="fas fa-shield-alt"></i>
                      </div>
                      <span class="info-tab-text">Póliza</span>
                  </button>
              </li>
              <li class="nav-item" role="presentation">
                  <button class="nav-link medical-info-tab"
                          id="responsable-tab"
                          data-bs-toggle="pill"
                          data-bs-target="#responsable"
                          type="button"
                          role="tab"
                          aria-controls="responsable"
                          aria-selected="false">
                      <div class="info-tab-icon">
                          <i class="fas fa-user-tie"></i>
                      </div>
                      <span class="info-tab-text">Responsable</span>
                  </button>
              </li>
              <li class="nav-item" role="presentation">
                  <button class="nav-link medical-info-tab"
                          id="familiar-tab"
                          data-bs-toggle="pill"
                          data-bs-target="#familiar"
                          type="button"
                          role="tab"
                          aria-controls="familiar"
                          aria-selected="false">
                      <div class="info-tab-icon">
                          <i class="fas fa-users"></i>
                      </div>
                      <span class="info-tab-text">Familiar</span>
                  </button>
              </li>
          </ul>
      </div>
      
      <div class="tab-content medical-info-tab-content" id="infoTabContent">
          <!-- Pestaña DOMICILIO -->
          <div class="tab-pane fade show active" id="domicilio" role="tabpanel" aria-labelledby="domicilio-tab">
              <div class="medical-info-panel">
                  <div class="panel-header">
                      <h6 class="text-success mb-2">
                          <i class="fas fa-home me-2"></i>
                          Información de Domicilio
                      </h6>
                      <p class="text-muted small mb-3">
                          <i class="fas fa-map-marker-alt me-1"></i>
                          Complete la dirección de residencia del paciente
                      </p>
                  </div>
              <div class="form-group">
                  <label for="CALLE">Calle:</label>
                  <input type="text" class="form-control" id="CALLE" name="CALLE" value="<?php echo $paciente ? htmlspecialchars($paciente['CALLE']) : ''; ?>" maxlength="80">
              </div>
              <div class="form-group">
                  <label for="PROVINCIA">Provincia:</label>
                  <input type="text" class="form-control" id="PROVINCIA" name="PROVINCIA" value="<?php echo $paciente ? htmlspecialchars($paciente['PROVINCIA']) : ''; ?>" maxlength="30">
              </div>
              <div class="form-group">
                  <label for="LOCALIDAD">Localidad:</label>
                  <input type="text" class="form-control" id="LOCALIDAD" name="LOCALIDAD" value="<?php echo $paciente ? htmlspecialchars($paciente['LOCALIDAD']) : ''; ?>" maxlength="35">
              </div>
              <div class="form-group">
                  <label for="MUNICIPIO">Municipio:</label>
                  <input type="text" class="form-control" id="MUNICIPIO" name="MUNICIPIO" value="<?php echo $paciente ? htmlspecialchars($paciente['MUNICIPIO']) : ''; ?>" maxlength="35">
              </div>
              <div class="form-group">
                  <label for="PAIS">País:</label>
                  <input type="text" class="form-control" id="PAIS" name="PAIS" value="<?php echo $paciente ? htmlspecialchars($paciente['PAIS']) : ''; ?>" maxlength="35">
              </div>
              <div class="form-group">
                  <label for="REFERENCIA">Referencia:</label>
                  <input type="text" class="form-control" id="REFERENCIA" name="REFERENCIA" value="<?php echo $paciente ? htmlspecialchars($paciente['REFERENCIA']) : ''; ?>" maxlength="30">
              </div>
              </div>
          </div>
          
          <!-- Pestaña CONTACTO -->
          <div class="tab-pane fade" id="contacto" role="tabpanel" aria-labelledby="contacto-tab">
              <div class="medical-info-panel">
                  <div class="panel-header">
                      <h6 class="text-info mb-2">
                          <i class="fas fa-phone me-2"></i>
                          Información de Contacto
                      </h6>
                      <p class="text-muted small mb-3">
                          <i class="fas fa-address-book me-1"></i>
                          Datos de comunicación con el paciente
                      </p>
                  </div>
              <div class="form-group">
                  <label for="CELULAR">Teléfono Celular:</label>
                  <input type="text" class="form-control" id="CELULAR" name="CELULAR" value="<?php echo $paciente ? htmlspecialchars($paciente['CELULAR']) : ''; ?>" maxlength="10" placeholder="Ej: **********" title="Solo números, máximo 10 dígitos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              </div>
              <div class="form-group">
                  <label for="TELEFONO">Teléfono de Casa:</label>
                  <input type="text" class="form-control" id="TELEFONO" name="TELEFONO" value="<?php echo $paciente ? htmlspecialchars($paciente['TELEFONO']) : ''; ?>" maxlength="10" placeholder="Ej: **********" title="Solo números, máximo 10 dígitos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              </div>
              <div class="form-group">
                  <label for="TELTRABAJO">Teléfono del Trabajo:</label>
                  <input type="text" class="form-control" id="TELTRABAJO" name="TELTRABAJO" value="<?php echo $paciente ? htmlspecialchars($paciente['TELTRABAJO']) : ''; ?>" maxlength="10" placeholder="Ej: **********" title="Solo números, máximo 10 dígitos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              </div>
              <div class="form-group">
                  <label for="FAX">Fax:</label>
                  <input type="text" class="form-control" id="FAX" name="FAX" value="<?php echo $paciente ? htmlspecialchars($paciente['FAX']) : ''; ?>" maxlength="10" placeholder="Ej: **********" title="Solo números, máximo 10 dígitos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              </div>
              <div class="form-group">
                  <label for="ECORREO">Correo Electrónico:</label>
                  <input type="email" class="form-control" id="ECORREO" name="ECORREO" value="<?php echo $paciente ? htmlspecialchars($paciente['ECORREO']) : ''; ?>" maxlength="50">
              </div>
              </div>
          </div>
          
          <!-- Pestaña PÓLIZA (se utiliza NSS en lugar de POLIZA) -->
          <div class="tab-pane fade" id="poliza" role="tabpanel" aria-labelledby="poliza-tab">
              <div class="medical-info-panel">
                  <div class="panel-header">
                      <h6 class="text-purple mb-2" style="color: #8b5cf6;">
                          <i class="fas fa-shield-alt me-2"></i>
                          Información de Póliza y Seguro
                      </h6>
                      <p class="text-muted small mb-3">
                          <i class="fas fa-id-card me-1"></i>
                          Datos del seguro médico del paciente
                      </p>
                  </div>
              <div class="form-group">
                  <label for="ARS">ARS:</label>
                  <select class="form-control" id="ARS" name="ARS" required>
                      <option value="">Seleccionar ARS</option>
                      <?php
                      $stmt_ars = $pdo->query("SELECT CLAVE, NOMBRE FROM ASEGURADORA ORDER BY NOMBRE");
                      while ($row = $stmt_ars->fetch(PDO::FETCH_ASSOC)) {
                          $selected = "";
                          if (isset($paciente['ARS']) && $paciente['ARS'] == $row['CLAVE']) {
                              $selected = "selected";
                          }
                          echo "<option value=\"" . htmlspecialchars($row['CLAVE']) . "\" $selected>" . htmlspecialchars($row['NOMBRE']) . "</option>";
                      }
                      ?>
                  </select>
              </div>
              <div class="form-group">
                  <label for="PLANES">Planes:</label>
                  <input type="text" class="form-control" id="PLANES" name="PLANES" value="<?php echo $paciente ? htmlspecialchars($paciente['PLANES']) : ''; ?>" maxlength="20">
              </div>
              <div class="form-group">
                  <label for="AFILIADO">Afiliado:</label>
                  <input type="text" class="form-control" id="AFILIADO" name="AFILIADO" value="<?php echo $paciente ? htmlspecialchars($paciente['AFILIADO']) : ''; ?>" maxlength="18">
              </div>
              <div class="form-group">
                  <label for="VIGENCIA">Vigencia:</label>
                  <input type="date" class="form-control" id="VIGENCIA" name="VIGENCIA" value="<?php echo isset($paciente) && !empty($paciente['VIGENCIA']) ? htmlspecialchars(date('Y-m-d', strtotime($paciente['VIGENCIA']))) : date('Y-m-d'); ?>">
              </div>
              <div class="form-group">
                  <label for="NSS">Número de Seguro Social (NSS):</label>
                  <input type="text" class="form-control" id="NSS" name="NSS" readonly value="<?php echo $paciente ? htmlspecialchars($paciente['NSS']) : ''; ?>">
              </div>
              <div class="form-group">
                  <label for="CATEGORIA">Categoría:</label>
                  <select class="form-control" id="CATEGORIA" name="CATEGORIA" required>
                      <option value="">Seleccionar Categoría</option>
                      <?php echo implode('', array_map(function($cat) use ($paciente) {
                          return '<option value="' . htmlspecialchars($cat['CLAVE']) . '" ' .
                                 ((isset($paciente['CATEGORIA']) && $paciente['CATEGORIA'] == $cat['CLAVE']) ? 'selected' : '') .
                                 '>' . htmlspecialchars($cat['NOMBRE']) . '</option>';
                      }, $categList)); ?>
                  </select>
              </div>
              </div>
          </div>
          
          <!-- Pestaña RESPONSABLE -->
          <div class="tab-pane fade" id="responsable" role="tabpanel" aria-labelledby="responsable-tab">
              <div class="medical-info-panel">
                  <div class="panel-header">
                      <h6 class="text-warning mb-2">
                          <i class="fas fa-user-tie me-2"></i>
                          Información del Responsable
                      </h6>
                      <p class="text-muted small mb-3">
                          <i class="fas fa-handshake me-1"></i>
                          Datos de la persona responsable del paciente
                      </p>
                  </div>
              <div class="form-group">
                  <label for="NOMBRERESP">Nombre del Responsable:</label>
                  <input type="text" class="form-control" id="NOMBRERESP" name="NOMBRERESP" value="<?php echo $paciente ? htmlspecialchars($paciente['NOMBRERESP']) : ''; ?>" maxlength="50">
              </div>
              <div class="form-group">
                  <label for="DIRECCIONRESP">Dirección del Responsable:</label>
                  <input type="text" class="form-control" id="DIRECCIONRESP" name="DIRECCIONRESP" value="<?php echo $paciente ? htmlspecialchars($paciente['DIRECCIONRESP']) : ''; ?>" maxlength="50">
              </div>
              <div class="form-group">
                  <label for="CEDULARESP">Cédula del Responsable:</label>
                  <input type="text" class="form-control" id="CEDULARESP" name="CEDULARESP" value="<?php echo $paciente ? htmlspecialchars($paciente['CEDULARESP']) : ''; ?>" maxlength="11" title="Ingrese la cédula del responsable" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              </div>
              <div class="form-group">
                  <label for="TELEFONORESP">Teléfono del Responsable:</label>
                  <input type="text" class="form-control" id="TELEFONORESP" name="TELEFONORESP" value="<?php echo $paciente ? htmlspecialchars($paciente['TELEFONORESP']) : ''; ?>" maxlength="10" placeholder="Ej: **********" title="Solo números, máximo 10 dígitos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              </div>
              </div>
          </div>
          
          <!-- Pestaña FAMILIAR -->
          <div class="tab-pane fade" id="familiar" role="tabpanel" aria-labelledby="familiar-tab">
              <div class="medical-info-panel">
                  <div class="panel-header">
                      <h6 class="text-danger mb-2">
                          <i class="fas fa-users me-2"></i>
                          Información del Familiar
                      </h6>
                      <p class="text-muted small mb-3">
                          <i class="fas fa-heart me-1"></i>
                          Datos del familiar próximo del paciente
                      </p>
                  </div>
              <div class="form-group">
                  <label for="FAMILIARPROX">Nombre del Familiar Próximo:</label>
                  <input type="text" class="form-control" id="FAMILIARPROX" name="FAMILIARPROX" value="<?php echo $paciente ? htmlspecialchars($paciente['FAMILIARPROX']) : ''; ?>" maxlength="35">
              </div>
              <div class="form-group">
                  <label for="DIRECCIONFAMILIAR">Dirección del Familiar Próximo:</label>
                  <input type="text" class="form-control" id="DIRECCIONFAMILIAR" name="DIRECCIONFAMILIAR" value="<?php echo $paciente ? htmlspecialchars($paciente['DIRECCIONFAMILIAR']) : ''; ?>" maxlength="35">
              </div>
              <div class="form-group">
                  <label for="TELEFONOFAMILIAR">Teléfono del Familiar Próximo:</label>
                  <input type="text" class="form-control" id="TELEFONOFAMILIAR" name="TELEFONOFAMILIAR" value="<?php echo $paciente ? htmlspecialchars($paciente['TELEFONOFAMILIAR']) : ''; ?>" maxlength="10" placeholder="Ej: **********" title="Solo números, máximo 10 dígitos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              </div>
          </div>
      </div>
      
      <div class="form-group mt-3">
          <label for="OBSERVACIONES">Observaciones (por la secretaria):</label>
          <textarea class="form-control" id="OBSERVACIONES" name="OBSERVACIONES" maxlength="60"><?php echo $paciente ? htmlspecialchars($paciente['OBSERVACIONES']) : ''; ?></textarea>
      </div>
      
      <div class="form-group">
          <label>Nivel Escolar:</label><br>
          <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelPrimario" value="0" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="0" ? 'checked' : ''; ?>>
              <label class="form-check-label" for="nivelPrimario">Primario</label>
          </div>
          <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelSecundario" value="1" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="1" ? 'checked' : ''; ?>>
              <label class="form-check-label" for="nivelSecundario">Secundario</label>
          </div>
          <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelUniversitario" value="2" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="2" ? 'checked' : ''; ?>>
              <label class="form-check-label" for="nivelUniversitario">Universitario</label>
          </div>
          <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelNinguno" value="3" <?php echo $paciente && htmlspecialchars($paciente['NIVELESCOLAR'])=="3" ? 'checked' : ''; ?>>
              <label class="form-check-label" for="nivelNinguno">Ninguno</label>
          </div>
      </div>
      <div class="form-group">
          <label>Procedencia:</label><br>
          <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaUrbano" value="1" <?php echo isset($paciente) && $paciente['PROCEDENCIA']=='1' ? 'checked' : ''; ?>>
              <label class="form-check-label" for="procedenciaUrbano">Urbano</label>
          </div>
          <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaRural" value="0" <?php echo isset($paciente) && $paciente['PROCEDENCIA']=='0' ? 'checked' : ''; ?>>
              <label class="form-check-label" for="procedenciaRural">Rural</label>
          </div>
      </div>
      <div class="form-group">
          <label for="RECORDCLINICA">Record Clínico:</label>
          <input type="text" class="form-control" id="RECORDCLINICA" name="RECORDCLINICA" value="<?php echo $paciente ? htmlspecialchars($paciente['RECORDCLINICA']) : ''; ?>">
      </div>
              </div>
          </div>

      <?php if ($mensaje_error): ?>
          <div class="alert alert-warning"><?php echo htmlspecialchars($mensaje_error); ?></div>
      <?php endif; ?>
      <?php if ($mensaje_exito): ?>
          <div class="alert alert-success"><?php echo htmlspecialchars($mensaje_exito); ?></div>
      <?php endif; ?>

      <div class="d-flex gap-3 mt-4">
          <button type="submit" class="btn btn-medical-primary" id="btnIngresar" disabled>
              <i class="fas fa-<?php echo $paciente ? 'edit' : 'user-plus'; ?> me-2"></i>
              <?php echo $paciente ? 'Actualizar' : 'Registrar'; ?> Paciente
          </button>
          <?php if (isset($_GET['cedula_duplicada']) && $_GET['cedula_duplicada'] == 1): ?>
              <button type="button" class="btn btn-medical-warning" onclick="cancelarYEnfocarCedula();">
                  <i class="fas fa-times me-2"></i>Cancelar
              </button>
          <?php endif; ?>
      </div>
      
      <script>
          document.addEventListener("DOMContentLoaded", function () {
              const nombres = document.getElementById('NOMBRES');
              const apellidos = document.getElementById('APELLIDOS');
              const cedula = document.getElementById('CEDULA');
              const fechanac = document.getElementById('FECHANAC');
              const btnIngresar = document.getElementById('btnIngresar');
      
              function verificarCampos() {
                  if (nombres.value.trim() !== '' && 
                      apellidos.value.trim() !== '' && 
                      cedula.value.trim() !== '' && 
                      fechanac.value.trim() !== '') {
                      btnIngresar.disabled = false;
                  } else {
                      btnIngresar.disabled = true;
                  }
              }
      
              nombres.addEventListener('input', verificarCampos);
              apellidos.addEventListener('input', verificarCampos);
              cedula.addEventListener('input', verificarCampos);
              fechanac.addEventListener('input', verificarCampos);
      
              verificarCampos();
          });
      </script>
      
      <script src="/mi_consultorio/public/js/formateo.js"></script>
      <script>
          function validarNumeros(event) {
              var input = event.target;
              var valor = input.value;
              input.value = valor.replace(/[^0-9]/g, '');
              if (input.value.length > 11) {
                  input.value = input.value.slice(0, 11);
              }
          }
      
          function validarFechaNacimiento(event) {
              var input = event.target;
              var fechaNacimiento = new Date(input.value);
              var fechaHoy = new Date();
              if (fechaNacimiento > fechaHoy) {
                  input.setCustomValidity("La fecha de nacimiento no puede ser en el futuro.");
              } else {
                  input.setCustomValidity("");
              }
          }
      </script>
      
      <script>
          function validarCedulaRD_JS(cedula) {
              if (cedula.length !== 11 || !/^\d+$/.test(cedula)) {
                  return false;
              }
              let suma = 0;
              for (let i = 0; i < 10; i++) {
                  let num = parseInt(cedula.charAt(i)) * (i % 2 === 0 ? 1 : 2);
                  if (num > 9) {
                      num -= 9;
                  }
                  suma += num;
              }
              let digitoVerificador = (10 - (suma % 10)) % 10;
              return digitoVerificador === parseInt(cedula.charAt(10));
          }
      
         // Evento para validar duplicidad de cedula y su formato
      document.addEventListener("DOMContentLoaded", function() {
    const cedulaInput = document.getElementById('CEDULA');
    cedulaInput.addEventListener('blur', function() {
        let cedula = cedulaInput.value.trim();
        // Se verifica si la cédula tiene 11 dígitos
        if (cedula.length === 11) {
            // Primero, verificar duplicidad vía AJAX
            fetch('check_cedula.php?CEDULA=' + encodeURIComponent(cedula))
                .then(response => response.json())
                .then(data => {
                    if (data.exists) {
                        // Si la cédula ya existe, se muestra el mensaje y se redirige
                        alert("La cédula ya existe para el paciente: " + data.NOMBRE_COMPLETO);
                        window.location.href = "crear_paciente.php?CLAVE=" + data.CLAVE + "&cedula_duplicada=1";
                    } else {
                        // Si no existe duplicidad, se valida el formato para RD
                        if (cedula !== "00000000000" && !validarCedulaRD_JS(cedula)) {
                            alert("La cédula ingresada no es válida para RD.");
                            setTimeout(() => {
                                cedulaInput.focus();
                                cedulaInput.select();
                            }, 100);
                        }
                    }
                })
                .catch(error => console.error('Error al verificar cédula:', error));
        }
    });
});

    // Funcionalidad moderna de captura de imagen médica
      document.addEventListener("DOMContentLoaded", function() {
          const video = document.getElementById('video');
          const canvas = document.getElementById('canvas');
          const capturarBtn = document.getElementById('capturar');
          const iniciarCameraBtn = document.getElementById('iniciarCamera');
          const previewImage = document.getElementById('previewImage');
          const previewContainer = document.getElementById('previewContainer');
          const imagenCapturadaInput = document.getElementById('imagenCapturada');
          const fotoInput = document.getElementById('foto');

          let currentStream = null;
          let cameraActive = false;

          // Función para iniciar la cámara
          function iniciarCamera() {
              if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                  navigator.mediaDevices.getUserMedia({
                      video: {
                          width: { ideal: 640 },
                          height: { ideal: 480 },
                          facingMode: 'user'
                      }
                  })
                  .then(stream => {
                      currentStream = stream;
                      video.srcObject = stream;
                      video.play();
                      cameraActive = true;

                      // Actualizar botones
                      capturarBtn.disabled = false;
                      capturarBtn.innerHTML = '<i class="fas fa-camera me-2"></i>Capturar Fotografía';
                      iniciarCameraBtn.style.display = 'none';

                      console.log("Cámara iniciada correctamente");
                  })
                  .catch(err => {
                      console.error("Error al acceder a la cámara:", err);
                      capturarBtn.disabled = true;
                      capturarBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Cámara no disponible';
                      iniciarCameraBtn.style.display = 'inline-block';
                  });
              } else {
                  capturarBtn.disabled = true;
                  capturarBtn.innerHTML = '<i class="fas fa-times me-2"></i>Cámara no soportada';
              }
          }

          // Función para mostrar vista previa
          function mostrarVistaPrevia(src) {
              previewImage.src = src;
              previewImage.style.display = 'block';
              previewContainer.style.display = 'none';

              // Agregar efecto de aparición
              previewImage.style.opacity = '0';
              previewImage.style.transform = 'scale(0.8)';

              setTimeout(() => {
                  previewImage.style.transition = 'all 0.3s ease';
                  previewImage.style.opacity = '1';
                  previewImage.style.transform = 'scale(1)';
              }, 50);
          }

          // Iniciar cámara automáticamente
          iniciarCamera();

          // Evento para reiniciar cámara
          if (iniciarCameraBtn) {
              iniciarCameraBtn.addEventListener('click', iniciarCamera);
          }

          // Capturar imagen cuando se hace clic en el botón
          capturarBtn.addEventListener('click', function() {
              if (!cameraActive) {
                  console.warn('La cámara no está activa');
                  return;
              }

              // Efecto visual del botón
              capturarBtn.style.transform = 'scale(0.95)';
              setTimeout(() => {
                  capturarBtn.style.transform = 'scale(1)';
              }, 150);

              canvas.width = video.videoWidth;
              canvas.height = video.videoHeight;
              let context = canvas.getContext('2d');
              context.drawImage(video, 0, 0, canvas.width, canvas.height);
              let dataURL = canvas.toDataURL('image/jpeg', 0.8);
              imagenCapturadaInput.value = dataURL;

              // Mostrar vista previa
              mostrarVistaPrevia(dataURL);

              console.log("Fotografía capturada exitosamente");

              // Efecto de flash
              const flash = document.createElement('div');
              flash.style.cssText = `
                  position: fixed;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background: white;
                  opacity: 0.8;
                  z-index: 9999;
                  pointer-events: none;
              `;
              document.body.appendChild(flash);

              setTimeout(() => {
                  flash.style.opacity = '0';
                  flash.style.transition = 'opacity 0.3s';
                  setTimeout(() => flash.remove(), 300);
              }, 100);
          });

          // Manejar subida de archivo
          if (fotoInput) {
              fotoInput.addEventListener('change', function(e) {
                  const file = e.target.files[0];
                  if (file) {
                      // Validar tipo de archivo
                      if (!file.type.startsWith('image/')) {
                          alert('Por favor seleccione un archivo de imagen válido');
                          return;
                      }

                      // Validar tamaño (5MB máximo)
                      if (file.size > 5 * 1024 * 1024) {
                          alert('El archivo es demasiado grande. Máximo 5MB.');
                          return;
                      }

                      const reader = new FileReader();
                      reader.onload = function(e) {
                          const dataURL = e.target.result;
                          imagenCapturadaInput.value = dataURL;
                          mostrarVistaPrevia(dataURL);
                          console.log("Imagen cargada desde archivo");
                      };
                      reader.readAsDataURL(file);
                  }
              });
          }

          // Limpiar recursos al salir de la página
          window.addEventListener('beforeunload', function() {
              if (currentStream) {
                  currentStream.getTracks().forEach(track => track.stop());
              }
          });

          // Funcionalidad mejorada para los tabs de información
          const infoTabs = document.querySelectorAll('.medical-info-tab');

          infoTabs.forEach(tab => {
              tab.addEventListener('shown.bs.tab', function(e) {
                  // Agregar animación al panel activo
                  const targetPanel = document.querySelector(e.target.getAttribute('data-bs-target'));
                  if (targetPanel) {
                      const panel = targetPanel.querySelector('.medical-info-panel');
                      if (panel) {
                          panel.style.animation = 'none';
                          setTimeout(() => {
                              panel.style.animation = 'slideInPanel 0.4s ease-out';
                          }, 10);
                      }
                  }
              });

              // Efectos hover mejorados
              tab.addEventListener('mouseenter', function() {
                  if (!this.classList.contains('active')) {
                      this.style.transform = 'translateY(-3px) scale(1.02)';
                  }
              });

              tab.addEventListener('mouseleave', function() {
                  if (!this.classList.contains('active')) {
                      this.style.transform = 'translateY(0) scale(1)';
                  }
              });
          });

          // Atajos de teclado para los tabs de información
          document.addEventListener('keydown', function(e) {
              if (e.altKey) {
                  switch(e.key) {
                      case '1':
                          e.preventDefault();
                          document.getElementById('domicilio-tab').click();
                          break;
                      case '2':
                          e.preventDefault();
                          document.getElementById('contacto-tab').click();
                          break;
                      case '3':
                          e.preventDefault();
                          document.getElementById('poliza-tab').click();
                          break;
                      case '4':
                          e.preventDefault();
                          document.getElementById('responsable-tab').click();
                          break;
                      case '5':
                          e.preventDefault();
                          document.getElementById('familiar-tab').click();
                          break;
                  }
              }
          });

          // Mostrar información de atajos para tabs de información
          const infoShortcuts = document.createElement('div');
          infoShortcuts.className = 'position-fixed bottom-0 end-0 p-3 text-muted small';
          infoShortcuts.style.zIndex = '1000';
          infoShortcuts.innerHTML = `
              <div class="bg-white rounded p-2 shadow-sm border" style="opacity: 0.8;">
                  <strong>Atajos Info:</strong> Alt**** (Pestañas)
              </div>
          `;

          document.body.appendChild(infoShortcuts);

          // Ocultar atajos después de 5 segundos
          setTimeout(() => {
              infoShortcuts.style.opacity = '0.3';
          }, 5000);

          infoShortcuts.addEventListener('mouseenter', () => {
              infoShortcuts.style.opacity = '1';
          });

          infoShortcuts.addEventListener('mouseleave', () => {
              infoShortcuts.style.opacity = '0.3';
          });
      });
    
      </script>
    
    </form>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    
</div>
</body>
</html>
