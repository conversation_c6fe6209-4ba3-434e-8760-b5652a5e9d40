object TRpReport
  PageOrientation = rpOrientationPortrait
  Pagesize = rpPageSizeCustom
  PagesizeQt = 2
  PageHeight = 7639
  PageWidth = 5903
  PageBackColor = 16777215
  SubReports = <
    item
      SubReport = TRpSubReport0
    end>
  DataInfo = <
    item
      Alias = 'DETALLE'
      DatabaseAlias = 'CONSULTORIO'
      SQL = 
        'Select T.clave ,T.clav<PERSON> ,T.TRATAMIENTO,T. fecha_cap , p.nombr' +
        'es, p. apellidos,p.cedula, p.nss, p.naf,p.poliza, p.edad,p.fecha' +
        'nac,p.ars,p.sincedula, a.nombre  from TRATAMIENTOS T  left join ' +
        'pacientes p on T.clavepac=p.clave'#13#10'left join aseguradora a on A.' +
        'clave = ars where T.clave =:clave'
    end
    item
      Alias = 'EMPRESA'
      DatabaseAlias = 'CONSULTORIO'
      SQL = 
        'select rnc, nombre, especialidad, execquatur,codclin,colegiacion' +
        ',cidc,clinica,calle, provincia,localidad,pais,NumConsultorio,fax' +
        ',correoe,paginaweb,fecha,telefono,fax,clinica FROM EMPRESA where' +
        ' consultorio=:consultorio'
    end
    item
      Alias = 'CONFIG'
      DatabaseAlias = 'CONSULTORIO'
      SQL = 
        'SELECT IMPRIMENCABEZADO,pathconfig FROM CONFIG  where consultori' +
        'o=1'
    end>
  DatabaseInfo = <
    item
      Alias = 'CONSULTORIO'
      LoadParams = True
      LoadDriverParams = True
      LoginPrompt = False
      ReportTable = 'REPMAN_REPORTS'
      ReportSearchField = 'REPORT_NAME'
      ReportField = 'REPORT'
      ReportGroupsTable = 'REPMAN_GROUPS'
      ADOConnectionString = ''
    end>
  Params = <
    item
      Name = 'CLAVE'
      AllowNulls = False
      Value = '1'
      Datasets.Strings = (
        'DETALLE')
      SearchDataset = 'DETALLE'
      Description = ''
      Hint = ''
      Search = ''
      ErrorMessage = ''
      Validation = ''
    end
    item
      Name = 'CONSULTORIO'
      AllowNulls = False
      Value = 1
      ParamType = rpParamInteger
      Datasets.Strings = (
        'EMPRESA')
      SearchDataset = 'EMPRESA'
      SearchParam = 'CONSULTORIO'
      Description = ''
      Hint = ''
      Search = ''
      ErrorMessage = ''
      Validation = ''
    end>
  StreamFormat = rpStreamText
  ReportAction = []
  Type1Font = poHelvetica
  WFontName = 'Arial'
  LFontName = 'Helvetica'
  object TRpSubReport0: TRpSubReport
    Sections = <
      item
        Section = TRpSection1
      end
      item
        Section = TRpSection0
      end
      item
        Section = TRpSection2
      end>
  end
  object TRpSection0: TRpSection
    Width = 10772
    Height = 270
    SubReport = TRpSubReport0
    ChangeBool = False
    PageRepeat = False
    SkipPage = False
    AlignBottom = False
    SectionType = rpsecdetail
    Components = <
      item
        Component = TRpExpression7
      end>
    AutoExpand = True
    AutoContract = True
    ExternalTable = 'REPMAN_REPORTS'
    ExternalField = 'REPORT'
    ExternalSearchField = 'REPORT_NAME'
    StreamFormat = rpStreamText
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    ChangeExpression = ''
    BeginPageExpression = ''
    ChangeExpression = ''
    SkipExpreV = ''
    SkipExpreH = ''
    SkipToPageExpre = ''
    BackExpression = ''
    Stream = {0000000000000000}
  end
  object TRpSection1: TRpSection
    Width = 10772
    Height = 2625
    SubReport = TRpSubReport0
    GroupName = 'TRATAMIENTO'
    ChangeBool = False
    PageRepeat = False
    SkipPage = False
    AlignBottom = False
    SectionType = rpsecgheader
    Components = <
      item
        Component = TRpExpression0
      end
      item
        Component = TRpExpression1
      end
      item
        Component = TRpExpression2
      end
      item
        Component = TRpExpression3
      end
      item
        Component = TRpExpression4
      end
      item
        Component = TRpShape0
      end
      item
        Component = TRpExpression5
      end
      item
        Component = TRpLabel0
      end
      item
        Component = TRpExpression6
      end
      item
        Component = TRpImage0
      end>
    ExternalTable = 'REPMAN_REPORTS'
    ExternalField = 'REPORT'
    ExternalSearchField = 'REPORT_NAME'
    StreamFormat = rpStreamText
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    ChangeExpression = ''
    BeginPageExpression = ''
    ChangeExpression = ''
    SkipExpreV = ''
    SkipExpreH = ''
    SkipToPageExpre = ''
    BackExpression = ''
    Stream = {0000000000000000}
  end
  object TRpSection2: TRpSection
    Width = 10772
    Height = 1215
    SubReport = TRpSubReport0
    GroupName = 'TRATAMIENTO'
    ChangeBool = False
    PageRepeat = False
    SkipPage = False
    AlignBottom = True
    SectionType = rpsecgfooter
    Components = <
      item
        Component = TRpExpression8
      end
      item
        Component = TRpExpression9
      end
      item
        Component = TRpExpression11
      end
      item
        Component = TRpExpression12
      end
      item
        Component = TRpExpression13
      end
      item
        Component = TRpShape1
      end
      item
        Component = TRpExpression14
      end>
    ExternalTable = 'REPMAN_REPORTS'
    ExternalField = 'REPORT'
    ExternalSearchField = 'REPORT_NAME'
    StreamFormat = rpStreamText
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    ChangeExpression = ''
    BeginPageExpression = ''
    ChangeExpression = ''
    SkipExpreV = ''
    SkipExpreH = ''
    SkipToPageExpre = ''
    BackExpression = ''
    Stream = {0000000000000000}
  end
  object TRpExpression0: TRpExpression
    Width = 10755
    Height = 300
    PosX = 0
    PosY = 1305
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      'IIF(trim(empresa.telefono)>'#39#39', '#39'TEL. '#39' + trim(empresa.telefono),' +
      #39#39') +'#13#10'IIF (trim(empresa.fax)>'#39#39',   '#39' WHATSAPP . '#39' + trim(empres' +
      'a.fax),'#39#39')'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression1: TRpExpression
    Width = 10755
    Height = 285
    PosX = 0
    PosY = 630
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      'IIF( trim(EMPRESA.EXECQUATUR)>'#39' '#39' , '#9#39' Exequatur  '#39' + trim(EMPRE' +
      'SA.EXECQUATUR), '#39' '#39') +'#13#10'IIF( trim(EMPRESA.COLEGIACION)>'#39' '#39',     ' +
      #39' Colegiacion '#39'+trim(EMPRESA.COLEGIACION) , '#39' '#39'  ) +'#13#10'IIF( trim(' +
      'EMPRESA.CIDC)> '#39' '#39',           '#39' Num.CIDC '#39'+trim(EMPRESA.CIDC), '#39 +
      ' '#39')'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression2: TRpExpression
    Width = 10755
    Height = 285
    PosX = 0
    PosY = 60
    Type1Font = poHelvetica
    FontSize = 14
    FontStyle = 1
    Alignment = 4
    VAlignment = 32
    SingleLine = True
    BidiModes.Strings = (
      'BidiNo')
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'TRIM(EMPRESA.NOMBRE)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression3: TRpExpression
    Width = 10755
    Height = 345
    PosX = 0
    PosY = 345
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'trim(EMPRESA.ESPECIALIDAD)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression4: TRpExpression
    Width = 2070
    Height = 345
    PosX = 8400
    PosY = 2190
    Type1Font = poHelvetica
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'DETALLE.FECHA_CAP'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpShape0: TRpShape
    Width = 10815
    Height = 75
    PosX = 0
    PosY = 2535
    PenWidth = 0
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
  end
  object TRpExpression5: TRpExpression
    Width = 7020
    Height = 285
    PosX = 0
    PosY = 2190
    Type1Font = poHelvetica
    CutText = True
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'length(detalle.nombres)>0'
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = #39'Nombres :'#39' + trim(DETALLE.NOMBRES)+'#39' '#39'+ trim(DETALLE.APELLIDOS)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpLabel0: TRpLabel
    Width = 10755
    Height = 225
    PosX = 0
    PosY = 1725
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    WideText = 'INFORME TRATAMIENTO MEDICO'
  end
  object TRpExpression6: TRpExpression
    Width = 10755
    Height = 285
    PosX = 0
    PosY = 975
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      'IIF( trim(empresa.calle)>'#39#39' , trim(empresa.calle), '#39#39')     +'#39'  '#39 +
      ' +'#13#10'IIF(  TRIM(EMPRESA.PROVINCIA)>'#39#39',    TRIM(EMPRESA.PROVINCIA)' +
      ','#39#39')  +'#39' '#39' +'#13#10'IIF(  TRIM(EMPRESA.PAIS)>'#39#39',    TRIM(EMPRESA.PAIS)' +
      ','#39#39')'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression7: TRpExpression
    Width = 10245
    Height = 165
    PosX = 225
    PosY = 60
    Type1Font = poHelvetica
    MultiPage = True
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'TRIM(DETALLE.TRATAMIENTO)>'#39#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'trim(DETALLE.TRATAMIENTO)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression8: TRpExpression
    Width = 510
    Height = 225
    PosX = 5865
    PosY = 120
    Type1Font = poHelvetica
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = #39'Edad : '#39'+ str(DETALLE.EDAD) + '#39'  A'#241'os'#39
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression9: TRpExpression
    Width = 915
    Height = 345
    PosX = 9090
    PosY = 120
    Type1Font = poHelvetica
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = #39'NSS : '#39'+trim(DETALLE.NSS)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression11: TRpExpression
    Width = 3105
    Height = 285
    PosX = 5865
    PosY = 405
    Type1Font = poHelvetica
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'trim(DETALLE.NOMBRE)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression12: TRpExpression
    Width = 10410
    Height = 240
    PosX = 120
    PosY = 915
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      'trim(EMPRESA.CLINICA) + '#39' Telefono '#39'+trim(EMPRESA.TELEFONO) + '#39' ' +
      'Codigo clinica '#39'+ trim(EMPRESA.CODCLIN)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression13: TRpExpression
    Width = 5685
    Height = 285
    PosX = 60
    PosY = 120
    Type1Font = poHelvetica
    CutText = True
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = #39'Nombres :'#39' + trim(DETALLE.NOMBRES)+'#39' '#39'+ trim(DETALLE.APELLIDOS)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpShape1: TRpShape
    Width = 10815
    Height = 75
    PosX = 0
    PosY = 0
    PenWidth = 0
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
  end
  object TRpExpression14: TRpExpression
    Width = 2580
    Height = 285
    PosX = 60
    PosY = 465
    Type1Font = poHelvetica
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'DETALLE.SINCEDULA='#39'1'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      #39'Cedula : '#39'+ SUBSTR(DETALLE.CEDULA,1,3)+ '#39'-'#39' +SUBSTR(DETALLE.CED' +
      'ULA,4,7)+ '#39'-'#39' + SUBSTR(DETALLE.CEDULA,11,1)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpImage0: TRpImage
    Width = 1134
    Height = 1134
    PosX = 225
    PosY = 120
    PrintCondition = 'FILEEXISTS(CONFIG.PATHCONFIG + '#39'reportes\homs.bmp'#39')'
    DoBeforePrint = ''
    DoAfterPrint = ''
    Expression = 'CONFIG.PATHCONFIG+'#39'REPORTES/HOMS.BMP'#39
    Stream = {0000000000000000}
  end
end
