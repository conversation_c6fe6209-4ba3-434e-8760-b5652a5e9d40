<?php
require_once '../config/database.php';

$pdo = include '../config/database.php';
header('Content-Type: application/json; charset=UTF-8');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    // Validación básica
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método no permitido');
    }

    // Recoger datos del formulario
    $clavepac        = $_POST['CLAVEPAC'] ?? null;
    $concepto        = $_POST['CONCEPTO'] ?? '';
    $precio          = $_POST['PRECIO'] ?? 0;
    $pagado          = $_POST['PAGADO'] ?? 0;
    $modopgo         = $_POST['MODOPGO'] ?? '';
    $fechapago       = $_POST['FECHAPAGO'] ?? null;
    $fars            = $_POST['FARS'] ?? null;
    $noautorizacion  = $_POST['NOAUTORIZACION'] ?? '';
    $procedimiento   = $_POST['PROCEDIMIENTO'] ?? '';
    $valorreclamado  = $_POST['VALORRECLAMADO'] ?? 0;
    $nopago          = $_POST['NOPAGO'] ?? 0;
    $plazo           = $_POST['PLAZO'] ?? 0;

    $fecha           = date('Y-m-d');
    $hora            = date('H:i:s');
    $estatus         = 'A';
    $streclama       = 'A';
    $usuario         = 1; // valor por defecto o autenticado
    $consultorio     = 1;

    // Calcular próximo NUMFACT automáticamente
    $stmt = $pdo->query("SELECT MAX(NUMFACT) AS ultimo FROM FACTURAS");
    $row = $stmt->fetch();
    $numfact = ($row && $row['ultimo']) ? ($row['ultimo'] + 1) : 1001;

    // Insertar en la base de datos
    $sql = "INSERT INTO FACTURAS (
        CLAVEPAC, NUMFACT, FARS, PRECIO, CONCEPTO, FECHAPROC, FECHAPAGO, MODOPGO,
        ESTATUS, PAGADO, NOPAGO, PLAZO, INTERES, NOAUTORIZACION, VALORRECLAMADO,
        PROCEDIMIENTOS, STRECLAMA, FECHA, HORA, USUARIO, CONSULTORIO
    ) VALUES (
        :clavepac, :numfact, :fars, :precio, :concepto, NULL, :fechapago, :modopgo,
        :estatus, :pagado, :nopago, :plazo, 0, :noautorizacion, :valorreclamado,
        :procedimiento, :streclama, :fecha, :hora, :usuario, :consultorio
    )";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':clavepac'       => $clavepac,
        ':numfact'        => $numfact,
        ':fars'           => $fars,
        ':precio'         => $precio,
        ':concepto'       => $concepto,
        ':fechapago'      => $fechapago,
        ':modopgo'        => $modopgo,
        ':estatus'        => $estatus,
        ':pagado'         => $pagado,
        ':nopago'         => $nopago,
        ':plazo'          => $plazo,
        ':noautorizacion' => $noautorizacion,
        ':valorreclamado' => $valorreclamado,
        ':procedimiento'  => $procedimiento,
        ':streclama'      => $streclama,
        ':fecha'          => $fecha,
        ':hora'           => $hora,
        ':usuario'        => $usuario,
        ':consultorio'    => $consultorio
    ]);

    // Redirigir de nuevo con éxito
    header("Location: facturacion.php?clavepac=" . $clavepac);
    exit;

} catch (Exception $e) {
    echo json_encode(['status' => 'error', 'message' => '❌ Error al guardar factura: ' . $e->getMessage()]);
    exit;
}
