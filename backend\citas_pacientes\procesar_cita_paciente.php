<?php
session_start();
if (!isset($_SESSION['usuario']) || $_SESSION['rol'] !== 'paciente') {
    header("Location: ../../login.php");
    exit();
}

require_once '../config/database.php';

$cedula = $_SESSION['cedula'];
$fecha = $_POST['fecha'] ?? '';
$hora = $_POST['hora'] ?? '';

if (!$fecha || !$hora) {
    die("Fecha y hora son obligatorias.");
}

try {
    // Verifica si ya tiene una cita para ese día
    
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM CITAMEDIC WHERE NSS = ? AND FECHACON = ?");
$stmt->execute([$cedula, $fecha]);

if ($stmt->fetchColumn() > 0) {
    session_start();
    $_SESSION['error_cita'] = "⚠️ Ya tienes una cita para esa fecha. Por favor elige otra.";
    header("Location: crear_cita_paciente.php?fecha=" . urlencode($fecha));
  //  exit;
}
    
    
    // Datos del paciente
    $stmt = $pdo->prepare("SELECT CLAVE, NOMBREAPELLIDO, TELEFONO FROM PACIENTES WHERE CEDULA = ?");
    $stmt->execute([$cedula]);
    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);


    if (!$paciente) {
        die("Paciente no encontrado.");
    }

    // Obtener configuración del sistema
    $config = $pdo->query("SELECT CONSULTORIO, DURACION FROM CONFIG LIMIT 1")->fetch(PDO::FETCH_ASSOC);
    $consultorio = $config['CONSULTORIO'];
    $duracion = $config['DURACION'];

    // Obtener el NUMDOCTOR desde la tabla EMPRESA
    $stmt = $pdo->prepare("SELECT RNC FROM EMPRESA WHERE CONSULTORIO = ?");
    $stmt->execute([$consultorio]);
    $empresa = $stmt->fetch(PDO::FETCH_ASSOC);
    $numDoctor = $empresa ? $empresa['RNC'] : '9999999999999';

    // Agregar observación personalizada
    $observacion = "CITA REMOTA POR EL CLIENTE";

    // Insertar la cita con observación
    $stmt = $pdo->prepare("INSERT INTO CITAMEDIC 
        (CLAVEPAC, NOMBRES, CONSULTORIO, FECHACON, HORACON, TIPOCITA, NUMDOCTOR, DURACION, NSS, TELEFONO, MODOASISTENCIA, ESTATUS, OBSERVACION)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    $stmt->execute([
        $paciente['CLAVE'],
        $paciente['NOMBREAPELLIDO'],
        $consultorio,
        $fecha,
        $hora,
        3,                        // TIPOCITA = Consulta
        $numDoctor,
        $duracion,
        $cedula,
        $paciente['TELEFONO'],
        0,                        // MODOASISTENCIA
        6,                        // ESTATUS = Pendiente aprobacion
        $observacion              // ✅ Observación
    ]);

    echo "<div style='max-width: 600px; margin: 40px auto; font-family: Arial, sans-serif; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f0f9ff;'>";
    echo "<h3 style='color: #0275d8;'>✅ Tu cita ha sido registrada.</h3>";
    echo "<p>Tu solicitud está pendiente de aprobación por el personal del consultorio.</p>";
    echo "<a href='../../paciente_panel.php' style='display: inline-block; margin-top: 20px; padding: 10px 20px; background-color: #0275d8; color: #fff; border-radius: 5px; text-decoration: none;'>Volver al Panel</a>";
    echo "</div>";

} catch (PDOException $e) {
    echo "<p style='color:red;'>Error al procesar la cita: " . htmlspecialchars($e->getMessage()) . "</p>";
}
