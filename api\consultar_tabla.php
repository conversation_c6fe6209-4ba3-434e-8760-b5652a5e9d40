<?php

//include_once '../config/config.php';

header("Content-Type: application/json");

require_once __DIR__ . '/../config/config.php';
$conn = getDBConnection();
$conn->set_charset("utf8");

// Verificar si se pasa el nombre de la tabla mediante parámetros GET o POST
$table = isset($_GET['tabla']) ? $_GET['tabla'] : null;

if (!$table) {
    echo json_encode([
        "status" => "error",
        "message" => "El parámetro 'table' es obligatorio."
    ]);
    exit;
}

// Sanitizar el nombre de la tabla para evitar SQL Injection
$table = $conn->real_escape_string($table);

// Consulta dinámica para obtener los datos
$query = "SELECT * FROM $table WHERE SINCRONIZADO = 0";
$resultado = $conn->query($query);

if ($resultado && $resultado->num_rows > 0) {
    // Crear un array para almacenar los datos
    $data = [];

    while ($fila = $resultado->fetch_assoc()) {
        $data[] = $fila;
    }

    // Convertir el array de resultados a formato JSON y devolverlo
    echo json_encode($data);

    // Actualizar el campo SINCRONIZADO a 1
    $updateQuery = "UPDATE $table SET SINCRONIZADO = 1 WHERE SINCRONIZADO = 0";
    if (!$conn->query($updateQuery)) {
        error_log("Error al actualizar SINCRONIZADO en la tabla '$table': " . $conn->error);
    }
} else {
    echo json_encode([
        "status" => "Error",
        "message" => "No se encontraron registros sin sincronizar en la tabla '$table'."
    ]);
}

$conn->close();
?>
