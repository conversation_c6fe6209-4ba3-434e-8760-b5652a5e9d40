<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA = $_GET['CEDULA'] ?? '';
$CLAVE = $_GET['CLAVE'] ?? null;
$nuevo = isset($_GET['nuevo']);
$mensaje = '';
$registros = [];
$seleccionado = null;

// Obtener registros anteriores
if ($CLAVEPAC) {
  $stmt = $pdo->prepare("SELECT * FROM COMPLEMENTARIAS WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHA_CAP DESC");
  $stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
  $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Selección de registro actual
if ($nuevo) {
  $seleccionado = null;
} elseif ($CLAVE) {
  $stmt = $pdo->prepare("SELECT * FROM COMPLEMENTARIAS WHERE CLAVE = :CLAVE");
  $stmt->execute(['CLAVE' => $CLAVE]);
  $seleccionado = $stmt->fetch(PDO::FETCH_ASSOC);
} elseif (count($registros) > 0) {
  $seleccionado = $registros[0];
}

// Guardar
if ($_SERVER["REQUEST_METHOD"] === "POST") {
  $esActualizacion = !empty($_POST['CLAVE']);
  $texto = trim($_POST['COMPLEMENTARIOS']) ?? null;

  if (!$texto) {
    $mensaje = "❌ El campo de complementarias no puede estar vacío.";
  } else {
    try {
      if ($esActualizacion) {
        $stmt = $pdo->prepare("UPDATE COMPLEMENTARIAS SET COMPLEMENTARIOS = :texto, SINCRONIZADO = 0 WHERE CLAVE = :clave");
        $stmt->execute([
          'texto' => $texto,
          'clave' => $_POST['CLAVE']
        ]);
        $nuevo_id = $_POST['CLAVE'];
      } else {
        $stmt = $pdo->prepare("INSERT INTO COMPLEMENTARIAS (CLAVEPAC, COMPLEMENTARIOS, FECHA_CAP, CEDULA) VALUES (:CLAVEPAC, :texto, CURRENT_TIMESTAMP, :CEDULA)");
        $stmt->execute([
          'CLAVEPAC' => $CLAVEPAC,
          'texto' => $texto,
          'CEDULA' => $CEDULA
        ]);
        $nuevo_id = $pdo->lastInsertId();
      }
      header("Location: pruebas_complementarias.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$nuevo_id");
      exit;
    } catch (PDOException $e) {
      $mensaje = "❌ Error: " . $e->getMessage();
    }
  }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Complementarias</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="p-4">
<div class="row">
  <div class="col-md-3 border-end">
    <h5>Fechas</h5>
    <ul class="list-group">
      <?php foreach ($registros as $r): ?>
        <li class="list-group-item <?= ($seleccionado && $seleccionado['CLAVE'] == $r['CLAVE']) ? 'active' : '' ?>">
          <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&CLAVE=<?= $r['CLAVE'] ?>" class="text-decoration-none <?= ($seleccionado && $seleccionado['CLAVE'] == $r['CLAVE']) ? 'text-white' : '' ?>">
            <?= $r['FECHA_CAP'] ?>
          </a>
        </li>
      <?php endforeach; ?>
    </ul>
  </div>

  <div class="col-md-9">
    <h4>Pruebas Complementarias</h4>

    <?php if ($mensaje): ?>
      <div class="alert alert-danger"><?= $mensaje ?></div>
    <?php endif; ?>

    <form method="post">
      <?php if ($seleccionado): ?>
        <input type="hidden" name="CLAVE" value="<?= $seleccionado['CLAVE'] ?>">
      <?php endif; ?>

      <div class="mb-3">
        <label for="COMPLEMENTARIOS" class="form-label">Complementarias</label>
        <textarea name="COMPLEMENTARIOS" id="COMPLEMENTARIOS" class="form-control" rows="6" maxlength="2500"><?= $seleccionado['COMPLEMENTARIOS'] ?? '' ?></textarea>
      </div>

      <button class="btn btn-success">Guardar</button>
      <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&nuevo=1" class="btn btn-secondary">Nuevo</a>
    </form>
  </div>
</div>
</body>
</html>
