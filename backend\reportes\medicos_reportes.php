<?php
// backend/reportes/medicos_reportes.php
session_start();
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: ../../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

$usuario = htmlspecialchars($_SESSION['usuario']);

// Procesar filtros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$tipo_examen = $_GET['tipo_examen'] ?? '';
$sexo = $_GET['sexo'] ?? '';
$edad_min = $_GET['edad_min'] ?? '';
$edad_max = $_GET['edad_max'] ?? '';
$reporte_tipo = $_GET['tipo'] ?? 'examenes';
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reportes Médicos - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background-color: #f8f9fa; }
        .header-section {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-notes-medical me-3"></i>Reportes Médicos</h1>
                    <p class="mb-0">Análisis de historias clínicas y exámenes médicos</p>
                </div>
                <div class="col-md-4 text-end">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-end">
                            <li class="breadcrumb-item"><a href="../../../index.php" class="text-white">Inicio</a></li>
                            <li class="breadcrumb-item"><a href="../../reportes.php" class="text-white">Reportes</a></li>
                            <li class="breadcrumb-item active text-white-50">Médicos</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filtros -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-filter me-2"></i>Filtros de Reporte</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label for="fecha_inicio" class="form-label">Fecha Inicio</label>
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" value="<?php echo $fecha_inicio; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="fecha_fin" class="form-label">Fecha Fin</label>
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" value="<?php echo $fecha_fin; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="tipo_examen" class="form-label">Tipo de Examen</label>
                        <select class="form-select" id="tipo_examen" name="tipo_examen">
                            <option value="">Todos</option>
                            <option value="fisico" <?php echo $tipo_examen == 'fisico' ? 'selected' : ''; ?>>Examen Físico</option>
                            <option value="laboratorio" <?php echo $tipo_examen == 'laboratorio' ? 'selected' : ''; ?>>Laboratorio</option>
                            <option value="orina" <?php echo $tipo_examen == 'orina' ? 'selected' : ''; ?>>Examen de Orina</option>
                            <option value="coprologico" <?php echo $tipo_examen == 'coprologico' ? 'selected' : ''; ?>>Coprológico</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="sexo" class="form-label">Sexo</label>
                        <select class="form-select" id="sexo" name="sexo">
                            <option value="">Todos</option>
                            <option value="M" <?php echo $sexo == 'M' ? 'selected' : ''; ?>>Masculino</option>
                            <option value="F" <?php echo $sexo == 'F' ? 'selected' : ''; ?>>Femenino</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label for="edad_min" class="form-label">Edad Min</label>
                        <input type="number" class="form-control" id="edad_min" name="edad_min" value="<?php echo $edad_min; ?>" min="0" max="120">
                    </div>
                    <div class="col-md-1">
                        <label for="edad_max" class="form-label">Edad Max</label>
                        <input type="number" class="form-control" id="edad_max" name="edad_max" value="<?php echo $edad_max; ?>" min="0" max="120">
                    </div>
                    <div class="col-md-2">
                        <label for="tipo" class="form-label">Tipo de Reporte</label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="examenes" <?php echo $reporte_tipo == 'examenes' ? 'selected' : ''; ?>>Exámenes</option>
                            <option value="diagnosticos" <?php echo $reporte_tipo == 'diagnosticos' ? 'selected' : ''; ?>>Diagnósticos</option>
                            <option value="tratamientos" <?php echo $reporte_tipo == 'tratamientos' ? 'selected' : ''; ?>>Tratamientos</option>
                            <option value="estadisticas" <?php echo $reporte_tipo == 'estadisticas' ? 'selected' : ''; ?>>Estadísticas</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-search me-2"></i>Generar Reporte
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportarExcel()">
                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportarPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Estadísticas Generales -->
        <?php
        // Construir filtros base
        $where_conditions = ["DATE(e.FECHA_CAP) BETWEEN ? AND ?"];
        $params = [$fecha_inicio, $fecha_fin];

        if ($sexo) {
            $where_conditions[] = "p.SEXO = ?";
            $params[] = $sexo;
        }

        $where_clause = "WHERE " . implode(" AND ", $where_conditions);
        
        // Agregar filtro de edad si se especifica
        $edad_clause = "";
        if ($edad_min || $edad_max) {
            if ($edad_min && $edad_max) {
                $edad_clause = " AND TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) BETWEEN $edad_min AND $edad_max";
            } elseif ($edad_min) {
                $edad_clause = " AND TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) >= $edad_min";
            } elseif ($edad_max) {
                $edad_clause = " AND TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) <= $edad_max";
            }
        }

        // Estadísticas de exámenes físicos
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM EXAMENFISICO e 
            JOIN PACIENTES p ON e.CLAVEPAC = p.CLAVE 
            $where_clause $edad_clause
        ");
        $stmt->execute($params);
        $total_examenes_fisicos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Estadísticas de exámenes de laboratorio
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM BIOQUIMICO b 
            JOIN PACIENTES p ON b.CLAVEPAC = p.CLAVE 
            WHERE DATE(b.FECHA_CAP) BETWEEN ? AND ?
            " . ($sexo ? " AND p.SEXO = ?" : "") . $edad_clause
        );
        $lab_params = [$fecha_inicio, $fecha_fin];
        if ($sexo) $lab_params[] = $sexo;
        $stmt->execute($lab_params);
        $total_laboratorios = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Estadísticas de exámenes de orina
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM EXAMENORINA o 
            JOIN PACIENTES p ON o.CLAVEPAC = p.CLAVE 
            WHERE DATE(o.FECHA_CAP) BETWEEN ? AND ?
            " . ($sexo ? " AND p.SEXO = ?" : "") . $edad_clause
        );
        $stmt->execute($lab_params);
        $total_orina = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Estadísticas de coprológicos
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM COPROLOGICO c 
            JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE 
            WHERE DATE(c.FECHA_CAP) BETWEEN ? AND ?
            " . ($sexo ? " AND p.SEXO = ?" : "") . $edad_clause
        );
        $stmt->execute($lab_params);
        $total_coprologico = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        ?>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-user-md fa-2x mb-2"></i>
                        <h4><?php echo $total_examenes_fisicos; ?></h4>
                        <p class="mb-0">Exámenes Físicos</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-flask fa-2x mb-2"></i>
                        <h4><?php echo $total_laboratorios; ?></h4>
                        <p class="mb-0">Laboratorios</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-vial fa-2x mb-2"></i>
                        <h4><?php echo $total_orina; ?></h4>
                        <p class="mb-0">Exámenes de Orina</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-microscope fa-2x mb-2"></i>
                        <h4><?php echo $total_coprologico; ?></h4>
                        <p class="mb-0">Coprológicos</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>Distribución de Exámenes</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="tipoExamenChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>Exámenes por Día</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="examenesDiariosChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Análisis de Signos Vitales -->
        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-heartbeat me-2"></i>Promedios de Signos Vitales</h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $stmt = $pdo->prepare("
                            SELECT 
                                AVG(PESO) as peso_promedio,
                                AVG(TALLA) as talla_promedio,
                                AVG(TAS) as tas_promedio,
                                AVG(TAD) as tad_promedio,
                                AVG(FC) as fc_promedio,
                                AVG(TEMP) as temp_promedio,
                                AVG(IMC) as imc_promedio
                            FROM EXAMENFISICO e 
                            JOIN PACIENTES p ON e.CLAVEPAC = p.CLAVE 
                            $where_clause $edad_clause
                        ");
                        $stmt->execute($params);
                        $promedios = $stmt->fetch(PDO::FETCH_ASSOC);
                        ?>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6>Peso Promedio</h6>
                                    <h4 class="text-primary"><?php echo round($promedios['peso_promedio'], 1); ?> kg</h4>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6>Talla Promedio</h6>
                                    <h4 class="text-success"><?php echo round($promedios['talla_promedio'], 1); ?> cm</h4>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6>IMC Promedio</h6>
                                    <h4 class="text-warning"><?php echo round($promedios['imc_promedio'], 1); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6>TAS Promedio</h6>
                                    <h5 class="text-danger"><?php echo round($promedios['tas_promedio'], 1); ?></h5>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6>TAD Promedio</h6>
                                    <h5 class="text-danger"><?php echo round($promedios['tad_promedio'], 1); ?></h5>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6>FC Promedio</h6>
                                    <h5 class="text-info"><?php echo round($promedios['fc_promedio'], 1); ?></h5>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center p-3 border rounded">
                                    <h6>Temp. Promedio</h6>
                                    <h5 class="text-secondary"><?php echo round($promedios['temp_promedio'], 1); ?>°C</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Distribución de IMC</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="imcChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabla de Exámenes Recientes -->
        <?php if ($reporte_tipo == 'examenes'): ?>
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>Exámenes Recientes</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="tablaExamenes">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>Paciente</th>
                                <th>Tipo</th>
                                <th>Peso</th>
                                <th>Talla</th>
                                <th>TAS/TAD</th>
                                <th>FC</th>
                                <th>Temp</th>
                                <th>IMC</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "SELECT e.FECHA_CAP, p.NOMBREAPELLIDO, e.PESO, e.TALLA, e.TAS, e.TAD, e.FC, e.TEMP, e.IMC
                                   FROM EXAMENFISICO e 
                                   JOIN PACIENTES p ON e.CLAVEPAC = p.CLAVE 
                                   $where_clause $edad_clause 
                                   ORDER BY e.FECHA_CAP DESC 
                                   LIMIT 50";
                            
                            $stmt = $pdo->prepare($sql);
                            $stmt->execute($params);
                            $examenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            foreach ($examenes as $examen):
                            ?>
                            <tr>
                                <td><?php echo date('d/m/Y', strtotime($examen['FECHA_CAP'])); ?></td>
                                <td><?php echo htmlspecialchars($examen['NOMBREAPELLIDO']); ?></td>
                                <td><span class="badge bg-info">Físico</span></td>
                                <td><?php echo $examen['PESO'] ? $examen['PESO'] . ' kg' : 'N/A'; ?></td>
                                <td><?php echo $examen['TALLA'] ? $examen['TALLA'] . ' cm' : 'N/A'; ?></td>
                                <td><?php echo ($examen['TAS'] && $examen['TAD']) ? $examen['TAS'] . '/' . $examen['TAD'] : 'N/A'; ?></td>
                                <td><?php echo $examen['FC'] ?? 'N/A'; ?></td>
                                <td><?php echo $examen['TEMP'] ? $examen['TEMP'] . '°C' : 'N/A'; ?></td>
                                <td>
                                    <?php 
                                    if ($examen['IMC']) {
                                        $imc = $examen['IMC'];
                                        $clase = '';
                                        if ($imc < 18.5) $clase = 'text-primary';
                                        elseif ($imc < 25) $clase = 'text-success';
                                        elseif ($imc < 30) $clase = 'text-warning';
                                        else $clase = 'text-danger';
                                        echo "<span class='$clase'>" . round($imc, 1) . "</span>";
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Datos para gráficos
        <?php
        // Datos para gráfico de tipos de examen
        $tipos_data = [
            ['tipo' => 'Examen Físico', 'cantidad' => $total_examenes_fisicos],
            ['tipo' => 'Laboratorio', 'cantidad' => $total_laboratorios],
            ['tipo' => 'Examen de Orina', 'cantidad' => $total_orina],
            ['tipo' => 'Coprológico', 'cantidad' => $total_coprologico]
        ];

        $labels_tipos = [];
        $data_tipos = [];
        foreach ($tipos_data as $tipo) {
            if ($tipo['cantidad'] > 0) {
                $labels_tipos[] = $tipo['tipo'];
                $data_tipos[] = $tipo['cantidad'];
            }
        }

        // Datos para gráfico diario de exámenes físicos
        $stmt = $pdo->prepare("
            SELECT DATE(e.FECHA_CAP) as fecha, COUNT(*) as cantidad 
            FROM EXAMENFISICO e 
            JOIN PACIENTES p ON e.CLAVEPAC = p.CLAVE 
            $where_clause $edad_clause 
            GROUP BY DATE(e.FECHA_CAP) 
            ORDER BY fecha DESC 
            LIMIT 30
        ");
        $stmt->execute($params);
        $datos_diarios = array_reverse($stmt->fetchAll(PDO::FETCH_ASSOC));

        $labels_diarios = [];
        $data_diarios = [];
        foreach ($datos_diarios as $dato) {
            $labels_diarios[] = date('d/m', strtotime($dato['fecha']));
            $data_diarios[] = $dato['cantidad'];
        }

        // Datos para gráfico de IMC
        $stmt = $pdo->prepare("
            SELECT 
                CASE 
                    WHEN IMC < 18.5 THEN 'Bajo peso'
                    WHEN IMC BETWEEN 18.5 AND 24.9 THEN 'Normal'
                    WHEN IMC BETWEEN 25 AND 29.9 THEN 'Sobrepeso'
                    ELSE 'Obesidad'
                END as categoria_imc,
                COUNT(*) as cantidad
            FROM EXAMENFISICO e 
            JOIN PACIENTES p ON e.CLAVEPAC = p.CLAVE 
            $where_clause $edad_clause AND e.IMC IS NOT NULL AND e.IMC > 0
            GROUP BY categoria_imc
        ");
        $stmt->execute($params);
        $datos_imc = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $labels_imc = [];
        $data_imc = [];
        foreach ($datos_imc as $imc) {
            $labels_imc[] = $imc['categoria_imc'];
            $data_imc[] = $imc['cantidad'];
        }
        ?>

        // Gráfico de tipos de examen
        const ctx1 = document.getElementById('tipoExamenChart').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode($labels_tipos); ?>,
                datasets: [{
                    data: <?php echo json_encode($data_tipos); ?>,
                    backgroundColor: ['#17a2b8', '#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Gráfico diario
        const ctx2 = document.getElementById('examenesDiariosChart').getContext('2d');
        new Chart(ctx2, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($labels_diarios); ?>,
                datasets: [{
                    label: 'Exámenes por día',
                    data: <?php echo json_encode($data_diarios); ?>,
                    borderColor: '#17a2b8',
                    backgroundColor: 'rgba(23, 162, 184, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Gráfico de IMC
        const ctx3 = document.getElementById('imcChart').getContext('2d');
        new Chart(ctx3, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($labels_imc); ?>,
                datasets: [{
                    label: 'Pacientes',
                    data: <?php echo json_encode($data_imc); ?>,
                    backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        function exportarExcel() {
            window.location.href = 'api/exportar_medicos.php?formato=excel&' + new URLSearchParams(window.location.search);
        }

        function exportarPDF() {
            window.location.href = 'api/exportar_medicos.php?formato=pdf&' + new URLSearchParams(window.location.search);
        }
    </script>
</body>
</html>
