<?php
session_start();
require_once '../config/database.php';

/* ---------- Parámetros ---------- */
$perPage = 10;
$page    = max(1, (int)($_GET['pagina'] ?? 1));
$offset  = ($page - 1) * $perPage;
$q       = $_GET['busqueda'] ?? '';

$where  = $q ? "WHERE NOMBRES LIKE ? OR APELLIDOS LIKE ? OR CEDULA LIKE ?" : '';
$params = $q ? array_fill(0, 3, "%$q%") : [];

/* ---------- Conteo total ---------- */
$stmtTotal = $pdo->prepare("SELECT COUNT(*) FROM PACIENTES $where");
$stmtTotal->execute($params);
$totalRows  = (int)$stmtTotal->fetchColumn();
$totalPages = (int)ceil($totalRows / $perPage);

/* ---------- <PERSON><PERSON> de la página actual ---------- */
$sql = "SELECT CLAVE, NOMBRES, APELLIDOS, CEDULA
        FROM PACIENTES $where
        ORDER BY NOMBRES
        LIMIT $perPage OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

/* ---------- Helpers ---------- */
function h(string $s): string            { return htmlspecialchars($s, ENT_QUOTES); }
function linkPag(int $p, string $q): string {
    return '?' . http_build_query(['pagina' => $p] + ($q ? ['busqueda' => $q] : []));
}
function mark(string $txt, string $needle): string {
    return $needle
        ? preg_replace('~(' . preg_quote($needle, '~') . ')~i',
            '<mark class="px-1 bg-yellow-200 dark:bg-yellow-600/60">$1</mark>',
            h($txt))
        : h($txt);
}

/* --- helpers para la nueva paginación --- */
function pageBtn(int $p, string $label, bool $disabled, string $q): string {
    $cls = $disabled
        ? 'pointer-events-none opacity-40'
        : 'hover:bg-gray-100 dark:hover:bg-gray-700';
    return '<li>
              <a href="'.linkPag($p, $q).'"
                 class="min-w-[38px] px-3 py-1.5 rounded border '.$cls.'">'.$label.'</a>
            </li>';
}
function pageNum(int $n, string $q, bool $current = false): string {
    $cls = $current
        ? 'pointer-events-none bg-indigo-600 text-white'
        : 'hover:bg-gray-100 dark:hover:bg-gray-700';
    return '<li><a href="'.linkPag($n,$q).'"
               class="min-w-[38px] px-3 py-1.5 rounded border '.$cls.'">'.$n.'</a></li>';
}
function gap(): string { return '<li class="px-2 py-1.5 text-gray-500 select-none">…</li>'; }
?>
<!DOCTYPE html>
<html lang="es" class="h-full" data-theme="light">
<head>
  <meta charset="UTF-8">
  <title>Seleccionar paciente</title>

  <!-- Tailwind vía CDN -->
  <script src="https://cdn.tailwindcss.com?plugins=typography"></script>
  <!-- Dark-mode automático -->
  <script>if (matchMedia('(prefers-color-scheme: dark)').matches)
      document.documentElement.classList.add('dark');</script>

  <!-- Alpine.js (solo para autofocus + Esc) -->
  <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

  <!-- FontAwesome (opcional) -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/js/all.min.js" defer></script>

  <style>[x-cloak]{display:none}</style>
</head>
<body class="min-h-full bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-gray-100">

<div class="mx-auto max-w-5xl px-4 py-6" x-data x-init="$refs.busq.focus()">

  <!-- Cabecera -->
  <header class="flex items-center gap-4 mb-6">
    <h1 class="text-2xl font-semibold flex items-center gap-2">
      <i class="fa-solid fa-user-injured"></i> Seleccionar paciente
    </h1>
  </header>

  <!-- Buscador -->
  <form method="get" class="flex gap-2 mb-4">
    <label class="sr-only" for="busq">Buscar</label>
    <input x-ref="busq" id="busq" name="busqueda" value="<?=h($q)?>" autocomplete="off"
           placeholder="Nombre, apellido o cédula"
           @keydown.escape.window="location='seleccionar_paciente.php'"
           class="flex-grow px-3 py-2 rounded border border-gray-300 dark:border-gray-600
                  bg-white dark:bg-gray-800 focus:ring-2 focus:ring-indigo-500" />
    <?php if ($q): ?>
      <a href="seleccionar_paciente.php"
         class="inline-flex items-center gap-1 px-3 py-2 rounded border
                border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
        <i class="fa-solid fa-xmark"></i> Limpiar
      </a>
    <?php endif; ?>
    <button class="px-3 py-2 rounded text-white bg-indigo-600 hover:bg-indigo-700">
      <i class="fa-solid fa-magnifying-glass"></i>
    </button>
  </form>

  <!-- Info resultados -->
  <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
    Mostrando <strong><?=count($rows)?></strong> de <strong><?=$totalRows?></strong>
    <?php if ($q): ?> | Buscado: «<?=h($q)?>»<?php endif; ?>
  </p>

  <!-- Tabla -->
  <div class="overflow-x-auto shadow rounded-lg ring-1 ring-gray-200 dark:ring-gray-700">
    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
      <thead class="bg-gray-100 dark:bg-gray-800 sticky top-0 z-10 text-sm font-semibold">
        <tr>
          <th class="px-4 py-3 text-left">Nombre</th>
          <th class="px-4 py-3 text-left">Cédula</th>
          <th class="px-4 py-3 text-right">Acción</th>
        </tr>
      </thead>
      <tbody>
        <?php if ($rows): foreach ($rows as $p): ?>
          <tr tabindex="0"
              onclick="location='set_paciente.php?CLAVEPAC=<?=$p['CLAVE']?>'"
              onkeydown="if(event.key==='Enter')this.click()"
              class="divide-x divide-gray-100 dark:divide-gray-800
                     hover:bg-indigo-50/60 dark:hover:bg-indigo-800/30
                     focus:ring-2 focus:ring-indigo-600 cursor-pointer">
            <td class="px-4 py-2"><?=mark($p['NOMBRES'].' '.$p['APELLIDOS'],$q)?></td>
            <td class="px-4 py-2"><?=mark($p['CEDULA'],$q)?></td>
            <td class="px-4 py-2 text-right">
              <span class="text-indigo-600 dark:text-indigo-400 hover:underline">
                Ver historia
              </span>
            </td>
          </tr>
        <?php endforeach; else: ?>
          <tr><td colspan="3" class="px-4 py-6 text-center text-gray-500">Sin resultados</td></tr>
        <?php endif; ?>
      </tbody>
    </table>
  </div>

  <!-- Paginación -->
  <?php if ($totalPages > 1): ?>
    <nav class="mt-8">
      <ul class="flex flex-wrap justify-center gap-1 sm:gap-2 text-sm font-medium">

        <!-- anterior -->
        <?=pageBtn($page - 1, '&laquo;', $page <= 1, $q)?>

        <?php
        $win = 2;                                // rango: actual ± $win
        $from = max(1, $page - $win);
        $to   = min($totalPages, $page + $win);

        if ($from > 1)   echo pageNum(1, $q) . gap();      // primera + elipsis

        for ($i = $from; $i <= $to; $i++)       // ventana centrada
            echo pageNum($i, $q, $i === $page);

        if ($to < $totalPages)                  // elipsis + última
            echo ($to < $totalPages - 1 ? gap() : '') . pageNum($totalPages, $q);
        ?>

        <!-- siguiente -->
        <?=pageBtn($page + 1, '&raquo;', $page >= $totalPages, $q)?>

      </ul>
    </nav>
  <?php endif; ?>

</div>
</body>
</html>
