<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

//include_once '../config/config.php';
//require_once __DIR__ . '../config/config.php';

header('Content-Type: application/json; charset=UTF-8');
require_once __DIR__ . '/../config/config.php';
//$conn = getDBConnection();

function convertirFecha($fecha) {
    $fecha = trim($fecha);
    if (DateTime::createFromFormat('Y-m-d H:i:s', $fecha) || DateTime::createFromFormat('Y-m-d', $fecha)) {
        return $fecha;
    }
    $fecha = str_replace(['/', '.', '‐'], '-', $fecha);
    $formatos = [ 'm/d/Y h:i:s a', 'm/d/Y h:i a', 'd/m/Y h:i:s a', 'd/m/Y h:i a',
        'm/d/Y h:i:s A', 'm/d/Y h:i A',  'd/m/Y h:i:s A', 'd/m/Y h:i A',
        'd-m-Y H:i:s', 'd-m-Y', 'm-d-Y H:i:s', 'm-d-Y', 'd/m/Y H:i:s', 'd/m/Y',
        'm/d/Y H:i:s', 'm/d/Y', 'd.m.Y H:i:s', 'd.m.Y', 'd M Y', 'd M Y H:i:s',
        'd F Y', 'd F Y H:i:s', 'Y-m-d\TH:i:sP', 'U'
    ];
    
    foreach ($formatos as $formato) {
        $fechaObj = DateTime::createFromFormat($formato, $fecha);
        if ($fechaObj) {
            return (strpos($formato, 'H:i:s') !== false) ? $fechaObj->format('Y-m-d H:i:s') : $fechaObj->format('Y-m-d');
        }
    }
    return date('Y-m-d H:i:  s');
}

try {
   $conn = getDBConnection();
$conn->set_charset("utf8");

// ✅ Verificamos que el parámetro 'tabla' exista
if (!isset($_GET['tabla'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Falta el parámetro tabla']);
    exit;
}

$tabla = $conn->real_escape_string($_GET['tabla']);

$tablas_permitidas = ['PACIENTES', 'CITAMEDIC', 'EMPRESA', 'CONFIG', 'FOTOGRAFIAS', 'FACTURAS',
'ELIMINACIONES','RESULTADOS_PACIENTES', 'CERTIFICADO','COMPLEMENTARIAS','DIAGNOSTICO',
'ENFERMEDAD_ACTUAL','ESTUDIOS','INFORMES','PRESCRIPCION','SEGUIMIENTO','TRATAMIENTOS','ANTECEDENTES',
'ANTGINECOBSTETRICO',  'BIOQUIMICO',   'CIRUGIA',  'COLPOSCOPIA', 'COPROLOGICO',  'EMBARAZO',
    'EXAMENFISICO', 'EXAMENORINA', 'FINEMBARAZO', 'GASTROCOPIA','HEMOGRAMAGRAL',  'OBSTETRICIA','VISITAS'];

// ✅ Verificamos que la tabla esté permitida
if (!in_array($tabla, $tablas_permitidas)) {
    http_response_code(400);
    echo json_encode(['error' => 'Tabla no permitida']);
    exit;
}


// ✅ PROCESAR ELIMINACIONES (Método POST)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    
        // Procesar inserciones/actualizaciones para las demás tablas
        foreach ($input as $registro) {
            if (!isset($registro['CLAVE']) && !in_array($tabla, ['EMPRESA', 'CONFIG'])) {
                continue;
            }
           
            if ($tabla === 'PACIENTES') {
                if (empty($registro['NOMBRES']) || empty($registro['APELLIDOS']) || empty($registro['CEDULA'])) {
                    echo json_encode(["status" => "error", "message" => "Faltan campos obligatorios en PACIENTES"]);
                    exit;
                }
           
                $registro['FECHANAC'] = !empty($registro['FECHANAC']) ? convertirFecha($registro['FECHANAC']) : null;
                $registro['FECHAINGRESO'] = !empty($registro['FECHAINGRESO']) ? convertirFecha($registro['FECHAINGRESO']) : null;
                $registro['VIGENCIA'] = !empty($registro['VIGENCIA']) ? convertirFecha($registro['VIGENCIA']) : null;
                $registro['LAST_MODIFIED'] = (!empty($registro['LAST_MODIFIED']) && $registro['LAST_MODIFIED'] !== '0000-00-00 00:00:00')
                    ? convertirFecha($registro['LAST_MODIFIED']) : date('Y-m-d H:i:s');
               
                $sqlCheck = "SELECT REGISTRO FROM PACIENTES WHERE CEDULA = ?";
                $stmtCheck = $conn->prepare($sqlCheck);
                $stmtCheck->bind_param("s", $registro['CEDULA']);
                $stmtCheck->execute();
                $resultCheck = $stmtCheck->get_result();
               
                if ($resultCheck->num_rows > 0) {
                    $row = $resultCheck->fetch_assoc();
                    $registro['REGISTRO'] = $row['REGISTRO'];
                } else {
                    $sqlMax = "SELECT MAX(REGISTRO) as maxReg FROM PACIENTES";
                    $resultMax = $conn->query($sqlMax);
                    $rowMax = $resultMax->fetch_assoc();
                    $registro['REGISTRO'] = ($rowMax['maxReg'] !== null) ? ($rowMax['maxReg'] + 1) : 1;
                }
                $stmtCheck->close();       
            }
            
            else if ($tabla === 'CITAMEDIC') {
                if (empty($registro['NSS']) || empty($registro['FECHACON']) || empty($registro['HORACON'])) {
                    echo json_encode(["status" => "error", "message" => "Campos obligatorios faltantes en CITAMEDIC"]);
                    exit;
                }
                $registro['FECHACON'] = convertirFecha($registro['FECHACON']);
                $registro['FECHA_CAP'] = !empty($registro['FECHA_CAP']) ? convertirFecha($registro['FECHA_CAP']) : date('Y-m-d H:i:s');
                $sqlBuscarPaciente = "SELECT CLAVE FROM PACIENTES WHERE NSS = ?";
                $stmtBuscarPaciente = $conn->prepare($sqlBuscarPaciente);
                $stmtBuscarPaciente->bind_param("s", $registro['NSS']);
                $stmtBuscarPaciente->execute();
                $resultPaciente = $stmtBuscarPaciente->get_result();
                $pacienteRemoto = $resultPaciente->fetch_assoc();
                if ($pacienteRemoto) {
                    $registro['CLAVEPAC'] = $pacienteRemoto['CLAVE']; 
                } else {
                    echo json_encode(["status" => "error", "message" => "Paciente no encontrado en remoto para NSS: " . $registro['NSS']]);
                    exit;
                }
                $stmtBuscarPaciente->close();
            
                $sqlVerificarCita = "SELECT CLAVEPAC FROM CITAMEDIC WHERE FECHACON = ? AND HORACON = ?";
                $stmtVerificarCita = $conn->prepare($sqlVerificarCita);
                $stmtVerificarCita->bind_param("ss", $registro['FECHACON'], $registro['HORACON']);
                $stmtVerificarCita->execute();
                $resultCita = $stmtVerificarCita->get_result();
                $citaExistente = $resultCita->fetch_assoc();
                $stmtVerificarCita->close();
            
                if ($citaExistente) {
                    if ($citaExistente['CLAVEPAC'] !== $registro['CLAVEPAC']) {
                        echo json_encode(["status" => "error", "message" => "Esta cita ya está asignada a otro paciente"]);
                        exit;
                    }
                }            
            }
            
            
            else if ($tabla === 'EMPRESA') {
                file_put_contents('debug_log.txt', "📌 Datos recibidos para EMPRESA: " . print_r($registro, true) . "\n", FILE_APPEND);
                $registro['RNC'] = trim($registro['RNC']);
                if (empty($registro['CALLE']) || empty($registro['TELEFONO'])) {
                    echo json_encode(["status" => "error", "message" => "Campos obligatorios faltantes en EMPRESA"]);
                    exit;
                }
                if (!empty($registro['FECHA'])) {
                    $registro['FECHA'] = convertirFecha($registro['FECHA']);
                }
            }
          
            else if ($tabla === 'CONFIG') {
                $registro['FECHA_CAP'] = !empty($registro['FECHA_CAP']) ? convertirFecha($registro['FECHA_CAP']) : date('Y-m-d H:i:s');
            }
           
           
           
            else if ($tabla === 'RESULTADOS_PACIENTES') {
                
                
             if (empty($registro['CEDULA']) || empty($registro['ARCHIVO'])) {
                    echo json_encode(["status" => "error", "message" => "Campos obligatorios faltantes en RESULTADOS_PACIENTES"]);
                    exit;
                }   
                
                // --- NUEVA LÓGICA: Resolver CLAVEPAC usando CEDULA ---
                $cedula = $conn->real_escape_string($registro['CEDULA']);
                $sqlPaciente = "SELECT CLAVE FROM PACIENTES WHERE CEDULA = '$cedula'";
                $resultPaciente = $conn->query($sqlPaciente);
                
                if ($resultPaciente && $resultPaciente->num_rows > 0) {
                    $rowPaciente = $resultPaciente->fetch_assoc();
                    $registro['CLAVEPAC'] = $rowPaciente['CLAVE']; // Asigna la CLAVEPAC encontrada
                } else {
                    // Si el paciente no se encuentra, reportar error y salir
                    echo json_encode(["status" => "error", "message" => "Paciente no encontrado en la tabla PACIENTES para la CÉDULA: " . $cedula]);
                    exit;
                }
                // --- FIN NUEVA LÓGICA ---  
                
                
                $registro['FECHA_SUBIDA'] = !empty($registro['FECHA_SUBIDA']) ? convertirFecha($registro['FECHA_SUBIDA']) : date('Y-m-d H:i:s');
            }
           
           
           
            else if ($tabla === 'FOTOGRAFIAS') {
                if (empty($registro['CEDULA'])) {
                    echo json_encode(["status" => "error", "message" => "El campo CEDULA es obligatorio en FOTOGRAFIAS"]);
                    exit;
                }
                
                $cedula = $registro['CEDULA'];
                $sqlPaciente = "SELECT CLAVE FROM PACIENTES WHERE CEDULA = '" . $conn->real_escape_string($cedula) . "'";
                $resultPaciente = $conn->query($sqlPaciente);
               
                if ($resultPaciente && $resultPaciente->num_rows > 0) {
                    $rowPaciente = $resultPaciente->fetch_assoc();
                    $registro['CLAVEPAC'] = $rowPaciente['CLAVE'];
                } else {
                    echo json_encode(["status" => "error", "message" => "No se encontró el paciente remoto para la cédula " . $cedula]);
                    exit;
                }
                
                if (isset($registro['ARCHIVO'])) {
                    $registro['ARCHIVO'] = str_replace('\\', '/', $registro['ARCHIVO']);
                }
                if (empty($registro['NOMBRE']) || empty($registro['ARCHIVO']) || empty($registro['CLAVEPAC'])) {
                    echo json_encode(["status" => "error", "message" => "Faltan campos obligatorios en FOTOGRAFIAS"]);
                    exit;
                }
                $registro['FECHA_CAP'] = !empty($registro['FECHA_CAP']) ? convertirFecha($registro['FECHA_CAP']) : date('Y-m-d H:i:s');
                if (!isset($registro['TIPO'])) { $registro['TIPO'] = 1; }
            }
           
           
           
         else if ($tabla === 'FACTURAS') {
    // Validación básica: se requiere CEDULA, UUID, CLAVEPAC y NUMFACT.
    if (empty($registro['CEDULA']) || empty($registro['UUID']) || empty($registro['CLAVEPAC']) || empty($registro['NUMFACT'])) {
        echo json_encode(["status" => "error", "message" => "Campos obligatorios faltantes en FACTURAS"]);
        exit;
    }
    
    // Conversión de fechas
    $registro['FECHAPROC'] = !empty($registro['FECHAPROC']) ? convertirFecha($registro['FECHAPROC']) : null;
    $registro['FECHAPAGO'] = !empty($registro['FECHAPAGO']) ? convertirFecha($registro['FECHAPAGO']) : null;
    $registro['FECHA']     = !empty($registro['FECHA'])     ? convertirFecha($registro['FECHA'])     : date('Y-m-d');
    $registro['HORA']      = !empty($registro['HORA'])      ? $registro['HORA'] : date('H:i:s');

    // Usamos la cédula para buscar el paciente en la tabla PACIENTES remota y obtener su CLAVE
    $sqlPaciente = "SELECT CLAVE FROM PACIENTES WHERE CEDULA = ?";
    $stmtPaciente = $conn->prepare($sqlPaciente);
    $stmtPaciente->bind_param("s", $registro['CEDULA']);
    $stmtPaciente->execute();
    $resultPaciente = $stmtPaciente->get_result();
    if ($resultPaciente && $resultPaciente->num_rows > 0) {
        $rowPaciente = $resultPaciente->fetch_assoc();
        // Actualizamos el campo CLAVEPAC con la clave real del paciente en el entorno remoto
        $registro['CLAVEPAC'] = $rowPaciente['CLAVE'];
    } else {
        echo json_encode(["status" => "error", "message" => "Paciente no encontrado en remoto para cédula " . $registro['CEDULA']]);
        exit;
    }
    $stmtPaciente->close();
    
    // Verificar si ya existe una factura con el mismo UUID en remoto
    $sqlCheck = "SELECT CLAVE FROM FACTURAS WHERE UUID = ?";
    $stmtCheck = $conn->prepare($sqlCheck);
    $stmtCheck->bind_param("s", $registro['UUID']);
    $stmtCheck->execute();
    $res = $stmtCheck->get_result();
    if ($res && $res->num_rows > 0) {
        // Ya existe, podemos optar por actualizar o simplemente continuar
        continue;
    }
    $stmtCheck->close();
}

     
 // Aquí empieza el tratamiento genérico para tus tablas de historia clínica
 else if (in_array($tabla, [ 'CERTIFICADO','COMPLEMENTARIAS','DIAGNOSTICO',
    'ENFERMEDAD_ACTUAL','ESTUDIOS','INFORMES','PRESCRIPCION','SEGUIMIENTO','TRATAMIENTOS','ANTECEDENTES',
    'ANTGINECOBSTETRICO','BIOQUIMICO', 'CIRUGIA','COLPOSCOPIA','COPROLOGICO','EMBARAZO','EXAMENFISICO',
    'EXAMENORINA',   'FINEMBARAZO', 'GASTROCOPIA', 'HEMOGRAMAGRAL', 'OBSTETRICIA','VISITAS'])) {
    // 1) Validar campos obligatorios
    if (empty($registro['CEDULA']) ) {
        http_response_code(400);
        echo json_encode([
            'status'  => 'error',
            'message' => "Falta CEDULA  en $tabla"
        ]);
        exit;
    }

 // 2) Convertir FECHA_CAP Y OTRAS FECHAS DE HISTORIA CLINICA

 
  $registro['FECHAVISITA'] = !empty($registro['FECHAVISITA']) ? convertirFecha($registro['FECHAVISITA']) : null;


if ($tabla !== 'EMBARAZO') {
    $registro['FECHA_CAP'] = !empty($registro['FECHA_CAP']) ? convertirFecha($registro['FECHA_CAP']) : null;
} else {
    unset($registro['FECHA_CAP']);
}

$campos_fecha = ['FUM','FUA','FUC','FUP','FPP','FUPAGO'];

foreach ($campos_fecha as $campo) {
    if (isset($registro[$campo]) && !empty($registro[$campo])) {
        $registro[$campo] = convertirFecha($registro[$campo]);
    } else {
        unset($registro[$campo]); // ❌ Quita del array si no existe o está vacío
    }
}

if ($tabla === 'EMBARAZO') {
    $registro['FECHAINIEMB']  = !empty($registro['FECHAINIEMB'])  ? convertirFecha($registro['FECHAINIEMB'])  : null;
    $registro['FECHAFINEMB']  = !empty($registro['FECHAFINEMB'])  ? convertirFecha($registro['FECHAFINEMB'])  : null;
}
  
    // 3) Comprobar que venga el UUID (viene de local)
    if (empty($registro['UUID'])) {
        http_response_code(400);
        echo json_encode([
            'status'  => 'error',
            'message' => "Falta UUID en $tabla"
        ]);
        exit;
    }

    // 4) Resolver CLAVEPAC contra la tabla PACIENTES remota usando CEDULA
    $ced = $conn->real_escape_string($registro['CEDULA']);
    $rs  = $conn->query("SELECT CLAVE FROM PACIENTES WHERE CEDULA = '$ced'");
    if (!$rs || $rs->num_rows === 0) {
        http_response_code(400);
        echo json_encode([
            'status'  => 'error',
            'message' => "Paciente no encontrado en remoto para CEDULA $ced"
        ]);
        exit;
    }
    $registro['CLAVEPAC'] = $rs->fetch_assoc()['CLAVE'];
   
    // ——— NUEVO: Resolver CLAVEEMB solo en OBSTETRICIA ———
    if ($tabla === 'OBSTETRICIA') {
        // tienes NUMEMB en $registro
        $numemb = intval($conn->real_escape_string($registro['NUMEMB']));
        $rs2    = $conn->query( "SELECT CLAVE FROM EMBARAZO WHERE CEDULA = '$ced'
               AND NUMEMB  = $numemb" );
       
        if (!$rs2 || $rs2->num_rows === 0) {
            http_response_code(400);
            echo json_encode([
              'status'  => 'error',
              'message' => "Embarazo no encontrado en remoto para CEDULA $ced, NUMEMB $numemb"
            ]);
            exit;
        }
        $registro['CLAVEEMB'] = $rs2->fetch_assoc()['CLAVE'];
    }
    // ——————————————————————————————————————————————————
    
    // 5) Marcar registro como sincronizado
    $registro['SINCRONIZADO'] = 1;
}
    
     
    else if ($tabla === 'ELIMINACIONES') {
    if (empty($registro['TABLA']) || empty($registro['CAMPO_CLAVE']) || empty($registro['VALOR_CLAVE'])) {
        echo json_encode(["status" => "error", "message" => "Campos obligatorios faltantes en ELIMINACIONES"]);
        exit;
    }

    $tablaDestino = $conn->real_escape_string($registro['TABLA']);
    $campoClave   = $conn->real_escape_string($registro['CAMPO_CLAVE']);
    $valorClave   = trim($conn->real_escape_string($registro['VALOR_CLAVE']));
    $clave        = (int)$registro['CLAVE'];

    // 🔍 Verificamos si es CITAMEDIC y tenemos FECHACON y HORACON
    if ($tablaDestino === 'CITAMEDIC' && !empty($registro['FECHACON']) && !empty($registro['HORACON'])) {
        $fechaCon = convertirFecha($registro['FECHACON']);
        $horaCon = trim($registro['HORACON']);

        $sqlDelete = "DELETE FROM CITAMEDIC WHERE NSS = '$valorClave' AND FECHACON = '$fechaCon' AND HORACON = '$horaCon'";
    } else {
        // Eliminación general para otras tablas
        $sqlDelete = "DELETE FROM $tablaDestino WHERE $campoClave = '$valorClave'";
    }

    if ($conn->query($sqlDelete)) {
        $conn->query("UPDATE ELIMINACIONES SET SINCRONIZADO = 1 WHERE CLAVE = $clave");
        echo json_encode(["status" => "success", "message" => "Registro eliminado correctamente"]);
    } else {
        echo json_encode(["status" => "error", "message" => "Error al eliminar: " . $conn->error]);
    }

    return; // ⛔ Salimos del foreach para que no siga con inserciones innecesarias
}

            // Se marca el registro como sincronizado
            $registro['SINCRONIZADO'] = 1;
            
            $campos = array_keys($registro);
            $valores = array_map(function ($v) use ($conn) {
    if (is_null($v)) {
        return "NULL"; // sin comillas para el NULL SQL
    }
    return "'" . $conn->real_escape_string($v) . "'";
}, array_values($registro));

            $updates = implode(', ', array_map(function ($k) {
                return "$k = VALUES($k)";
            }, $campos));
            $sql = "INSERT INTO $tabla (" . implode(', ', $campos) . ") VALUES (" . implode(', ', $valores) . ") ON DUPLICATE KEY UPDATE $updates";
            if (!$conn->query($sql)) {
                file_put_contents('debug_empresa.txt', "❌ ERROR en MySQL: " . $conn->error . "\n", FILE_APPEND);
                http_response_code(500);
                echo json_encode(['error' => 'Error en la consulta: ' . $conn->error]);
                exit;
            }
        }
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
} finally {
    $conn->close();
}
?>
