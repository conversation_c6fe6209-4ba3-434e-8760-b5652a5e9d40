object TRpReport
  PageOrientation = rpOrientationPortrait
  Pagesize = rpPageSizeUser
  PagesizeQt = 2
  PageHeight = 7639
  PageWidth = 5903
  CustomPageHeight = 7540
  PageBackColor = 16777215
  TopMargin = 283
  RightMargin = 0
  BottomMargin = 170
  SubReports = <
    item
      SubReport = TRpSubReport0
    end>
  DataInfo = <
    item
      Alias = 'DETALLE'
      DatabaseAlias = 'CONSULTORIO'
      SQL = 
        'Select r.clave ,r.clavepac ,r.descripcion,r. fecha_cap , p.nombr' +
        'es, p. a<PERSON>lid<PERSON>,p.cedula, p.sincedula,p.nss, p.AFILIADO,p.poliz' +
        'a, p.edad,p.fechanac,p.ars,p.RH, a.nombre  from prescripcion r  ' +
        'left join pacientes p on r.clavepac=p.clave'#13#10'left join asegurado' +
        'ra a on clave = ars where r.clave =:clave'
    end
    item
      Alias = 'EMPRESA'
      DatabaseAlias = 'CONSULTORIO'
      SQL = 
        'select rnc, consultorio,nombre, especialidad, execquatur,codclin' +
        ',colegiacion,cidc,clinica,calle, provincia,localidad,pais,NumCon' +
        'sultorio,correoe,paginaweb,fecha,telefono,Telefonodr,fax,celular' +
        'dr,clinica FROM EMPRESA where consultorio=:consultorio'
    end
    item
      Alias = 'CITA'
      DatabaseAlias = 'CONSULTORIO'
      SQL = 
        'Select c.fechacon,c.horacon  from'#13#10' prescripcion r  left join ci' +
        'tamedic c on r.clavepac=c.clavepac'#13#10' where r.clave =:clave  orde' +
        'r by c.fechacon desc rows 1'#13#10#13#10
    end
    item
      Alias = 'CONFIG'
      DatabaseAlias = 'CONSULTORIO'
      SQL = 
        'select imprimencabezado, pathconfig from config  where consultor' +
        'io=:consultorio'
    end>
  DatabaseInfo = <
    item
      Alias = 'CONSULTORIO'
      LoadParams = True
      LoadDriverParams = True
      LoginPrompt = False
      ReportTable = 'REPMAN_REPORTS'
      ReportSearchField = 'REPORT_NAME'
      ReportField = 'REPORT'
      ReportGroupsTable = 'REPMAN_GROUPS'
      ADOConnectionString = ''
    end>
  Params = <
    item
      Name = 'CLAVE'
      AllowNulls = False
      Value = '64'
      Datasets.Strings = (
        'DETALLE'
        'CITA')
      SearchDataset = 'DETALLE'
      SearchParam = 'CLAVE'
      Description = ''
      Hint = ''
      Search = ''
      ErrorMessage = ''
      Validation = ''
    end
    item
      Name = 'CONSULTORIO'
      AllowNulls = False
      Value = 1
      ParamType = rpParamInteger
      Datasets.Strings = (
        'EMPRESA'
        'CONFIG')
      SearchDataset = 'EMPRESA'
      SearchParam = 'CONSULTORIO'
      Description = ''
      Hint = ''
      Search = ''
      ErrorMessage = ''
      Validation = ''
    end>
  StreamFormat = rpStreamText
  ReportAction = []
  Type1Font = poHelvetica
  WFontName = 'Arial'
  LFontName = 'Helvetica'
  object TRpSubReport0: TRpSubReport
    Sections = <
      item
        Section = TRpSection1
      end
      item
        Section = TRpSection0
      end
      item
        Section = TRpSection2
      end>
    Alias = 'DETALLE'
  end
  object TRpSection0: TRpSection
    Width = 10772
    Height = 4485
    SubReport = TRpSubReport0
    ChangeBool = False
    PageRepeat = False
    SkipPage = False
    AlignBottom = False
    SectionType = rpsecdetail
    Components = <
      item
        Component = TRpLabel0
      end
      item
        Component = TRpExpression7
      end>
    AutoExpand = True
    AutoContract = True
    ExternalTable = 'REPMAN_REPORTS'
    ExternalField = 'REPORT'
    ExternalSearchField = 'REPORT_NAME'
    StreamFormat = rpStreamText
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    ChangeExpression = ''
    BeginPageExpression = ''
    ChangeExpression = ''
    SkipExpreV = ''
    SkipExpreH = ''
    SkipToPageExpre = ''
    BackExpression = ''
    Stream = {0000000000000000}
  end
  object TRpSection1: TRpSection
    Width = 10772
    Height = 1871
    SubReport = TRpSubReport0
    GroupName = 'DATOS'
    ChangeBool = False
    PageRepeat = True
    SkipPage = True
    AlignBottom = False
    SectionType = rpsecgheader
    Components = <
      item
        Component = TRpExpression0
      end
      item
        Component = TRpExpression1
      end
      item
        Component = TRpExpression2
      end
      item
        Component = TRpExpression3
      end
      item
        Component = TRpExpression4
      end
      item
        Component = TRpExpression5
      end
      item
        Component = TRpShape0
      end
      item
        Component = TRpImage0
      end>
    ExternalTable = 'REPMAN_REPORTS'
    ExternalField = 'REPORT'
    ExternalSearchField = 'REPORT_NAME'
    StreamFormat = rpStreamText
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    ChangeExpression = ''
    BeginPageExpression = ''
    ChangeExpression = ''
    SkipExpreV = ''
    SkipExpreH = ''
    SkipToPageExpre = ''
    BackExpression = ''
    Stream = {0000000000000000}
  end
  object TRpSection2: TRpSection
    Width = 10772
    Height = 855
    SubReport = TRpSubReport0
    GroupName = 'DATOS'
    ChangeBool = True
    PageRepeat = False
    SkipPage = False
    AlignBottom = True
    SectionType = rpsecgfooter
    Components = <
      item
        Component = TRpExpression8
      end
      item
        Component = TRpExpression9
      end
      item
        Component = TRpExpression11
      end
      item
        Component = TRpExpression13
      end
      item
        Component = TRpShape1
      end
      item
        Component = TRpExpression6
      end
      item
        Component = TRpExpression15
      end
      item
        Component = TRpExpression10
      end>
    AutoExpand = True
    AutoContract = True
    VertDesp = True
    ExternalTable = 'REPMAN_REPORTS'
    ExternalField = 'REPORT'
    ExternalSearchField = 'REPORT_NAME'
    StreamFormat = rpStreamText
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    ChangeExpression = ''
    BeginPageExpression = ''
    ChangeExpression = ''
    SkipExpreV = ''
    SkipExpreH = ''
    SkipToPageExpre = ''
    BackExpression = ''
    Stream = {0000000000000000}
  end
  object TRpExpression0: TRpExpression
    Width = 11490
    Height = 285
    PosX = 0
    PosY = 60
    Type1Font = poHelvetica
    FontSize = 14
    FontStyle = 1
    Alignment = 4
    VAlignment = 32
    SingleLine = True
    BidiModes.Strings = (
      'BidiNo')
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'TRIM(EMPRESA.NOMBRE)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression1: TRpExpression
    Width = 11490
    Height = 330
    PosX = 0
    PosY = 345
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'trim(EMPRESA.ESPECIALIDAD)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression2: TRpExpression
    Width = 11490
    Height = 225
    PosX = 0
    PosY = 975
    Type1Font = poHelvetica
    FontSize = 9
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      'trim(empresa.calle) + '#39'   '#39' + TRIM(EMPRESA.PROVINCIA) +   IIF (T' +
      'RIM(EMPRESA.correoe)>'#39#39','#39'  Email : '#39' +  TRIM(EMPRESA.correoe) ,'#39 +
      #39')'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression3: TRpExpression
    Width = 10755
    Height = 240
    PosX = 0
    PosY = 1260
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      'IIF( trim(empresa.telefonodr)>'#39#39', '#39'TEL.Dr.  '#39' +  trim(empresa.te' +
      'lefonodr), '#39#39'  ) +  '#13#10'IIF( trim(empresa.celulardr)>'#39#39', '#39'  CEL.Dr' +
      '.  '#39' +  trim(empresa.celulardr), '#39#39'   ) + '#13#10'IIF( trim(empresa.te' +
      'lefono)>'#39#39', '#39'  TEL.Cl'#237'nica.  '#39' +  trim(empresa.telefono) ,'#39#39'   )' +
      ' + '#13#10'IIF( trim(empresa.fax)>'#39#39',         '#39'  WhatsApp.   '#39' +  trim' +
      '(empresa.fax), '#39#39'   )'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression5: TRpExpression
    Width = 4125
    Height = 270
    PosX = 6435
    PosY = 1590
    Type1Font = poHelvetica
    Alignment = 2
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'DETALLE.FECHA_CAP'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpShape0: TRpShape
    Width = 11505
    Height = 75
    PosX = 0
    PosY = 1878
    Align = rpalleftright
    Shape = rpsHorzLine
    PenWidth = 0
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
  end
  object TRpLabel0: TRpLabel
    Width = 570
    Height = 450
    PosX = 0
    PosY = 0
    Type1Font = poHelvetica
    FontSize = 16
    FontStyle = 1
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    WideText = 'Rx'
  end
  object TRpExpression7: TRpExpression
    Width = 10005
    Height = 218
    PosX = 690
    PosY = 0
    Type1Font = poHelvetica
    FontSize = 9
    WordWrap = True
    MultiPage = True
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'trim(DETALLE.DESCRIPCION)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression8: TRpExpression
    Width = 5700
    Height = 225
    PosX = 45
    PosY = 0
    Type1Font = poHelvetica
    FontSize = 9
    CutText = True
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'trim(DETALLE.NOMBRES) + trim(DETALLE.APELLIDOS)>'#39#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'trim(DETALLE.NOMBRES)+'#39' '#39'+ trim(DETALLE.APELLIDOS)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression9: TRpExpression
    Width = 1950
    Height = 232
    PosX = 5920
    PosY = 9
    Type1Font = poHelvetica
    FontSize = 9
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'detalle.SINCEDULA='#39'1'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      #39'Cedula : '#39'+SUBSTR(Detalle.CEDULA,1,3)+ '#39'-'#39' +SUBSTR(Detalle.CEDU' +
      'LA,4,7)+ '#39'-'#39' + SUBSTR(Detalle.CEDULA,11,1)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression11: TRpExpression
    Width = 1665
    Height = 232
    PosX = 8970
    PosY = 9
    Type1Font = poHelvetica
    FontSize = 9
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'trim(DETALLE.NSS)>'#39#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = #39'NSS : '#39'+trim(DETALLE.NSS)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression13: TRpExpression
    Width = 5745
    Height = 240
    PosX = 51
    PosY = 252
    Type1Font = poHelvetica
    FontSize = 9
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = ''
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 'IIF( TRIM(DETALLE.NOMBRE)=='#39'NO ASEGURADO'#39','#39' '#39',DETALLE.NOMBRE)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpShape1: TRpShape
    Width = 11505
    Height = 40
    PosX = 0
    PosY = 0
    Align = rpalleftright
    Shape = rpsHorzLine
    PenWidth = 0
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
  end
  object TRpExpression6: TRpExpression
    Width = 1605
    Height = 225
    PosX = 8970
    PosY = 225
    Type1Font = poHelvetica
    FontSize = 9
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'trim(AFILIADO)>'#39#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = #39'No.AF: '#39'+trim(AFILIADO)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression4: TRpExpression
    Width = 11490
    Height = 345
    PosX = 0
    PosY = 630
    Type1Font = poHelvetica
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'CONFIG.IMPRIMENCABEZADO=='#39'S'#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      'IIF(  trim(EMPRESA.EXECQUATUR)>'#39#39','#39'Exequatur  '#39' + trim(EMPRESA.E' +
      'XECQUATUR)  ,'#39#39'   )+'#13#10'IIF(  trim(EMPRESA.COLEGIACION)>'#39#39','#39'  Cole' +
      'giacion  '#39' + trim(EMPRESA.COLEGIACION) ,'#39#39'    )+'#13#10'IIF(  trim(EMP' +
      'RESA.CIDC)>'#39#39','#39'  Num.CIDC   '#39' + trim(EMPRESA.CIDC) ,'#39#39'    )'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression10: TRpExpression
    Width = 1956
    Height = 228
    PosX = 5920
    PosY = 252
    Type1Font = poHelvetica
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'trim(detalle.RH)<>'#39#39
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = #39'Tipificacion : '#39' + trim(detalle.RH)'
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpExpression15: TRpExpression
    Width = 10575
    Height = 225
    PosX = 120
    PosY = 570
    Type1Font = poHelvetica
    FontSize = 9
    Alignment = 4
    VAlignment = 32
    AutoExpand = False
    AutoContract = False
    ExportPosition = 0
    ExportSize = 1
    ExportDoNewLine = False
    PrintCondition = 'fechacon>today'
    DoBeforePrint = ''
    DoAfterPrint = ''
    WFontName = 'Arial'
    LFontName = 'Helvetica'
    Expression = 
      #39'El d'#237'a '#39' + FORMATSTR('#39'dddddd'#39',CITA.FECHACON) +'#39' a las : '#39' +trim' +
      ' (cita.HORACON) + '#39'Horas Tiene Cita en mi consultorio'#39
    DisplayFormat = ''
    ExportDisplayFormat = ''
    AgIniValue = '0'
    ExportExpression = ''
  end
  object TRpImage0: TRpImage
    Width = 1260
    Height = 915
    PosX = 0
    PosY = 0
    DrawStyle = rpDrawStretch
    CachedImage = rpCachedVariable
    PrintCondition = 
      'CONFIG.IMPRIMENCABEZADO=='#39'S'#39' and '#13#10'FILEEXISTS(CONFIG.PATHCONFIG ' +
      '+ '#39'reportes\homs.bmp'#39')'
    DoBeforePrint = ''
    DoAfterPrint = ''
    Expression = 'CONFIG.PATHCONFIG+'#39'REPORTES/HOMS.BMP'#39
    Stream = {0000000000000000}
  end
end
