<?php
header('Content-Type: application/json; charset=UTF-8');
ini_set('display_errors', 1);
error_reporting(E_ALL);

try {
    $pdo = include '../config/database.php';
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data || !isset($data[1])) {
        echo json_encode(['status' => 'error', 'message' => 'Datos de factura no recibidos correctamente']);
        exit;
    }

    $factura = $data[1];

    // Validaciones básicas
    if (empty($factura['clavepac']) || empty($factura['concepto']) || empty($factura['precio'])) {
        echo json_encode(['status' => 'error', 'message' => 'Faltan datos requeridos']);
        exit;
    }

    // Buscar el próximo número de factura
    $stmtMax = $pdo->query("SELECT MAX(NUMFACT) AS maxnum FROM FACTURAS");
    $rowMax = $stmtMax->fetch();
    $numfact = $rowMax['maxnum'] ? $rowMax['maxnum'] + 1 : 1001;

    // Calcular campos adicionales
    $nopago = $factura['precio'] - $factura['pagado'];
    $fecha = date('Y-m-d');
    $hora = date('H:i:s');

    $stmt = $pdo->prepare("
        INSERT INTO FACTURAS (
            CLAVEPAC, NUMFACT, FARS, PRECIO, CONCEPTO, FECHAPAGO, MODOPGO,
            ESTATUS, PAGADO, NOPAGO, PLAZO, INTERES, NOAUTORIZACION,
            VALORRECLAMADO, PROCEDIMIENTOS, STRECLAMA, FECHA, HORA, USUARIO, CONSULTORIO
        ) VALUES (
            :clavepac, :numfact, :fars, :precio, :concepto, :fechapago, :modopgo,
            :estatus, :pagado, :nopago, :plazo, :interes, :noaut,
            :valorreclamado, :procedimiento, :streclama, :fecha, :hora, :usuario, :consultorio
        )
    ");

    $stmt->execute([
        ':clavepac'       => $factura['clavepac'],
        ':numfact'        => $numfact,
        ':fars'           => $factura['fars'] ?? null,
        ':precio'         => $factura['precio'],
        ':concepto'       => $factura['concepto'],
        ':fechapago'      => $factura['fechapago'] ?? null,
        ':modopgo'        => $factura['modopgo'] ?? '',
        ':estatus'        => $factura['estatus'] ?? 'A',
        ':pagado'         => $factura['pagado'],
        ':nopago'         => $nopago,
        ':plazo'          => $factura['plazo'] ?? 0,
        ':interes'        => $factura['interes'] ?? 0,
        ':noaut'          => $factura['noautorizacion'] ?? '',
        ':valorreclamado' => $factura['valorreclamado'] ?? 0,
        ':procedimiento'  => $factura['procedimiento'] ?? '',
        ':streclama'      => $factura['streclama'] ?? 'A',
        ':fecha'          => $fecha,
        ':hora'           => $hora,
        ':usuario'        => $factura['usuario'] ?? 1,
        ':consultorio'    => $factura['consultorio'] ?? 1
    ]);

    echo json_encode([
        'status' => 'success',
        'message' => 'Factura guardada correctamente',
        'numfact' => $numfact
    ]);
} catch (PDOException $e) {
    echo json_encode([
        'status' => 'error',
        'message' => '❌ Error al guardar factura: ' . $e->getMessage()
    ]);
}
