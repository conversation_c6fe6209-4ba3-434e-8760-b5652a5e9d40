<?php
// examen_fisico.php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA']   ?? ''; // Asumiendo que CEDULA es la clave para buscar al paciente
$CLAVE    = $_GET['CLAVE']    ?? null;
$nuevo    = isset($_GET['nuevo']);
$mensaje  = '';
$registros = [];
$seleccionado = null;

// 1) Eliminar examen por POST seguro
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && !empty($_POST['CLAVE_ELIMINAR'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM EXAMENFISICO WHERE CLAVE = ?");
        $stmt->execute([$_POST['CLAVE_ELIMINAR']]);
        $mensaje = "✅ Registro eliminado correctamente.";
        header("Location: examen_fisico.php?CLAVEPAC=" . urlencode($CLAVEPAC) . "&CEDULA=" . urlencode($CEDULA));
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar: " . $e->getMessage();
    }
}

// 2) Cargar registros
// Siempre buscar la CLAVEPAC correcta del paciente en la tabla PACIENTES
// usando la CEDULA enviada, para asegurar la integridad referencial antes de cargar los registros.
$clav_pac_real = $CLAVEPAC; // Valor por defecto si no se encuentra o no hay CEDULA

if (!empty($CEDULA)) {
    try {
        $stmtPaciente = $pdo->prepare("SELECT CLAVE FROM PACIENTES WHERE CEDULA = :cedula");
        $stmtPaciente->execute(['cedula' => $CEDULA]);
        $rowPaciente = $stmtPaciente->fetch(PDO::FETCH_ASSOC);

        if ($rowPaciente) {
            $clav_pac_real = $rowPaciente['CLAVE']; // Usa la CLAVEPAC real del paciente
        } else {
            $mensaje = "⚠️ Advertencia: Paciente con CÉDULA: " . htmlspecialchars($CEDULA) . " no encontrado en PACIENTES. Usando CLAVEPAC original.";
        }
    } catch (PDOException $e) {
        $mensaje = "❌ Error al buscar paciente en PACIENTES: " . $e->getMessage();
    }
}

if ($clav_pac_real) { // Usamos la CLAVEPAC real o la original si no se encontró
    $stmt = $pdo->prepare("SELECT * FROM EXAMENFISICO WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHA_CAP DESC");
    $stmt->execute(['CLAVEPAC' => $clav_pac_real]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 3) Seleccionar registro actual o nuevo
if ($nuevo) {
    $seleccionado = []; // Inicializar como array vacío para nuevo registro
    $CLAVE = null; // Asegurar CLAVE es null para nueva inserción
} elseif ($CLAVE) {
    $stmt = $pdo->prepare("SELECT * FROM EXAMENFISICO WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $seleccionado = $stmt->fetch(PDO::FETCH_ASSOC);
} elseif (!empty($registros)) {
    $seleccionado = $registros[0];
    $CLAVE = $seleccionado['CLAVE']; // Establecer CLAVE si se carga el primer registro
}


// 4) Guardar o actualizar examen
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['accion']) && $_POST['accion'] === 'guardar') {
    $esActualizacion = !empty($_POST['CLAVE']);
    $campos = ['PESO','TALLA','TAS','TAD','FR','FC','PULSO','TEMP','OTROS','HALLAZGOS'];
    $data_form = []; // Datos recibidos del formulario
    foreach ($campos as $c) {
        $data_form[$c] = $_POST[$c] ?: null;
    }

    try {
        if ($esActualizacion) {
            $updateParams = []; // Solo los parámetros para la actualización
            $sets = [];
            foreach ($campos as $c) {
                $sets[] = "$c = :$c";
                $updateParams[$c] = $data_form[$c];
            }
            $updateParams['CLAVE'] = $_POST['CLAVE'];
            
            // Si SINCRONIZADO es una columna que se debe actualizar, inclúyela aquí.
            // Actualmente se actualiza con un literal '0', lo cual es correcto si no es un placeholder.
            $sql = "UPDATE EXAMENFISICO SET " . implode(', ', $sets) . ", SINCRONIZADO = 0 WHERE CLAVE = :CLAVE";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($updateParams); // Usar $updateParams aquí
            $newId = $_POST['CLAVE']; // En actualización, el ID no cambia
            $mensaje = "✅ Examen físico actualizado correctamente.";

        } else { // Es una inserción
            $insertParams = $data_form; // Copia los datos del formulario
            $insertParams['CLAVEPAC'] = $clav_pac_real; // Añade la CLAVEPAC real
            $insertParams['CEDULA'] = $CEDULA; // Añade la CEDULA
            $insertParams['FECHA_CAP'] = date('Y-m-d H:i:s'); // O CURRENT_TIMESTAMP en la SQL
            $insertParams['SINCRONIZADO'] = 0; // Añade SINCRONIZADO

            $keys = array_keys($insertParams);
            $cols = implode(', ', $keys);
            $phs  = ':' . implode(', :', $keys);
            
            // Asegúrate de que FECHA_CAP se maneje si es necesario
            $sql = "INSERT INTO EXAMENFISICO ($cols) VALUES ($phs)";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($insertParams); // Usar $insertParams aquí
            $newId = $pdo->lastInsertId();
            $mensaje = "✅ Nuevo examen físico guardado correctamente.";
        }
        
        // Redirige usando la CLAVEPAC real y la CEDULA original
        header("Location: examen_fisico.php?CLAVEPAC=" . urlencode($CLAVEPAC) . "&CEDULA=" . urlencode($CEDULA) . "&CLAVE=" . urlencode($newId));
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar el examen físico: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Examen Físico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .form-label {
            font-weight: bold;
        }
        .card-header h5 {
            margin-bottom: 0;
        }
    </style>
</head>
<body class="p-4 bg-light">
    <div class="row">
        <div class="col-md-3 border-end">
            <h5 class="mb-3 text-primary">FECHAS DE EXÁMENES</h5>
            <ul class="list-group">
                <?php if (!empty($registros)): ?>
                    <?php foreach ($registros as $r): ?>
                        <?php $active = (isset($seleccionado['CLAVE']) && $seleccionado['CLAVE'] == $r['CLAVE']); ?>
                        <li class="list-group-item <?= $active ? 'active' : '' ?>">
                            <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($r['CLAVE']) ?>"
                               class="text-decoration-none <?= $active ? 'text-white' : '' ?>">
                                <?= htmlspecialchars($r['FECHA_CAP']) ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                <?php else: ?>
                    <li class="list-group-item text-muted">No hay exámenes registrados.</li>
                <?php endif; ?>
                <li class="list-group-item mt-3">
                    <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&nuevo=1" class="btn btn-sm btn-primary w-100">➕ Nuevo Examen</a>
                </li>
            </ul>
        </div>

        <div class="col-md-9">
            <h4 class="mb-4 text-center">Detalles del Examen Físico</h4>
            <?php if ($mensaje): ?><div class="alert alert-info text-center"><?= htmlspecialchars($mensaje) ?></div><?php endif; ?>

            <form method="post" novalidate>
                <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                <input type="hidden" name="CEDULA"   value="<?= htmlspecialchars($CEDULA) ?>">
                <?php if (!empty($seleccionado['CLAVE'])): ?>
                    <input type="hidden" name="CLAVE"     value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                <?php endif; ?>

                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Medidas Antropométricas y Signos Vitales</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php 
                            $medidas = [
                                'PESO'=>'Peso (lb)', 'TALLA'=>'Talla (cm)', 'TAS'=>'TA-S (mmHg)', 
                                'TAD'=>'TA-D (mmHg)', 'FR'=>'FR (resp/min)', 'FC'=>'FC (lat/min)', 
                                'PULSO'=>'Pulso', 'TEMP'=>'Temp (°C)'
                            ];
                            foreach ($medidas as $c=>$lbl): ?>
                                <div class="col-md-4 mb-3">
                                    <label for="<?= $c ?>" class="form-label fw-bold"><?= $lbl ?></label>
                                    <input type="text" name="<?= $c ?>" id="<?= $c ?>"
                                           class="form-control form-control-sm decimal-input"
                                           value="<?= htmlspecialchars($seleccionado[$c] ?? '') ?>">
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Observaciones y Hallazgos Relevantes</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="OTROS" class="form-label fw-bold">Otros</label>
                            <input type="text" name="OTROS" id="OTROS" 
                                   class="form-control form-control-sm" maxlength="500"
                                   value="<?= htmlspecialchars($seleccionado['OTROS'] ?? '') ?>">
                        </div>
                        <div class="mb-3">
                            <label for="HALLAZGOS" class="form-label fw-bold">Hallazgos (Detalle)</label>
                            <textarea name="HALLAZGOS" id="HALLAZGOS" 
                                     class="form-control" rows="4" maxlength="700"><?= htmlspecialchars($seleccionado['HALLAZGOS'] ?? '') ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2 mt-4">
                    <button type="submit" name="accion" value="guardar" class="btn btn-success btn-lg">💾 Guardar</button>
                    <?php if (!empty($seleccionado['CLAVE']) && !$nuevo): // Botón Eliminar solo si es un registro existente y no es un "nuevo" formulario ?>
                        <button type="submit" name="eliminar" value="1" class="btn btn-danger btn-lg" onclick="return confirm('¿Deseas eliminar este examen físico de forma permanente?');">🗑️ Eliminar</button>
                        <button type="button" class="btn btn-info btn-lg" onclick="window.print()">🖨️ Imprimir</button>
                    <?php endif; ?>
                    <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg ms-auto">Volver</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const decimalInputs = document.querySelectorAll('.decimal-input');
            const form = document.querySelector('form');

            // Función para formatear el input decimal (ej. 123.45)
            function formatearDecimal(input) {
                let val = input.value.replace(/[^0-9.]/g, ''); // Elimina todo excepto números y puntos
                const parts = val.split('.');
                if (parts.length > 2) { // Si hay más de un punto, elimina los extras
                    val = parts[0] + '.' + parts.slice(1).join('');
                }
                
                const [intPart, decPart] = val.split('.');
                let formattedInt = intPart.substring(0, 3); // Limita a 3 dígitos enteros
                let formattedDec = decPart ? decPart.substring(0, 2) : ''; // Limita a 2 dígitos decimales

                if (decPart !== undefined) {
                    input.value = formattedInt + '.' + formattedDec;
                } else {
                    input.value = formattedInt;
                }
            }

            // Función para validar el formato al enviar el formulario
            function validarFormulario(event) {
                const regex = /^\d{1,3}(\.\d{1,2})?$/;
                let isValid = true;
                decimalInputs.forEach(input => {
                    const value = input.value.trim();
                    if (value !== '' && !regex.test(value)) {
                        input.setCustomValidity('Formato inválido. Use hasta 3 enteros y 2 decimales (ej. 123.45).');
                        input.reportValidity();
                        isValid = false;
                    } else {
                        input.setCustomValidity(''); // Campo válido
                    }
                });

                if (!isValid) {
                    event.preventDefault(); // Detiene el envío si hay campos inválidos
                }
            }

            // Inicializar formateo y añadir listeners
            decimalInputs.forEach(input => {
                input.setAttribute('maxlength', '6'); // Max 3 enteros + 1 punto + 2 decimales = 6
                input.addEventListener('input', () => formatearDecimal(input));
                formatearDecimal(input); // Formatea el valor inicial si ya existe
            });

            form.addEventListener('submit', validarFormulario);
        });
    </script>
</body>
</html>