<?php
$pdo = require_once __DIR__ . '/../config/database.php';

$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA = $_GET['CEDULA'] ?? '';
$CLAVE = $_GET['CLAVE'] ?? null;
$tabla = strtolower($_GET['TABLA'] ?? $_GET['tabla'] ?? ''); // Preferir TABLA, sino tabla
$nuevo = isset($_GET['nuevo']);
$mensaje = '';
$datos = null;
$registros = [];

// Configuración de tablas
$tablas = [
    'complementarias'   => ['campo' => 'COMPLEMENTARIOS', 'max' => 2500, 'tabla_real' => 'COMPLEMENTARIAS', 'titulo' => 'Información Complementaria'],
    'diagnostico'       => ['campo' => 'TEXTO', 'max' => 1400, 'tabla_real' => 'DIAGNOSTICO', 'titulo' => 'Diagnóstico'],
    'seguimiento'       => ['campo' => 'SEGUIMIENTO', 'max' => 6000, 'tabla_real' => 'SEGUIMIENTO', 'titulo' => 'Seguimiento del Paciente'],
    'tratamientos'      => ['campo' => 'TRATAMIENTO', 'max' => 1400, 'tabla_real' => 'TRATAMIENTOS', 'titulo' => 'Tratamientos'],
    'estudios'          => ['campo' => 'DESCRIPCION', 'max' => 3000, 'tabla_real' => 'ESTUDIOS', 'titulo' => 'Estudios Realizados'],
    'informes'          => ['campo' => 'INFORME', 'max' => 3500, 'tabla_real' => 'INFORMES', 'titulo' => 'Informes Médicos'],
    'prescripcion'      => ['campo' => 'DESCRIPCION', 'max' => 3000, 'tabla_real' => 'PRESCRIPCION', 'titulo' => 'Prescripción'],
    'enfermedad_actual' => ['campo' => 'TEXTO', 'max' => 2400, 'tabla_real' => 'ENFERMEDAD_ACTUAL', 'titulo' => 'Enfermedad Actual']
];

if (!isset($tablas[$tabla])) {
    exit("❌ Tabla no reconocida.");
}

$tablaConfig = $tablas[$tabla];
$tablaReal = $tablaConfig['tabla_real'];
$campoTexto = $tablaConfig['campo'];
$maxLength = $tablaConfig['max'];
$tituloModulo = $tablaConfig['titulo'];

// --- Lógica de Robustez: Resolver CLAVEPAC usando CEDULA ---
$clav_pac_real = $CLAVEPAC; // Valor por defecto si no se encuentra o no hay CEDULA

if (!empty($CEDULA)) {
    try {
        $stmtPaciente = $pdo->prepare("SELECT CLAVE FROM PACIENTES WHERE CEDULA = :cedula");
        $stmtPaciente->execute(['cedula' => $CEDULA]);
        $rowPaciente = $stmtPaciente->fetch(PDO::FETCH_ASSOC);

        if ($rowPaciente) {
            $clav_pac_real = $rowPaciente['CLAVE']; // Asigna la CLAVEPAC encontrada en remoto
        } else {
            $mensaje = "⚠️ Advertencia: Paciente con CÉDULA: " . htmlspecialchars($CEDULA) . " no encontrado en PACIENTES. Usando CLAVEPAC original.";
        }
    } catch (PDOException $e) {
        $mensaje = "❌ Error al buscar paciente en PACIENTES: " . $e->getMessage();
    }
}
// --- FIN Lógica de Robustez CLAVEPAC ---


// 1) Eliminar registro
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && isset($_POST['CLAVE_ELIMINAR'])) {
    $claveEliminar = $_POST['CLAVE_ELIMINAR'];
    try {
        $stmt = $pdo->prepare("DELETE FROM {$tablaReal} WHERE CLAVE = ?");
        $stmt->execute([$claveEliminar]);
        $mensaje = "✅ Registro eliminado correctamente.";
        // Redirigir para refrescar la página, sin la CLAVE eliminada
        header("Location: modulo_texto_simple.php?CLAVEPAC=" . urlencode($CLAVEPAC) . "&CEDULA=" . urlencode($CEDULA) . "&TABLA=" . urlencode($tabla));
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar: " . $e->getMessage();
    }
}

// 2) Obtener todos los registros del paciente para la tabla actual
if ($clav_pac_real) {
    $stmt = $pdo->prepare("SELECT CLAVE, FECHA_CAP FROM {$tablaReal} WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHA_CAP DESC");
    $stmt->execute(['CLAVEPAC' => $clav_pac_real]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    
    
  // ——————> Aquí, si no nos pasaron CLAVE en la URL, redirigimos al primero:
    if (!$nuevo && empty($_GET['CLAVE']) && count($registros) > 0) {
        header(
            "Location: modulo_texto_simple.php"
          . "?CLAVEPAC=" . urlencode($CLAVEPAC)
          . "&CEDULA="  . urlencode($CEDULA)
          . "&TABLA="   . urlencode($tabla)
          . "&CLAVE="   . urlencode($registros[0]['CLAVE'])
        );
        exit;
    }
  
}


// 3) Lógica para cargar un registro existente o preparar uno nuevo
if ($nuevo) {
    $datos = []; // Crear un array vacío para un nuevo registro
    $CLAVE = null; // Asegurarse de que CLAVE sea null para la inserción
} elseif ($CLAVE) {
    $stmt = $pdo->prepare("SELECT * FROM {$tablaReal} WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $datos = $stmt->fetch(PDO::FETCH_ASSOC);
} elseif (!empty($registros)) { // Si no hay CLAVE y no es nuevo, carga el primer registro
    $datos = $registros[0];
    $CLAVE = $datos['CLAVE'];
}


// 4) Guardar o actualizar registro
// DEBUG TEMPORAL - Solo logs (sin output visual)
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("=== DEBUG POST ===");
    error_log("POST completo: " . print_r($_POST, true));
    error_log("REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD']);
    error_log("guardar_accion isset: " . (isset($_POST['guardar_accion']) ? 'SI' : 'NO'));
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['guardar_accion'])) {
    // Debug: Log de intento de guardado
    error_log("=== INTENTO DE GUARDADO ===");
    error_log("Tabla: " . $tabla);
    error_log("Campo: " . $campoTexto);
    error_log("CLAVEPAC: " . $CLAVEPAC);
    error_log("CEDULA: " . $CEDULA);
    error_log("POST CLAVE: " . ($_POST['CLAVE'] ?? 'NO_DEFINIDO'));

    $texto = trim($_POST[$campoTexto] ?? '');
    error_log("Texto a guardar: " . substr($texto, 0, 100) . "...");

    if ($texto === '') {
        $mensaje = '❌ Debe ingresar información en el campo de texto.';
        error_log("ERROR: Texto vacío");
    } else {
        try {
            if (!empty($_POST['CLAVE'])) { // Es una actualización
                error_log("Ejecutando UPDATE para CLAVE: " . $_POST['CLAVE']);
                $stmt = $pdo->prepare("UPDATE {$tablaReal} SET {$campoTexto} = :texto, SINCRONIZADO = 0 WHERE CLAVE = :CLAVE");
                $stmt->execute([
                    'texto' => $texto,
                    'CLAVE' => $_POST['CLAVE']
                ]);
                $CLAVE = $_POST['CLAVE'];
                $mensaje = "✅ Registro actualizado correctamente.";
                error_log("UPDATE exitoso. Filas afectadas: " . $stmt->rowCount());
            } else { // Es una nueva inserción
                error_log("Ejecutando INSERT nuevo registro");
                $stmt = $pdo->prepare("INSERT INTO {$tablaReal} (CLAVEPAC, CEDULA, FECHA_CAP, {$campoTexto}, SINCRONIZADO) VALUES (:CLAVEPAC, :CEDULA, CURRENT_TIMESTAMP, :texto, 0)");
                $stmt->execute([
                    'CLAVEPAC' => $CLAVEPAC,
                    'CEDULA' => $CEDULA,
                    'texto' => $texto
                ]);
                $CLAVE = $pdo->lastInsertId();
                $mensaje = "✅ Registro insertado correctamente.";
                error_log("INSERT exitoso. Nuevo ID: " . $CLAVE);
            }
            // Redirigir para cargar el registro recién guardado/actualizado y limpiar el POST
            header("Location: modulo_texto_simple.php?CLAVEPAC=" . urlencode($CLAVEPAC) . "&CEDULA=" . urlencode($CEDULA) . "&TABLA=" . urlencode($tabla) . "&CLAVE=" . urlencode($CLAVE));
            exit;
        } catch (PDOException $e) {
            $mensaje = "❌ Error al guardar: " . $e->getMessage();
            error_log("ERROR PDO: " . $e->getMessage());
            error_log("SQL State: " . $e->getCode());
        }
    }
}


?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title><?= htmlspecialchars($tituloModulo) ?> – Paciente (<?= htmlspecialchars($CLAVEPAC) ?>)</title>
    <link rel="icon" href="data:,"
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .form-label { font-weight: bold; }
        .card-header h5 { margin-bottom: 0; }

        /* Estilos médicos profesionales */
        :root {
            --medical-primary: #1e40af;
            --medical-secondary: #3b82f6;
            --medical-success: #10b981;
            --medical-info: #0ea5e9;
            --medical-warning: #f59e0b;
            --medical-danger: #ef4444;
        }

        .medical-header {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.2);
        }

        .medical-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .medical-card .card-header {
            background: linear-gradient(135deg, var(--medical-success) 0%, #34d399 100%);
            border: none;
            padding: 1rem 1.5rem;
        }

        .medical-sidebar {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(30, 64, 175, 0.1);
        }

        .medical-sidebar h5 {
            color: var(--medical-primary);
            font-weight: 700;
            margin-bottom: 1.5rem;
            text-align: center;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--medical-primary);
        }

        .list-group-item {
            border: 1px solid rgba(30, 64, 175, 0.1);
            margin-bottom: 0.5rem;
            border-radius: 8px !important;
            transition: all 0.3s ease;
        }

        .list-group-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
        }

        .list-group-item.active {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            border-color: var(--medical-primary);
            transform: translateX(5px);
            box-shadow: 0 6px 20px rgba(30, 64, 175, 0.3);
        }

        .btn-medical-primary {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-medical-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
            color: white;
        }

        .btn-medical-success {
            background: linear-gradient(135deg, var(--medical-success) 0%, #34d399 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-medical-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            color: white;
        }

        .btn-medical-danger {
            background: linear-gradient(135deg, var(--medical-danger) 0%, #f87171 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .btn-medical-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
            color: white;
        }

        .form-control {
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--medical-primary);
            box-shadow: 0 0 0 0.2rem rgba(30, 64, 175, 0.25);
        }

        textarea.form-control {
            min-height: 200px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
        }

        /* Animaciones */
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .medical-card {
            animation: slideInRight 0.5s ease-out;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .medical-header {
                padding: 1rem;
                text-align: center;
            }

            .medical-sidebar {
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body class="bg-light" style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;">
<div class="container-fluid p-4">
    <!-- Header médico -->
    <div class="medical-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h3 class="mb-1">
                    <i class="fas fa-file-medical-alt me-2"></i>
                    <?= htmlspecialchars($tituloModulo) ?>
                </h3>
                <p class="mb-0 opacity-75">
                    <i class="fas fa-user me-1"></i>
                    Paciente: <?= htmlspecialchars($CLAVEPAC) ?> |
                    <i class="fas fa-id-card me-1"></i>
                    CI: <?= htmlspecialchars($CEDULA) ?>
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex align-items-center justify-content-end">
                    <i class="fas fa-calendar-alt me-2"></i>
                    <span><?= date('d/m/Y H:i') ?></span>
                </div>
            </div>
        </div>
    </div>

<div class="row">
    <!-- Menú lateral -->
    <div class="col-md-3">
        <div class="medical-sidebar">
            <h5>
                <i class="fas fa-history me-2"></i>
                HISTORIAL DE <?= mb_strtoupper(str_replace('_', ' ', $tabla)) ?>
            </h5>
            <ul class="list-group list-group-flush">
                <?php if (!empty($registros)): ?>
                    <?php foreach ($registros as $index => $r): ?>
                        <?php $active = isset($CLAVE) && $CLAVE == $r['CLAVE']; ?>
                        <li class="list-group-item <?= $active ? 'active' : '' ?>">
                            <a href="?CLAVEPAC=<?= urlencode($CLAVEPAC) ?>&CEDULA=<?= urlencode($CEDULA) ?>&TABLA=<?= urlencode($tabla) ?>&CLAVE=<?= urlencode($r['CLAVE']) ?>"
                               class="text-decoration-none <?= $active ? 'text-white' : 'text-dark' ?> d-block">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-file-alt <?= $active ? 'text-white' : 'text-primary' ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold">Registro #<?= $index + 1 ?></div>
                                        <small class="<?= $active ? 'text-white-50' : 'text-muted' ?>">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?= date('d/m/Y H:i', strtotime($r['FECHA_CAP'])) ?>
                                        </small>
                                    </div>
                                    <?php if ($active): ?>
                                        <i class="fas fa-chevron-right text-white-50"></i>
                                    <?php endif; ?>
                                </div>
                            </a>
                        </li>
                    <?php endforeach; ?>
                <?php else: ?>
                    <li class="list-group-item text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <div class="text-muted">
                            <strong>No hay registros</strong><br>
                            <small>No hay registros para <?= htmlspecialchars($tituloModulo) ?></small>
                        </div>
                    </li>
                <?php endif; ?>
            </ul>

            <div class="mt-3">
                <a href="?CLAVEPAC=<?= urlencode($CLAVEPAC) ?>&CEDULA=<?= urlencode($CEDULA) ?>&TABLA=<?= urlencode($tabla) ?>&nuevo=1"
                   class="btn btn-primary w-100">
                    <i class="fas fa-plus me-2"></i>Nuevo Registro
                </a>
            </div>
        </div>
    </div>

    <!-- Contenido principal -->
    <div class="col-md-9">
        <?php if ($mensaje): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <?= htmlspecialchars($mensaje) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Un solo formulario para guardar y eliminar -->
       <!-- Un solo formulario para guardar y eliminar -->
        <form method="post" novalidate>
            <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
            <input type="hidden" name="CEDULA"   value="<?= htmlspecialchars($CEDULA) ?>">
            <input type="hidden" name="TABLA"    value="<?= htmlspecialchars($tabla) ?>">
            <?php if (!empty($CLAVE) && !$nuevo): ?>
                <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($CLAVE) ?>">
            <?php endif; ?>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Contenido del Registro</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="<?= htmlspecialchars($campoTexto) ?>" class="form-label">Texto:</label>
                        <textarea
                            name="<?= htmlspecialchars($campoTexto) ?>"
                            id="<?= htmlspecialchars($campoTexto) ?>"
                            class="form-control"
                            rows="10"
                            maxlength="<?= htmlspecialchars($maxLength) ?>"
                        ><?= htmlspecialchars($datos[$campoTexto] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <div class="d-flex gap-2 mt-4 align-items-center">
                <!-- Guardar -->
                <input type="submit" name="guardar_accion" value="💾 Guardar" class="btn btn-success btn-lg">
                <!-- Nuevo -->
                <a href="?CLAVEPAC=<?= urlencode($CLAVEPAC) ?>&CEDULA=<?= urlencode($CEDULA) ?>&TABLA=<?= urlencode($tabla) ?>&nuevo=1"
                   class="btn btn-secondary btn-lg">
                    🆕 Nuevo
                </a>
                <!-- Imprimir (solo si hay registros) -->
                <?php if (!empty($registros)): ?>
                <button type="button" class="btn btn-info btn-lg" onclick="window.print()">  🖨️ Imprimir </button>
                <?php endif; ?>
               
                <!-- Separador para empujar Eliminar y Volver al final -->
                <div class="flex-grow-1"></div>
                <!-- Eliminar se movió fuera del formulario -->
                <!-- Volver -->
                <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg">
                    Volver
                </a>
            </div>
        </form>

        <!-- Formulario separado para eliminar -->
        <?php if (!empty($CLAVE) && !$nuevo): ?>
        <div class="mt-3">
            <form method="post" style="display: inline;" id="formEliminar">
                <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                <input type="hidden" name="CEDULA"   value="<?= htmlspecialchars($CEDULA) ?>">
                <input type="hidden" name="TABLA"    value="<?= htmlspecialchars($tabla) ?>">
               <input type="hidden" name="CLAVE_ELIMINAR" value="<?= htmlspecialchars($CLAVE) ?>">
                <button type="submit" name="eliminar" value="1" class="btn btn-danger btn-lg"
                        onclick="return confirm('¿Deseas eliminar este registro de forma permanente?');">
                    🗑️ Eliminar
                </button>
            </form>
        </div>
        <?php endif; ?>
    </div>
</div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<!-- Sistema de Impresión Médica -->
<script src="js/impresion_medica.js"></script>

<script>
// Contador de caracteres
document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.getElementById(<?= json_encode($campoTexto) ?>);
    const charCount = document.getElementById('charCount');
    const maxLength = <?= $maxLength ?>;

    // Verificar que los elementos existan antes de usarlos
    if (!textarea) {
        console.warn('Textarea no encontrado: ' + <?= json_encode($campoTexto) ?>);
        return;
    }

    function updateCharCount() {
        if (!textarea || !charCount) return; // Verificación de seguridad

        const currentLength = textarea.value.length;
        charCount.textContent = currentLength.toLocaleString();

        // Cambiar color según el porcentaje usado
        const percentage = (currentLength / maxLength) * 100;
        if (percentage > 90) {
            charCount.className = 'text-danger fw-bold';
        } else if (percentage > 75) {
            charCount.className = 'text-warning fw-bold';
        } else {
            charCount.className = 'text-success';
        }
    }

    // Actualizar contador al cargar y al escribir (solo si charCount existe)
    if (charCount) {
        updateCharCount();
        textarea.addEventListener('input', updateCharCount);
        textarea.addEventListener('paste', function() {
            setTimeout(updateCharCount, 10);
        });
    }

    // Auto-resize del textarea
    function autoResize() {
        if (!textarea) return; // Verificación de seguridad
        textarea.style.height = 'auto';
        textarea.style.height = Math.max(200, textarea.scrollHeight) + 'px';
    }

    if (textarea) {
        textarea.addEventListener('input', autoResize);
        autoResize();
    }

    // Guardar automático cada 30 segundos (opcional)
    let autoSaveTimer;
    let hasChanges = false;

    if (textarea) {
        textarea.addEventListener('input', function() {
            hasChanges = true;
            clearTimeout(autoSaveTimer);

            // Mostrar indicador de cambios no guardados
            const saveBtn = document.querySelector('button[name="guardar_accion"]');
            if (saveBtn && hasChanges) {
                saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>Guardar *';
            }
        });
    }

    // Resetear indicador al guardar
    const formGuardar = document.querySelector('form');
    if (formGuardar) {
        formGuardar.addEventListener('submit', function(e) {
            // Solo procesar si es el botón de guardar
            if (e.submitter && e.submitter.name === 'guardar_accion') {
                hasChanges = false;
                const saveBtn = document.querySelector('button[name="guardar_accion"]');
                if (saveBtn) {
                    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Guardando...';
                    saveBtn.disabled = true;
                }
            }
        });
    }

    // Advertencia al salir sin guardar
    window.addEventListener('beforeunload', function(e) {
        if (hasChanges) {
            e.preventDefault();
            e.returnValue = '¿Está seguro de que desea salir? Hay cambios sin guardar.';
            return e.returnValue;
        }
    });

    // Atajos de teclado
    document.addEventListener('keydown', function(e) {
        // Ctrl+S para guardar
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveBtn = document.querySelector('button[name="guardar_accion"]');
            if (saveBtn) {
                saveBtn.click();
            }
        }

        // Ctrl+N para nuevo
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const url = '?CLAVEPAC=' + encodeURIComponent(<?= json_encode($CLAVEPAC) ?>) +
                       '&CEDULA=' + encodeURIComponent(<?= json_encode($CEDULA) ?>) +
                       '&TABLA=' + encodeURIComponent(<?= json_encode($tabla) ?>) +
                       '&nuevo=1';
            window.location.href = url;
        }
    });

    // Mostrar información de atajos
    const shortcutsInfo = document.createElement('div');
    shortcutsInfo.className = 'position-fixed bottom-0 end-0 p-3 text-muted small';
    shortcutsInfo.style.cssText = 'z-index: 1000; opacity: 0.7;';
    shortcutsInfo.innerHTML = `
        <div class="bg-white rounded p-2 shadow-sm border">
            <strong>Atajos:</strong><br>
            <small>
                Ctrl+S: Guardar<br>
                Ctrl+N: Nuevo<br>
                Ctrl+P: Imprimir
            </small>
        </div>
    `;

    document.body.appendChild(shortcutsInfo);

    // Ocultar después de 8 segundos
    setTimeout(() => {
        shortcutsInfo.style.opacity = '0.3';
    }, 8000);

    shortcutsInfo.addEventListener('mouseenter', () => {
        shortcutsInfo.style.opacity = '1';
    });

    shortcutsInfo.addEventListener('mouseleave', () => {
        shortcutsInfo.style.opacity = '0.3';
    });
});
</script>

</body>
</html>


