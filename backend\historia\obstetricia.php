<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC     = $_GET['CLAVEPAC'] ?? '';
$CEDULA       = $_GET['CEDULA']   ?? '';
$CLAVE        = $_GET['CLAVE']    ?? null;
$nuevo        = isset($_GET['nuevo']);
$mensaje      = '';
$registros    = [];
$seleccionado = [];

// 1) Obtener embarazo activo
$stmtEmbActivo = $pdo->prepare(
    "SELECT * FROM EMBARAZO
     WHERE CLAVEPAC   = :CLAVEPAC
       AND ESTATUSEMB = 'A'
     ORDER BY FECHAINIEMB DESC LIMIT 1"
);
$stmtEmbActivo->execute(['CLAVEPAC' => $CLAVEPAC]);
$embarazoActivo = $stmtEmbActivo->fetch(PDO::FETCH_ASSOC) ?: [];

// 2) Obtener todos los registros de obstetricia
if ($CLAVEPAC) {
    $stmt = $pdo->prepare(
        "SELECT * FROM OBSTETRICIA
         WHERE CLAVEPAC = :CLAVEPAC
         ORDER BY FECHA_CAP DESC"
    );
    $stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 3) Determinar registro a mostrar
if ($nuevo) {
    $seleccionado = [];
} elseif ($CLAVE) {
    $stmt = $pdo->prepare("SELECT * FROM OBSTETRICIA WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $seleccionado = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
} elseif (!empty($registros)) {
    $seleccionado = $registros[0];
}

// 4) Incluir patología de embarazo activo
if (!empty($embarazoActivo['PATOLOGIA'])) {
    $seleccionado['PATOLOGIA'] = $embarazoActivo['PATOLOGIA'];
}

// 5) Procesar POST: eliminar o guardar según acción
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $accion = $_POST['accion'] ?? '';

    // 5.1) Eliminar registro
    if ($accion === 'eliminar' && !empty($_POST['CLAVE'])) {
        $del = $pdo->prepare("DELETE FROM OBSTETRICIA WHERE CLAVE = :CLAVE");
        $del->execute(['CLAVE' => $_POST['CLAVE']]);
        header("Location: obstetricia.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
        exit;
    }

    // 5.2) Guardar (insertar o actualizar)
    if ($accion === 'guardar') {
        $esActualizacion = !empty($_POST['CLAVE']);

        // Validar campos FLOAT
        $camposFloat = ['EG','SX','AU','PESO','FCF'];
        $errores = [];
        foreach ($camposFloat as $campo) {
            $v = trim($_POST[$campo] ?? '');
            if ($v !== '' && !preg_match('/^\d{1,3}(\.\d{1,2})?$/', $v)) {
                $errores[] = "$campo: formato inválido (máx. 3 dígitos y opcional .xx)";
            }
        }
        if ($errores) {
            $mensaje = 'Errores:<br>' . implode('<br>', $errores);
        } else {
            // Construir datos
            $campos = [
                'EG','SX','AU','PESO','TA','PRES','FCF','VARICES','EDEMA',
                'TT','POSBEBE','FUM','FPP','EXCLINICO','EXMAMAS','EXODONT',
                'PELVIS','PAPANIC','COLPOSCOPIA','CERVIX'
            ];
            $data = [];
            foreach ($campos as $c) {
                $data[$c] = $_POST[$c] ?? null;
            }
            // Obtener clave y numemb de EMBARAZO activo
            $stmtEmb = $pdo->prepare(
                "SELECT CLAVE, NUMEMB FROM EMBARAZO
                 WHERE CLAVEPAC = :CLAVEPAC AND ESTATUSEMB = 'A' LIMIT 1"
            );
            $stmtEmb->execute(['CLAVEPAC' => $CLAVEPAC]);
            $act = $stmtEmb->fetch(PDO::FETCH_ASSOC) ?: [];

            if ($esActualizacion) {
                $data['CLAVE'] = $_POST['CLAVE'];
                // UPDATE
                $sets = [];
                foreach ($campos as $c) {
                    $sets[] = "$c = :$c";
                }
                $sets[] = 'SINCRONIZADO = 0';
                $sql = "UPDATE OBSTETRICIA SET " . implode(', ', $sets) . " WHERE CLAVE = :CLAVE";
            } else {
                // INSERT
                $data['CLAVEPAC'] = $CLAVEPAC;
                $data['CEDULA']   = $CEDULA;
                $data['CLAVEEMB'] = $act['CLAVE'] ?? null;
                $data['NUMEMB']   = $act['NUMEMB'] ?? null;
                // Columnas y placeholders
                $cols = array_merge(['CLAVEPAC','CLAVEEMB','NUMEMB'], $campos, ['ESTATUSEMB','CEDULA']);
                $phs  = array_map(function($c){ return ':' . $c; }, $cols);
                $sql = sprintf(
                    "INSERT INTO OBSTETRICIA (%s) VALUES (%s)",
                    implode(', ', $cols),
                    implode(', ', $phs)
                );
                // Forzar ESTATUSEMB = 'A'
                $data['ESTATUSEMB'] = 'A';
            }

            try {
                $stmt = $pdo->prepare($sql);
                $stmt->execute($data);
                // Actualizar patología EMBARAZO
                if (!empty($act['CLAVE']) && isset($_POST['PATOLOGIA'])) {
                    $u = $pdo->prepare("UPDATE EMBARAZO SET PATOLOGIA = :pato WHERE CLAVE = :c");
                    $u->execute(['pato'=>$_POST['PATOLOGIA'],'c'=>$act['CLAVE']]);
                }
                $newId = $esActualizacion ? $_POST['CLAVE'] : $pdo->lastInsertId();
                header("Location: obstetricia.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$newId");
                exit;
            } catch (PDOException $e) {
                $mensaje = '❌ Error: ' . $e->getMessage();
            }
        }
    }
}
?>


<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Obstetricia</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
  .sugerido {
    background-color: #fff8c4 !important;
  }
</style>
  
  <script>
    // Función que formatea y recorta a NUMERIC(5,2)
    function formatearDecimal(input) {
      let val = input.value.replace(/[^0-9.]/g, '');
      val = val.replace(/\.(?=.*\.)/g, '');
      const endsWithDot = val.endsWith('.');
      const [intRaw = '', decRaw = ''] = val.split('.');
      const intPart = intRaw.slice(0, 3);
      const decPart = decRaw.slice(0, 2);
      let newVal = intPart;
      if (endsWithDot) {
        newVal = `${intPart}.`;
      } else if (decPart.length > 0) {
        newVal = `${intPart}.${decPart}`;
      }
      input.value = newVal;
    }

    // ← NUEVO: función validarFormulario añadida
    function validarFormulario(event) {
      const regex = /^\d{1,3}(\.\d{1,2})?$/;
      let valido = true;
      document.querySelectorAll('.decimal-input').forEach(input => {
        const val = input.value.trim();
        if (val !== '' && !regex.test(val)) {
          input.setCustomValidity('Formato inválido: hasta 3 dígitos y opcional .xx');
          input.reportValidity();
          valido = false;
        } else {
          input.setCustomValidity('');
        }
      });
      if (!valido) event.preventDefault();
    }

    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.decimal-input').forEach(input => {
        input.setAttribute('maxlength', '6'); // Máximo: 3 cifras + punto + 2 decimales
        // ← NUEVO: Atributos HTML5 pattern y title para validación extra
        input.setAttribute('pattern', '^\\d{1,3}(\\.\\d{1,2})?$');
        input.setAttribute('title', 'Hasta 3 dígitos y opcional .xx');
        formatearDecimal(input);
        input.addEventListener('input', () => formatearDecimal(input));
      });
      // ← NUEVO: Listener de submit para validar el formulario antes de enviar
      const form = document.querySelector('form');
      form.addEventListener('submit', validarFormulario);
    });
  </script>
</head>
<body class="p-3">
    
    <?php if (isset($_GET['error']) && $_GET['error'] === 'falta_fum'): ?>
  <div class="alert alert-danger">
    ❌ No se puede iniciar embarazo. Debe registrar una FUM primero (en Obstetricia o Antecedentes Gineco-Obstétricos).
  </div>
<?php endif; ?>
    
    
<div class="row">
  <div class="col-md-3 border-end">
    <h5>Registros</h5>
    <ul class="list-group">
      <?php foreach ($registros as $r): ?>
        <li class="list-group-item <?= ($seleccionado && $seleccionado['CLAVE'] == $r['CLAVE']) ? 'active' : '' ?>">
          <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&CLAVE=<?= $r['CLAVE'] ?>" class="text-decoration-none <?= ($seleccionado && $seleccionado['CLAVE'] == $r['CLAVE']) ? 'text-white' : '' ?>">
            <?= $r['FECHA_CAP'] ?>
          </a>
        </li>
      <?php endforeach; ?>
    </ul>
  </div>

  <div class="col-md-9">
    <h4>Formulario Obstétrico</h4>
    
 
 
 <?php
// Comprobar si hay embarazo activo
$embarazoActivo = null;
if (!empty($CLAVEPAC)) {
  $stmt = $pdo->prepare("SELECT * FROM EMBARAZO WHERE CLAVEPAC = :CLAVEPAC AND ESTATUSEMB = 'A' ORDER BY FECHAINIEMB DESC LIMIT 1");
  $stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
  $embarazoActivo = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>

<div class="mb-3 d-flex gap-2 align-items-center">
  <?php if ($embarazoActivo): ?>
    <span class="badge bg-success">🤰 Embarazo activo iniciado: <?= $embarazoActivo['FECHAINIEMB'] ?></span>
    <form method="post" action="finalizar_embarazo.php" class="d-inline" onsubmit="return confirm('¿Finalizar este embarazo?')">
      <input type="hidden" name="CLAVE" value="<?= $embarazoActivo['CLAVE'] ?>">
      <button type="submit" class="btn btn-danger btn-sm">Finalizar Embarazo</button>
    </form>
  <?php else: ?>
    <form method="post" action="iniciar_embarazo.php" class="d-inline">
      <input type="hidden" name="CLAVEPAC" value="<?= $CLAVEPAC ?>">
      <input type="hidden" name="CEDULA" value="<?= $CEDULA ?>">
      <button type="submit" class="btn btn-primary btn-sm">Iniciar Embarazo</button>
    </form>
  <?php endif; ?>
  
  <!-- 🔍 NUEVO: Botón para ver embarazos anteriores -->
 <a href="embarazos.php?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>" class="btn btn-info btn-sm text-white">
  Ver embarazos anteriores
</a>
  
</div>

    <?php if ($mensaje): ?>
     <div class="alert <?= str_starts_with($mensaje, '⚠️') ? 'alert-danger' : 'alert-info' ?>">
    <?= $mensaje ?>
  </div>
    <?php endif; ?>

    <form method="post">
      <?php if ($seleccionado): ?>
        <input type="hidden" name="CLAVE" value="<?= $seleccionado['CLAVE'] ?>">
      <?php endif; ?>
      
      <!-- clave de paciente y cédula, siempre -->
  <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
  <input type="hidden" name="CEDULA"   value="<?= htmlspecialchars($CEDULA) ?>">
      
      
     

      <div class="row">
        <?php
        $camposFloat = [
          'EG' => 'EG', 'SX' => 'SX', 'AU' => 'AU', 'PESO' => 'Peso', 'FCF' => 'FCF'
        ];
        foreach ($camposFloat as $campo => $label):
          $valor = $seleccionado[$campo] ?? '';
        ?>
          <div class="col-md-4 mb-2">
            <label for="<?= $campo ?>"><?= $label ?></label>
            <input
              type="text"
              id="<?= $campo ?>"
              name="<?= $campo ?>"
              class="form-control decimal-input"
              value="<?= htmlspecialchars($valor) ?>"
              <?= $campo === 'EG' ? 'readonly' : '' ?>
            >
          </div>
        <?php endforeach; ?>

        <div class="col-md-4 mb-2">
          <label>TA</label>
          <input type="text" name="TA" class="form-control" maxlength="7" value="<?= $seleccionado['TA'] ?? '' ?>">
        </div>

        <div class="col-md-4 mb-2">
          <label>TT</label>
          <input type="text" name="TT" class="form-control" maxlength="15" value="<?= $seleccionado['TT'] ?? '' ?>">
        </div>
      </div>

      <!-- Selects -->
      <div class="row">
        <?php
        $selects = [
          'PRES' => ['label' => 'Presentación', 'options' => ['Cef', 'Pelv.', 'Tran.', 'Podálica', 'De hombro', 'De cara', 'Indeterminada']],
          'VARICES' => ['label' => 'Varices', 'options' => ['SI', 'NO']],
          'EDEMA' => ['label' => 'Edema', 'options' => ['-', '+', '++', '+++', '++++', 'Ausentes', 'Ligeros']],
          'POSBEBE' => ['label' => 'Posición del bebé', 'options' => ['Derecho', 'Izquierdo']]
        ];
        foreach ($selects as $campo => $cfg): ?>
          <div class="col-md-3 mb-2">
            <label for="<?= $campo ?>"><?= $cfg['label'] ?></label>
            <select name="<?= $campo ?>" id="<?= $campo ?>" class="form-select">
              <option value="">--</option>
              <?php foreach ($cfg['options'] as $op): ?>
                <option value="<?= $op ?>" <?= ($seleccionado[$campo] ?? '') === $op ? 'selected' : '' ?>><?= $op ?></option>
              <?php endforeach; ?>
            </select>
          </div>
        <?php endforeach; ?>
      </div>

      <div class="row">
        <div class="col-md-4 mb-2">
          <label>FUM</label>
          <input type="date" name="FUM" class="form-control <?= isset($mensaje) && str_contains($mensaje, 'FUM') ? 'sugerido' : '' ?>" value="<?= $seleccionado['FUM'] ?? '' ?>">

        </div>
        <div class="col-md-4 mb-2">
          <label>FPP</label>
          <input type="date" name="FPP" class="form-control" value="<?= $seleccionado['FPP'] ?? '' ?>" readonly>
        </div>
        <div class="col-md-4 mb-2">
          <label># Embarazo</label>
          <input type="number" class="form-control" value="<?= $embarazoActivo['NUMEMB'] ?? '' ?>" disabled>
          
        </div>
      </div>

      <hr>
      <h5>Exploraciones</h5>
      <div class="row">
        <?php
        $exploraciones = ['EXCLINICO', 'EXMAMAS', 'EXODONT', 'PELVIS', 'PAPANIC', 'COLPOSCOPIA', 'CERVIX'];
        foreach ($exploraciones as $campo): ?>
          <div class="col-md-3 mb-2">
            <label for="<?= $campo ?>"><?= $campo ?></label>
            <select name="<?= $campo ?>" id="<?= $campo ?>" class="form-select">
              <?php foreach (['Normal', 'Anormal'] as $op): ?>
                <option value="<?= $op ?>" <?= ($seleccionado[$campo] ?? '') === $op ? 'selected' : '' ?>><?= $op ?></option>
              <?php endforeach; ?>
            </select>
          </div>
        <?php endforeach; ?>
      </div>

      <div class="mb-3">
        <label for="PATOLOGIA">Patología del embarazo</label>
        <textarea name="PATOLOGIA" id="PATOLOGIA" class="form-control" maxlength="200"><?= $seleccionado['PATOLOGIA'] ?? '' ?></textarea>
      </div>


    <div class="d-flex gap-2 mt-3">
    <!-- Botón Guardar -->
    <button type="submit" name="accion" value="guardar" class="btn btn-success">
      💾 Guardar
    </button>

    <!-- Botón Nuevo (link, no envía form) -->
    <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&nuevo=1" class="btn btn-secondary">
      🆕 Nuevo
    </a>

    <!-- Botón Eliminar -->
    <?php if (!empty($seleccionado['CLAVE'])): ?>
      <button type="submit" name="accion" value="eliminar"
              class="btn btn-danger"
              onclick="return confirm('¿Seguro que quieres eliminar este registro?');">
        🗑️ Eliminar
      </button>
    <?php endif; ?>

    <!-- Botón Imprimir (link) -->
    <?php if (!empty($seleccionado['CLAVE'])): ?>
      <a href="imprimir_obstetricia.php?CLAVE=<?= $seleccionado['CLAVE'] ?>"
         target="_blank"
         class="btn btn-outline-primary">
        🖨️ Imprimir
      </a>
    <?php endif; ?>
  </div>
</form>

  <?php if ($embarazoActivo): ?>
  <hr>
  <h5>📋 Resumen de controles del Embarazo #<?= $embarazoActivo['NUMEMB'] ?></h5>
  <div class="table-responsive">
    <table class="table table-sm table-bordered align-middle">
      <thead class="table-light">
        <tr>
          <th>📅 Fecha</th>
          <th>EG</th>
          <th>Peso</th>
          <th>AU</th>
          <th>FCF</th>
          <th>TA</th>
        </tr>
      </thead>
      <tbody>
        <?php
        $stmt = $pdo->prepare("SELECT FECHA_CAP, EG, PESO, AU, FCF, TA 
                               FROM OBSTETRICIA 
                               WHERE CLAVEPAC = :CLAVEPAC AND CLAVEEMB = :CLAVEEMB 
                               ORDER BY FECHA_CAP DESC");
        $stmt->execute([
          'CLAVEPAC' => $CLAVEPAC,
          'CLAVEEMB' => $embarazoActivo['CLAVE']
        ]);
        foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $row): ?>
          <tr>
            <td><?= $row['FECHA_CAP'] ?></td>
            <td><?= $row['EG'] ?></td>
            <td><?= $row['PESO'] ?></td>
            <td><?= $row['AU'] ?></td>
            <td><?= $row['FCF'] ?></td>
            <td><?= $row['TA'] ?></td>
          </tr>
        <?php endforeach; ?>
      </tbody>
    </table>
  </div>
<?php endif; ?>

    
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function () {
  const fumInput = document.querySelector('input[name="FUM"]');
  const fppInput = document.querySelector('input[name="FPP"]');
  const egInput  = document.querySelector('input[name="EG"]');

  function calcularFPP() {
    if (!fumInput.value) return;
    const fumDate = new Date(fumInput.value);
    fumDate.setDate(fumDate.getDate() + 280);
    fppInput.value = fumDate.toISOString().split('T')[0];
  }

  function calcularEG() {
    if (!fumInput.value || !egInput) return;
    const hoy      = new Date();
    const fumDate  = new Date(fumInput.value);
    const diffMs   = hoy - fumDate;
    const diffDias = diffMs / (1000*60*60*24);
    let semanas    = Math.round((diffDias / 7) * 100) / 100;
    egInput.value  = (semanas > 43 ? 0 : semanas).toFixed(2);
  }

  // Cada vez que cambie FUM recalcúlalo
  fumInput.addEventListener('change', () => {
    calcularFPP();
    calcularEG();
  });

  // Si ya había valor al cargar la página
  if (fumInput.value) {
    calcularFPP();
    calcularEG();
  }
});
</script>

</body>
</html>
