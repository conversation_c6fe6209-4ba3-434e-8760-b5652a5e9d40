<?php
session_start();
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Asegura que solo los pacientes puedan subir archivos
if (!isset($_SESSION['usuario']) || $_SESSION['rol'] !== 'paciente') {
    header('Location: /mi_consultorio/no_autorizado.php');
    exit;
}

// Conexión a la base de datos
require_once dirname(__DIR__) . '/config/database.php';

// Validar si llegó el archivo y la cédula
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['archivo']) && isset($_POST['CEDULA'])) {
    $cedula = $_POST['CEDULA'];
    $descripcion = $_POST['descripcion'] ?? 'Sin descripción';
    $archivo = $_FILES['archivo'];


 // --- INICIO DE CAMBIO 1: Obtener CLAVEPAC a partir de la CEDULA ---
    $clavePac = null;
    try {
        $stmtPaciente = $pdo->prepare("SELECT CLAVE FROM PACIENTES WHERE CEDULA = ?");
        $stmtPaciente->execute([$cedula]);
        $resultPaciente = $stmtPaciente->fetch(PDO::FETCH_ASSOC);
        if ($resultPaciente) {
            $clavePac = $resultPaciente['CLAVE'];
        } else {
            // Manejar el caso donde la cédula no encuentra un paciente (importante)
            die("<p style='color:red;'>❌ Error: La cédula del paciente no fue encontrada en la base de datos.</p>");
        }
    } catch (PDOException $e) {
        die("<p style='color:red;'>❌ Error al buscar CLAVEPAC del paciente: " . $e->getMessage() . "</p>");
    }
    // --- FIN DE CAMBIO 1 ---

    // Validaciones básicas
    if ($archivo['error'] !== UPLOAD_ERR_OK) {
        die("Error al subir el archivo. Código: " . $archivo['error']);
    }

    // Ruta absoluta para guardar archivos
    $uploadDir = __DIR__ . '/upload/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }

    // Evitar nombres duplicados y limpiar caracteres
    $nombreOriginal = basename($archivo['name']);
    $nombreUnico = time() . "_" . preg_replace('/[^a-zA-Z0-9_\.-]/', '_', $nombreOriginal);
    $rutaDestino = $uploadDir . $nombreUnico;

    // Mover archivo al destino final
    if (move_uploaded_file($archivo['tmp_name'], $rutaDestino)) {
        // Guardar referencia en la base de datos
      
      // --- INICIO DE CAMBIO 2: Incluir CLAVEPAC en el INSERT ---
        $stmt = $pdo->prepare("INSERT INTO RESULTADOS_PACIENTES (CLAVEPAC, CEDULA, NOMBRE_ARCHIVO_ORIGINAL, ARCHIVO, DESCRIPCION, FECHA_SUBIDA) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([$clavePac, $cedula, $nombreOriginal, $nombreUnico, $descripcion]);
        // --- FIN DE CAMBIO 2 ---
      
      //  $stmt = $pdo->prepare("INSERT INTO RESULTADOS_PACIENTES (CEDULA, ARCHIVO, DESCRIPCION, FECHA_SUBIDA) VALUES (?, ?, ?, NOW())");
      //  $stmt->execute([$cedula, $nombreUnico, $descripcion]);

        echo "<p style='color:green;'>✅ Archivo subido correctamente.</p>";
        echo "<a href='/mi_consultorio/paciente_panel.php'>⬅️ Volver al panel</a>";
    } else {
        echo "<p style='color:red;'>❌ No se pudo mover el archivo.</p>";
    }
} else {
    echo "<p style='color:red;'>❌ No se recibió archivo o cédula.</p>";
}
?>