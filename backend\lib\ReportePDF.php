<?php
// backend/lib/ReportePDF.php
// Clase para generar PDFs profesionales para reportes

class ReportePDF {
    private $html;
    private $titulo;
    private $empresa;
    private $usuario;
    private $fecha_generacion;
    
    public function __construct($titulo = 'Reporte', $empresa = 'Consultorio Médico') {
        $this->titulo = $titulo;
        $this->empresa = $empresa;
        $this->usuario = $_SESSION['usuario'] ?? 'Sistema';
        $this->fecha_generacion = date('d/m/Y H:i:s');
    }
    
    public function iniciarHTML() {
        $this->html = "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>{$this->titulo}</title>
    <style>
        body { 
            font-family: 'DejaVu Sans', Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            font-size: 11px; 
            line-height: 1.4;
        }
        .header { 
            text-align: center; 
            margin-bottom: 25px; 
            border-bottom: 2px solid #2c3e50; 
            padding-bottom: 15px; 
        }
        .header h1 { 
            color: #2c3e50; 
            margin: 0 0 5px 0; 
            font-size: 18px; 
            font-weight: bold;
        }
        .header h2 { 
            color: #34495e; 
            margin: 0 0 10px 0; 
            font-size: 14px; 
            font-weight: normal;
        }
        .header p { 
            color: #7f8c8d; 
            margin: 0; 
            font-size: 10px; 
        }
        .info-section { 
            background-color: #ecf0f1; 
            padding: 10px; 
            margin-bottom: 20px; 
            border-radius: 4px;
            font-size: 10px;
        }
        .info-section p { 
            margin: 2px 0; 
        }
        .stats-section { 
            background-color: #f8f9fa; 
            padding: 15px; 
            margin: 20px 0; 
            border-radius: 5px; 
            border-left: 4px solid #3498db;
        }
        .stats-grid { 
            display: table; 
            width: 100%; 
            margin: 10px 0;
        }
        .stats-row { 
            display: table-row; 
        }
        .stat-item { 
            display: table-cell; 
            background: white; 
            padding: 10px; 
            text-align: center; 
            border: 1px solid #bdc3c7; 
            vertical-align: middle;
        }
        .stat-item h4 { 
            margin: 0 0 5px 0; 
            color: #2c3e50; 
            font-size: 14px; 
        }
        .stat-item p { 
            margin: 0; 
            color: #7f8c8d; 
            font-size: 9px; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin: 15px 0; 
            font-size: 9px;
        }
        th, td { 
            border: 1px solid #bdc3c7; 
            padding: 6px 4px; 
            text-align: left; 
            vertical-align: top;
        }
        th { 
            background-color: #34495e; 
            color: white; 
            font-weight: bold; 
            text-align: center;
        }
        tr:nth-child(even) { 
            background-color: #f8f9fa; 
        }
        .money-positive { 
            color: #27ae60; 
            font-weight: bold; 
        }
        .money-negative { 
            color: #e74c3c; 
            font-weight: bold; 
        }
        .money-pending { 
            color: #f39c12; 
            font-weight: bold; 
        }
        .estado-atendido { 
            color: #27ae60; 
            font-weight: bold; 
        }
        .estado-no-asistio { 
            color: #e74c3c; 
            font-weight: bold; 
        }
        .estado-citado { 
            color: #3498db; 
            font-weight: bold; 
        }
        .estado-pendiente { 
            color: #f39c12; 
            font-weight: bold; 
        }
        .sexo-m { 
            color: #3498db; 
            font-weight: bold; 
        }
        .sexo-f { 
            color: #e91e63; 
            font-weight: bold; 
        }
        .imc-normal { 
            color: #27ae60; 
            font-weight: bold; 
        }
        .imc-sobrepeso { 
            color: #f39c12; 
            font-weight: bold; 
        }
        .imc-obesidad { 
            color: #e74c3c; 
            font-weight: bold; 
        }
        .sync-complete { 
            color: #27ae60; 
            font-weight: bold; 
        }
        .sync-partial { 
            color: #f39c12; 
            font-weight: bold; 
        }
        .sync-pending { 
            color: #e74c3c; 
            font-weight: bold; 
        }
        .role-admin { 
            color: #e74c3c; 
            font-weight: bold; 
        }
        .role-doctor { 
            color: #3498db; 
            font-weight: bold; 
        }
        .role-secretaria { 
            color: #27ae60; 
            font-weight: bold; 
        }
        .totals-row { 
            background-color: #34495e !important; 
            color: white; 
            font-weight: bold; 
        }
        .totals-row td { 
            background-color: #34495e; 
            color: white; 
        }
        .section-title { 
            background-color: #34495e; 
            color: white; 
            padding: 8px 12px; 
            margin: 20px 0 10px 0; 
            font-weight: bold; 
            font-size: 12px;
        }
        .footer { 
            margin-top: 30px; 
            padding-top: 15px; 
            border-top: 1px solid #bdc3c7; 
            text-align: center; 
            font-size: 8px; 
            color: #7f8c8d; 
        }
        .page-break { 
            page-break-before: always; 
        }
        .no-break { 
            page-break-inside: avoid; 
        }
    </style>
</head>
<body>";
        
        return $this;
    }
    
    public function agregarEncabezado($subtitulo = '', $periodo = '') {
        $this->html .= "
    <div class='header'>
        <h1>{$this->empresa}</h1>
        <h2>{$this->titulo}</h2>";
        
        if ($subtitulo) {
            $this->html .= "<p style='font-size: 11px; color: #34495e; margin: 5px 0;'>$subtitulo</p>";
        }
        
        if ($periodo) {
            $this->html .= "<p style='font-size: 10px;'>Período: $periodo</p>";
        }
        
        $this->html .= "
    </div>
    
    <div class='info-section'>
        <p><strong>Generado por:</strong> {$this->usuario}</p>
        <p><strong>Fecha de generación:</strong> {$this->fecha_generacion}</p>
    </div>";
        
        return $this;
    }
    
    public function agregarSeccionEstadisticas($titulo, $estadisticas) {
        $this->html .= "<div class='stats-section no-break'>
        <h3 style='margin: 0 0 10px 0; color: #2c3e50;'>$titulo</h3>
        <div class='stats-grid'>
            <div class='stats-row'>";
        
        foreach ($estadisticas as $stat) {
            $this->html .= "<div class='stat-item'>
                <h4>{$stat['valor']}</h4>
                <p>{$stat['etiqueta']}</p>
            </div>";
        }
        
        $this->html .= "</div>
        </div>
    </div>";
        
        return $this;
    }
    
    public function agregarTabla($titulo, $encabezados, $datos, $totales = null) {
        $this->html .= "<div class='section-title'>$titulo</div>";
        $this->html .= "<table>";
        
        // Encabezados
        $this->html .= "<thead><tr>";
        foreach ($encabezados as $encabezado) {
            $this->html .= "<th>$encabezado</th>";
        }
        $this->html .= "</tr></thead>";
        
        // Datos
        $this->html .= "<tbody>";
        foreach ($datos as $fila) {
            $this->html .= "<tr>";
            foreach ($fila as $celda) {
                $this->html .= "<td>$celda</td>";
            }
            $this->html .= "</tr>";
        }
        
        // Totales si se proporcionan
        if ($totales) {
            $this->html .= "<tr class='totals-row'>";
            foreach ($totales as $total) {
                $this->html .= "<td>$total</td>";
            }
            $this->html .= "</tr>";
        }
        
        $this->html .= "</tbody></table>";
        
        return $this;
    }
    
    public function agregarSeccionPersonalizada($contenido) {
        $this->html .= $contenido;
        return $this;
    }
    
    public function agregarSaltoLinea() {
        $this->html .= "<div class='page-break'></div>";
        return $this;
    }
    
    public function finalizarHTML() {
        $this->html .= "
    <div class='footer'>
        <p>Reporte generado automáticamente por el Sistema de Gestión del Consultorio</p>
        <p>Este documento contiene información confidencial</p>
    </div>
</body>
</html>";
        
        return $this;
    }
    
    public function obtenerHTML() {
        return $this->html;
    }
    
    public function generarPDF($nombre_archivo = null) {
        if (!$nombre_archivo) {
            $nombre_archivo = 'reporte_' . date('Y-m-d_H-i-s') . '.pdf';
        }
        
        // Por ahora devolvemos HTML hasta que instalemos TCPDF
        header('Content-Type: text/html; charset=utf-8');
        header("Content-Disposition: attachment; filename=\"$nombre_archivo.html\"");
        
        return $this->html;
    }
    
    // Métodos de utilidad para formatear datos
    public static function formatearFecha($fecha) {
        return $fecha ? date('d/m/Y', strtotime($fecha)) : 'N/A';
    }
    
    public static function formatearHora($hora) {
        return $hora ? date('H:i', strtotime($hora)) : 'N/A';
    }
    
    public static function formatearMoneda($cantidad) {
        return 'RD$' . number_format($cantidad, 2);
    }
    
    public static function formatearSexo($sexo) {
        return $sexo == 'M' ? 'Masculino' : 'Femenino';
    }
    
    public static function formatearEstadoCita($estatus) {
        $estados = [
            0 => 'Atendido',
            1 => 'Canceló',
            2 => 'No asistió',
            3 => 'Citado',
            4 => 'Llegó tarde',
            5 => 'Esperando',
            6 => 'Pendiente aprobación'
        ];
        return $estados[$estatus] ?? 'Desconocido';
    }
    
    public static function formatearEstadoPago($pagado, $pendiente) {
        if ($pendiente == 0) return 'Pagado';
        if ($pagado == 0) return 'Pendiente';
        return 'Parcial';
    }
}
?>
