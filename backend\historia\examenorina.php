<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA']   ?? '';
$CLAVE    = $_GET['CLAVE']    ?? null; // CLAVE del registro si estamos editando uno específico
$nuevo    = isset($_GET['nuevo']); // Para indicar que se quiere un formulario vacío
$mensaje  = '';
$messageType = '';
$registros = []; // Para almacenar todos los exámenes de orina del paciente
$seleccionado = null; // Para el examen que se muestra en el formulario

$tableName = 'EXAMENORINA'; // Nombre de tu tabla de examen de orina

// --- 1) Eliminar examen de orina por POST seguro ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && !empty($_POST['CLAVE_ELIMINAR'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM " . $tableName . " WHERE CLAVE = ?");
        $stmt->execute([$_POST['CLAVE_ELIMINAR']]);
        $mensaje = "✅ Examen de Orina eliminado exitosamente.";
        $messageType = 'success';
        // Redireccionar para recargar la página y no mostrar el registro eliminado
        header("Location: examenorina.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar examen de orina: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// --- 2) Cargar registros históricos de examen de orina ---
if ($CLAVEPAC) {
    $stmt = $pdo->prepare("SELECT CLAVE, FECHA_CAP FROM " . $tableName . " WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC");
    $stmt->execute([$CLAVEPAC]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// --- 3) Seleccionar registro actual o nuevo ---
// Definir los campos para inicializar el formulario o cargar datos
$campos_orina = [
    'CLAVE' => null,
    'DENSIDAD' => '', 'OSMOL' => '', 'GLUCOSA' => '', 'PROTEINAS' => '',
    'PROT24' => '', 'MICROALB' => '', 'CCETON' => '', 'ALB_CR' => '',
    'HEMAT' => '', 'LEUC' => '', 'C_HIAL' => '', 'C_GRAN' => '',
    'C_HIGR' => '', 'BACTER' => '', 'PIURIA' => '', 'OTROS' => '',
    'COLOR' => '', 'OLOR' => '', 'ASPECTO' => '', 'PH' => '',
    'BILIRRUBINA' => '',
    // UUID se asume que DB lo maneja
    'CEDULA' => $CEDULA,
    'SINCRONIZADO' => 0
];

// Si se pidió un formulario nuevo
if ($nuevo) {
    $seleccionado = $campos_orina; // Inicializa con valores vacíos
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'Ingresando un nuevo examen de orina.';
    $messageType = 'info';
} elseif ($CLAVE) { // Si se pidió un examen específico por su CLAVE
    $stmt = $pdo->prepare("SELECT * FROM " . $tableName . " WHERE CLAVE = ?");
    $stmt->execute([$CLAVE]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($data_db) {
        $campos_orina_temp = $campos_orina;
        // Si la columna UUID existe en DB, es bueno que el array $seleccionado la contenga
        if (isset($data_db['UUID'])) {
            $campos_orina_temp['UUID'] = '';
        }
        $seleccionado = array_merge($campos_orina_temp, $data_db);
        $mensaje = 'Examen de Orina cargado. Modifique y guarde, o cree uno nuevo.';
        $messageType = 'info';
    } else {
        $seleccionado = $campos_orina;
        $seleccionado['CLAVEPAC'] = $CLAVEPAC;
        $seleccionado['CEDULA'] = $CEDULA;
        $mensaje = 'No se encontró el examen de orina solicitado. Creando uno nuevo.';
        $messageType = 'warning';
    }
} elseif (!empty($registros)) { // Si no se pidió nuevo ni específico, carga el más reciente
    $stmt = $pdo->prepare("SELECT * FROM " . $tableName . " WHERE CLAVE = ?");
    $stmt->execute([$registros[0]['CLAVE']]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    $campos_orina_temp = $campos_orina;
    if (isset($data_db['UUID'])) {
        $campos_orina_temp['UUID'] = '';
    }
    $seleccionado = array_merge($campos_orina_temp, $data_db);
    $mensaje = 'Mostrando el examen de orina más reciente.';
    $messageType = 'info';
} else { // Si no hay registros ni se pidió nuevo, inicializa un formulario vacío
    $seleccionado = $campos_orina;
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'No se encontraron exámenes de orina. Ingrese los datos.';
    $messageType = 'secondary';
}

// --- 4) Guardar o actualizar examen ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['eliminar'])) {
    $esActualizacion = !empty($_POST['CLAVE']);
    $data = [];

    // Recopilar datos del POST, asegurando saneamiento básico
    foreach ($campos_orina as $campo => $valor_defecto) {
        if ($campo === 'CLAVE' || $campo === 'CLAVEPAC' || $campo === 'CEDULA' || $campo === 'SINCRONIZADO' || $campo === 'UUID') {
            continue; // UUID no se procesa desde PHP
        }
        $val = $_POST[$campo] ?? '';

        // Saneamiento específico según el tipo de columna en la base de datos
        switch ($campo) {
            case 'PROT24':
            case 'ALB_CR':
            case 'C_HIGR':
                $data[$campo] = filter_var($val, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) ?: null; // decimal(X,1) o decimal(X,2)
                break;
            case 'OSMOL':
            case 'GLUCOSA':
            case 'PROTEINAS':
            case 'MICROALB':
            case 'HEMAT':
            case 'LEUC':
            case 'C_HIAL':
            case 'C_GRAN':
            case 'BILIRRUBINA':
                $data[$campo] = filter_var($val, FILTER_SANITIZE_NUMBER_INT) ?: null; // smallint
                break;
            default: // char, varchar
                $data[$campo] = htmlspecialchars(trim($val)) ?: null;
                break;
        }
    }

    // Campos de control adicionales
    $data['CLAVEPAC'] = $CLAVEPAC;
    $data['CEDULA'] = $CEDULA;
    $data['SINCRONIZADO'] = 0;

    try {
        if ($esActualizacion) {
            $data['CLAVE'] = $_POST['CLAVE'];
            $sets = [];
            foreach ($data as $key => $value) {
                if ($key !== 'CLAVE') {
                    $sets[] = "$key = :$key";
                }
            }
            $sql = "UPDATE " . $tableName . " SET " . implode(', ', $sets) . " WHERE CLAVE = :CLAVE";
        } else {
            $cols = implode(', ', array_keys($data));
            $phs  = ':' . implode(', :', array_keys($data));
            $sql  = "INSERT INTO " . $tableName . " ($cols) VALUES ($phs)";
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($data);

        $newClave = $esActualizacion ? $_POST['CLAVE'] : $pdo->lastInsertId();
        
        $mensaje = '✅ Examen de Orina guardado exitosamente.';
        $messageType = 'success';
        header("Location: examenorina.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$newClave");
        exit;

    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar el examen de orina: " . $e->getMessage();
        $messageType = 'danger';
        error_log("Error saving examenorina: " . $e->getMessage() . " - SQL: " . $sql);
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examen de Orina</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .form-container { padding: 20px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 15px rgba(0,0,0,0.1); margin-top: 20px; }
        .form-section { border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin-bottom: 20px; background-color: #fefefe; }
        .form-section h4 { margin-bottom: 15px; color: #0d6efd; border-bottom: 1px solid #0d6efd; padding-bottom: 5px; }
        .list-group-item.active { background-color: #0d6efd !important; border-color: #0d6efd !important; }
        .list-group-item.active a { color: #fff !important; }
        .decimal-input { text-align: right; }
        .int-input { text-align: right; }
    </style>
</head>
<body class="p-4">
    <div class="row">
        <div class="col-md-3 border-end">
            <h5 class="mb-3">Historial de Exámenes de Orina</h5>
            <ul class="list-group">
                <?php if (empty($registros)): ?>
                    <li class="list-group-item text-muted">No hay registros anteriores.</li>
                <?php endif; ?>
                <?php foreach ($registros as $r): ?>
                    <?php
                    $fecha_formateada = (new DateTime($r['FECHA_CAP']))->format('Y-m-d H:i');
                    $active = isset($seleccionado['CLAVE']) && $seleccionado['CLAVE'] == $r['CLAVE'];
                    ?>
                    <li class="list-group-item <?= $active ? 'active' : '' ?>">
                        <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($r['CLAVE']) ?>"
                           class="text-decoration-none <?= $active ? 'text-white' : '' ?>">
                            <?= htmlspecialchars($fecha_formateada) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <div class="col-md-9 form-container">
            <h4 class="mb-3">Examen de Orina</h4>
            <p class="text-muted">Paciente: <strong><?= htmlspecialchars($CLAVEPAC) ?></strong> (CI: <strong><?= htmlspecialchars($CEDULA) ?></strong>)</p>

            <?php if ($mensaje): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($mensaje) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form method="post" novalidate>
                <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                <input type="hidden" name="CEDULA" value="<?= htmlspecialchars($CEDULA) ?>">
                <?php if (!empty($seleccionado['CLAVE'])): ?>
                    <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                    <input type="hidden" name="CLAVE_ELIMINAR" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                <?php endif; ?>
                <input type="hidden" name="SINCRONIZADO" value="<?= htmlspecialchars($seleccionado['SINCRONIZADO']) ?>">


                <div class="form-section">
                    <h4>Propiedades Físicas</h4>
                    <div class="row g-3">
                        <div class="col-md-4"><label for="COLOR" class="form-label">Color</label><input type="text" class="form-control" id="COLOR" name="COLOR" value="<?= htmlspecialchars($seleccionado['COLOR'] ?? '') ?>" maxlength="15"></div>
                        <div class="col-md-4"><label for="OLOR" class="form-label">Olor</label><input type="text" class="form-control" id="OLOR" name="OLOR" value="<?= htmlspecialchars($seleccionado['OLOR'] ?? '') ?>" maxlength="15"></div>
                        <div class="col-md-4"><label for="ASPECTO" class="form-label">Aspecto</label><input type="text" class="form-control" id="ASPECTO" name="ASPECTO" value="<?= htmlspecialchars($seleccionado['ASPECTO'] ?? '') ?>" maxlength="20"></div>
                        <div class="col-md-4"><label for="DENSIDAD" class="form-label">Densidad</label><input type="text" class="form-control" id="DENSIDAD" name="DENSIDAD" value="<?= htmlspecialchars($seleccionado['DENSIDAD'] ?? '') ?>" maxlength="5"></div>
                        <div class="col-md-4"><label for="PH" class="form-label">pH</label><input type="text" class="form-control" id="PH" name="PH" value="<?= htmlspecialchars($seleccionado['PH'] ?? '') ?>" maxlength="3"></div>
                        <div class="col-md-4"><label for="OSMOL" class="form-label">Osmolalidad</label><input type="text" class="form-control int-input" id="OSMOL" name="OSMOL" value="<?= htmlspecialchars($seleccionado['OSMOL'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Componentes Químicos</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="GLUCOSA" class="form-label">Glucosa</label><input type="text" class="form-control int-input" id="GLUCOSA" name="GLUCOSA" value="<?= htmlspecialchars($seleccionado['GLUCOSA'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="PROTEINAS" class="form-label">Proteínas</label><input type="text" class="form-control int-input" id="PROTEINAS" name="PROTEINAS" value="<?= htmlspecialchars($seleccionado['PROTEINAS'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="CCETON" class="form-label">C. Ceton</label><input type="text" class="form-control" id="CCETON" name="CCETON" value="<?= htmlspecialchars($seleccionado['CCETON'] ?? '') ?>" maxlength="4"></div>
                        <div class="col-md-3"><label for="BILIRRUBINA" class="form-label">Bilirrubina</label><input type="text" class="form-control int-input" id="BILIRRUBINA" name="BILIRRUBINA" value="<?= htmlspecialchars($seleccionado['BILIRRUBINA'] ?? '') ?>"></div>
                        <div class="col-md-4"><label for="PROT24" class="form-label">Prot. 24h</label><input type="text" class="form-control decimal-input" id="PROT24" name="PROT24" value="<?= htmlspecialchars($seleccionado['PROT24'] ?? '') ?>"></div>
                        <div class="col-md-4"><label for="MICROALB" class="form-label">Microalb.</label><input type="text" class="form-control int-input" id="MICROALB" name="MICROALB" value="<?= htmlspecialchars($seleccionado['MICROALB'] ?? '') ?>"></div>
                        <div class="col-md-4"><label for="ALB_CR" class="form-label">Alb/Cr</label><input type="text" class="form-control decimal-input" id="ALB_CR" name="ALB_CR" value="<?= htmlspecialchars($seleccionado['ALB_CR'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Sedimento Urinario</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="HEMAT" class="form-label">Hematíes</label><input type="text" class="form-control int-input" id="HEMAT" name="HEMAT" value="<?= htmlspecialchars($seleccionado['HEMAT'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="LEUC" class="form-label">Leucocitos</label><input type="text" class="form-control int-input" id="LEUC" name="LEUC" value="<?= htmlspecialchars($seleccionado['LEUC'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="C_HIAL" class="form-label">Cil. Hialinos</label><input type="text" class="form-control int-input" id="C_HIAL" name="C_HIAL" value="<?= htmlspecialchars($seleccionado['C_HIAL'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="C_GRAN" class="form-label">Cil. Granulosos</label><input type="text" class="form-control int-input" id="C_GRAN" name="C_GRAN" value="<?= htmlspecialchars($seleccionado['C_GRAN'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="C_HIGR" class="form-label">Cil. H. Gran.</label><input type="text" class="form-control decimal-input" id="C_HIGR" name="C_HIGR" value="<?= htmlspecialchars($seleccionado['C_HIGR'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="BACTER" class="form-label">Bacterias</label><input type="text" class="form-control" id="BACTER" name="BACTER" value="<?= htmlspecialchars($seleccionado['BACTER'] ?? '') ?>" maxlength="3"></div>
                        <div class="col-md-3"><label for="PIURIA" class="form-label">Piuria</label><input type="text" class="form-control" id="PIURIA" name="PIURIA" value="<?= htmlspecialchars($seleccionado['PIURIA'] ?? '') ?>" maxlength="3"></div>
                        <div class="col-md-3"><label for="OTROS" class="form-label">Otros</label><input type="text" class="form-control" id="OTROS" name="OTROS" value="<?= htmlspecialchars($seleccionado['OTROS'] ?? '') ?>" maxlength="20"></div>
                    </div>
                </div>

                <div class="d-flex gap-2 mt-3">
                    <button type="submit" name="accion" value="guardar" class="btn btn-success">💾 Guardar</button>
                    <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&nuevo=1" class="btn btn-secondary">🆕 Nuevo</a>
                    <?php if (!empty($seleccionado['CLAVE'])): ?>
                        <button type="submit" name="eliminar" value="1" class="btn btn-danger" onclick="return confirm('¿Deseas eliminar este examen de orina?');">🗑️ Eliminar</button>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">🖨️ Imprimir</button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Función para formatear campos decimales (PROT24, ALB_CR, C_HIGR)
        function formatearDecimal(input) {
            let val = input.value.replace(/[^0-9.]/g, '');
            val = val.replace(/\.(?=.*\.)/g, ''); // Solo un punto decimal
            const endsWithDot = val.endsWith('.');
            const [intRaw = '', decRaw = ''] = val.split('.');
            let intPart = intRaw;
            let decPart = decRaw;

            // Ajustes específicos por tipo decimal:
            // decimal(3,1) como PROT24 (2 enteros, 1 decimal)
            if (input.id === 'PROT24') {
                intPart = intRaw.slice(0, 2);
                decPart = decRaw.slice(0, 1);
            }
            // decimal(5,2) como ALB_CR (3 enteros, 2 decimales)
            else if (input.id === 'ALB_CR') {
                intPart = intRaw.slice(0, 3);
                decPart = decRaw.slice(0, 2);
            }
            // decimal(2,1) como C_HIGR (1 entero, 1 decimal)
            else if (input.id === 'C_HIGR') {
                 intPart = intRaw.slice(0, 1);
                 decPart = decRaw.slice(0, 1);
            }

            input.value = endsWithDot ? `${intPart}.` : (decPart ? `${intPart}.${decPart}` : intPart);
        }

        // Función para formatear campos enteros (smallint)
        function formatearEntero(input) {
            let val = input.value.replace(/[^0-9]/g, ''); // Solo números enteros
            input.value = val.slice(0, 5); // smallint va hasta 32767, 5 dígitos
        }

        // Función para validar el formulario antes de enviar
        function validarFormulario(e) {
            let ok = true;

            // Validar campos decimales
            document.querySelectorAll('.decimal-input').forEach(input => {
                let v = input.value.trim();
                let regex;
                if (input.id === 'PROT24') {
                    regex = /^\d{1,2}(\.\d{1})?$/; // decimal(3,1)
                } else if (input.id === 'ALB_CR') {
                    regex = /^\d{1,3}(\.\d{1,2})?$/; // decimal(5,2)
                } else if (input.id === 'C_HIGR') {
                    regex = /^\d{1}(\.\d{1})?$/; // decimal(2,1)
                }

                if (v && !regex.test(v)) {
                    input.setCustomValidity('Formato inválido para este campo.');
                    input.reportValidity();
                    ok = false;
                } else {
                    input.setCustomValidity('');
                }
            });

            // Validar campos enteros
            document.querySelectorAll('.int-input').forEach(input => {
                let v = input.value.trim();
                const regex = /^\d{1,5}$/; // smallint (hasta 5 dígitos)

                if (v && !regex.test(v)) {
                    input.setCustomValidity('Solo números enteros (máx. 5 dígitos).');
                    input.reportValidity();
                    ok = false;
                } else {
                    input.setCustomValidity('');
                }
            });

            if (!ok) e.preventDefault();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Adjuntar formateadores y validadores para campos decimales
            document.querySelectorAll('.decimal-input').forEach(i => {
                // Ajusta maxlength para los campos decimales:
                if (i.id === 'PROT24') i.setAttribute('maxlength', '4'); // 2 + . + 1
                else if (i.id === 'ALB_CR') i.setAttribute('maxlength', '6'); // 3 + . + 2
                else if (i.id === 'C_HIGR') i.setAttribute('maxlength', '3'); // 1 + . + 1
                i.addEventListener('input', () => formatearDecimal(i));
                formatearDecimal(i); // Formatea el valor inicial
            });

            // Adjuntar formateadores y validadores para campos enteros
            document.querySelectorAll('.int-input').forEach(i => {
                i.setAttribute('maxlength', '5'); // smallint
                i.addEventListener('input', () => formatearEntero(i));
                formatearEntero(i); // Formatea el valor inicial
            });

            document.querySelector('form').addEventListener('submit', validarFormulario);
        });
    </script>
</body>
</html>