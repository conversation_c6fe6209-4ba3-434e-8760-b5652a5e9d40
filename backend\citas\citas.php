<?php
include('../config/database.php');

// Obtener las citas programadas
$sql = "SELECT * FROM CITAMEDIC ORDER BY FECHACON ASC";
$stmt = $pdo->query($sql);
$citas = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Citas Programadas</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table, th, td {
            border: 1px solid black;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        .acciones a {
            margin-right: 10px;
            text-decoration: none;
            padding: 5px 10px;
        }
        .btn-editar {
            background-color: #007BFF;
            color: white;
            border-radius: 3px;
        }
        .btn-eliminar {
            background-color: #DC3545;
            color: white;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Citas Programadas</h1>

    <!-- Mensajes de confirmación -->
    <?php if (isset($_GET['mensaje'])): ?>
        <div class="mensaje">
            <?php
            if ($_GET['mensaje'] === 'creada') {
                echo "¡Cita creada exitosamente!";
            } elseif ($_GET['mensaje'] === 'editada') {
                echo "¡Cita editada exitosamente!";
            } elseif ($_GET['mensaje'] === 'eliminada') {
                echo "¡Cita eliminada exitosamente!";
            }
            ?>
        </div>
    <?php endif; ?>

    <!-- Tabla de citas -->
    <?php if (!empty($citas)): ?>
        <table>
            <thead>
                <tr>
                    <th>Nombre del Paciente</th>
                    <th>Fecha</th>
                    <th>Hora</th>
                    <th>Estatus</th>
                    <th>Acciones</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($citas as $cita): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($cita['PACIENTE']); ?></td>
                        <td><?php echo date('Y-m-d', strtotime($cita['FECHACON'])); ?></td>
                        <td><?php echo date('H:i', strtotime($cita['FECHACON'])); ?></td>
                        <td><?php echo htmlspecialchars($cita['ESTATUS']); ?></td>
                        <td class="acciones">
                            <a href="editar_cita.php?clave=<?php echo $cita['CLAVE']; ?>" class="btn-editar">Editar</a>
                            <a href="eliminar_cita.php?clave=<?php echo $cita['CLAVE']; ?>" class="btn-eliminar" onclick="return confirm('¿Estás seguro de eliminar esta cita?');">Eliminar</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>No hay citas programadas.</p>
    <?php endif; ?>
</body>
</html>
