<?php
// Habilitar la visualización de errores para depuración (QUITAR EN PRODUCCIÓN)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// --- 1) Conexión a la Base de Datos MAESTRA (según tu función) ---
require_once __DIR__ . '/../config/database.php';

$subdominio = $_GET['subdominio'] ?? $_POST['subdominio'] ?? '';
$pdo = null; // PDO para el tenant específico
$empresa = null; // Datos de la tabla EMPRESA del tenant
$facturas = []; // Facturas del tenant
$error = null; // Errores de la conexión a la BD maestra
$error_tenant = null; // Errores específicos de la conexión al tenant
$mensaje_status = null; // Mensajes de estado (éxito/error)

// --- 2) Lógica para procesar peticiones (POST) de eliminar y editar facturas ---
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $action = $_POST['action'] ?? '';
    $id_factura_accion = $_POST['id_factura'] ?? null;
    $subdominio_accion = $_POST['subdominio'] ?? '';

    if ($id_factura_accion && $subdominio_accion) {
        try {
            // Conectar al tenant para realizar la acción
            $masterPdo = getMasterPdo();
            $stmt_master = $masterPdo->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio = ? AND Estado = 'Activo'");
            $stmt_master->execute([$subdominio_accion]);
            $cfg = $stmt_master->fetch(PDO::FETCH_ASSOC);

            if ($cfg && !empty($cfg['CadenaConexionDB'])) {
                // Parsea la cadena de conexión del tenant y conecta
                preg_match('/host=([^;]+)/', $cfg['CadenaConexionDB'], $matches_host); $host = $matches_host[1] ?? '';
                preg_match('/dbname=([^;]+)/', $cfg['CadenaConexionDB'], $matches_dbname); $dbname = $matches_dbname[1] ?? '';
                preg_match('/user=([^;]+)/', $cfg['CadenaConexionDB'], $matches_user); $user = $matches_user[1] ?? '';
                preg_match('/password=([^;]+)/', $cfg['CadenaConexionDB'], $matches_pass); $password = $matches_pass[1] ?? '';
                $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
                $pdo_tenant = new PDO($dsn, $user, $password, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);

                if ($action === 'delete_invoice') {
                    // Eliminar la factura
                    $stmt = $pdo_tenant->prepare("DELETE FROM FACTURAS_SOFTWARE WHERE CLAVE = ?");
                    $stmt->execute([$id_factura_accion]);
                    $mensaje_status = "✅ Factura eliminada exitosamente.";
                } elseif ($action === 'edit_invoice') {
                    // Actualizar la factura
                    $concepto_puro = trim($_POST['concepto'] ?? '');
                    $precio = floatval($_POST['precio'] ?? 0);
                    $moneda = $_POST['moneda'] ?? 'USD';
                    $modopago = $_POST['modopago'] ?? 'Transferencia';
                    $fechapago = $_POST['fechapago'] ?? date('Y-m-d');
                    $fechavencimiento = $_POST['fechavencimiento'] ?? '';
                    $estatus = $_POST['estatus'] ?? '';
                    $banco_destino = $_POST['banco_destino'] ?? '';
                    $cuenta_marcsoftware = trim($_POST['cuenta_marcsoftware'] ?? '');
                    $beneficiario_marcsoftware = $_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions';

                    $concepto_final = $concepto_puro;
                    if ($modopago === 'Transferencia') {
                        $concepto_final .= "\n\nDATOS PARA TRANSFERENCIA:\n"
                                          . "Banco: {$banco_destino}\n"
                                          . "Cuenta: {$cuenta_marcsoftware}\n"
                                          . "Beneficiario: {$beneficiario_marcsoftware}";
                    }

                    $stmt_update = $pdo_tenant->prepare("UPDATE FACTURAS_SOFTWARE SET
                        CONCEPTO = ?, PRECIO = ?, MONEDA = ?, MODO_PAGO = ?, FECHA_FACTURA = ?, FECHA_VENCIMIENTO = ?,
                        BANCO_DESTINO = ?, CUENTA_DESTINO = ?, BENEFICIARIO = ?, ESTATUS = ?
                        WHERE CLAVE = ?");

                    $stmt_update->execute([
                        $concepto_final, $precio, $moneda, $modopago, $fechapago, $fechavencimiento,
                        $banco_destino, $cuenta_marcsoftware, $beneficiario_marcsoftware, $estatus,
                        $id_factura_accion
                    ]);
                    $mensaje_status = "✅ Factura actualizada exitosamente.";
                }

                // Redireccionar para evitar reenviar el formulario y refrescar la página
                header("Location: historico_facturas.php?subdominio=" . urlencode($subdominio_accion) . "&status=" . urlencode(substr($mensaje_status, 2)));
                exit();
            }
        } catch (Exception $e) {
            $mensaje_status = "❌ Error en la acción: " . htmlspecialchars($e->getMessage());
        }
    }
}

// --- 3) Lógica para cargar los datos de la página (GET) ---
try {
    $masterPdo = getMasterPdo();
    $stmt = $masterPdo->query("SELECT Subdominio, NombreConsultorio, CadenaConexionDB FROM Consultorios WHERE Estado='Activo'");
    $consultorios = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $consultorios = [];
    $error = "Error al conectar o cargar consultorios: " . htmlspecialchars($e->getMessage());
}

if ($subdominio) {
    $cfg = null;
    foreach ($consultorios as $c) {
        if ($c['Subdominio'] === $subdominio) {
            $cfg = $c;
            break;
        }
    }

    if ($cfg) {
        $dsnString = trim($cfg['CadenaConexionDB']);
        $parts = [];
        $regex = '/^mysql:host=(.*?);dbname=(.*?);user=(.*?);password=****$/';

        if (preg_match($regex, $dsnString, $matches)) {
            $parts['host'] = $matches[1];
            $parts['dbname'] = $matches[2];
            $parts['user'] = $matches[3];
            $parts['password'] = $matches[4];
        }

        if (isset($parts['host']) && isset($parts['dbname'])) {
            $dsn = sprintf("mysql:host=%s;dbname=%s;charset=utf8mb4", $parts['host'], $parts['dbname']);

            try {
                $pdo = new PDO($dsn, $parts['user'], $parts['password'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]);

                $empresaStmt = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
                $empresa = $empresaStmt->fetch();

                $facturasStmt = $pdo->query("SELECT CLAVE, NUMERO_FACTURA, FECHA_FACTURA, FECHA_VENCIMIENTO, PRECIO, MONEDA, ESTATUS, CONCEPTO, MODO_PAGO, BANCO_DESTINO, CUENTA_DESTINO, BENEFICIARIO
                    FROM FACTURAS_SOFTWARE ORDER BY FECHA_FACTURA DESC");
                $facturas = $facturasStmt->fetchAll();

            } catch (PDOException $e) {
                $error_tenant = "Error de conexión o consulta al consultorio '{$subdominio}': " . htmlspecialchars($e->getMessage());
            }
        } else {
            $error_tenant = "Subdominio '{$subdominio}' no encontrado en la base de datos maestra o cadena de conexión inválida.";
        }
    } else {
        $error_tenant = "Subdominio '{$subdominio}' no encontrado o inactivo.";
    }
}

// Mensaje de estado al recargar la página después de una acción
if (isset($_GET['status'])) {
    $mensaje_status = "✅ Factura " . htmlspecialchars($_GET['status']);
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Histórico de Facturas - MarcSoftware Solutions</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
<div class="container mt-4">
  <h2>📃 Histórico de Facturas</h2>
  <p class="text-muted">Selecciona un consultorio para ver y gestionar sus facturas.</p>

  <?php if ($mensaje_status): ?>
    <div class="alert alert-success mt-3"><?= $mensaje_status ?></div>
  <?php endif; ?>
  <?php if (isset($error)): ?>
    <div class="alert alert-danger"><?= $error ?></div>
  <?php endif; ?>
  <?php if (isset($error_tenant)): ?>
    <div class="alert alert-warning"><?= $error_tenant ?></div>
  <?php endif; ?>

  <form method="GET" class="mb-4 d-flex align-items-center">
    <label for="subdominio-select" class="form-label mb-0 me-2">Consultorio:</label>
    <select id="subdominio-select" name="subdominio" onchange="this.form.submit()" class="form-select w-auto">
      <option value="">-- Elegir un Consultorio --</option>
      <?php foreach ($consultorios as $c): ?>
        <option value="<?= htmlspecialchars($c['Subdominio']) ?>" <?= ($subdominio === $c['Subdominio']) ? 'selected' : '' ?>>
          <?= htmlspecialchars($c['NombreConsultorio']) ?>
        </option>
      <?php endforeach; ?>
    </select>
    <?php if ($subdominio): ?>
      <a href="crear_factura_cliente.php?subdominio=<?= urlencode($subdominio) ?>" class="btn btn-success ms-3">
        ➕ Nueva Factura para <?= htmlspecialchars($empresa['NOMBRE'] ?? $subdominio) ?>
      </a>
    <?php endif; ?>
  </form>

  <?php if ($pdo && $empresa): ?>
    <div class="card mb-4">
      <div class="card-header bg-info text-white">Datos del Consultorio: <strong><?= htmlspecialchars($empresa['NOMBRE'] ?? $subdominio) ?></strong></div>
      <div class="card-body">
        <p><strong>Nombre:</strong> <?= htmlspecialchars($empresa['NOMBRE'] ?? 'N/A') ?></p>
        <p><strong>RNC:</strong> <?= htmlspecialchars($empresa['RNC'] ?? 'N/A') ?></p>
        <p><strong>Especialidad:</strong> <?= htmlspecialchars($empresa['ESPECIALIDAD'] ?? 'N/A') ?></p>
        <p><strong>Subdominio:</strong> <?= htmlspecialchars($subdominio) ?></p>
        <p><strong>Teléfono:</strong> <?= htmlspecialchars($empresa['TELEFONO'] ?? 'N/A') ?> ·
           <strong>Email:</strong> <?= htmlspecialchars($empresa['CORREOE'] ?? 'N/A') ?></p>
        <p><strong>Dirección:</strong>
             <?= htmlspecialchars(($empresa['CALLE'] ?? '') . ', ' . ($empresa['MUNICIPIO'] ?? '') . ', ' . ($empresa['PROVINCIA'] ?? '')) ?></p>
      </div>
    </div>

    <?php if (empty($facturas)): ?>
      <div class="alert alert-info">No hay facturas registradas para este consultorio aún.</div>
    <?php else: ?>
      <div class="card">
        <div class="card-header bg-primary text-white">Facturas Emitidas</div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th>Factura #</th>
                  <th>Fecha Emisión</th>
                  <th>Fecha Venc.</th>
                  <th>Monto</th>
                  <th>Estado</th>
                  <th style="width: 250px;">Acciones</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($facturas as $f): ?>
                  <tr>
                    <td><?= htmlspecialchars($f['NUMERO_FACTURA']) ?></td>
                    <td><?= htmlspecialchars($f['FECHA_FACTURA']) ?></td>
                    <td><?= htmlspecialchars($f['FECHA_VENCIMIENTO']) ?></td>
                    <td><?= htmlspecialchars($f['MONEDA']) ?> <?= number_format($f['PRECIO'], 2) ?></td>
                    <td><span class="badge bg-<?= ($f['ESTATUS'] == 'PAGADO') ? 'success' : 'warning' ?>">
                      <?= htmlspecialchars(strtoupper($f['ESTATUS'])) ?>
                    </span></td>
                    <td>
                      <a href="generar_factura_pdf.php?id_factura=<?= $f['CLAVE'] ?>&subdominio=<?= urlencode($subdominio) ?>" target="_blank" class="btn btn-sm btn-info me-1" title="Imprimir Factura">
                        🖨️ PDF
                      </a>
                      <!-- Botón de Editar: Ahora abre el modal y llena los datos -->
                      <button type="button" class="btn btn-sm btn-warning me-1" title="Editar Factura"
                              onclick="openEditModal(<?= htmlspecialchars(json_encode($f)) ?>, '<?= htmlspecialchars($subdominio) ?>')">
                        ✏️ Editar
                      </button>
                      <form method="POST" style="display:inline-block;" onsubmit="return confirm('¿Estás seguro de que deseas eliminar la factura <?= htmlspecialchars($f['NUMERO_FACTURA']) ?>? Esta acción no se puede deshacer.');">
                        <input type="hidden" name="action" value="delete_invoice">
                        <input type="hidden" name="id_factura" value="<?= htmlspecialchars($f['CLAVE']) ?>">
                        <input type="hidden" name="subdominio" value="<?= urlencode($subdominio) ?>">
                        <button type="submit" class="btn btn-sm btn-danger" title="Eliminar Factura">
                          <i class="fas fa-trash-alt"></i>
                        </button>
                      </form>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    <?php endif; ?>
  <?php endif; ?>
</div>

<!-- Modal para Editar Factura -->
<div class="modal fade" id="editInvoiceModal" tabindex="-1" aria-labelledby="editInvoiceModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="editInvoiceModalLabel">Editar Factura</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form method="POST">
        <div class="modal-body">
            <input type="hidden" name="action" value="edit_invoice">
            <input type="hidden" name="id_factura" id="modal-id-factura" value="">
            <input type="hidden" name="subdominio" value="<?= urlencode($subdominio) ?>">

            <p class="text-muted">Editando Factura No. <span id="modal-numero-factura"></span></p>

            <div class="card mb-4">
                <div class="card-header"><h5>💰 Detalles de Facturación</h5></div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Concepto/Servicio:</label>
                        <textarea name="concepto" id="modal-concepto" class="form-control" rows="3" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Precio:</label>
                            <input type="number" name="precio" id="modal-precio" step="0.01" class="form-control" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Moneda:</label>
                            <select name="moneda" id="modal-moneda" class="form-control">
                                <option value="USD">USD (Dólares)</option>
                                <option value="DOP">RD$ (Pesos)</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Modo de Pago:</label>
                            <select name="modopago" id="modal-modopago" class="form-control" onchange="toggleBankFields()">
                                <option value="Transferencia">Transferencia</option>
                                <option value="Efectivo">Efectivo</option>
                                <option value="Cheque">Cheque</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div id="bank-fields" class="card mb-4">
                <div class="card-header"><h5>🏦 Datos Bancarios - MarcSoftware Solutions</h5></div>
                <div class="card-body">
                    <p class="text-info">El cliente transferirá a estas cuentas:</p>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Banco Destino:</label>
                            <select name="banco_destino" id="modal-banco-destino" class="form-control">
                                <option value="">Seleccionar Banco</option>
                                <option value="Banco Popular">Banco Popular</option>
                                <option value="Banreservas">Banreservas</option>
                                <option value="Banco BHD León">Banco BHD León</option>
                                <option value="Banco Scotiabank">Banco Scotiabank</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Cuenta MarcSoftware:</label>
                            <input type="text" name="cuenta_marcsoftware" id="modal-cuenta-marcsoftware" class="form-control" placeholder="Número de cuenta">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Beneficiario:</label>
                            <input type="text" name="beneficiario_marcsoftware" id="modal-beneficiario-marcsoftware" class="form-control" value="MarcSoftware Solutions">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Fecha de Factura:</label>
                    <input type="date" name="fechapago" id="modal-fechapago" class="form-control">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Fecha de Vencimiento:</label>
                    <input type="date" name="fechavencimiento" id="modal-fechavencimiento" class="form-control">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Estatus:</label>
                    <select name="estatus" id="modal-estatus" class="form-control">
                        <option value="PENDIENTE">PENDIENTE</option>
                        <option value="PAGADO">PAGADO</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
          <button type="submit" class="btn btn-success">💾 Guardar Cambios</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
function openEditModal(factura, subdominio) {
    // Limpiar el concepto para mostrar solo la parte que el usuario escribió
    const full_concepto_db = factura.CONCEPTO;
    const bank_details_start = "\n\nDATOS PARA TRANSFERENCIA:\n";
    let concepto_limpio = full_concepto_db;
    if (full_concepto_db.indexOf(bank_details_start) !== -1) {
        concepto_limpio = full_concepto_db.split(bank_details_start)[0].trim();
    }

    // Llenar los campos del modal
    document.getElementById('modal-id-factura').value = factura.CLAVE;
    document.getElementById('modal-numero-factura').innerText = factura.NUMERO_FACTURA;
    document.getElementById('modal-concepto').value = concepto_limpio;
    document.getElementById('modal-precio').value = factura.PRECIO;
    document.getElementById('modal-moneda').value = factura.MONEDA;
    document.getElementById('modal-modopago').value = factura.MODO_PAGO;
    document.getElementById('modal-fechapago').value = factura.FECHA_FACTURA;
    document.getElementById('modal-fechavencimiento').value = factura.FECHA_VENCIMIENTO;
    document.getElementById('modal-estatus').value = factura.ESTATUS;

    // Llenar los campos bancarios si existen
    document.getElementById('modal-banco-destino').value = factura.BANCO_DESTINO || '';
    document.getElementById('modal-cuenta-marcsoftware').value = factura.CUENTA_DESTINO || '';
    document.getElementById('modal-beneficiario-marcsoftware').value = factura.BENEFICIARIO || 'MarcSoftware Solutions';

    // Asegurar que el campo de subdominio oculto tenga el valor correcto
    const subdominioInputs = document.querySelectorAll('input[name="subdominio"]');
    subdominioInputs.forEach(input => {
        if (input.id !== 'modal-subdominio-select') { // No sobreescribir el input del select principal
            input.value = subdominio;
        }
    });

    // Mostrar/ocultar los campos bancarios según el modo de pago
    toggleBankFields();

    // Abrir el modal
    var editModal = new bootstrap.Modal(document.getElementById('editInvoiceModal'));
    editModal.show();
}

function toggleBankFields() {
    const modo = document.getElementById('modal-modopago').value;
    const bankFields = document.getElementById('bank-fields');
    if (bankFields) {
        bankFields.style.display = modo === 'Transferencia' ? 'block' : 'none';
    }
}
</script>
</body>
</html>
