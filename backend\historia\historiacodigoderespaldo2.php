<?php
require_once __DIR__ . '/../config/database.php';

$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA = $_GET['CEDULA'] ?? '';
$CLAVE = $_GET['CLAVE'] ?? null;
$tabla = strtolower($_GET['TABLA'] ?? $_GET['tabla'] ?? ''); // Preferir TABLA, sino tabla
$nuevo = isset($_GET['nuevo']);
$mensaje = '';
$datos = null;
$registros = [];

// Configuración de tablas
$tablas = [
    'complementarias'   => ['campo' => 'COMPLEMENTARIOS', 'max' => 2500, 'tabla_real' => 'COMPLEMENTARIAS', 'titulo' => 'Información Complementaria'],
    'diagnostico'       => ['campo' => 'TEXTO', 'max' => 1400, 'tabla_real' => 'DIAGNOSTICO', 'titulo' => 'Diagnóstico'],
    'seguimiento'       => ['campo' => 'SEGUIMIENTO', 'max' => 6000, 'tabla_real' => 'SEGUIMIENTO', 'titulo' => 'Seguimiento del Paciente'],
    'tratamientos'      => ['campo' => 'TRATAMIENTO', 'max' => 1400, 'tabla_real' => 'TRATAMIENTOS', 'titulo' => 'Tratamientos'],
    'estudios'          => ['campo' => 'DESCRIPCION', 'max' => 3000, 'tabla_real' => 'ESTUDIOS', 'titulo' => 'Estudios Realizados'],
    'informes'          => ['campo' => 'INFORME', 'max' => 3500, 'tabla_real' => 'INFORMES', 'titulo' => 'Informes Médicos'],
    'prescripcion'      => ['campo' => 'DESCRIPCION', 'max' => 3000, 'tabla_real' => 'PRESCRIPCION', 'titulo' => 'Prescripción'],
    'enfermedad_actual' => ['campo' => 'TEXTO', 'max' => 2400, 'tabla_real' => 'ENFERMEDAD_ACTUAL', 'titulo' => 'Enfermedad Actual']
];

if (!isset($tablas[$tabla])) {
    exit("❌ Tabla no reconocida.");
}

$tablaConfig = $tablas[$tabla];
$tablaReal = $tablaConfig['tabla_real'];
$campoTexto = $tablaConfig['campo'];
$maxLength = $tablaConfig['max'];
$tituloModulo = $tablaConfig['titulo'];

// --- Lógica de Robustez: Resolver CLAVEPAC usando CEDULA ---
$clav_pac_real = $CLAVEPAC; // Valor por defecto si no se encuentra o no hay CEDULA

if (!empty($CEDULA)) {
    try {
        $stmtPaciente = $pdo->prepare("SELECT CLAVE FROM PACIENTES WHERE CEDULA = :cedula");
        $stmtPaciente->execute(['cedula' => $CEDULA]);
        $rowPaciente = $stmtPaciente->fetch(PDO::FETCH_ASSOC);

        if ($rowPaciente) {
            $clav_pac_real = $rowPaciente['CLAVE']; // Asigna la CLAVEPAC encontrada en remoto
        } else {
            $mensaje = "⚠️ Advertencia: Paciente con CÉDULA: " . htmlspecialchars($CEDULA) . " no encontrado en PACIENTES. Usando CLAVEPAC original.";
        }
    } catch (PDOException $e) {
        $mensaje = "❌ Error al buscar paciente en PACIENTES: " . $e->getMessage();
    }
}
// --- FIN Lógica de Robustez CLAVEPAC ---


// 1) Eliminar registro
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && isset($_POST['CLAVE_ELIMINAR'])) {
    $claveEliminar = $_POST['CLAVE_ELIMINAR'];
    try {
        $stmt = $pdo->prepare("DELETE FROM {$tablaReal} WHERE CLAVE = ?");
        $stmt->execute([$claveEliminar]);
        $mensaje = "✅ Registro eliminado correctamente.";
        // Redirigir para refrescar la página, sin la CLAVE eliminada
        header("Location: modulo_texto_simple.php?CLAVEPAC=" . urlencode($CLAVEPAC) . "&CEDULA=" . urlencode($CEDULA) . "&TABLA=" . urlencode($tabla));
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar: " . $e->getMessage();
    }
}

// 2) Obtener todos los registros del paciente para la tabla actual
if ($clav_pac_real) {
    $stmt = $pdo->prepare("SELECT CLAVE, FECHA_CAP FROM {$tablaReal} WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHA_CAP DESC");
    $stmt->execute(['CLAVEPAC' => $clav_pac_real]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    
    
  // ——————> Aquí, si no nos pasaron CLAVE en la URL, redirigimos al primero:
    if (!$nuevo && empty($_GET['CLAVE']) && count($registros) > 0) {
        header(
            "Location: modulo_texto_simple.php"
          . "?CLAVEPAC=" . urlencode($CLAVEPAC)
          . "&CEDULA="  . urlencode($CEDULA)
          . "&TABLA="   . urlencode($tabla)
          . "&CLAVE="   . urlencode($registros[0]['CLAVE'])
        );
        exit;
    }
  
}


// 3) Lógica para cargar un registro existente o preparar uno nuevo
if ($nuevo) {
    $datos = []; // Crear un array vacío para un nuevo registro
    $CLAVE = null; // Asegurarse de que CLAVE sea null para la inserción
} elseif ($CLAVE) {
    $stmt = $pdo->prepare("SELECT * FROM {$tablaReal} WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $datos = $stmt->fetch(PDO::FETCH_ASSOC);
} elseif (!empty($registros)) { // Si no hay CLAVE y no es nuevo, carga el primer registro
    $datos = $registros[0];
    $CLAVE = $datos['CLAVE'];
}


// 4) Guardar o actualizar registro
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['guardar_accion'])) { 
    $texto = trim($_POST[$campoTexto] ?? '');
    if ($texto === '') {
        $mensaje = '❌ Debe ingresar información en el campo de texto.';
    } else {
        try {
            if (!empty($_POST['CLAVE'])) { // Es una actualización
                $stmt = $pdo->prepare("UPDATE {$tablaReal} SET {$campoTexto} = :texto, SINCRONIZADO = 0 WHERE CLAVE = :CLAVE");
                $stmt->execute([
                    'texto' => $texto,
                    'CLAVE' => $_POST['CLAVE']
                ]);
                $CLAVE = $_POST['CLAVE'];
                $mensaje = "✅ Registro actualizado correctamente.";
            } else { // Es una nueva inserción
                $stmt = $pdo->prepare("INSERT INTO {$tablaReal} (CLAVEPAC, CEDULA, FECHA_CAP, {$campoTexto}, SINCRONIZADO) VALUES (:CLAVEPAC, :CEDULA, CURRENT_TIMESTAMP, :texto, 0)");
                $stmt->execute([
                    'CLAVEPAC' => $clav_pac_real, // Usar la CLAVEPAC real
                    'CEDULA' => $CEDULA,
                    'texto' => $texto
                ]);
                $CLAVE = $pdo->lastInsertId();
                $mensaje = "✅ Registro insertado correctamente.";
            }
            // Redirigir para cargar el registro recién guardado/actualizado y limpiar el POST
            header("Location: modulo_texto_simple.php?CLAVEPAC=" . urlencode($CLAVEPAC) . "&CEDULA=" . urlencode($CEDULA) . "&TABLA=" . urlencode($tabla) . "&CLAVE=" . urlencode($CLAVE));
            exit;
        } catch (PDOException $e) {
            $mensaje = "❌ Error al guardar: " . $e->getMessage();
        }
    }
}


?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title><?= htmlspecialchars($tituloModulo) ?> – Paciente (<?= htmlspecialchars($CLAVEPAC) ?>)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .form-label { font-weight: bold; }
        .card-header h5 { margin-bottom: 0; }
    </style>
</head>
<body class="p-4 bg-light">
<div class="row">
    <!-- Menú lateral -->
    <div class="col-md-3 border-end">
        <h5 class="mb-3 text-primary">
            HISTORIAL DE <?= mb_strtoupper(str_replace('_', ' ', $tabla)) ?>
        </h5>
        <ul class="list-group">
            <?php if (!empty($registros)): ?>
                <?php foreach ($registros as $r): ?>
                    <?php $active = isset($CLAVE) && $CLAVE == $r['CLAVE']; ?>
                    <li class="list-group-item <?= $active ? 'active' : '' ?>">
                        <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&TABLA=<?= htmlspecialchars($tabla) ?>&CLAVE=<?= htmlspecialchars($r['CLAVE']) ?>"
                           class="text-decoration-none <?= $active ? 'text-white' : '' ?>">
                            <?= htmlspecialchars($r['FECHA_CAP']) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <li class="list-group-item text-muted">
                    No hay registros para <?= htmlspecialchars($tituloModulo) ?>.
                </li>
            <?php endif; ?>
            <li class="list-group-item mt-3">
                <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&TABLA=<?= htmlspecialchars($tabla) ?>&nuevo=1"
                   class="btn btn-sm btn-primary w-100">
                    ➕ Nuevo Registro
                </a>
            </li>
        </ul>
    </div>

    <!-- Contenido principal -->
    <div class="col-md-9">
        <h4 class="mb-4 text-center"><?= htmlspecialchars($tituloModulo) ?></h4>
        <?php if ($mensaje): ?>
            <div class="alert alert-info text-center"><?= htmlspecialchars($mensaje) ?></div>
        <?php endif; ?>

        <!-- Un solo formulario para guardar y eliminar -->
        <form method="post" novalidate>
            <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
            <input type="hidden" name="CEDULA"   value="<?= htmlspecialchars($CEDULA) ?>">
            <input type="hidden" name="TABLA"    value="<?= htmlspecialchars($tabla) ?>">
            <?php if (!empty($CLAVE) && !$nuevo): ?>
                <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($CLAVE) ?>">
            <?php endif; ?>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Contenido del Registro</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="<?= htmlspecialchars($campoTexto) ?>" class="form-label">Texto:</label>
                        <textarea
                            name="<?= htmlspecialchars($campoTexto) ?>"
                            id="<?= htmlspecialchars($campoTexto) ?>"
                            class="form-control"
                            rows="10"
                            maxlength="<?= htmlspecialchars($maxLength) ?>"
                        ><?= htmlspecialchars($datos[$campoTexto] ?? '') ?></textarea>
                    </div>
                </div>
            </div>

            <div class="d-flex gap-2 mt-4 align-items-center">
                <!-- Guardar -->
                <button type="submit" name="guardar_accion" class="btn btn-success btn-lg">  💾 Guardar </button>
                <!-- Nuevo -->
                <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&TABLA=<?= htmlspecialchars($tabla) ?>&nuevo=1"
                   class="btn btn-secondary btn-lg">
                    🆕 Nuevo
                </a>
                <!-- Imprimir -->
                <button type="button" class="btn btn-info btn-lg" onclick="window.print()">  🖨️ Imprimir </button>
               
                <!-- Separador para empujar Eliminar y Volver al final -->
                <div class="flex-grow-1"></div>
                <!-- Eliminar (solo si existe registro) -->
                <?php if (!empty($CLAVE) && !$nuevo): ?>
                    <button type="submit" name="eliminar" value="1"  class="btn btn-danger btn-lg" 
                    onclick="return confirm('¿Deseas eliminar este registro de forma permanente?');">      🗑️ Eliminar  </button>
                <?php endif; ?>
                <!-- Volver -->
                <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg">
                    Volver
                </a>
            </div>
        </form>
    </div>
</div>
</body>
</html>


