<?php
// backend/reportes/api/estadisticas_rapidas.php
header('Content-Type: application/json');
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario'])) {
    http_response_code(401);
    echo json_encode(['error' => 'No autorizado']);
    exit;
}

require_once __DIR__ . '/../../config/database.php';

try {
    $estadisticas = [];
    
    // Total de pacientes
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
    $estadisticas['total_pacientes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Citas de hoy
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE()");
    $estadisticas['citas_hoy'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Ingresos del mes actual
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(PRECIO), 0) as total 
        FROM FACTURAS 
        WHERE MONTH(FECHA) = MONTH(CURDATE()) 
        AND YEAR(FECHA) = YEAR(CURDATE())
        AND ESTATUS = 'A'
    ");
    $ingresos = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $estadisticas['ingresos_mes'] = number_format($ingresos, 2);
    
    // Historias clínicas del mes
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM EXAMENFISICO 
        WHERE MONTH(FECHA_CAP) = MONTH(CURDATE()) 
        AND YEAR(FECHA_CAP) = YEAR(CURDATE())
    ");
    $estadisticas['historias_mes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Citas pendientes
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM CITAMEDIC 
        WHERE FECHACON >= CURDATE() 
        AND ESTATUS IN (3, 6)
    ");
    $estadisticas['citas_pendientes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Pacientes atendidos hoy
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM CITAMEDIC 
        WHERE FECHACON = CURDATE() 
        AND ESTATUS = 0
    ");
    $estadisticas['pacientes_atendidos_hoy'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Facturas pendientes de pago
    $stmt = $pdo->query("
        SELECT COUNT(*) as total, COALESCE(SUM(NOPAGO), 0) as monto_pendiente
        FROM FACTURAS 
        WHERE NOPAGO > 0 
        AND ESTATUS = 'A'
    ");
    $pendientes = $stmt->fetch(PDO::FETCH_ASSOC);
    $estadisticas['facturas_pendientes'] = $pendientes['total'];
    $estadisticas['monto_pendiente'] = number_format($pendientes['monto_pendiente'], 2);
    
    // Nuevos pacientes este mes
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM PACIENTES 
        WHERE MONTH(FECHAINGRESO) = MONTH(CURDATE()) 
        AND YEAR(FECHAINGRESO) = YEAR(CURDATE())
    ");
    $estadisticas['pacientes_nuevos_mes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo json_encode($estadisticas);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error en la base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error interno: ' . $e->getMessage()]);
}
?>
