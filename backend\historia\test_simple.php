<?php
/**
 * Test simple para verificar el guardado
 */

ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once __DIR__ . '/../config/database.php';

echo "<h2>Test Simple de Guardado</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Datos recibidos:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    $CLAVEPAC = $_POST['CLAVEPAC'] ?? '';
    $CEDULA = $_POST['CEDULA'] ?? '';
    $TABLA = $_POST['TABLA'] ?? '';
    $DESCRIPCION = $_POST['DESCRIPCION'] ?? '';
    
    if (!empty($CLAVEPAC) && !empty($CEDULA) && !empty($TABLA) && !empty($DESCRIPCION)) {
        try {
            $tablaReal = strtoupper($TABLA);
            $sql = "INSERT INTO {$tablaReal} (CLAVEPAC, CEDULA, FECHA_CAP, DESCRIPCION, SINCRONIZADO) VALUES (?, ?, CURRENT_TIMESTAMP, ?, 0)";
            
            echo "<h3>SQL a ejecutar:</h3>";
            echo "<code>$sql</code><br>";
            echo "Parámetros: CLAVEPAC=$CLAVEPAC, CEDULA=$CEDULA, DESCRIPCION=$DESCRIPCION<br>";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$CLAVEPAC, $CEDULA, $DESCRIPCION]);
            
            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<div style='color: green;'><h3>✅ ÉXITO!</h3>";
                echo "Registro insertado correctamente con ID: $newId</div>";
            } else {
                echo "<div style='color: red;'><h3>❌ ERROR</h3>";
                echo "No se pudo insertar el registro</div>";
            }
            
        } catch (PDOException $e) {
            echo "<div style='color: red;'><h3>❌ ERROR PDO</h3>";
            echo "Error: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div style='color: orange;'><h3>⚠️ DATOS INCOMPLETOS</h3>";
        echo "Faltan datos requeridos</div>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Simple</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        form { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        input, select, textarea { width: 100%; padding: 8px; margin: 5px 0; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; }
    </style>
</head>
<body>
    <form method="POST">
        <h3>Formulario de Prueba</h3>
        
        <label>CLAVEPAC:</label>
        <input type="number" name="CLAVEPAC" value="1" required>
        
        <label>CEDULA:</label>
        <input type="text" name="CEDULA" value="12345678" required>
        
        <label>TABLA:</label>
        <select name="TABLA" required>
            <option value="estudios">ESTUDIOS</option>
            <option value="informes">INFORMES</option>
            <option value="prescripcion">PRESCRIPCION</option>
            <option value="seguimientos">SEGUIMIENTOS</option>
        </select>
        
        <label>DESCRIPCION:</label>
        <textarea name="DESCRIPCION" rows="4" required>Contenido de prueba para verificar el guardado</textarea>
        
        <button type="submit" name="guardar_accion">Probar Guardado</button>
    </form>
    
    <hr>
    
    <h3>Verificar Tablas</h3>
    <?php
    try {
        $tablas = ['ESTUDIOS', 'INFORMES', 'PRESCRIPCION', 'SEGUIMIENTOS', 'DIAGNOSTICO', 'TRATAMIENTOS'];
        
        foreach ($tablas as $tabla) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$tabla'");
            if ($stmt->rowCount() > 0) {
                echo "✅ Tabla $tabla existe<br>";
                
                // Contar registros
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
                $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                echo "&nbsp;&nbsp;&nbsp;📊 $total registros<br>";
            } else {
                echo "❌ Tabla $tabla NO existe<br>";
            }
        }
    } catch (Exception $e) {
        echo "Error verificando tablas: " . $e->getMessage();
    }
    ?>
    
    <hr>
    
    <h3>Enlaces</h3>
    <a href="modulo_texto_simple.php?CLAVEPAC=1&CEDULA=12345678&TABLA=estudios">Módulo Normal - Estudios</a><br>
    <a href="modulo_texto_simple.php?CLAVEPAC=1&CEDULA=12345678&TABLA=seguimiento">Módulo Normal - Seguimiento</a><br>
</body>
</html>
