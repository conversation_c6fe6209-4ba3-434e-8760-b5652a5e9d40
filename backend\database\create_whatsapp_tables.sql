-- Tabla para almacenar mensajes de WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    direction ENUM('inbound', 'outbound') NOT NULL,
    message_sid VARCHAR(100),
    status ENUM('sent', 'delivered', 'read', 'failed') DEFAULT 'sent',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_direction (direction),
    INDEX idx_created_at (created_at)
);

-- Tabla para almacenar configuraciones de WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla para almacenar plantillas de mensajes
CREATE TABLE IF NOT EXISTS whatsapp_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    subject VARCHAR(200),
    message TEXT NOT NULL,
    variables JSON, -- Para almacenar variables como {nombre}, {fecha}, etc.
    type ENUM('notification', 'reminder', 'confirmation', 'welcome') NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabla para relacionar pacientes con números de WhatsApp
CREATE TABLE IF NOT EXISTS patient_whatsapp (
    id INT AUTO_INCREMENT PRIMARY KEY,
    cedula VARCHAR(20) NOT NULL,
    whatsapp_number VARCHAR(20) NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    verification_code VARCHAR(10),
    verification_expires TIMESTAMP NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_cedula_whatsapp (cedula, whatsapp_number),
    INDEX idx_cedula (cedula),
    INDEX idx_whatsapp_number (whatsapp_number)
);

-- Tabla para almacenar conversaciones en curso
CREATE TABLE IF NOT EXISTS whatsapp_conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone VARCHAR(20) NOT NULL,
    cedula VARCHAR(20),
    current_step VARCHAR(50), -- Para manejar flujos de conversación
    context JSON, -- Para almacenar datos temporales de la conversación
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_cedula (cedula),
    INDEX idx_expires_at (expires_at)
);

-- Insertar plantillas básicas
INSERT INTO whatsapp_templates (name, subject, message, variables, type) VALUES
('cita_aprobada', 'Cita Aprobada', 
'🎉 ¡Tu cita ha sido aprobada!\n\n📅 Fecha: {fecha}\n🕐 Hora: {hora}\n👨‍⚕️ Doctor: {doctor}\n🏥 Consultorio: {consultorio}\n\nPor favor confirma tu asistencia respondiendo *SÍ* o *CONFIRMAR*.\n\nSi no puedes asistir, responde *NO* o *CANCELAR*.', 
'["fecha", "hora", "doctor", "consultorio"]', 
'notification'),

('recordatorio_cita', 'Recordatorio de Cita', 
'⏰ Recordatorio: Tienes una cita mañana\n\n📅 Fecha: {fecha}\n🕐 Hora: {hora}\n👨‍⚕️ Doctor: {doctor}\n\nPor favor confirma tu asistencia respondiendo *SÍ* o *CONFIRMAR*.\n\nSi no puedes asistir, responde *NO* o *CANCELAR*.', 
'["fecha", "hora", "doctor"]', 
'reminder'),

('cita_confirmada', 'Cita Confirmada', 
'✅ Tu cita ha sido confirmada.\n\n📅 Fecha: {fecha}\n🕐 Hora: {hora}\n\n¡Te esperamos!', 
'["fecha", "hora"]', 
'confirmation'),

('cita_cancelada', 'Cita Cancelada', 
'❌ Tu cita del {fecha} a las {hora} ha sido cancelada.\n\nPuedes solicitar una nueva cita cuando gustes.', 
'["fecha", "hora"]', 
'notification'),

('bienvenida_paciente', 'Bienvenida Nuevo Paciente', 
'¡Bienvenido/a {nombre} al Consultorio Médico Agustín! 🏥\n\nYa puedes:\n• Solicitar citas escribiendo *CITA*\n• Ver tus citas con *MIS CITAS*\n• Obtener ayuda con *AYUDA*\n\n¡Estamos aquí para cuidar tu salud!', 
'["nombre"]', 
'welcome');

-- Insertar configuraciones básicas
INSERT INTO whatsapp_config (config_key, config_value, description) VALUES
('reminder_hours_before', '24', 'Horas antes de la cita para enviar recordatorio'),
('max_conversation_duration', '30', 'Minutos máximos para mantener una conversación activa'),
('auto_response_enabled', 'true', 'Habilitar respuestas automáticas'),
('business_hours_start', '08:00', 'Hora de inicio de atención'),
('business_hours_end', '18:00', 'Hora de fin de atención'),
('business_days', 'monday,tuesday,wednesday,thursday,friday', 'Días de atención separados por coma');
