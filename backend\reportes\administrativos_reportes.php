<?php
// backend/reportes/administrativos_reportes.php
session_start();
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: ../../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

$usuario = htmlspecialchars($_SESSION['usuario']);

// Procesar filtros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$usuario_filtro = $_GET['usuario_filtro'] ?? '';
$tabla_filtro = $_GET['tabla_filtro'] ?? '';
$reporte_tipo = $_GET['tipo'] ?? 'sistema';
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reportes Administrativos - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background-color: #f8f9fa; }
        .header-section {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(45deg, #6c757d, #495057);
            color: white;
            border-radius: 15px;
        }
        .status-online { color: #28a745; }
        .status-offline { color: #dc3545; }
        .status-sync { color: #17a2b8; }
        .status-pending { color: #ffc107; }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-cogs me-3"></i>Reportes Administrativos</h1>
                    <p class="mb-0">Estadísticas del sistema y actividad administrativa</p>
                </div>
                <div class="col-md-4 text-end">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-end">
                            <li class="breadcrumb-item"><a href="../../../index.php" class="text-white">Inicio</a></li>
                            <li class="breadcrumb-item"><a href="../../reportes.php" class="text-white">Reportes</a></li>
                            <li class="breadcrumb-item active text-white-50">Administrativos</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filtros -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-filter me-2"></i>Filtros de Reporte</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="fecha_inicio" class="form-label">Fecha Inicio</label>
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" value="<?php echo $fecha_inicio; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="fecha_fin" class="form-label">Fecha Fin</label>
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" value="<?php echo $fecha_fin; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="usuario_filtro" class="form-label">Usuario</label>
                        <select class="form-select" id="usuario_filtro" name="usuario_filtro">
                            <option value="">Todos</option>
                            <?php
                            $stmt = $pdo->query("SELECT DISTINCT usuario FROM usuarios ORDER BY usuario");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                $selected = $usuario_filtro == $row['usuario'] ? 'selected' : '';
                                echo "<option value='{$row['usuario']}' $selected>{$row['usuario']}</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="tabla_filtro" class="form-label">Tabla</label>
                        <select class="form-select" id="tabla_filtro" name="tabla_filtro">
                            <option value="">Todas</option>
                            <option value="PACIENTES" <?php echo $tabla_filtro == 'PACIENTES' ? 'selected' : ''; ?>>Pacientes</option>
                            <option value="CITAMEDIC" <?php echo $tabla_filtro == 'CITAMEDIC' ? 'selected' : ''; ?>>Citas</option>
                            <option value="FACTURAS" <?php echo $tabla_filtro == 'FACTURAS' ? 'selected' : ''; ?>>Facturas</option>
                            <option value="EXAMENFISICO" <?php echo $tabla_filtro == 'EXAMENFISICO' ? 'selected' : ''; ?>>Exámenes</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="tipo" class="form-label">Tipo de Reporte</label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="sistema" <?php echo $reporte_tipo == 'sistema' ? 'selected' : ''; ?>>Sistema</option>
                            <option value="usuarios" <?php echo $reporte_tipo == 'usuarios' ? 'selected' : ''; ?>>Usuarios</option>
                            <option value="sincronizacion" <?php echo $reporte_tipo == 'sincronizacion' ? 'selected' : ''; ?>>Sincronización</option>
                            <option value="actividad" <?php echo $reporte_tipo == 'actividad' ? 'selected' : ''; ?>>Actividad</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-secondary">
                            <i class="fas fa-search me-2"></i>Generar Reporte
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportarExcel()">
                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportarPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Estadísticas del Sistema -->
        <?php
        // Estadísticas generales del sistema
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
        $total_pacientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC");
        $total_citas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        $stmt = $pdo->query("SELECT COUNT(*) as total FROM FACTURAS WHERE ESTATUS = 'A'");
        $total_facturas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios");
        $total_usuarios = $stmt->fetch(PDO::FETCH_ASSOC)['total']; 

        // Registros no sincronizados
        $tablas_sync = ['PACIENTES', 'CITAMEDIC', 'FACTURAS', 'EXAMENFISICO', 'ANTECEDENTES'];
        $total_no_sync = 0;
        foreach ($tablas_sync as $tabla) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla WHERE SINCRONIZADO = 0");
                $total_no_sync += $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            } catch (Exception $e) {
                // Tabla no existe o no tiene campo SINCRONIZADO
            }
        }

        // Actividad reciente (últimos 30 días)
        $stmt = $pdo->query("
            SELECT COUNT(*) as total 
            FROM PACIENTES 
            WHERE FECHAINGRESO >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");
        $pacientes_recientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        $stmt = $pdo->query("
            SELECT COUNT(*) as total 
            FROM CITAMEDIC 
            WHERE FECHACON >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");
        $citas_recientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        ?>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-database fa-2x mb-2"></i>
                        <h4><?php echo $total_pacientes; ?></h4>
                        <p class="mb-0">Total Pacientes</p>
                        <small>+<?php echo $pacientes_recientes; ?> últimos 30 días</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                        <h4><?php echo $total_citas; ?></h4>
                        <p class="mb-0">Total Citas</p>
                        <small>+<?php echo $citas_recientes; ?> últimos 30 días</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><?php echo $total_usuarios; ?></h4>
                        <p class="mb-0">Usuarios del Sistema</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-sync-alt fa-2x mb-2"></i>
                        <h4 class="<?php echo $total_no_sync > 0 ? 'status-pending' : 'status-sync'; ?>">
                            <?php echo $total_no_sync; ?>
                        </h4>
                        <p class="mb-0">Pendientes Sync</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>Distribución de Usuarios por Rol</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="usuariosRolChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>Actividad del Sistema (30 días)</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="actividadChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estado de Sincronización -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-sync me-2"></i>Estado de Sincronización por Tabla</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Tabla</th>
                                <th>Total Registros</th>
                                <th>Sincronizados</th>
                                <th>Pendientes</th>
                                <th>% Sincronizado</th>
                                <th>Estado</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            foreach ($tablas_sync as $tabla) {
                                try {
                                    $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
                                    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                                    
                                    $stmt = $pdo->query("SELECT COUNT(*) as sincronizados FROM $tabla WHERE SINCRONIZADO = 1");
                                    $sincronizados = $stmt->fetch(PDO::FETCH_ASSOC)['sincronizados'];
                                    
                                    $pendientes = $total - $sincronizados;
                                    $porcentaje = $total > 0 ? round(($sincronizados / $total) * 100, 1) : 100;
                                    
                                    $estado_clase = $porcentaje == 100 ? 'success' : ($porcentaje >= 80 ? 'warning' : 'danger');
                                    $estado_texto = $porcentaje == 100 ? 'Completo' : ($porcentaje >= 80 ? 'Parcial' : 'Pendiente');
                                    ?>
                                    <tr>
                                        <td><strong><?php echo $tabla; ?></strong></td>
                                        <td><?php echo $total; ?></td>
                                        <td class="status-sync"><?php echo $sincronizados; ?></td>
                                        <td class="status-pending"><?php echo $pendientes; ?></td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-<?php echo $estado_clase; ?>" 
                                                     style="width: <?php echo $porcentaje; ?>%">
                                                    <?php echo $porcentaje; ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?php echo $estado_clase; ?>">
                                                <?php echo $estado_texto; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php
                                } catch (Exception $e) {
                                    ?>
                                    <tr>
                                        <td><strong><?php echo $tabla; ?></strong></td>
                                        <td colspan="5" class="text-muted">No disponible</td>
                                    </tr>
                                    <?php
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Información del Sistema -->
        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>Información del Sistema</h5>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm">
                            <tr>
                                <td><strong>Versión PHP:</strong></td>
                                <td><?php echo phpversion(); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Servidor Web:</strong></td>
                                <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'No disponible'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Base de Datos:</strong></td>
                                <td><?php 
                                try {
                                    $stmt = $pdo->query("SELECT VERSION() as version");
                                    echo $stmt->fetch(PDO::FETCH_ASSOC)['version'];
                                } catch (Exception $e) {
                                    echo 'No disponible';
                                }
                                ?></td>
                            </tr>
                            <tr>
                                <td><strong>Zona Horaria:</strong></td>
                                <td><?php echo date_default_timezone_get(); ?></td>
                            </tr>
                            <tr>
                                <td><strong>Fecha/Hora Actual:</strong></td>
                                <td><?php echo date('d/m/Y H:i:s'); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-users-cog me-2"></i>Usuarios del Sistema</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Usuario</th>
                                        <th>Rol</th>
                                        <th>Estado</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $stmt = $pdo->query("SELECT usuario, role FROM usuarios ORDER BY usuario");
                                    while ($user = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                        ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($user['usuario']); ?></td>
                                            <td>
                                                <span class="badge bg-<?php 
                                                    echo $user['role'] == 'admin' ? 'danger' : 
                                                         ($user['role'] == 'doctor' ? 'primary' : 'success'); 
                                                ?>">
                                                    <?php echo ucfirst($user['role']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check-circle"></i> Activo
                                                </span>
                                            </td>
                                        </tr>
                                        <?php
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Datos para gráficos
        <?php
        // Datos para gráfico de usuarios por rol
        $stmt = $pdo->query("SELECT role, COUNT(*) as cantidad FROM usuarios GROUP BY role");
        $datos_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $labels_roles = [];
        $data_roles = [];
        foreach ($datos_roles as $rol) {
            $labels_roles[] = ucfirst($rol['role']);
            $data_roles[] = $rol['cantidad'];
        }

        // Datos para gráfico de actividad (últimos 30 días)
        $stmt = $pdo->query("
            SELECT DATE(FECHAINGRESO) as fecha, COUNT(*) as nuevos_pacientes
            FROM PACIENTES
            WHERE FECHAINGRESO >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            GROUP BY DATE(FECHAINGRESO)
            ORDER BY fecha
        ");
        $actividad_pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stmt = $pdo->query("
            SELECT DATE(FECHACON) as fecha, COUNT(*) as nuevas_citas
            FROM CITAMEDIC
            WHERE FECHACON >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            GROUP BY DATE(FECHACON)
            ORDER BY fecha
        ");
        $actividad_citas = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Crear array de fechas de los últimos 30 días
        $fechas_actividad = [];
        $pacientes_actividad = [];
        $citas_actividad = [];

        for ($i = 29; $i >= 0; $i--) {
            $fecha = date('Y-m-d', strtotime("-$i days"));
            $fechas_actividad[] = date('d/m', strtotime($fecha));

            // Buscar datos para esta fecha
            $pacientes_dia = 0;
            $citas_dia = 0;

            foreach ($actividad_pacientes as $ap) {
                if ($ap['fecha'] == $fecha) {
                    $pacientes_dia = $ap['nuevos_pacientes'];
                    break;
                }
            }

            foreach ($actividad_citas as $ac) {
                if ($ac['fecha'] == $fecha) {
                    $citas_dia = $ac['nuevas_citas'];
                    break;
                }
            }

            $pacientes_actividad[] = $pacientes_dia;
            $citas_actividad[] = $citas_dia;
        }
        ?>

        // Gráfico de usuarios por rol
        const ctx1 = document.getElementById('usuariosRolChart').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode($labels_roles); ?>,
                datasets: [{
                    data: <?php echo json_encode($data_roles); ?>,
                    backgroundColor: ['#dc3545', '#007bff', '#28a745', '#ffc107']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Gráfico de actividad
        const ctx2 = document.getElementById('actividadChart').getContext('2d');
        new Chart(ctx2, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($fechas_actividad); ?>,
                datasets: [{
                    label: 'Nuevos Pacientes',
                    data: <?php echo json_encode($pacientes_actividad); ?>,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Nuevas Citas',
                    data: <?php echo json_encode($citas_actividad); ?>,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        function exportarExcel() {
            window.location.href = 'api/exportar_administrativos.php?formato=excel&' + new URLSearchParams(window.location.search);
        }

        function exportarPDF() {
            window.location.href = 'api/exportar_administrativos.php?formato=pdf&' + new URLSearchParams(window.location.search);
        }

        // Actualizar estadísticas cada 30 segundos
        setInterval(function() {
            fetch('api/estadisticas_tiempo_real.php')
                .then(response => response.json())
                .then(data => {
                    // Actualizar contadores si es necesario
                    console.log('Estadísticas actualizadas:', data);
                })
                .catch(error => console.error('Error actualizando estadísticas:', error));
        }, 30000);
    </script>
</body>
</html>
