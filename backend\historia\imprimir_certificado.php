<?php
/**
 * Plantilla de Impresión para Certificados Médicos
 * Genera certificados médicos profesionales con formato oficial
 * 
 * <AUTHOR> de Consultorio
 * @version 1.0
 */

session_start();
require_once __DIR__ . '/../config/database.php';

// Obtener parámetros
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA = $_GET['CEDULA'] ?? '';
$CLAVE = $_GET['CLAVE'] ?? '';

// Parámetros obtenidos correctamente

// Obtener información del paciente
$stmt_paciente = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
$stmt_paciente->execute([$CLAVEPAC]);
$paciente = $stmt_paciente->fetch(PDO::FETCH_ASSOC);

if (!$paciente) {
    die("Error: Paciente no encontrado");
}

// Calcular edad
$edad = '';
if (!empty($paciente['FECHANAC'])) {
    $nac = new DateTime($paciente['FECHANAC']);
    $hoy = new DateTime();
    $edad = $hoy->diff($nac)->y . ' años';
}

// Obtener información de la empresa/consultorio
$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

// Obtener datos del certificado si se especifica CLAVE
$certificado = null;
if ($CLAVE) {
    // Buscar en la tabla de certificados
    $stmt = $pdo->prepare("SELECT * FROM CERTIFICADO WHERE CLAVE = ?");
    $stmt->execute([$CLAVE]);
    $certificado = $stmt->fetch(PDO::FETCH_ASSOC);

    // Certificado obtenido de la base de datos
}

// Función para formatear fecha
function formatearFecha($fecha) {
    if (empty($fecha)) return date('d/m/Y');
    $dt = new DateTime($fecha);
    return $dt->format('d/m/Y');
}

// Función para formatear fecha en texto
function formatearFechaTexto($fecha) {
    if (empty($fecha)) $fecha = date('Y-m-d');
    $dt = new DateTime($fecha);
    $meses = [
        1 => 'enero', 2 => 'febrero', 3 => 'marzo', 4 => 'abril',
        5 => 'mayo', 6 => 'junio', 7 => 'julio', 8 => 'agosto',
        9 => 'septiembre', 10 => 'octubre', 11 => 'noviembre', 12 => 'diciembre'
    ];
    
    $dia = $dt->format('j');
    $mes = $meses[(int)$dt->format('n')];
    $año = $dt->format('Y');
    
    return "$dia de $mes de $año";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificado Médico - <?php echo htmlspecialchars($paciente['NOMBREAPELLIDO']); ?></title>
    
    <!-- Bootstrap para impresión -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Estilos para certificado médico oficial */
        @page {
            size: A4;
            margin: 2cm;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #000;
        }
        
        .certificate-container {
            border: 3px solid #1e40af;
            border-radius: 15px;
            padding: 2rem;
            min-height: 80vh;
            position: relative;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }
        
        .certificate-header {
            text-align: center;
            border-bottom: 2px solid #1e40af;
            padding-bottom: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .clinic-name {
            font-size: 20pt;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
        }
        
        .clinic-details {
            font-size: 11pt;
            color: #666;
            margin-bottom: 1rem;
        }
        
        .certificate-title {
            font-size: 24pt;
            font-weight: bold;
            text-align: center;
            margin: 2rem 0;
            color: #1e40af;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        
        .certificate-number {
            text-align: right;
            font-size: 10pt;
            color: #666;
            margin-bottom: 1rem;
        }
        
        .certificate-body {
            text-align: justify;
            font-size: 13pt;
            line-height: 1.8;
            margin: 2rem 0;
            padding: 1rem;
        }
        
        .patient-name {
            font-weight: bold;
            text-decoration: underline;
            color: #1e40af;
        }
        
        .certificate-content {
            background: #f8f9fa;
            border-left: 4px solid #1e40af;
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }
        
        .signature-section {
            margin-top: 4rem;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: end;
        }
        
        .signature-box {
            text-align: center;
            border-top: 2px solid #000;
            padding-top: 0.5rem;
            margin-top: 3rem;
        }
        
        .doctor-info {
            font-size: 11pt;
            font-weight: bold;
        }
        
        .footer-info {
            position: absolute;
            bottom: 1rem;
            left: 2rem;
            right: 2rem;
            font-size: 9pt;
            color: #666;
            border-top: 1px solid #ccc;
            padding-top: 0.5rem;
            text-align: center;
        }
        
        /* Estilos para pantalla */
        @media screen {
            body {
                background: #f5f5f5;
                padding: 2rem;
            }
            
            .print-container {
                background: white;
                max-width: 21cm;
                margin: 0 auto;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
                border-radius: 8px;
                overflow: hidden;
            }
            
            .no-print {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
            }
        }
        
        /* Ocultar elementos en impresión */
        @media print {
            .no-print {
                display: none !important;
            }
            
            .print-container {
                box-shadow: none;
                margin: 0;
                padding: 0;
            }
        }
        
        /* Decoraciones */
        .certificate-decoration {
            position: absolute;
            opacity: 0.1;
            font-size: 8rem;
            color: #1e40af;
        }
        
        .decoration-top-left {
            top: 1rem;
            left: 1rem;
        }
        
        .decoration-bottom-right {
            bottom: 1rem;
            right: 1rem;
        }
        
        .watermark {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 4rem;
            color: rgba(30, 64, 175, 0.05);
            font-weight: bold;
            z-index: 0;
        }
    </style>
</head>
<body>
    <!-- Botones de control (solo en pantalla) -->
    <div class="no-print">
        <div class="btn-group">
            <button onclick="window.print()" class="btn btn-primary">
                <i class="fas fa-print"></i> Imprimir
            </button>
            <button onclick="window.close()" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cerrar
            </button>
        </div>
    </div>

    <div class="print-container">
        <div class="certificate-container">
            <!-- Decoraciones -->
            <div class="certificate-decoration decoration-top-left">
                <i class="fas fa-user-md"></i>
            </div>
            <div class="certificate-decoration decoration-bottom-right">
                <i class="fas fa-heartbeat"></i>
            </div>
            <div class="watermark">CERTIFICADO MÉDICO</div>
            
            <!-- Número de certificado -->
            <div class="certificate-number">
                Certificado No. <?php echo str_pad($CLAVE ?? rand(1000, 9999), 6, '0', STR_PAD_LEFT); ?>
            </div>
            
            <!-- Header del certificado -->
            <div class="certificate-header">
                <?php if ($empresa): ?>
                    <div class="clinic-name"><?php echo htmlspecialchars($empresa['NOMBRE']); ?></div>

                    <!-- Especialidad del médico -->
                    <?php if (!empty($empresa['ESPECIALIDAD'])): ?>
                        <div style="font-size: 14pt; font-weight: bold; color: #1e40af; margin: 0.5rem 0;">
                            <?php echo htmlspecialchars($empresa['ESPECIALIDAD']); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Exequatur -->
                    <?php if (!empty($empresa['EXECQUATUR'])): ?>
                        <div style="font-size: 12pt; color: #333; margin: 0.3rem 0;">
                            EXEQUATUR <?php echo htmlspecialchars($empresa['EXECQUATUR']); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($empresa['CALLE'])): ?>
                        <div class="clinic-details">
                            <?php echo htmlspecialchars($empresa['CALLE']); ?>
                            <?php if (!empty($empresa['TELEFONO'])): ?>
                                | Tel: <?php echo htmlspecialchars($empresa['TELEFONO']); ?>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="clinic-name">CONSULTORIO MÉDICO</div>
                <?php endif; ?>

                <div style="margin-top: 1rem; font-size: 10pt; color: #666;">
                    Registro Médico Profesional
                </div>
            </div>

            <!-- Título del certificado -->
            <div class="certificate-title">
                CERTIFICADO MÉDICO
            </div>

            <!-- Cuerpo del certificado -->
            <div class="certificate-body">
                <p>El suscrito médico <strong>Dr. <?php echo htmlspecialchars($_SESSION['usuario'] ?? 'Médico Tratante'); ?></strong>, 
                debidamente autorizado para el ejercicio de la medicina, por medio del presente</p>
                
                <p style="text-align: center; font-weight: bold; font-size: 14pt; margin: 1.5rem 0;">
                    CERTIFICA QUE:
                </p>
                
                <p>El/La paciente <span class="patient-name"><?php echo strtoupper(htmlspecialchars($paciente['NOMBREAPELLIDO'])); ?></span>, 
                identificado(a) con cédula de identidad No. <strong><?php echo htmlspecialchars($paciente['CEDULA']); ?></strong>, 
                de <strong><?php echo $edad; ?></strong> de edad, 
                <?php if (!empty($paciente['SEXO'])): ?>
                    de sexo <strong><?php echo $paciente['SEXO'] === 'F' ? 'femenino' : 'masculino'; ?></strong>,
                <?php endif; ?>
                ha sido examinado(a) en esta fecha.</p>
                
                <?php if ($certificado): ?>
                    <div class="certificate-content">
                        <?php if (!empty($certificado['DIAGCERT'])): ?>
                            <p><strong>DIAGNÓSTICO:</strong></p>
                            <p style="margin-left: 20px; margin-bottom: 1rem;"><?php echo nl2br(htmlspecialchars($certificado['DIAGCERT'])); ?></p>
                        <?php endif; ?>

                        <?php if (!empty($certificado['RECOMENDACION'])): ?>
                            <p><strong>RECOMENDACIONES MÉDICAS:</strong></p>
                            <p style="margin-left: 20px; margin-bottom: 1rem;"><?php echo nl2br(htmlspecialchars($certificado['RECOMENDACION'])); ?></p>
                        <?php endif; ?>

                        <?php if (!empty($certificado['FECHA_CAP'])): ?>
                            <p><strong>FECHA DEL CERTIFICADO:</strong> <?php echo date('d/m/Y', strtotime($certificado['FECHA_CAP'])); ?></p>
                        <?php endif; ?>

                        <?php if (empty($certificado['DIAGCERT']) && empty($certificado['RECOMENDACION'])): ?>
                            <p style="color: #666; font-style: italic;">[Certificado médico sin contenido específico registrado]</p>
                        <?php endif; ?>
                    </div>
                <?php else: ?>
                    <div class="certificate-content">
                        <p style="color: #666; font-style: italic; text-align: center; padding: 2rem;">
                            [No se encontró información específica del certificado]<br>
                            <small>Certificado médico general sin diagnóstico específico registrado</small>
                        </p>
                    </div>
                <?php endif; ?>
                
                <p>Se extiende el presente certificado a solicitud del interesado(a) para los fines que estime conveniente.</p>
                
                <p style="text-align: right; margin-top: 2rem;">
                    Dado en la ciudad de _________________, a los <?php echo formatearFechaTexto(date('Y-m-d')); ?>.
                </p>
            </div>

            <!-- Sección de firmas -->
            <div class="signature-section">
                <div>
                    <div class="signature-box">
                        <div class="doctor-info">
                            Dr. <?php echo htmlspecialchars($_SESSION['usuario'] ?? 'Médico Tratante'); ?><br>
                            <small>Firma y Sello del Médico</small>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="signature-box">
                        <div class="doctor-info">
                            <?php echo strtoupper(htmlspecialchars($paciente['NOMBREAPELLIDO'])); ?><br>
                            <small>Firma del Paciente</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Información del pie -->
            <div class="footer-info">
                <div>
                    <strong>Fecha de emisión:</strong> <?php echo formatearFecha(date('Y-m-d')); ?> | 
                    <strong>Hora:</strong> <?php echo date('H:i'); ?> | 
                    <strong>Documento generado por:</strong> Sistema de Historia Clínica
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-imprimir si se especifica en la URL
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('auto_print') === '1') {
            window.onload = function() {
                setTimeout(() => {
                    window.print();
                }, 500);
            };
        }
    </script>
</body>
</html>
