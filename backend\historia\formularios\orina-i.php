<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Orina I" en Resultados de Laboratorio
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Función para obtener los exámenes de orina del paciente
function getExamenesOrina($pdo, $clavePac) {
    $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha 
            FROM EXAMENORINA WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Función para obtener un examen de orina específico
function getExamenOrina($pdo, $id) {
    $sql = "SELECT * FROM EXAMENORINA WHERE CLAVE = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$id]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    if ($action === 'getExamenesOrina') {
        echo json_encode(getExamenesOrina($pdo, $clavePac));
        exit;
    }
    else if ($action === 'getExamenOrina') {
        $id = intval($_POST['id']);
        echo json_encode(getExamenOrina($pdo, $id));
        exit;
    }
    else if ($action === 'saveExamenOrina') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $color = $_POST['color'];
        $olor = $_POST['olor'];
        $aspecto = $_POST['aspecto'];
        $ph = $_POST['ph'];
        $densidad = $_POST['densidad'];
        $proteinas = $_POST['proteinas'];
        $glucosa = $_POST['glucosa'];
        $bilirrubina = $_POST['bilirrubina'];
        $cceton = $_POST['cceton'];
        $hemat = $_POST['hemat'];
        $leuc = $_POST['leuc'];
        $c_hial = $_POST['c_hial'];
        $c_gran = $_POST['c_gran'];
        $bacter = $_POST['bacter'];
        $otros = $_POST['otros'];
        
        try {
            if ($id > 0) {
                // Actualizar registro existente
                $sql = "UPDATE EXAMENORINA SET 
                        COLOR = ?, OLOR = ?, ASPECTO = ?, PH = ?, DENSIDAD = ?, 
                        PROTEINAS = ?, GLUCOSA = ?, BILIRRUBINA = ?, CCETON = ?, 
                        HEMAT = ?, LEUC = ?, C_HIAL = ?, C_GRAN = ?, 
                        BACTER = ?, OTROS = ? 
                        WHERE CLAVE = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $color, $olor, $aspecto, $ph, $densidad, 
                    $proteinas, $glucosa, $bilirrubina, $cceton, 
                    $hemat, $leuc, $c_hial, $c_gran, 
                    $bacter, $otros, $id
                ]);
            } else {
                // Insertar nuevo registro
                $sql = "INSERT INTO EXAMENORINA (
                        CLAVEPAC, COLOR, OLOR, ASPECTO, PH, DENSIDAD, 
                        PROTEINAS, GLUCOSA, BILIRRUBINA, CCETON, 
                        HEMAT, LEUC, C_HIAL, C_GRAN, 
                        BACTER, OTROS) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $clavePac, $color, $olor, $aspecto, $ph, $densidad, 
                    $proteinas, $glucosa, $bilirrubina, $cceton, 
                    $hemat, $leuc, $c_hial, $c_gran, 
                    $bacter, $otros
                ]);
                
                $id = $pdo->lastInsertId();
            }
            
            echo json_encode(['success' => true, 'id' => $id]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteExamenOrina') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "DELETE FROM EXAMENORINA WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Obtener lista de exámenes de orina
$examenes = getExamenesOrina($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Examen de Orina I</h4>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    Registros
                </div>
                <div class="card-body">
                    <div class="lista-registros" id="orinaList">
                        <?php if (count($examenes) > 0): ?>
                            <ul class="list-unstyled m-2">
                                <?php foreach ($examenes as $item): ?>
                                    <li><a href="#" class="orina-item" data-id="<?php echo $item['id']; ?>"><?php echo $item['fecha']; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-center">No hay registros</p>
                        <?php endif; ?>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" id="btnNuevoOrina">Nuevo</button>
                        <button class="btn btn-sm btn-info" id="btnEditarOrina">Editar</button>
                        <button class="btn btn-sm btn-danger" id="btnEliminarOrina">Eliminar</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    Datos del Examen de Orina
                </div>
                <div class="card-body">
                    <form id="formOrina">
                        <input type="hidden" id="orinaId" name="id" value="0">
                        
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="colorOrina">Color</label>
                                <input type="text" class="form-control" id="colorOrina" name="color">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="olorOrina">Olor</label>
                                <input type="text" class="form-control" id="olorOrina" name="olor">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="aspectoOrina">Aspecto</label>
                                <input type="text" class="form-control" id="aspectoOrina" name="aspecto">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="phOrina">pH</label>
                                <input type="text" class="form-control" id="phOrina" name="ph">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="densidadOrina">Densidad</label>
                                <input type="text" class="form-control" id="densidadOrina" name="densidad">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="proteinasOrina">Proteínas</label>
                                <input type="text" class="form-control" id="proteinasOrina" name="proteinas">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="glucosaOrina">Glucosa</label>
                                <input type="text" class="form-control" id="glucosaOrina" name="glucosa">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="bilirrubinaOrina">Bilirrubina</label>
                                <input type="text" class="form-control" id="bilirrubinaOrina" name="bilirrubina">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="ccetonOrina">Cuerpos Cetónicos</label>
                                <input type="text" class="form-control" id="ccetonOrina" name="cceton">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="hematOrina">Hematíes</label>
                                <input type="text" class="form-control" id="hematOrina" name="hemat">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="leucOrina">Leucocitos</label>
                                <input type="text" class="form-control" id="leucOrina" name="leuc">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="c_hialOrina">Cilindros Hialinos</label>
                                <input type="text" class="form-control" id="c_hialOrina" name="c_hial">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-3">
                                <label for="c_granOrina">Cilindros Granulosos</label>
                                <input type="text" class="form-control" id="c_granOrina" name="c_gran">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="bacterOrina">Bacterias</label>
                                <input type="text" class="form-control" id="bacterOrina" name="bacter">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="otrosOrina">Otros</label>
                            <textarea class="form-control" id="otrosOrina" name="otros" rows="2"></textarea>
                        </div>
                        
                        <div class="text-right">
                            <button type="button" class="btn btn-secondary" id="btnCancelarOrina">Cancelar</button>
                            <button type="button" class="btn btn-primary" id="btnGuardarOrina">Guardar</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentOrinaId = 0;
    let editMode = false;
    
    // Inicializar formulario
    disableForm(true);
    $('#btnEditarOrina').prop('disabled', true);
    $('#btnEliminarOrina').prop('disabled', true);
    $('#btnCancelarOrina').hide();
    $('#btnGuardarOrina').hide();
    
    // Si hay registros, seleccionar el primero
    if ($('.orina-item').length > 0) {
        $('.orina-item:first').click();
    }
    
    // Cargar examen de orina al hacer clic en un elemento de la lista
    $(document).on('click', '.orina-item', function(e) {
        e.preventDefault();
        
        $('.orina-item').removeClass('font-weight-bold');
        $(this).addClass('font-weight-bold');
        
        const id = $(this).data('id');
        currentOrinaId = id;
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'getExamenOrina',
                id: id
            },
            dataType: 'json',
            success: function(data) {
                if (data) {
                    $('#orinaId').val(data.CLAVE);
                    $('#colorOrina').val(data.COLOR);
                    $('#olorOrina').val(data.OLOR);
                    $('#aspectoOrina').val(data.ASPECTO);
                    $('#phOrina').val(data.PH);
                    $('#densidadOrina').val(data.DENSIDAD);
                    $('#proteinasOrina').val(data.PROTEINAS);
                    $('#glucosaOrina').val(data.GLUCOSA);
                    $('#bilirrubinaOrina').val(data.BILIRRUBINA);
                    $('#ccetonOrina').val(data.CCETON);
                    $('#hematOrina').val(data.HEMAT);
                    $('#leucOrina').val(data.LEUC);
                    $('#c_hialOrina').val(data.C_HIAL);
                    $('#c_granOrina').val(data.C_GRAN);
                    $('#bacterOrina').val(data.BACTER);
                    $('#otrosOrina').val(data.OTROS);
                    
                    disableForm(true);
                    $('#btnEditarOrina').prop('disabled', false);
                    $('#btnEliminarOrina').prop('disabled', false);
                    $('#btnCancelarOrina').hide();
                    $('#btnGuardarOrina').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al cargar examen de orina:", error);
                alert("Error al cargar examen de orina. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Nuevo examen de orina
    $('#btnNuevoOrina').click(function() {
        clearForm();
        disableForm(false);
        editMode = false;
        
        $('#btnEditarOrina').prop('disabled', true);
        $('#btnEliminarOrina').prop('disabled', true);
        $('#btnCancelarOrina').show();
        $('#btnGuardarOrina').show();
    });
    
    // Editar examen de orina
    $('#btnEditarOrina').click(function() {
        if (currentOrinaId > 0) {
            disableForm(false);
            editMode = true;
            
            $('#btnNuevoOrina').prop('disabled', true);
            $('#btnEliminarOrina').prop('disabled', true);
            $('#btnCancelarOrina').show();
            $('#btnGuardarOrina').show();
        }
    });
    
    // Eliminar examen de orina
    $('#btnEliminarOrina').click(function() {
        if (currentOrinaId > 0) {
            if (confirm("¿Está seguro de eliminar este examen de orina?")) {
                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: {
                        action: 'deleteExamenOrina',
                        id: currentOrinaId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert("Examen de orina eliminado correctamente.");
                            location.reload(); // Recargar para actualizar la lista
                        } else {
                            alert("Error al eliminar examen de orina: " + response.error);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error al eliminar examen de orina:", error);
                        alert("Error al eliminar examen de orina. Consulte la consola para más detalles.");
                    }
                });
            }
        }
    });
    
    // Cancelar
    $('#btnCancelarOrina').click(function() {
        if (editMode && currentOrinaId > 0) {
            // Volver a cargar el examen actual
            $('.orina-item[data-id="' + currentOrinaId + '"]').click();
        } else {
            clearForm();
        }
        
        disableForm(true);
        $('#btnNuevoOrina').prop('disabled', false);
        $('#btnEditarOrina').prop('disabled', currentOrinaId === 0);
        $('#btnEliminarOrina').prop('disabled', currentOrinaId === 0);
        $('#btnCancelarOrina').hide();
        $('#btnGuardarOrina').hide();
    });
    
    // Guardar examen de orina
    $('#btnGuardarOrina').click(function() {
        const formData = {
            action: 'saveExamenOrina',
            id: editMode ? currentOrinaId : 0,
            color: $('#colorOrina').val(),
            olor: $('#olorOrina').val(),
            aspecto: $('#aspectoOrina').val(),
            ph: $('#phOrina').val(),
            densidad: $('#densidadOrina').val(),
            proteinas: $('#proteinasOrina').val(),
            glucosa: $('#glucosaOrina').val(),
            bilirrubina: $('#bilirrubinaOrina').val(),
            cceton: $('#ccetonOrina').val(),
            hemat: $('#hematOrina').val(),
            leuc: $('#leucOrina').val(),
            c_hial: $('#c_hialOrina').val(),
            c_gran: $('#c_granOrina').val(),
            bacter: $('#bacterOrina').val(),
            otros: $('#otrosOrina').val()
        };
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert("Examen de orina guardado correctamente.");
                    location.reload(); // Recargar para actualizar la lista
                } else {
                    alert("Error al guardar examen de orina: " + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al guardar examen de orina:", error);
                alert("Error al guardar examen de orina. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Funciones auxiliares
    function clearForm() {
        $('#formOrina')[0].reset();
        $('#orinaId').val(0);
        currentOrinaId = 0;
    }
    
    function disableForm(disabled) {
        $('#formOrina input, #formOrina textarea').prop('disabled', disabled);
    }
});
</script>
