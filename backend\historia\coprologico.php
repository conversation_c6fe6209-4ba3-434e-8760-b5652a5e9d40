<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$pdo = require_once __DIR__ . '/../config/database.php';

$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA']   ?? '';
$CLAVE    = $_GET['CLAVE']    ?? null; // CLAVE del registro si estamos editando uno específico
$nuevo    = isset($_GET['nuevo']); // Para indicar que se quiere un formulario vacío
$mensaje  = '';
$messageType = '';
$registros = []; // Para almacenar todos los exámenes coprológicos del paciente
$seleccionado = null; // Para el examen que se muestra en el formulario

$tableName = 'COPROLOGICO'; // Nombre de tu tabla de examen coprológico

// --- 1) Eliminar examen coprológico por POST seguro ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && !empty($_POST['CLAVE_ELIMINAR'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM " . $tableName . " WHERE CLAVE = ?");
        $stmt->execute([$_POST['CLAVE_ELIMINAR']]);
        $mensaje = "✅ Examen Coprológico eliminado exitosamente.";
        $messageType = 'success';
        // Redireccionar para recargar la página y no mostrar el registro eliminado
        header("Location: coprologico.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar examen coprológico: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// --- 2) Cargar registros históricos de examen coprológico ---
if ($CLAVEPAC) {
    $stmt = $pdo->prepare("SELECT CLAVE, FECHA_CAP FROM " . $tableName . " WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC");
    $stmt->execute([$CLAVEPAC]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// --- 3) Seleccionar registro actual o nuevo ---
// Definir los campos para inicializar el formulario o cargar datos
$campos_coprologico = [
    'CLAVE' => null,
    'CONSISTENCIA' => '', 'COLOR' => '', 'PARASITO' => '',
    'SANGREOCULTA' => '', 'PH' => '', 'DESCRIPCION' => '',
    // UUID se asume que DB lo maneja
    'CEDULA' => $CEDULA,
    'SINCRONIZADO' => 0
];

// Si se pidió un formulario nuevo
if ($nuevo) {
    $seleccionado = $campos_coprologico; // Inicializa con valores vacíos
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'Ingresando un nuevo examen coprológico.';
    $messageType = 'info';
} elseif ($CLAVE) { // Si se pidió un examen específico por su CLAVE
    $stmt = $pdo->prepare("SELECT * FROM " . $tableName . " WHERE CLAVE = ?");
    $stmt->execute([$CLAVE]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($data_db) {
        $campos_coprologico_temp = $campos_coprologico;
        // Si la columna UUID existe en DB, es bueno que el array $seleccionado la contenga
        if (isset($data_db['UUID'])) {
            $campos_coprologico_temp['UUID'] = '';
        }
        $seleccionado = array_merge($campos_coprologico_temp, $data_db);
        $mensaje = 'Examen Coprológico cargado. Modifique y guarde, o cree uno nuevo.';
        $messageType = 'info';
    } else {
        $seleccionado = $campos_coprologico;
        $seleccionado['CLAVEPAC'] = $CLAVEPAC;
        $seleccionado['CEDULA'] = $CEDULA;
        $mensaje = 'No se encontró el examen coprológico solicitado. Creando uno nuevo.';
        $messageType = 'warning';
    }
} elseif (!empty($registros)) { // Si no se pidió nuevo ni específico, carga el más reciente
    $stmt = $pdo->prepare("SELECT * FROM " . $tableName . " WHERE CLAVE = ?");
    $stmt->execute([$registros[0]['CLAVE']]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    $campos_coprologico_temp = $campos_coprologico;
    if (isset($data_db['UUID'])) {
        $campos_coprologico_temp['UUID'] = '';
    }
    $seleccionado = array_merge($campos_coprologico_temp, $data_db);
    $mensaje = 'Mostrando el examen coprológico más reciente.';
    $messageType = 'info';
} else { // Si no hay registros ni se pidió nuevo, inicializa un formulario vacío
    $seleccionado = $campos_coprologico;
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'No se encontraron exámenes coprológicos. Ingrese los datos.';
    $messageType = 'secondary';
}

// --- 4) Guardar o actualizar examen ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['eliminar'])) {
    $esActualizacion = !empty($_POST['CLAVE']);
    $data = [];

    // Recopilar datos del POST, asegurando saneamiento básico
    foreach ($campos_coprologico as $campo => $valor_defecto) {
        if ($campo === 'CLAVE' || $campo === 'CLAVEPAC' || $campo === 'CEDULA' || $campo === 'SINCRONIZADO' || $campo === 'UUID') {
            continue; // UUID no se procesa desde PHP
        }
        $val = $_POST[$campo] ?? '';

        // Saneamiento específico según el tipo de columna en la base de datos
        if ($campo === 'PH') {
            $data[$campo] = filter_var($val, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) ?: null; // decimal(5,2)
        } else { // varchar
            $data[$campo] = htmlspecialchars(trim($val)) ?: null;
        }
    }

    // Campos de control adicionales
    $data['CLAVEPAC'] = $CLAVEPAC;
    $data['CEDULA'] = $CEDULA;
    $data['SINCRONIZADO'] = 0;

    try {
        if ($esActualizacion) {
            $data['CLAVE'] = $_POST['CLAVE'];
            $sets = [];
            foreach ($data as $key => $value) {
                if ($key !== 'CLAVE') {
                    $sets[] = "$key = :$key";
                }
            }
            $sql = "UPDATE " . $tableName . " SET " . implode(', ', $sets) . " WHERE CLAVE = :CLAVE";
        } else {
            $cols = implode(', ', array_keys($data));
            $phs  = ':' . implode(', :', array_keys($data));
            $sql  = "INSERT INTO " . $tableName . " ($cols) VALUES ($phs)";
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($data);

        $newClave = $esActualizacion ? $_POST['CLAVE'] : $pdo->lastInsertId();
        
        $mensaje = '✅ Examen Coprológico guardado exitosamente.';
        $messageType = 'success';
        header("Location: coprologico.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$newClave");
        exit;

    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar el examen coprológico: " . $e->getMessage();
        $messageType = 'danger';
        error_log("Error saving coprologico: " . $e->getMessage() . " - SQL: " . $sql);
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examen Coprológico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .form-container { padding: 20px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 15px rgba(0,0,0,0.1); margin-top: 20px; }
        .form-section { border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin-bottom: 20px; background-color: #fefefe; }
        .form-section h4 { margin-bottom: 15px; color: #0d6efd; border-bottom: 1px solid #0d6efd; padding-bottom: 5px; }
        .list-group-item.active { background-color: #0d6efd !important; border-color: #0d6efd !important; }
        .list-group-item.active a { color: #fff !important; }
        .decimal-input { text-align: right; }
    </style>
</head>
<body class="p-4">
    <div class="row">
        <div class="col-md-3 border-end">
            <h5 class="mb-3">Historial de Exámenes Coprológicos</h5>
            <ul class="list-group">
                <?php if (empty($registros)): ?>
                    <li class="list-group-item text-muted">No hay registros anteriores.</li>
                <?php endif; ?>
                <?php foreach ($registros as $r): ?>
                    <?php
                    $fecha_formateada = (new DateTime($r['FECHA_CAP']))->format('Y-m-d H:i');
                    $active = isset($seleccionado['CLAVE']) && $seleccionado['CLAVE'] == $r['CLAVE'];
                    ?>
                    <li class="list-group-item <?= $active ? 'active' : '' ?>">
                        <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($r['CLAVE']) ?>"
                           class="text-decoration-none <?= $active ? 'text-white' : '' ?>">
                            <?= htmlspecialchars($fecha_formateada) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <div class="col-md-9 form-container">
            <h4 class="mb-3">Examen Coprológico</h4>
            <p class="text-muted">Paciente: <strong><?= htmlspecialchars($CLAVEPAC) ?></strong> (CI: <strong><?= htmlspecialchars($CEDULA) ?></strong>)</p>

            <?php if ($mensaje): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($mensaje) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form method="post" novalidate>
                <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                <input type="hidden" name="CEDULA" value="<?= htmlspecialchars($CEDULA) ?>">
                <?php if (!empty($seleccionado['CLAVE'])): ?>
                    <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                    <input type="hidden" name="CLAVE_ELIMINAR" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                <?php endif; ?>
                <input type="hidden" name="SINCRONIZADO" value="<?= htmlspecialchars($seleccionado['SINCRONIZADO']) ?>">

                <div class="form-section">
                    <h4>Resultados</h4>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="CONSISTENCIA" class="form-label">Consistencia</label>
                            <input type="text" class="form-control" id="CONSISTENCIA" name="CONSISTENCIA" value="<?= htmlspecialchars($seleccionado['CONSISTENCIA'] ?? '') ?>" maxlength="350">
                        </div>
                        <div class="col-md-6">
                            <label for="COLOR" class="form-label">Color</label>
                            <input type="text" class="form-control" id="COLOR" name="COLOR" value="<?= htmlspecialchars($seleccionado['COLOR'] ?? '') ?>" maxlength="350">
                        </div>
                        <div class="col-md-6">
                            <label for="PARASITO" class="form-label">Parásitos</label>
                            <input type="text" class="form-control" id="PARASITO" name="PARASITO" value="<?= htmlspecialchars($seleccionado['PARASITO'] ?? '') ?>" maxlength="350">
                        </div>
                        <div class="col-md-6">
                            <label for="SANGREOCULTA" class="form-label">Sangre Oculta</label>
                            <input type="text" class="form-control" id="SANGREOCULTA" name="SANGREOCULTA" value="<?= htmlspecialchars($seleccionado['SANGREOCULTA'] ?? '') ?>" maxlength="350">
                        </div>
                        <div class="col-md-6">
                            <label for="PH" class="form-label">pH</label>
                            <input type="text" class="form-control decimal-input" id="PH" name="PH" value="<?= htmlspecialchars($seleccionado['PH'] ?? '') ?>">
                        </div>
                        <div class="col-12">
                            <label for="DESCRIPCION" class="form-label">Descripción / Otros Hallazgos</label>
                            <textarea class="form-control" id="DESCRIPCION" name="DESCRIPCION" rows="3" maxlength="500"><?= htmlspecialchars($seleccionado['DESCRIPCION'] ?? '') ?></textarea>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2 mt-3">
                    <button type="submit" name="accion" value="guardar" class="btn btn-success">💾 Guardar</button>
                    <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&nuevo=1" class="btn btn-secondary">🆕 Nuevo</a>
                    <?php if (!empty($seleccionado['CLAVE'])): ?>
                        <button type="submit" name="eliminar" value="1" class="btn btn-danger" onclick="return confirm('¿Deseas eliminar este examen coprológico?');">🗑️ Eliminar</button>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">🖨️ Imprimir</button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Función para formatear el campo decimal PH (decimal 5,2)
        function formatearDecimalPH(input) {
            let val = input.value.replace(/[^0-9.]/g, '');
            val = val.replace(/\.(?=.*\.)/g, ''); // Solo un punto decimal
            const endsWithDot = val.endsWith('.');
            const [intRaw = '', decRaw = ''] = val.split('.');
            const intPart = intRaw.slice(0, 3); // PH es decimal(5,2), 3 enteros
            const decPart = decRaw.slice(0, 2); // 2 decimales
            input.value = endsWithDot ? `${intPart}.` : (decPart ? `${intPart}.${decPart}` : intPart);
        }

        // Función para validar el formulario antes de enviar
        function validarFormulario(e) {
            let ok = true;

            // Validar campo PH
            const phInput = document.getElementById('PH');
            let phValue = phInput.value.trim();
            const phRegex = /^\d{1,3}(\.\d{1,2})?$/; // decimal(5,2): hasta 3 enteros y 2 decimales

            if (phValue && !phRegex.test(phValue)) {
                phInput.setCustomValidity('Formato de pH inválido: hasta 3 enteros y 2 decimales.');
                phInput.reportValidity();
                ok = false;
            } else {
                phInput.setCustomValidity('');
            }
            
            // Validar longitudes de campos de texto (aunque maxlength ya ayuda, esto refuerza)
            document.querySelectorAll('input[type="text"][maxlength], textarea[maxlength]').forEach(input => {
                if (input.value.length > parseInt(input.maxLength)) {
                    input.setCustomValidity(`Este campo excede la longitud máxima de ${input.maxLength} caracteres.`);
                    input.reportValidity();
                    ok = false;
                } else {
                    input.setCustomValidity('');
                }
            });

            if (!ok) e.preventDefault();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Adjuntar formateador y validador para el campo PH
            const phInput = document.getElementById('PH');
            if (phInput) {
                phInput.setAttribute('maxlength', '6'); // 3 enteros + 1 punto + 2 decimales
                phInput.addEventListener('input', () => formatearDecimalPH(phInput));
                formatearDecimalPH(phInput); // Formatea el valor inicial
            }

            document.querySelector('form').addEventListener('submit', validarFormulario);
        });
    </script>
</body>
</html>