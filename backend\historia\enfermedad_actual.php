<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA = $_GET['CEDULA'] ?? '';
$CLAVE = $_GET['CLAVE'] ?? null;
$nuevo = isset($_GET['nuevo']);
$mensaje = "";
$registros = [];
$seleccionado = null;

// 1. Obtener registros para el paciente
if ($CLAVEPAC) {
    $stmt = $pdo->prepare("SELECT * FROM ENFERMEDAD_ACTUAL WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHA_CAP DESC");
    $stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 2. Si se está creando un nuevo registro, NO cargar datos previos
if ($nuevo) {
    $seleccionado = null;
} elseif ($CLAVE) {
    $stmt = $pdo->prepare("SELECT * FROM ENFERMEDAD_ACTUAL WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $seleccionado = $stmt->fetch(PDO::FETCH_ASSOC);
} elseif (count($registros) > 0) {
    $seleccionado = $registros[0];
}

// 3. GUARDAR
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $CLAVE_POST = $_POST['CLAVE'] ?? null;
    $TEXTO = trim($_POST['TEXTO'] ?? '');

    if ($CLAVE_POST) {
        // Actualizar
        $sql = "UPDATE ENFERMEDAD_ACTUAL SET TEXTO = :TEXTO, SINCRONIZADO = 0 WHERE CLAVE = :CLAVE";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            'TEXTO' => $TEXTO,
            'CLAVE' => $CLAVE_POST
        ]);
        header("Location: enfermedad_actual.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$CLAVE_POST");
        exit;
    } else {
        // Insertar
        $sql = "INSERT INTO ENFERMEDAD_ACTUAL (CLAVEPAC, CEDULA, TEXTO) VALUES (:CLAVEPAC, :CEDULA, :TEXTO)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            'CLAVEPAC' => $CLAVEPAC,
            'CEDULA' => $CEDULA,
            'TEXTO' => $TEXTO
        ]);
        $nuevo_id = $pdo->lastInsertId();
        header("Location: enfermedad_actual.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$nuevo_id");
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Enfermedad Actual</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="p-4">
  <div class="row">
    <!-- Lista de fechas -->
    <div class="col-md-3 border-end">
      <h5>FECHAS</h5>
      <ul class="list-group">
        <?php foreach ($registros as $r): ?>
          <li class="list-group-item <?= ($seleccionado && $seleccionado['CLAVE'] == $r['CLAVE']) ? 'active' : '' ?>">
            <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&CLAVE=<?= $r['CLAVE'] ?>"
               class="text-decoration-none <?= ($seleccionado && $seleccionado['CLAVE'] == $r['CLAVE']) ? 'text-white' : '' ?>">
              <?= $r['FECHA_CAP'] ?>
            </a>
          </li>
        <?php endforeach; ?>
      </ul>
    </div>

    <!-- Formulario -->
    <div class="col-md-9">
      <h4>Enfermedad Actual</h4>

      <form method="post">
        <?php if ($seleccionado): ?>
          <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
        <?php endif; ?>

        <div class="mb-3">
          <label for="TEXTO">Descripción</label>
          <textarea name="TEXTO" id="TEXTO" rows="8" class="form-control" required><?= htmlspecialchars($seleccionado['TEXTO'] ?? '') ?></textarea>
        </div>

        <button type="submit" class="btn btn-success">Guardar</button>
        <a href="?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>&nuevo=1" class="btn btn-secondary">Nuevo</a>
      </form>
    </div>
  </div>
</body>
</html>
