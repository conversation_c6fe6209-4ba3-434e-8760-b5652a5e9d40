<?php
namespace Picqer\Barcode;

class BarcodeGeneratorPNG extends BarcodeGenerator
{
    public function getBarcode($code, $type, $widthFactor = 2, $totalHeight = 30, $color = [0, 0, 0])
    {
        if ($type !== self::TYPE_CODE_128) {
            throw new \Exception('Only TYPE_CODE_128 is supported in this version.');
        }

        $im = imagecreate(strlen($code) * $widthFactor + 60, $totalHeight);
        $white = imagecolorallocate($im, 255, 255, 255);
        $black = imagecolorallocate($im, $color[0], $color[1], $color[2]);

        for ($i = 0; $i < strlen($code); $i++) {
            $x = $i * $widthFactor + 30;
            if ($code[$i] % 2 == 0) {
                imagefilledrectangle($im, $x, 0, $x + $widthFactor - 1, $totalHeight, $black);
            }
        }

        ob_start();
        imagepng($im);
        $imageData = ob_get_clean();
        imagedestroy($im);

        return $imageData;
    }
}
