<?php
require_once '../config/database.php';
$pdo = include '../config/database.php';

$clave = $_GET['clavepac'] ?? null;
if (!$clave) {
    echo json_encode(["status" => "error", "message" => "ID del paciente requerido"]);
    exit;
}

try {
    // Datos del paciente con nombre de ARS
    $sql = "SELECT p.CLAVE, CONCAT(p.NOMBRES, ' ', p.APELLIDOS) AS nombre,
                   p.CEDULA, p.CELULAR AS telefono, a.NOMBRE AS ars_nombre, a.CLAVE AS ars_clave
            FROM PACIENTES p
            LEFT JOIN ASEGURADORA a ON p.ARS = a.CLAVE
            WHERE p.CLAVE = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clave]);
    $paciente = $stmt->fetch();

    if (!$paciente) {
        echo json_encode(["status" => "error", "message" => "Paciente no encontrado"]);
        exit;
    }

    // Facturas del paciente
    $sql2 = "SELECT CLAVE, NUMFACT, FECHA, CONCEPTO, PRECIO, PAGADO, VALORRECLAMADO, ESTATUS
             FROM FACTURAS WHERE CLAVEPAC = ? ORDER BY NUMFACT DESC";
    $stmt2 = $pdo->prepare($sql2);
    $stmt2->execute([$clave]);
    $facturas = $stmt2->fetchAll();

    echo json_encode([
        "status" => "success",
        "paciente" => $paciente,
        "facturas" => $facturas
    ]);
} catch (PDOException $e) {
    echo json_encode(["status" => "error", "message" => $e->getMessage()]);
}
