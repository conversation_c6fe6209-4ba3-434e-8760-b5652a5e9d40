<?php
require_once 'backend/config/database.php';

// Verificar si el token es válido
$token = $_GET['token'] ?? '';
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE reset_token = ? AND reset_expira > NOW()");
$stmt->execute([$token]);
$user = $stmt->fetch();

if (!$user) {
    die("El enlace de recuperación no es válido o ha expirado.");
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restablecer Contraseña</title>
    <link rel="stylesheet" href="public/assets/css/styles.css">
</head>
<body>
    <h1>Restablecer Contraseña</h1>
    <form action="actualizar_password.php" method="POST">
        <input type="hidden" name="token" value="<?php echo $token; ?>">
        <label for="password">Nueva Contraseña:</label>
        <input type="password" id="password" name="password" required>
        <button type="submit">Actualizar Contraseña</button>
    </form>
</body>
</html>
