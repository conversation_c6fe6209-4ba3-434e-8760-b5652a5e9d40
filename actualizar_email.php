<?php
session_start();
require_once 'backend/config/database.php';

if (!isset($_SESSION['usuario']) || $_SESSION['rol'] !== 'paciente') {
    header('Location: login.php');
    exit;
}

$cedula = $_SESSION['cedula'] ?? $_SESSION['usuario'];
$mensaje = '';
$correoActual = '';

// Obtener el correo actual del paciente
$stmt = $pdo->prepare("SELECT ECORREO FROM PACIENTES WHERE CEDULA = ?");
$stmt->execute([$cedula]);
$paciente = $stmt->fetch(PDO::FETCH_ASSOC);
$correoActual = $paciente['ECORREO'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nuevoCorreo = trim($_POST['email']);

    if (!filter_var($nuevoCorreo, FILTER_VALIDATE_EMAIL)) {
        $mensaje = "❌ El correo ingresado no es válido.";
    } else {
        // Actualizar en PACIENTES
        $stmt = $pdo->prepare("UPDATE PACIENTES SET ECORREO = ? WHERE CEDULA = ?");
        $stmt->execute([$nuevoCorreo, $cedula]);

        // También lo actualizamos en USUARIOS (opcional)
        $stmt2 = $pdo->prepare("UPDATE usuarios SET email = ? WHERE cedula = ?");
        $stmt2->execute([$nuevoCorreo, $cedula]);

        $mensaje = "✅ Correo actualizado correctamente.";
        $correoActual = $nuevoCorreo;
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Actualizar Correo Electrónico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container mt-5">
    <h3 class="mb-4">Actualizar Correo Electrónico</h3>

    <?php if ($mensaje): ?>
        <div class="alert alert-info"><?php echo $mensaje; ?></div>
    <?php endif; ?>

    <form method="POST">
        <div class="mb-3">
            <label for="email" class="form-label">Correo actual:</label>
            <input type="email" class="form-control" id="email" name="email"
                   value="<?php echo htmlspecialchars($correoActual); ?>" required>
        </div>
        <button type="submit" class="btn btn-primary">Actualizar</button>
        <a href="paciente_panel.php" class="btn btn-secondary">Volver</a>
    </form>
</div>
</body>
</html>

