<?php
// backend/reportes/citas_reportes.php
session_start();
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: ../../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../lib/ValidadorReportes.php';
date_default_timezone_set('America/Santo_Domingo');

// Crear validador
$validador = new ValidadorReportes();
$errores_validacion = [];
$advertencias_validacion = [];

$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

$usuario = htmlspecialchars($_SESSION['usuario']);

// Procesar filtros con validación
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-t');
$doctor = $validador->sanitizarTexto($_GET['doctor'] ?? '');
$estatus = $_GET['estatus'] ?? '';
$reporte_tipo = $_GET['tipo'] ?? 'resumen';

// Validar parámetros si se enviaron
if ($_SERVER['REQUEST_METHOD'] === 'GET' && !empty($_GET)) {
    if (!$validador->validarFechas($fecha_inicio, $fecha_fin)) {
        $errores_validacion = $validador->obtenerErrores();
    }

    if (!$validador->validarEstadoCita($estatus)) {
        $errores_validacion = array_merge($errores_validacion, $validador->obtenerErrores());
    }

    $advertencias_validacion = $validador->obtenerAdvertencias();
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reportes de Citas - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background-color: #f8f9fa; }
        .header-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-calendar-alt me-3"></i>Reportes de Citas</h1>
                    <p class="mb-0">Análisis completo de citas médicas y productividad</p>
                </div>
                <div class="col-md-4 text-end">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-end">
                            <li class="breadcrumb-item"><a href="../../../index.php" class="text-white">Inicio</a></li>
                            <li class="breadcrumb-item"><a href="../../reportes.php" class="text-white">Reportes</a></li>
                            <li class="breadcrumb-item active text-white-50">Citas</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Mostrar errores y advertencias de validación -->
        <?php if (!empty($errores_validacion)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Errores de Validación:</h6>
            <ul class="mb-0">
                <?php foreach ($errores_validacion as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($advertencias_validacion)): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <h6><i class="fas fa-info-circle me-2"></i>Advertencias:</h6>
            <ul class="mb-0">
                <?php foreach ($advertencias_validacion as $advertencia): ?>
                    <li><?php echo htmlspecialchars($advertencia); ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Filtros -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-filter me-2"></i>Filtros de Reporte</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3" id="form-filtros-citas" data-validar="citas">
                    <div class="col-md-3">
                        <label for="fecha_inicio" class="form-label">Fecha Inicio</label>
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" value="<?php echo $fecha_inicio; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="fecha_fin" class="form-label">Fecha Fin</label>
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" value="<?php echo $fecha_fin; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="doctor" class="form-label">Doctor</label>
                        <select class="form-select" id="doctor" name="doctor">
                            <option value="">Todos</option>
                            <option value="1" <?php echo $doctor == '1' ? 'selected' : ''; ?>>Dr. Principal</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="estatus" class="form-label">Estado</label>
                        <select class="form-select" id="estatus" name="estatus">
                            <option value="">Todos</option>
                            <option value="0" <?php echo $estatus == '0' ? 'selected' : ''; ?>>Atendido</option>
                            <option value="1" <?php echo $estatus == '1' ? 'selected' : ''; ?>>Canceló</option>
                            <option value="2" <?php echo $estatus == '2' ? 'selected' : ''; ?>>No asistió</option>
                            <option value="3" <?php echo $estatus == '3' ? 'selected' : ''; ?>>Citado</option>
                            <option value="4" <?php echo $estatus == '4' ? 'selected' : ''; ?>>Llegó tarde</option>
                            <option value="5" <?php echo $estatus == '5' ? 'selected' : ''; ?>>Esperando</option>
                            <option value="6" <?php echo $estatus == '6' ? 'selected' : ''; ?>>Pendiente aprobación</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="tipo" class="form-label">Tipo de Reporte</label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="resumen" <?php echo $reporte_tipo == 'resumen' ? 'selected' : ''; ?>>Resumen</option>
                            <option value="detallado" <?php echo $reporte_tipo == 'detallado' ? 'selected' : ''; ?>>Detallado</option>
                            <option value="productividad" <?php echo $reporte_tipo == 'productividad' ? 'selected' : ''; ?>>Productividad</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Generar Reporte
                        </button>
                        <button type="button" class="btn btn-success" onclick="exportarExcel()">
                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportarPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Estadísticas Rápidas -->
        <?php if (empty($errores_validacion)): ?>
        <div class="row mb-4">
            <?php
            // Consultas para estadísticas
            $where_clause = "WHERE FECHACON BETWEEN ? AND ?";
            $params = [$fecha_inicio, $fecha_fin];

            if ($doctor) {
                $where_clause .= " AND NUMDOCTOR = ?";
                $params[] = $doctor;
            }
            if ($estatus !== '') {
                $where_clause .= " AND ESTATUS = ?";
                $params[] = $estatus;
            }

            // Verificar si hay datos antes de mostrar estadísticas
            try {

            // Total de citas
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_clause");
            $stmt->execute($params);
            $total_citas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Si ya hay filtro de estatus, usar los datos filtrados
            if ($estatus !== '') {
                $citas_atendidas = ($estatus == 0) ? $total_citas : 0;
                $citas_no_asistidas = ($estatus == 2) ? $total_citas : 0;
                $citas_canceladas = ($estatus == 1) ? $total_citas : 0;
                $citas_citadas = ($estatus == 3) ? $total_citas : 0;
                $citas_tarde = ($estatus == 4) ? $total_citas : 0;
                $citas_esperando = ($estatus == 5) ? $total_citas : 0;
                $citas_pendientes = ($estatus == 6) ? $total_citas : 0;
            } else {
                // Construir consultas base sin el filtro de estatus
                $where_base = "WHERE FECHACON BETWEEN ? AND ?";
                $params_base = [$fecha_inicio, $fecha_fin];

                if ($doctor) {
                    $where_base .= " AND NUMDOCTOR = ?";
                    $params_base[] = $doctor;
                }

                // Citas atendidas (0)
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_base AND ESTATUS = ?");
                $stmt->execute(array_merge($params_base, [0]));
                $citas_atendidas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

                // Citas no asistidas (2)
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_base AND ESTATUS = ?");
                $stmt->execute(array_merge($params_base, [2]));
                $citas_no_asistidas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

                // Citas canceladas (1)
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_base AND ESTATUS = ?");
                $stmt->execute(array_merge($params_base, [1]));
                $citas_canceladas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

                // Citas citadas (3)
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_base AND ESTATUS = ?");
                $stmt->execute(array_merge($params_base, [3]));
                $citas_citadas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

                // Citas llegó tarde (4)
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_base AND ESTATUS = ?");
                $stmt->execute(array_merge($params_base, [4]));
                $citas_tarde = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

                // Citas esperando (5)
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_base AND ESTATUS = ?");
                $stmt->execute(array_merge($params_base, [5]));
                $citas_esperando = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

                // Citas pendientes aprobación (6)
                $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_base AND ESTATUS = ?");
                $stmt->execute(array_merge($params_base, [6]));
                $citas_pendientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            }

            // Tasa de asistencia
            $tasa_asistencia = $total_citas > 0 ? round(($citas_atendidas / $total_citas) * 100, 1) : 0;
            ?>
            
            <div class="col-md-3">
                <div class="card text-white bg-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-check fa-2x mb-2"></i>
                        <h4><?php echo $total_citas; ?></h4>
                        <p class="mb-0">Total Citas</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-success">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h4><?php echo $citas_atendidas; ?></h4>
                        <p class="mb-0">Atendidas</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-user-times fa-2x mb-2"></i>
                        <h4><?php echo $citas_no_asistidas; ?></h4>
                        <p class="mb-0">No Asistieron</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-white bg-info">
                    <div class="card-body text-center">
                        <i class="fas fa-percentage fa-2x mb-2"></i>
                        <h4><?php echo $tasa_asistencia; ?>%</h4>
                        <p class="mb-0">Tasa Asistencia</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Estadísticas Detalladas por Estado -->
        <?php if ($estatus === ''): // Solo mostrar si no hay filtro de estado ?>
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Distribución por Estado de Cita</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-success me-2">0</span>
                                    <strong>Atendido:</strong>
                                    <span class="ms-auto"><?php echo $citas_atendidas; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-warning me-2">1</span>
                                    <strong>Canceló:</strong>
                                    <span class="ms-auto"><?php echo $citas_canceladas; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-danger me-2">2</span>
                                    <strong>No asistió:</strong>
                                    <span class="ms-auto"><?php echo $citas_no_asistidas; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-primary me-2">3</span>
                                    <strong>Citado:</strong>
                                    <span class="ms-auto"><?php echo $citas_citadas; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-info me-2">4</span>
                                    <strong>Llegó tarde:</strong>
                                    <span class="ms-auto"><?php echo $citas_tarde; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-secondary me-2">5</span>
                                    <strong>Esperando:</strong>
                                    <span class="ms-auto"><?php echo $citas_esperando; ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="d-flex align-items-center mb-2">
                                    <span class="badge bg-dark me-2">6</span>
                                    <strong>Pendiente aprobación:</strong>
                                    <span class="ms-auto"><?php echo $citas_pendientes; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Gráficos -->
        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>Distribución por Estado</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="estatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>Citas por Día</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="citasDiariasChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'>";
                echo "<h6><i class='fas fa-exclamation-triangle me-2'></i>Error al cargar datos</h6>";
                echo "<p>Ocurrió un error al procesar los datos: " . htmlspecialchars($e->getMessage()) . "</p>";
                echo "</div>";
            }
        ?>

        <!-- Tabla de Resultados -->
        <?php if ($reporte_tipo == 'detallado' && empty($errores_validacion)): ?>
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>Detalle de Citas</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="tablaCitas">
                        <thead>
                            <tr>
                                <th>Fecha</th>
                                <th>Hora</th>
                                <th>Paciente</th>
                                <th>Cédula</th>
                                <th>Estado</th>
                                <th>Doctor</th>
                                <th>Observaciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "SELECT c.FECHACON, c.HORACON, p.NOMBREAPELLIDO, p.CEDULA, c.ESTATUS, c.NUMDOCTOR, c.OBSERVACION
                                   FROM CITAMEDIC c
                                   JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
                                   $where_clause
                                   ORDER BY c.FECHACON, c.HORACON";
                            
                            $stmt = $pdo->prepare($sql);
                            $stmt->execute($params);
                            $citas = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            $estados = [
                                0 => 'Atendido',
                                1 => 'Canceló',
                                2 => 'No asistió',
                                3 => 'Citado',
                                4 => 'Llegó tarde',
                                5 => 'Esperando',
                                6 => 'Pendiente aprobación'
                            ];
                            
                            foreach ($citas as $cita):
                            ?>
                            <tr>
                                <td><?php echo date('d/m/Y', strtotime($cita['FECHACON'])); ?></td>
                                <td><?php echo date('H:i', strtotime($cita['HORACON'])); ?></td>
                                <td><?php echo htmlspecialchars($cita['NOMBREAPELLIDO']); ?></td>
                                <td><?php echo htmlspecialchars($cita['CEDULA']); ?></td>
                                <td>
                                    <span class="badge bg-<?php
                                        echo $cita['ESTATUS'] == 0 ? 'success' :
                                             ($cita['ESTATUS'] == 2 ? 'danger' : 'warning');
                                    ?>">
                                        <?php echo $estados[$cita['ESTATUS']] ?? 'Desconocido'; ?>
                                    </span>
                                </td>
                                <td>Dr. <?php echo $cita['NUMDOCTOR']; ?></td>
                                <td><?php echo htmlspecialchars($cita['OBSERVACION'] ?? ''); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <?php endif; // Fin del if (empty($errores_validacion)) ?>

        <?php if (!empty($errores_validacion)): ?>
        <div class="alert alert-warning">
            <h6><i class="fas fa-info-circle me-2"></i>Corrija los errores</h6>
            <p>Por favor, corrija los errores de validación mostrados arriba para ver el reporte.</p>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/validacion_reportes.js"></script>
    <script>
        // Datos para gráficos
        <?php
        // Datos para gráfico de estados
        $stmt = $pdo->prepare("
            SELECT ESTATUS, COUNT(*) as cantidad 
            FROM CITAMEDIC 
            $where_clause 
            GROUP BY ESTATUS
        ");
        $stmt->execute($params);
        $datos_estatus = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels_estatus = [];
        $data_estatus = [];
        $estados = [0 => 'Atendido', 1 => 'Canceló', 2 => 'No asistió', 3 => 'Citado', 4 => 'Llegó tarde', 5 => 'Esperando', 6 => 'Pendiente'];
        
        foreach ($datos_estatus as $dato) {
            $labels_estatus[] = $estados[$dato['ESTATUS']] ?? 'Desconocido';
            $data_estatus[] = $dato['cantidad'];
        }
        
        // Datos para gráfico diario
        $stmt = $pdo->prepare("
            SELECT DATE(FECHACON) as fecha, COUNT(*) as cantidad 
            FROM CITAMEDIC 
            $where_clause 
            GROUP BY DATE(FECHACON) 
            ORDER BY fecha
        ");
        $stmt->execute($params);
        $datos_diarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $labels_diarios = [];
        $data_diarios = [];
        
        foreach ($datos_diarios as $dato) {
            $labels_diarios[] = date('d/m', strtotime($dato['fecha']));
            $data_diarios[] = $dato['cantidad'];
        }
        ?>
        
        const estatusLabels = <?php echo json_encode($labels_estatus); ?>;
        const estatusData = <?php echo json_encode($data_estatus); ?>;
        const diariosLabels = <?php echo json_encode($labels_diarios); ?>;
        const diariosData = <?php echo json_encode($data_diarios); ?>;
        
        // Gráfico de estados
        const ctx1 = document.getElementById('estatusChart').getContext('2d');
        new Chart(ctx1, {
            type: 'pie',
            data: {
                labels: estatusLabels,
                datasets: [{
                    data: estatusData,
                    backgroundColor: [
                        '#28a745', '#dc3545', '#ffc107', '#17a2b8', '#6f42c1', '#fd7e14', '#20c997'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        // Gráfico diario
        const ctx2 = document.getElementById('citasDiariasChart').getContext('2d');
        new Chart(ctx2, {
            type: 'line',
            data: {
                labels: diariosLabels,
                datasets: [{
                    label: 'Citas por día',
                    data: diariosData,
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        function exportarExcel() {
            window.location.href = 'api/exportar_citas.php?formato=excel&' + new URLSearchParams(window.location.search);
        }
        
        function exportarPDF() {
            window.location.href = 'api/exportar_citas.php?formato=pdf&' + new URLSearchParams(window.location.search);
        }
    </script>
</body>
</html>
