<?php
// Incluir archivo de configuración para la conexión a la base de datos
include('../config/database.php');

$action = $_POST['action'];

if ($action == 'fetch') {
    // Obtener citas según el filtro
    $filter = $_POST['filter'];
    $query = "SELECT c.*, p.NOMBRES, p.APELLIDOS FROM CITAMEDIC c 
              JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE";

    if ($filter == 'today') {
        $query .= " WHERE FECHACON = CURDATE()";
    } elseif ($filter == 'past') {
        $query .= " WHERE FECHACON < CURDATE()";
    } elseif ($filter == 'future') {
        $query .= " WHERE FECHACON > CURDATE()";
    }

    $result = $conn->query($query);
    $data = $result->fetch_all(MYSQLI_ASSOC);
    echo json_encode($data);

} elseif ($action == 'insert') {
    // Insertar nueva cita
    $clavepac = $_POST['CLAVEPAC'];
    $fechacon = $_POST['FECHACON'];
    $horacon = $_POST['HORACON'];
    $remitidopor = $_POST['REMITIDOPOR'];
    $observacion = $_POST['OBSERVACION'];

    $query = "INSERT INTO CITAMEDIC (CLAVEPAC, FECHACON, HORACON, REMITIDOPOR, OBSERVACION) 
              VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('issss', $clavepac, $fechacon, $horacon, $remitidopor, $observacion);
    $stmt->execute();
    echo json_encode(['success' => $stmt->affected_rows > 0]);

} elseif ($action == 'update') {
    // Editar cita
    $clave = $_POST['CLAVE'];
    $fechcon = $_POST['HORACON'];
    $remitidopor = $_POST['REMITIDOPOR'];
    $observacion = $_POST['OBSERVACION'];

    $query = "UPDATE CITAMEDIC SET FECHACON = ?, HORACON = ?, REMITIDOPOR = ?, OBSERVACION = ? WHERE CLAVE = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('ssssi', $fechacon, $horacon, $remitidopor, $observacion, $clave);
    $stmt->execute();
    echo json_encode(['success' => $stmt->affected_rows > 0]);

} elseif ($action == 'delete') {
    // Eliminar cita
    $clave = $_POST['CLAVE'];
    $query = "DELETE FROM CITAMEDIC WHERE CLAVE = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param('i', $clave);
    $stmt->execute();
    echo json_encode(['success' => $stmt->affected_rows > 0]);
}

$conn->close();
?>
