<?php
// multitenant.php
$host = $_SERVER['HTTP_HOST'];
// Conectar a la BD maestra
$master = new PDO("mysql:host=…;dbname=consultorio_maestra", …);
$stmt = $master->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio = :sub");
$sub = explode('.', $host)[0];
$stmt->execute(['sub' => $sub]);
$row = $stmt->fetch(PDO::FETCH_ASSOC);
if (!$row) {
  die("Consultorio desconocido: $sub");
}
// Parsear DSN
parse_str(str_replace(['mysql:',';'], ['','&'], $row['CadenaConexionDB']), $parts);
// Ahora $parts['host'], $parts['dbname'], $parts['user'], $parts['password']
define('DB_HOST', $parts['host']);
define('DB_NAME', $parts['dbname']);
define('DB_USER', $parts['user']);
define('DB_PASS', $parts['password']);
