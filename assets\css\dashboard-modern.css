/**
 * Estilos adicionales para el dashboard moderno
 * Complementa los estilos inline del index.php
 */

/* Mejoras adicionales para el dashboard */
.dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.dashboard-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

/* Animaciones mejoradas */
@keyframes slideInFromTop {
    0% {
        transform: translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInFromLeft {
    0% {
        transform: translateX(-100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Efectos de hover mejorados */
.stat-card:hover .stat-icon {
    animation: pulse 0.6s ease-in-out;
}

.action-card:hover .action-icon {
    transform: rotate(5deg) scale(1.1);
    transition: transform 0.3s ease;
}

/* Mejoras para dispositivos móviles */
@media (max-width: 576px) {
    .dashboard-header {
        padding: 1rem;
        margin: 1rem 0;
    }
    
    .dashboard-header h1 {
        font-size: 1.5rem;
    }
    
    .user-info {
        flex-direction: column;
        gap: 0.5rem;
        padding: 1rem;
    }
    
    .stat-card .card-body {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .action-card .card-body {
        padding: 1rem;
    }
    
    .action-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

/* Mejoras para tablets */
@media (min-width: 577px) and (max-width: 991px) {
    .dashboard-header {
        padding: 1.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .action-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }
}

/* Efectos de glassmorphism mejorados */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Indicadores de estado */
.status-indicator {
    position: relative;
    display: inline-block;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    border: 2px solid white;
    animation: pulse 2s infinite;
}

/* Mejoras para accesibilidad */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus states mejorados */
.action-card:focus,
.stat-card:focus {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
}

/* Transiciones suaves para todos los elementos interactivos */
button, .btn, .nav-link, .dropdown-item {
    transition: all 0.2s ease-in-out;
}

/* Mejoras para el navbar */
.navbar-modern .navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

.navbar-modern .dropdown-menu {
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 0.5rem 0;
}

.navbar-modern .dropdown-item {
    padding: 0.5rem 1.5rem;
    transition: all 0.2s ease;
}

.navbar-modern .dropdown-item:hover {
    background: rgba(37, 99, 235, 0.1);
    color: #2563eb;
    transform: translateX(5px);
}

/* Mejoras para el footer */
.modern-footer .btn {
    transition: all 0.3s ease;
}

.modern-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Loader personalizado */
.custom-loader {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(37, 99, 235, 0.1);
    border-left-color: #2563eb;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

/* Notificaciones toast personalizadas */
.toast-container .toast {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(37, 99, 235, 0.5);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(37, 99, 235, 0.7);
}

/* Mejoras para impresión */
@media print {
    .navbar-modern,
    .modern-footer,
    .action-card {
        display: none !important;
    }
    
    .dashboard-header,
    .stat-card {
        background: white !important;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}

/* Modo oscuro (preparado para futuras implementaciones) */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #3b82f6;
        --secondary-color: #94a3b8;
        --dark-color: #f1f5f9;
        --light-color: #1e293b;
    }
}

/* Animaciones de entrada escalonadas */
.stagger-animation {
    animation-fill-mode: both;
}

.stagger-animation:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation:nth-child(6) { animation-delay: 0.6s; }
