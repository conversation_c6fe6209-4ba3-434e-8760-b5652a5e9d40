<?php
// backend/reportes/api/exportar_administrativos.php
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario'])) {
    http_response_code(401);
    die('No autorizado');
}

require_once __DIR__ . '/../../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

// Obtener parámetros
$formato = $_GET['formato'] ?? 'excel';
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-m-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$usuario_filtro = $_GET['usuario_filtro'] ?? '';
$tabla_filtro = $_GET['tabla_filtro'] ?? '';

try {
    // Obtener estadísticas del sistema
    $estadisticas = [];
    
    // Total de registros por tabla
    $tablas = ['PACIENTES', 'CITAMEDIC', 'FACTURAS', 'EXAMENFISICO', 'ANTECEDENTES'];
    foreach ($tablas as $tabla) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
            $estadisticas[$tabla] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        } catch (Exception $e) {
            $estadisticas[$tabla] = 'N/A';
        }
    }
    
    // Usuarios del sistema
    $stmt = $pdo->query("SELECT usuario, role FROM usuarios ORDER BY usuario");
    $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Estado de sincronización
    $sync_status = [];
    foreach ($tablas as $tabla) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            $stmt = $pdo->query("SELECT COUNT(*) as sincronizados FROM $tabla WHERE SINCRONIZADO = 1");
            $sincronizados = $stmt->fetch(PDO::FETCH_ASSOC)['sincronizados'];
            
            $sync_status[$tabla] = [
                'total' => $total,
                'sincronizados' => $sincronizados,
                'pendientes' => $total - $sincronizados,
                'porcentaje' => $total > 0 ? round(($sincronizados / $total) * 100, 1) : 100
            ];
        } catch (Exception $e) {
            $sync_status[$tabla] = [
                'total' => 'N/A',
                'sincronizados' => 'N/A',
                'pendientes' => 'N/A',
                'porcentaje' => 'N/A'
            ];
        }
    }

    if ($formato === 'excel') {
        // Exportar a Excel (CSV)
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_administrativo_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // BOM para UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Sección 1: Estadísticas del Sistema
        fputcsv($output, ['ESTADÍSTICAS DEL SISTEMA'], ';');
        fputcsv($output, ['Tabla', 'Total Registros'], ';');
        
        foreach ($estadisticas as $tabla => $total) {
            fputcsv($output, [$tabla, $total], ';');
        }
        
        fputcsv($output, [''], ';'); // Línea vacía
        
        // Sección 2: Usuarios del Sistema
        fputcsv($output, ['USUARIOS DEL SISTEMA'], ';');
        fputcsv($output, ['Usuario', 'Rol'], ';');
        
        foreach ($usuarios as $usuario) {
            fputcsv($output, [$usuario['usuario'], ucfirst($usuario['role'])], ';');
        }
        
        fputcsv($output, [''], ';'); // Línea vacía
        
        // Sección 3: Estado de Sincronización
        fputcsv($output, ['ESTADO DE SINCRONIZACIÓN'], ';');
        fputcsv($output, ['Tabla', 'Total', 'Sincronizados', 'Pendientes', '% Sincronizado'], ';');
        
        foreach ($sync_status as $tabla => $status) {
            fputcsv($output, [
                $tabla,
                $status['total'],
                $status['sincronizados'],
                $status['pendientes'],
                $status['porcentaje'] . '%'
            ], ';');
        }
        
        fclose($output);
        
    } else {
        // Exportar a PDF (HTML por ahora)
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_administrativo_' . date('Y-m-d') . '.html"');
        
        // Obtener información de la empresa
        $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
        $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
        $nombre_empresa = $empresa ? $empresa['NOMBRE'] : 'Consultorio Médico';
        
        // Información del sistema
        $php_version = phpversion();
        $server_software = $_SERVER['SERVER_SOFTWARE'] ?? 'No disponible';
        
        try {
            $stmt = $pdo->query("SELECT VERSION() as version");
            $db_version = $stmt->fetch(PDO::FETCH_ASSOC)['version'];
        } catch (Exception $e) {
            $db_version = 'No disponible';
        }
        
        $timezone = date_default_timezone_get();
        $current_datetime = date('d/m/Y H:i:s');
        
        echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Reporte Administrativo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; font-size: 12px; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .info { margin-bottom: 20px; }
        .section { margin: 30px 0; }
        .section h3 { background-color: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin-bottom: 15px; }
        .stats-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; margin: 20px 0; }
        .stat-item { background: #e9ecef; padding: 15px; border-radius: 5px; text-align: center; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .sync-complete { color: #28a745; font-weight: bold; }
        .sync-partial { color: #ffc107; font-weight: bold; }
        .sync-pending { color: #dc3545; font-weight: bold; }
        .role-admin { color: #dc3545; font-weight: bold; }
        .role-doctor { color: #007bff; font-weight: bold; }
        .role-secretaria { color: #28a745; font-weight: bold; }
        .system-info { background-color: #f8f9fa; padding: 15px; border-radius: 5px; }
        .footer { margin-top: 30px; text-align: center; font-size: 10px; color: #666; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>$nombre_empresa</h1>
        <h2>Reporte Administrativo del Sistema</h2>
        <p>Generado el: $current_datetime</p>
    </div>
    
    <div class='info'>
        <p><strong>Generado por:</strong> " . htmlspecialchars($_SESSION['usuario']) . "</p>
        <p><strong>Período de análisis:</strong> " . date('d/m/Y', strtotime($fecha_inicio)) . " - " . date('d/m/Y', strtotime($fecha_fin)) . "</p>
    </div>
    
    <div class='section'>
        <h3>Información del Sistema</h3>
        <div class='system-info'>
            <table>
                <tr><td><strong>Versión PHP:</strong></td><td>$php_version</td></tr>
                <tr><td><strong>Servidor Web:</strong></td><td>$server_software</td></tr>
                <tr><td><strong>Base de Datos:</strong></td><td>$db_version</td></tr>
                <tr><td><strong>Zona Horaria:</strong></td><td>$timezone</td></tr>
                <tr><td><strong>Fecha/Hora Actual:</strong></td><td>$current_datetime</td></tr>
            </table>
        </div>
    </div>
    
    <div class='section'>
        <h3>Estadísticas del Sistema</h3>
        <div class='stats-grid'>";
        
        foreach ($estadisticas as $tabla => $total) {
            echo "<div class='stat-item'>
                <h4>$total</h4>
                <p>$tabla</p>
            </div>";
        }
        
        echo "</div>
    </div>
    
    <div class='section'>
        <h3>Usuarios del Sistema</h3>
        <table>
            <thead>
                <tr>
                    <th>Usuario</th>
                    <th>Rol</th>
                    <th>Estado</th>
                </tr>
            </thead>
            <tbody>";
        
        foreach ($usuarios as $usuario) {
            $role_class = 'role-' . $usuario['role'];
            echo "<tr>
                <td>" . htmlspecialchars($usuario['usuario']) . "</td>
                <td class='$role_class'>" . ucfirst($usuario['role']) . "</td>
                <td><span style='color: #28a745;'>● Activo</span></td>
            </tr>";
        }
        
        echo "</tbody>
        </table>
    </div>
    
    <div class='section'>
        <h3>Estado de Sincronización por Tabla</h3>
        <table>
            <thead>
                <tr>
                    <th>Tabla</th>
                    <th>Total Registros</th>
                    <th>Sincronizados</th>
                    <th>Pendientes</th>
                    <th>% Sincronizado</th>
                    <th>Estado</th>
                </tr>
            </thead>
            <tbody>";
        
        foreach ($sync_status as $tabla => $status) {
            $sync_class = '';
            $estado_texto = '';
            
            if ($status['porcentaje'] === 'N/A') {
                $sync_class = '';
                $estado_texto = 'No disponible';
            } elseif ($status['porcentaje'] == 100) {
                $sync_class = 'sync-complete';
                $estado_texto = 'Completo';
            } elseif ($status['porcentaje'] >= 80) {
                $sync_class = 'sync-partial';
                $estado_texto = 'Parcial';
            } else {
                $sync_class = 'sync-pending';
                $estado_texto = 'Pendiente';
            }
            
            echo "<tr>
                <td><strong>$tabla</strong></td>
                <td>" . $status['total'] . "</td>
                <td>" . $status['sincronizados'] . "</td>
                <td>" . $status['pendientes'] . "</td>
                <td class='$sync_class'>" . $status['porcentaje'] . ($status['porcentaje'] !== 'N/A' ? '%' : '') . "</td>
                <td class='$sync_class'>$estado_texto</td>
            </tr>";
        }
        
        echo "</tbody>
        </table>
    </div>
    
    <div class='section'>
        <h3>Resumen de Actividad Reciente</h3>";
        
        // Actividad de los últimos 30 días
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES WHERE FECHAINGRESO >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
        $pacientes_recientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
        $citas_recientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM FACTURAS WHERE FECHA >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND ESTATUS = 'A'");
        $facturas_recientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<div class='stats-grid'>
            <div class='stat-item'>
                <h4>$pacientes_recientes</h4>
                <p>Nuevos Pacientes<br><small>(Últimos 30 días)</small></p>
            </div>
            <div class='stat-item'>
                <h4>$citas_recientes</h4>
                <p>Citas Programadas<br><small>(Últimos 30 días)</small></p>
            </div>
            <div class='stat-item'>
                <h4>$facturas_recientes</h4>
                <p>Facturas Generadas<br><small>(Últimos 30 días)</small></p>
            </div>
        </div>
    </div>
    
    <div class='footer'>
        <p>Reporte generado automáticamente por el Sistema de Gestión del Consultorio</p>
        <p>Este reporte contiene información confidencial del sistema</p>
    </div>
</body>
</html>";
    }

} catch (PDOException $e) {
    http_response_code(500);
    die('Error en la base de datos: ' . $e->getMessage());
} catch (Exception $e) {
    http_response_code(500);
    die('Error interno: ' . $e->getMessage());
}
?>
