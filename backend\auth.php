<?php
// auth.php

session_start();

// Verificar si el usuario está logueado
if (!isset($_SESSION['usuario'])) {
    header("Location: ./../login.php");
    exit();
}

// Puedes proteger por rol también
function requiereRol($rolEsperado) {
    if (!isset($_SESSION['rol']) || $_SESSION['rol'] !== $rolEsperado) {
        echo "<h3>🚫 Acceso denegado. No tienes permisos para ver esta página.</h3>";
        exit();
    }
}
?>
