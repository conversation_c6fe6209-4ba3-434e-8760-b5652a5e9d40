<?php
// test_tabla_citamedic.php
// Script para verificar la estructura de la tabla CITAMEDIC

session_start();
require_once '../config/database.php';

echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Verificación Tabla CITAMEDIC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
        .highlight { background-color: #ffffcc; font-weight: bold; }
    </style>
</head>
<body>";

echo "<h1>🔍 Verificación de la Tabla CITAMEDIC</h1>";

try {
    // 1. Verificar estructura de la tabla
    echo "<div class='section'>";
    echo "<h2>1. Estructura de la Tabla CITAMEDIC</h2>";
    
    $stmt = $pdo->query("DESCRIBE CITAMEDIC");
    $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th><th>Extra</th></tr>";
    
    $clave_info = null;
    
    foreach ($columnas as $columna) {
        $destacar = '';
        if ($columna['Field'] == 'CLAVE') {
            $destacar = 'class="highlight"';
            $clave_info = $columna;
        }
        
        echo "<tr $destacar>";
        echo "<td><strong>{$columna['Field']}</strong></td>";
        echo "<td>{$columna['Type']}</td>";
        echo "<td>{$columna['Null']}</td>";
        echo "<td>{$columna['Key']}</td>";
        echo "<td>{$columna['Default']}</td>";
        echo "<td>{$columna['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Análisis de la columna CLAVE
    if ($clave_info) {
        echo "<h3>Análisis de la Columna CLAVE:</h3>";
        echo "<ul>";
        echo "<li><strong>Tipo:</strong> {$clave_info['Type']}</li>";
        echo "<li><strong>Permite NULL:</strong> " . ($clave_info['Null'] == 'YES' ? 'Sí' : 'No') . "</li>";
        echo "<li><strong>Es Clave:</strong> {$clave_info['Key']}</li>";
        echo "<li><strong>Valor por defecto:</strong> " . ($clave_info['Default'] ?: 'Ninguno') . "</li>";
        echo "<li><strong>Extra:</strong> " . ($clave_info['Extra'] ?: 'Ninguno') . "</li>";
        echo "</ul>";
        
        if (strpos($clave_info['Extra'], 'auto_increment') !== false) {
            echo "<p class='success'>✅ La columna CLAVE es AUTO_INCREMENT - No necesita valor manual</p>";
        } else {
            echo "<p class='warning'>⚠️ La columna CLAVE NO es AUTO_INCREMENT - Necesita valor manual</p>";
        }
        
        if ($clave_info['Null'] == 'NO') {
            echo "<p class='warning'>⚠️ La columna CLAVE NO permite NULL - Debe tener un valor</p>";
        }
    } else {
        echo "<p class='error'>❌ No se encontró la columna CLAVE en la tabla</p>";
    }
    
    echo "</div>";
    
    // 2. Verificar datos existentes
    echo "<div class='section'>";
    echo "<h2>2. Datos Existentes en CITAMEDIC</h2>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p><strong>Total de registros:</strong> $total</p>";
    
    if ($total > 0) {
        // Mostrar algunos ejemplos de CLAVE
        $stmt = $pdo->query("SELECT CLAVE, FECHACON, HORACON FROM CITAMEDIC ORDER BY CLAVE LIMIT 10");
        $ejemplos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h4>Ejemplos de valores de CLAVE:</h4>";
        echo "<table>";
        echo "<tr><th>CLAVE</th><th>FECHACON</th><th>HORACON</th></tr>";
        
        foreach ($ejemplos as $ejemplo) {
            echo "<tr>";
            echo "<td>{$ejemplo['CLAVE']}</td>";
            echo "<td>{$ejemplo['FECHACON']}</td>";
            echo "<td>{$ejemplo['HORACON']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Analizar el patrón de las claves
        $stmt = $pdo->query("
            SELECT 
                MIN(CLAVE) as min_clave,
                MAX(CLAVE) as max_clave,
                COUNT(DISTINCT CLAVE) as claves_unicas,
                COUNT(*) as total_registros
            FROM CITAMEDIC
        ");
        $analisis = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h4>Análisis de Claves:</h4>";
        echo "<ul>";
        echo "<li><strong>Clave mínima:</strong> {$analisis['min_clave']}</li>";
        echo "<li><strong>Clave máxima:</strong> {$analisis['max_clave']}</li>";
        echo "<li><strong>Claves únicas:</strong> {$analisis['claves_unicas']}</li>";
        echo "<li><strong>Total registros:</strong> {$analisis['total_registros']}</li>";
        echo "</ul>";
        
        if ($analisis['claves_unicas'] == $analisis['total_registros']) {
            echo "<p class='success'>✅ Todas las claves son únicas</p>";
        } else {
            echo "<p class='error'>❌ Hay claves duplicadas</p>";
        }
        
        // Verificar si las claves son numéricas
        $stmt = $pdo->query("SELECT COUNT(*) as numericas FROM CITAMEDIC WHERE CLAVE REGEXP '^[0-9]+$'");
        $numericas = $stmt->fetch(PDO::FETCH_ASSOC)['numericas'];
        
        echo "<p><strong>Claves numéricas:</strong> $numericas de $total</p>";
        
        if ($numericas == $total) {
            echo "<p class='success'>✅ Todas las claves son numéricas</p>";
            
            // Sugerir próxima clave
            $stmt = $pdo->query("SELECT MAX(CAST(CLAVE AS UNSIGNED)) as max_numerica FROM CITAMEDIC WHERE CLAVE REGEXP '^[0-9]+$'");
            $maxNumerica = $stmt->fetch(PDO::FETCH_ASSOC)['max_numerica'];
            $proximaClave = $maxNumerica + 1;
            
            echo "<p class='success'>✅ Próxima clave sugerida: $proximaClave</p>";
        } else {
            echo "<p class='warning'>⚠️ Hay claves no numéricas - Usar UUID o timestamp</p>";
        }
    }
    
    echo "</div>";
    
    // 3. Probar inserción de prueba
    echo "<div class='section'>";
    echo "<h2>3. Prueba de Inserción</h2>";
    
    try {
        // Generar clave de prueba
        if ($clave_info && strpos($clave_info['Extra'], 'auto_increment') !== false) {
            // Si es auto_increment, no incluir CLAVE en el INSERT
            echo "<p class='warning'>⚠️ Probando INSERT sin CLAVE (AUTO_INCREMENT)</p>";
            
            $sqlPrueba = "INSERT INTO CITAMEDIC (CLAVEPAC, NOMBRES, FECHACON, HORACON, ESTATUS) VALUES (?, ?, ?, ?, ?)";
            $stmtPrueba = $pdo->prepare($sqlPrueba);
            
            // Usar datos de prueba
            $datosPrueba = ['TEST123', 'PACIENTE PRUEBA', date('Y-m-d'), '10:00', 3];
            
        } else {
            // Si no es auto_increment, generar clave manual
            echo "<p class='warning'>⚠️ Probando INSERT con CLAVE manual</p>";
            
            // Obtener próxima clave
            $stmt = $pdo->query("SELECT MAX(CAST(CLAVE AS UNSIGNED)) as max_clave FROM CITAMEDIC WHERE CLAVE REGEXP '^[0-9]+$'");
            $maxClave = $stmt->fetch(PDO::FETCH_ASSOC);
            $clavePrueba = ($maxClave['max_clave'] ? $maxClave['max_clave'] + 1 : 1);
            
            $sqlPrueba = "INSERT INTO CITAMEDIC (CLAVE, CLAVEPAC, NOMBRES, FECHACON, HORACON, ESTATUS) VALUES (?, ?, ?, ?, ?, ?)";
            $stmtPrueba = $pdo->prepare($sqlPrueba);
            
            // Usar datos de prueba
            $datosPrueba = [$clavePrueba, 'TEST123', 'PACIENTE PRUEBA', date('Y-m-d'), '10:00', 3];
        }
        
        echo "<pre>";
        echo "SQL: $sqlPrueba\n";
        echo "Parámetros: " . implode(', ', $datosPrueba) . "\n";
        echo "</pre>";
        
        // Ejecutar en modo de prueba (comentar para ejecutar realmente)
        echo "<p class='warning'>⚠️ Prueba simulada - No se ejecutará realmente</p>";
        echo "<p class='success'>✅ La consulta parece correcta</p>";
        
        // Para ejecutar realmente, descomenta las siguientes líneas:
        /*
        $stmtPrueba->execute($datosPrueba);
        $idInsertado = $pdo->lastInsertId();
        echo "<p class='success'>✅ Inserción exitosa. ID: $idInsertado</p>";
        
        // Limpiar registro de prueba
        if ($idInsertado) {
            $pdo->prepare("DELETE FROM CITAMEDIC WHERE CLAVE = ?")->execute([$idInsertado]);
            echo "<p>Registro de prueba eliminado</p>";
        }
        */
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error en la prueba: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "</div>";
    
    // 4. Recomendaciones
    echo "<div class='section'>";
    echo "<h2>4. Recomendaciones</h2>";
    
    if ($clave_info) {
        if (strpos($clave_info['Extra'], 'auto_increment') !== false) {
            echo "<div class='success'>";
            echo "<h4>✅ Solución Recomendada (AUTO_INCREMENT):</h4>";
            echo "<p>La columna CLAVE es AUTO_INCREMENT. Modifica el INSERT para NO incluir CLAVE:</p>";
            echo "<pre>INSERT INTO CITAMEDIC (CLAVEPAC, NOMBRES, CONSULTORIO, FECHACON, HORACON, TIPOCITA, NUMDOCTOR, DURACION, NSS, TELEFONO, MODOASISTENCIA, ESTATUS) 
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)</pre>";
            echo "</div>";
        } else {
            echo "<div class='warning'>";
            echo "<h4>⚠️ Solución Recomendada (CLAVE MANUAL):</h4>";
            echo "<p>La columna CLAVE NO es AUTO_INCREMENT. Usa el código que ya agregué para generar claves únicas.</p>";
            echo "<p>El código actual debería funcionar correctamente.</p>";
            echo "</div>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ Error durante la verificación: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>🔗 Enlaces</h2>";
echo "<p><a href='crear_cita.php'>🔗 Volver a Crear Cita</a></p>";
echo "</div>";

echo "</body></html>";
?>
