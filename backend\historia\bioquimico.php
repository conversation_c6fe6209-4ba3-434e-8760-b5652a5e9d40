<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$pdo = require_once '../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA']   ?? '';
$CLAVE    = $_GET['CLAVE']    ?? null; // CLAVE del registro si estamos editando uno específico
$nuevo    = isset($_GET['nuevo']); // Para indicar que se quiere un formulario vacío
$mensaje  = '';
$messageType = '';
$registros = []; // Para almacenar todos los exámenes bioquímicos del paciente
$seleccionado = null; // Para el examen que se muestra en el formulario

$tableName = 'BIOQUIMICO'; // Nombre de tu tabla de examen bioquímico

// --- 1) Eliminar examen bioquímico por POST seguro ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && !empty($_POST['CLAVE_ELIMINAR'])) {
    try {
        $stmt = $pdo->prepare("DELETE FROM " . $tableName . " WHERE CLAVE = ?");
        $stmt->execute([$_POST['CLAVE_ELIMINAR']]);
        $mensaje = "✅ Examen Bioquímico eliminado exitosamente.";
        $messageType = 'success';
        // Redireccionar para recargar la página y no mostrar el registro eliminado
        header("Location: bioquimico.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar examen bioquímico: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// --- 2) Cargar registros históricos de examen bioquímico ---
if ($CLAVEPAC) {
    $stmt = $pdo->prepare("SELECT CLAVE, FECHA_CAP FROM " . $tableName . " WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC");
    $stmt->execute([$CLAVEPAC]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// --- 3) Seleccionar registro actual o nuevo ---
// Definir los campos para inicializar el formulario o cargar datos
$campos_bioquimico = [
    'CLAVE' => null,
    'GLUCOSA' => '', 'UREANITRO' => '', 'CREATNINA' => '', 'UREATOTAL' => '',
    'ACIDOURICO' => '', 'CALCIO' => '', 'FOSFORO' => '', 'CLORURO' => '',
    'SODIO' => '', 'POTACIO' => '', 'PROTEINAST' => '', 'ALBUMINA' => '',
    'GLOBULINA' => '', 'RELACIONAG' => '', 'BILIRRUBINAD' => '', 'BILIRRUBINAI' => '',
    'BILIRRUBINAT' => '', 'COLESTEROLENZ' => '', 'COLESTEROLL' => '', 'COLESTEROLT' => '',
    'COLESTEROLHDL' => '', 'COLESTEROLLDL' => '', 'TRIGLICERIDOS' => '', 'LIPIDOS' => '',
    'SGDT' => '', 'SGPT' => '', 'LDH' => '', 'CPKTOTAL' => '', 'CPKMB' => '',
    'FOSTALCALINO' => '', 'FOSTACIDO' => '', 'FOSTPROTATICO' => '', 'LIPASA' => '',
    'AMILASA' => '', 'IGG' => '',
    // UUID se asume que DB lo maneja
    'CEDULA' => $CEDULA,
    'SINCRONIZADO' => 0
];

// Si se pidió un formulario nuevo
if ($nuevo) {
    $seleccionado = $campos_bioquimico; // Inicializa con valores vacíos
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'Ingresando un nuevo examen bioquímico.';
    $messageType = 'info';
} elseif ($CLAVE) { // Si se pidió un examen específico por su CLAVE
    $stmt = $pdo->prepare("SELECT * FROM " . $tableName . " WHERE CLAVE = ?");
    $stmt->execute([$CLAVE]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($data_db) {
        $campos_bioquimico_temp = $campos_bioquimico;
        if (isset($data_db['UUID'])) {
            $campos_bioquimico_temp['UUID'] = '';
        }
        $seleccionado = array_merge($campos_bioquimico_temp, $data_db);
        $mensaje = 'Examen Bioquímico cargado. Modifique y guarde, o cree uno nuevo.';
        $messageType = 'info';
    } else {
        $seleccionado = $campos_bioquimico;
        $seleccionado['CLAVEPAC'] = $CLAVEPAC;
        $seleccionado['CEDULA'] = $CEDULA;
        $mensaje = 'No se encontró el examen bioquímico solicitado. Creando uno nuevo.';
        $messageType = 'warning';
    }
} elseif (!empty($registros)) { // Si no se pidió nuevo ni específico, carga el más reciente
    $stmt = $pdo->prepare("SELECT * FROM " . $tableName . " WHERE CLAVE = ?");
    $stmt->execute([$registros[0]['CLAVE']]);
    $data_db = $stmt->fetch(PDO::FETCH_ASSOC);
    $campos_bioquimico_temp = $campos_bioquimico;
    if (isset($data_db['UUID'])) {
        $campos_bioquimico_temp['UUID'] = '';
    }
    $seleccionado = array_merge($campos_bioquimico_temp, $data_db);
    $mensaje = 'Mostrando el examen bioquímico más reciente.';
    $messageType = 'info';
} else { // Si no hay registros ni se pidió nuevo, inicializa un formulario vacío
    $seleccionado = $campos_bioquimico;
    $seleccionado['CLAVEPAC'] = $CLAVEPAC;
    $seleccionado['CEDULA'] = $CEDULA;
    $mensaje = 'No se encontraron exámenes bioquímicos. Ingrese los datos.';
    $messageType = 'secondary';
}

// --- 4) Guardar o actualizar examen ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['eliminar'])) {
    $esActualizacion = !empty($_POST['CLAVE']);
    $data = [];

    // Recopilar datos del POST, asegurando saneamiento básico
    foreach ($campos_bioquimico as $campo => $valor_defecto) {
        if ($campo === 'CLAVE' || $campo === 'CLAVEPAC' || $campo === 'CEDULA' || $campo === 'SINCRONIZADO' || $campo === 'UUID') {
            continue; // UUID no se procesa desde PHP
        }
        $val = $_POST[$campo] ?? '';

        // Todos los campos son decimal(8,2) excepto CLAVE, CLAVEPAC, CEDULA, SINCRONIZADO, UUID
        $data[$campo] = filter_var($val, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION) ?: null;
    }

    // Campos de control adicionales
    $data['CLAVEPAC'] = $CLAVEPAC;
    $data['CEDULA'] = $CEDULA;
    $data['SINCRONIZADO'] = 0;

    try {
        if ($esActualizacion) {
            $data['CLAVE'] = $_POST['CLAVE'];
            $sets = [];
            foreach ($data as $key => $value) {
                if ($key !== 'CLAVE') {
                    $sets[] = "$key = :$key";
                }
            }
            $sql = "UPDATE " . $tableName . " SET " . implode(', ', $sets) . " WHERE CLAVE = :CLAVE";
        } else {
            $cols = implode(', ', array_keys($data));
            $phs  = ':' . implode(', :', array_keys($data));
            $sql  = "INSERT INTO " . $tableName . " ($cols) VALUES ($phs)";
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($data);

        $newClave = $esActualizacion ? $_POST['CLAVE'] : $pdo->lastInsertId();
        
        $mensaje = '✅ Examen Bioquímico guardado exitosamente.';
        $messageType = 'success';
        header("Location: bioquimico.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&CLAVE=$newClave");
        exit;

    } catch (PDOException $e) {
        $mensaje = "❌ Error al guardar el examen bioquímico: " . $e->getMessage();
        $messageType = 'danger';
        error_log("Error saving bioquimico: " . $e->getMessage() . " - SQL: " . $sql);
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examen Bioquímico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .form-container { padding: 20px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 15px rgba(0,0,0,0.1); margin-top: 20px; }
        .form-section { border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin-bottom: 20px; background-color: #fefefe; }
        .form-section h4 { margin-bottom: 15px; color: #0d6efd; border-bottom: 1px solid #0d6efd; padding-bottom: 5px; }
        .list-group-item.active { background-color: #0d6efd !important; border-color: #0d6efd !important; }
        .list-group-item.active a { color: #fff !important; }
        .decimal-input { text-align: right; }
    </style>
</head>
<body class="p-4">
    <div class="row">
        <div class="col-md-3 border-end">
            <h5 class="mb-3">Historial de Exámenes Bioquímicos</h5>
            <ul class="list-group">
                <?php if (empty($registros)): ?>
                    <li class="list-group-item text-muted">No hay registros anteriores.</li>
                <?php endif; ?>
                <?php foreach ($registros as $r): ?>
                    <?php
                    $fecha_formateada = (new DateTime($r['FECHA_CAP']))->format('Y-m-d H:i');
                    $active = isset($seleccionado['CLAVE']) && $seleccionado['CLAVE'] == $r['CLAVE'];
                    ?>
                    <li class="list-group-item <?= $active ? 'active' : '' ?>">
                        <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($r['CLAVE']) ?>"
                           class="text-decoration-none <?= $active ? 'text-white' : '' ?>">
                            <?= htmlspecialchars($fecha_formateada) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <div class="col-md-9 form-container">
            <h4 class="mb-3">Examen Bioquímico</h4>
            <p class="text-muted">Paciente: <strong><?= htmlspecialchars($CLAVEPAC) ?></strong> (CI: <strong><?= htmlspecialchars($CEDULA) ?></strong>)</p>

            <?php if ($mensaje): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($mensaje) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form method="post" novalidate>
                <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                <input type="hidden" name="CEDULA" value="<?= htmlspecialchars($CEDULA) ?>">
                <?php if (!empty($seleccionado['CLAVE'])): ?>
                    <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                    <input type="hidden" name="CLAVE_ELIMINAR" value="<?= htmlspecialchars($seleccionado['CLAVE']) ?>">
                <?php endif; ?>
                <input type="hidden" name="SINCRONIZADO" value="<?= htmlspecialchars($seleccionado['SINCRONIZADO']) ?>">

                <div class="form-section">
                    <h4>Metabolismo de Glucosa y Nitrógeno</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="GLUCOSA" class="form-label">Glucosa</label><input type="text" class="form-control decimal-input" id="GLUCOSA" name="GLUCOSA" value="<?= htmlspecialchars($seleccionado['GLUCOSA'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="UREANITRO" class="form-label">Urea Nitrógeno</label><input type="text" class="form-control decimal-input" id="UREANITRO" name="UREANITRO" value="<?= htmlspecialchars($seleccionado['UREANITRO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="CREATNINA" class="form-label">Creatinina</label><input type="text" class="form-control decimal-input" id="CREATNINA" name="CREATNINA" value="<?= htmlspecialchars($seleccionado['CREATNINA'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="UREATOTAL" class="form-label">Urea Total</label><input type="text" class="form-control decimal-input" id="UREATOTAL" name="UREATOTAL" value="<?= htmlspecialchars($seleccionado['UREATOTAL'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="ACIDOURICO" class="form-label">Ácido Úrico</label><input type="text" class="form-control decimal-input" id="ACIDOURICO" name="ACIDOURICO" value="<?= htmlspecialchars($seleccionado['ACIDOURICO'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Electrolitos y Minerales</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="CALCIO" class="form-label">Calcio</label><input type="text" class="form-control decimal-input" id="CALCIO" name="CALCIO" value="<?= htmlspecialchars($seleccionado['CALCIO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="FOSFORO" class="form-label">Fósforo</label><input type="text" class="form-control decimal-input" id="FOSFORO" name="FOSFORO" value="<?= htmlspecialchars($seleccionado['FOSFORO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="CLORURO" class="form-label">Cloruro</label><input type="text" class="form-control decimal-input" id="CLORURO" name="CLORURO" value="<?= htmlspecialchars($seleccionado['CLORURO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="SODIO" class="form-label">Sodio</label><input type="text" class="form-control decimal-input" id="SODIO" name="SODIO" value="<?= htmlspecialchars($seleccionado['SODIO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="POTACIO" class="form-label">Potasio</label><input type="text" class="form-control decimal-input" id="POTACIO" name="POTACIO" value="<?= htmlspecialchars($seleccionado['POTACIO'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Proteínas Plasmáticas</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="PROTEINAST" class="form-label">Proteínas Totales</label><input type="text" class="form-control decimal-input" id="PROTEINAST" name="PROTEINAST" value="<?= htmlspecialchars($seleccionado['PROTEINAST'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="ALBUMINA" class="form-label">Albúmina</label><input type="text" class="form-control decimal-input" id="ALBUMINA" name="ALBUMINA" value="<?= htmlspecialchars($seleccionado['ALBUMINA'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="GLOBULINA" class="form-label">Globulina</label><input type="text" class="form-control decimal-input" id="GLOBULINA" name="GLOBULINA" value="<?= htmlspecialchars($seleccionado['GLOBULINA'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="RELACIONAG" class="form-label">Relación A/G</label><input type="text" class="form-control decimal-input" id="RELACIONAG" name="RELACIONAG" value="<?= htmlspecialchars($seleccionado['RELACIONAG'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="IGG" class="form-label">IgG</label><input type="text" class="form-control decimal-input" id="IGG" name="IGG" value="<?= htmlspecialchars($seleccionado['IGG'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Bilirrubinas</h4>
                    <div class="row g-3">
                        <div class="col-md-4"><label for="BILIRRUBINAD" class="form-label">Bilirrubina Directa</label><input type="text" class="form-control decimal-input" id="BILIRRUBINAD" name="BILIRRUBINAD" value="<?= htmlspecialchars($seleccionado['BILIRRUBINAD'] ?? '') ?>"></div>
                        <div class="col-md-4"><label for="BILIRRUBINAI" class="form-label">Bilirrubina Indirecta</label><input type="text" class="form-control decimal-input" id="BILIRRUBINAI" name="BILIRRUBINAI" value="<?= htmlspecialchars($seleccionado['BILIRRUBINAI'] ?? '') ?>"></div>
                        <div class="col-md-4"><label for="BILIRRUBINAT" class="form-label">Bilirrubina Total</label><input type="text" class="form-control decimal-input" id="BILIRRUBINAT" name="BILIRRUBINAT" value="<?= htmlspecialchars($seleccionado['BILIRRUBINAT'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Lípidos</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="COLESTEROLENZ" class="form-label">Colesterol Enzimático</label><input type="text" class="form-control decimal-input" id="COLESTEROLENZ" name="COLESTEROLENZ" value="<?= htmlspecialchars($seleccionado['COLESTEROLENZ'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="COLESTEROLL" class="form-label">Colesterol Libre</label><input type="text" class="form-control decimal-input" id="COLESTEROLL" name="COLESTEROLL" value="<?= htmlspecialchars($seleccionado['COLESTEROLL'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="COLESTEROLT" class="form-label">Colesterol Total</label><input type="text" class="form-control decimal-input" id="COLESTEROLT" name="COLESTEROLT" value="<?= htmlspecialchars($seleccionado['COLESTEROLT'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="COLESTEROLHDL" class="form-label">Colesterol HDL</label><input type="text" class="form-control decimal-input" id="COLESTEROLHDL" name="COLESTEROLHDL" value="<?= htmlspecialchars($seleccionado['COLESTEROLHDL'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="COLESTEROLLDL" class="form-label">Colesterol LDL</label><input type="text" class="form-control decimal-input" id="COLESTEROLLDL" name="COLESTEROLLDL" value="<?= htmlspecialchars($seleccionado['COLESTEROLLDL'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="TRIGLICERIDOS" class="form-label">Triglicéridos</label><input type="text" class="form-control decimal-input" id="TRIGLICERIDOS" name="TRIGLICERIDOS" value="<?= htmlspecialchars($seleccionado['TRIGLICERIDOS'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="LIPIDOS" class="form-label">Lípidos Totales</label><input type="text" class="form-control decimal-input" id="LIPIDOS" name="LIPIDOS" value="<?= htmlspecialchars($seleccionado['LIPIDOS'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="form-section">
                    <h4>Enzimas</h4>
                    <div class="row g-3">
                        <div class="col-md-3"><label for="SGDT" class="form-label">SGDT (AST)</label><input type="text" class="form-control decimal-input" id="SGDT" name="SGDT" value="<?= htmlspecialchars($seleccionado['SGDT'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="SGPT" class="form-label">SGPT (ALT)</label><input type="text" class="form-control decimal-input" id="SGPT" name="SGPT" value="<?= htmlspecialchars($seleccionado['SGPT'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="LDH" class="form-label">LDH</label><input type="text" class="form-control decimal-input" id="LDH" name="LDH" value="<?= htmlspecialchars($seleccionado['LDH'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="CPKTOTAL" class="form-label">CPK Total</label><input type="text" class="form-control decimal-input" id="CPKTOTAL" name="CPKTOTAL" value="<?= htmlspecialchars($seleccionado['CPKTOTAL'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="CPKMB" class="form-label">CPK MB</label><input type="text" class="form-control decimal-input" id="CPKMB" name="CPKMB" value="<?= htmlspecialchars($seleccionado['CPKMB'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="FOSTALCALINO" class="form-label">Fosfatasa Alcalina</label><input type="text" class="form-control decimal-input" id="FOSTALCALINO" name="FOSTALCALINO" value="<?= htmlspecialchars($seleccionado['FOSTALCALINO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="FOSTACIDO" class="form-label">Fosfatasa Ácida</label><input type="text" class="form-control decimal-input" id="FOSTACIDO" name="FOSTACIDO" value="<?= htmlspecialchars($seleccionado['FOSTACIDO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="FOSTPROTATICO" class="form-label">Fosfatasa Prostática</label><input type="text" class="form-control decimal-input" id="FOSTPROTATICO" name="FOSTPROTATICO" value="<?= htmlspecialchars($seleccionado['FOSTPROTATICO'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="LIPASA" class="form-label">Lipasa</label><input type="text" class="form-control decimal-input" id="LIPASA" name="LIPASA" value="<?= htmlspecialchars($seleccionado['LIPASA'] ?? '') ?>"></div>
                        <div class="col-md-3"><label for="AMILASA" class="form-label">Amilasa</label><input type="text" class="form-control decimal-input" id="AMILASA" name="AMILASA" value="<?= htmlspecialchars($seleccionado['AMILASA'] ?? '') ?>"></div>
                    </div>
                </div>

                <div class="d-flex gap-2 mt-3">
                    <button type="submit" name="accion" value="guardar" class="btn btn-success">💾 Guardar</button>
                    <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&nuevo=1" class="btn btn-secondary">🆕 Nuevo</a>
                    <?php if (!empty($seleccionado['CLAVE'])): ?>
                        <button type="submit" name="eliminar" value="1" class="btn btn-danger" onclick="return confirm('¿Deseas eliminar este examen bioquímico?');">🗑️ Eliminar</button>
                        <button type="button" class="btn btn-outline-primary" onclick="window.print()">🖨️ Imprimir</button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Función para formatear campos decimales (decimal 8,2)
        function formatearDecimal(input) {
            let val = input.value.replace(/[^0-9.]/g, '');
            val = val.replace(/\.(?=.*\.)/g, ''); // Solo un punto decimal
            const endsWithDot = val.endsWith('.');
            const [intRaw = '', decRaw = ''] = val.split('.');
            const intPart = intRaw.slice(0, 6); // decimal(8,2) -> 6 enteros
            const decPart = decRaw.slice(0, 2); // 2 decimales
            input.value = endsWithDot ? `${intPart}.` : (decPart ? `${intPart}.${decPart}` : intPart);
        }

        // Función para validar el formulario antes de enviar
        function validarFormulario(e) {
            let ok = true;
            // Patrón para decimal(8,2): hasta 6 enteros y 2 decimales.
            const regex = /^\d{1,6}(\.\d{1,2})?$/; 

            document.querySelectorAll('.decimal-input').forEach(input => {
                let v = input.value.trim();
                if (v && !regex.test(v)) {
                    input.setCustomValidity('Formato inválido: hasta 6 enteros y 2 decimales.');
                    input.reportValidity();
                    ok = false;
                } else {
                    input.setCustomValidity('');
                }
            });

            if (!ok) e.preventDefault();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Adjuntar formateador y validador a todos los campos con clase decimal-input
            document.querySelectorAll('.decimal-input').forEach(i => {
                i.setAttribute('maxlength', '9'); // 6 enteros + 1 punto + 2 decimales
                i.addEventListener('input', () => formatearDecimal(i));
                formatearDecimal(i); // Formatea el valor inicial al cargar
            });

            document.querySelector('form').addEventListener('submit', validarFormulario);
        });
    </script>
</body>
</html>