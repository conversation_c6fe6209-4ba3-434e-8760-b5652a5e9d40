<?php
// --- LÍNEAS TEMPORALES PARA DEPURACIÓN: ELIMINAR EN PRODUCCIÓN DESPUÉS DE SOLUCIONAR EL PROBLEMA ---
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once dirname(__DIR__, 2) . '/vendor/autoload.php';

require_once dirname(__DIR__) . '/config/database.php';


$masterPdo = getMasterPdo();

use Dompdf\Dompdf;
use Dompdf\Options;

// 1. Obtener el ID de la factura y el subdominio desde la URL
$id_factura = $_GET['id_factura'] ?? null;
$subdominio_seleccionado = $_GET['subdominio'] ?? null;

if (!$id_factura || !$subdominio_seleccionado) {
    die("Error: ID de factura o subdominio no especificado en la URL. No se puede generar el PDF.");
}

$pdo = null; // Variable para la conexión al tenant específico
$empresa = null; // Variable para los datos de la EMPRESA del tenant
$marcsoftware_telefono='8097538801';
$marcsoftware_email='<EMAIL>';
try {
    // Recuperar la cadena de conexión del tenant de la base de datos maestra
    $stmt_master = $masterPdo->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio = ? AND Estado = 'Activo'");
    $stmt_master->execute([$subdominio_seleccionado]);
    $cfg = $stmt_master->fetch(PDO::FETCH_ASSOC);

    if ($cfg && !empty($cfg['CadenaConexionDB'])) {
        // Parsear la cadena de conexión
        preg_match('/host=([^;]+)/', $cfg['CadenaConexionDB'], $matches_host); $host = $matches_host[1] ?? '';
        preg_match('/dbname=([^;]+)/', $cfg['CadenaConexionDB'], $matches_dbname); $dbname = $matches_dbname[1] ?? '';
        preg_match('/user=([^;]+)/', $cfg['CadenaConexionDB'], $matches_user); $user = $matches_user[1] ?? '';
        preg_match('/password=([^;]+)/', $cfg['CadenaConexionDB'], $matches_pass); $password = $matches_pass[1] ?? '';

        if ($host && $dbname && $user !== '') {
            $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
            $pdo = new PDO($dsn, $user, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);

            // 2. Obtener los datos completos de la factura y del cliente del tenant
            $stmt = $pdo->prepare("SELECT
                                        fs.*,
                                        cs.NOMBRE_COMPLETO AS CLIENTE_NOMBRE_COMPLETO,
                                        cs.RNC AS CLIENTE_RNC,
                                        cs.TELEFONO AS CLIENTE_TELEFONO,
                                        cs.EMAIL AS CLIENTE_EMAIL,
                                        cs.DIRECCION AS CLIENTE_DIRECCION,
                                        cs.CIUDAD AS CLIENTE_CIUDAD,
                                        cs.PROVINCIA AS CLIENTE_PROVINCIA,
                                        cs.ESPECIALIDAD AS CLIENTE_ESPECIALIDAD,
                                        cs.SUBDOMINIO AS CLIENTE_SUBDOMINIO
                                    FROM
                                        FACTURAS_SOFTWARE fs
                                    JOIN
                                        CLIENTES_SOFTWARE cs ON fs.CLIENTE_CLAVE = cs.CLAVE
                                    WHERE
                                        fs.CLAVE = ?");
            $stmt->execute([$id_factura]);
            $factura = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$factura) {
                die("Error: Factura con ID " . htmlspecialchars($id_factura) . " no encontrada para el consultorio '{$subdominio_seleccionado}'.");
            }

            // 3. Obtener los datos del "consultorio" (MarcSoftware Solutions) desde la tabla EMPRESA del tenant
            $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
            $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

            // Si no hay datos de empresa, usar valores por defecto para evitar errores
            $empresa_nombre = $empresa['NOMBRE'] ?? 'MarcSoftware Solutions';
            $empresa_rnc = $empresa['RNC'] ?? 'N/A';
            $empresa_telefono = $empresa['TELEFONO'] ?? 'N/A';
            $empresa_email = $empresa['CORREOE'] ?? '<EMAIL>';
            $empresa_direccion = '';
            if (isset($empresa['CALLE'])) $empresa_direccion .= htmlspecialchars($empresa['CALLE']);
            if (isset($empresa['MUNICIPIO'])) $empresa_direccion .= ($empresa_direccion ? ', ' : '') . htmlspecialchars($empresa['MUNICIPIO']);
            if (isset($empresa['PROVINCIA'])) $empresa_direccion .= ($empresa_direccion ? ', ' : '') . htmlspecialchars($empresa['PROVINCIA']);


            // 4. Construir el HTML de la factura (Este es un diseño básico. ¡Personalízalo!)
            $html = '
            <!DOCTYPE html>
            <html lang="es">
            <head>
                <meta charset="UTF-8">
                <title>Factura ' . htmlspecialchars($factura['NUMERO_FACTURA']) . '</title>
                <style>
                    /* Reset básico para HTML a PDF */
                    body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; font-size: 10pt; line-height: 1.4; margin: 0; padding: 0; }
                    .container { width: 90%; margin: 20px auto; padding: 20px; border: 1px solid #ddd; box-shadow: 0 0 10px rgba(0,0,0,0.1); }

                    .header { text-align: center; margin-bottom: 30px; }
                    .header h2 { margin: 0; padding: 0; color: #333; }
                    .header img { max-width: 180px; height: auto; margin-bottom: 10px; } /* Ajusta el tamaño de tu logo */

                    .company-info, .client-info { margin-bottom: 25px; overflow: hidden; } /* overflow:hidden para clearfix */
                    .company-info div, .client-info div { float: left; width: 48%; }
                    .company-info .right, .client-info .right { text-align: right; float: right; }
                    .info-label { font-weight: bold; color: #555; }
                    .info-value { margin-bottom: 5px; }

                    hr { border: 0; border-top: 1px solid #eee; margin: 20px 0; }

                    table { width: 100%; border-collapse: collapse; margin-top: 25px; }
                    th, td { border: 1px solid #e0e0e0; padding: 10px; text-align: left; vertical-align: top; }
                    th { background-color: #f8f8f8; color: #333; font-weight: bold; }
                    td:last-child { text-align: right; } /* Para el monto */

                    .total-section { text-align: right; margin-top: 25px; }
                    .total-box { display: inline-block; border: 1px solid #ccc; padding: 15px 20px; background-color: #f9f9f9; }
                    .total-box strong { font-size: 1.4em; color: #007bff; }

                    .notes { margin-top: 30px; font-size: 0.95em; color: #666; }
                    .notes p { margin-bottom: 8px; }

                    .footer { text-align: center; margin-top: 50px; padding-top: 15px; border-top: 1px solid #eee; font-size: 0.85em; color: #888; }

                    /* Evita que se rompan elementos en medio de la impresión */
                    .no-break { page-break-inside: avoid; }

                    /* Para márgenes de impresión en tamaño carta */
                    @page { margin: 1in; } /* Márgenes por defecto de 1 pulgada */
                </style>
            </head>
            <body>
                <div class="container no-break">
                    <div class="header">
                        <h2>FACTURA COMERCIAL</h2>
                    </div>

                    <div class="company-info">
                        <div class="no-break">
                            <p><span class="info-label">Facturado por:</span><br>
                            <strong>' . htmlspecialchars($empresa_nombre) . '</strong><br>
                            <span class="info-value">RNC: ' . htmlspecialchars($empresa_rnc) . '</span><br>
                            <span class="info-value">Teléfono: ' . htmlspecialchars($empresa_telefono) . '</span><br>
                            <span class="info-value">Email: ' . htmlspecialchars($empresa_email) . '</span><br>
                            <span class="info-value">Dirección: ' . htmlspecialchars($empresa_direccion) . '</span></p>
                        </div>
                        <div class="right no-break">
                            <p><span class="info-label">Factura No.:</span> <strong style="font-size:1.1em;">' . htmlspecialchars($factura['NUMERO_FACTURA']) . '</strong><br>
                            <span class="info-label">Fecha de Emisión:</span> ' . htmlspecialchars($factura['FECHA_FACTURA']) . '<br>
                            <span class="info-label">Fecha de Vencimiento:</span> ' . htmlspecialchars($factura['FECHA_VENCIMIENTO']) . '<br>
                            <span class="info-label">Estado:</span> <strong style="color:' . (($factura['ESTATUS'] == 'PAGADO') ? 'green' : 'orange') . ';">' . htmlspecialchars(strtoupper($factura['ESTATUS'])) . '</strong></p>
                        </div>
                    </div>

                    <hr>

                    <div class="client-info">
                        <div class="no-break">
                            <p><span class="info-label">Facturado a:</span><br>
                            <strong>' . htmlspecialchars($factura['CLIENTE_NOMBRE_COMPLETO']) . '</strong><br>
                            <span class="info-value">RNC: ' . htmlspecialchars($factura['CLIENTE_RNC']) . '</span><br>
                            <span class="info-value">Teléfono: ' . htmlspecialchars($factura['CLIENTE_TELEFONO']) . '</span><br>
                            <span class="info-value">Email: ' . htmlspecialchars($factura['CLIENTE_EMAIL']) . '</span><br>
                            <span class="info-value">Dirección: ' . htmlspecialchars($factura['CLIENTE_DIRECCION'] . ', ' . $factura['CLIENTE_CIUDAD'] . ', ' . $factura['CLIENTE_PROVINCIA']) . '</span></p>
                        </div>
                    </div>

                    <hr>

                    <table>
                        <thead>
                            <tr>
                                <th>Descripción del Servicio</th>
                                <th style="width: 15%; text-align: right;">Monto</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>' . nl2br(htmlspecialchars($factura['CONCEPTO'])) . '</td>
                                <td style="text-align: right;">' . htmlspecialchars($factura['MONEDA']) . ' ' . number_format($factura['PRECIO'], 2) . '</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="total-section no-break">
                        <div class="total-box">
                            <strong>Total:</strong> ' . htmlspecialchars($factura['MONEDA']) . ' ' . number_format($factura['PRECIO'], 2) . '
                        </div>
                    </div>

                    <div class="notes no-break">
                        <p><span class="info-label">Modo de Pago:</span> ' . htmlspecialchars($factura['MODO_PAGO']) . '</p>';
                        if ($factura['MODO_PAGO'] === 'Transferencia' && !empty($factura['BANCO_DESTINO'])) {
                            $html .= '<p><span class="info-label">Datos Bancarios para Transferencia:</span><br>';
                            $html .= 'Banco: ' . htmlspecialchars($factura['BANCO_DESTINO']) . '<br>';
                            $html .= 'Cuenta: ' . htmlspecialchars($factura['CUENTA_DESTINO']) . '<br>';
                            $html .= 'Beneficiario: ' . htmlspecialchars($factura['BENEFICIARIO']) . '</p>';
                        }
                    $html .= '
                        <p>Gracias por su preferencia y por elegir MarcSoftware Solutions. Este es un documento generado automáticamente y no requiere firma.</p>
                    </div>

                    <div class="footer no-break">
                        <p>MarcSoftware Solutions | Contacto: ' . htmlspecialchars($marcsoftware_telefono) . ' | ' . htmlspecialchars($marcsoftware_email) . '</p>
                    </div>
                </div>
            </body>
            </html>';

            // 5. Configurar Dompdf y generar el PDF
            $options = new Options();
            $options->set('isHtml5ParserEnabled', true);
            $options->set('isRemoteEnabled', true);
            $dompdf = new Dompdf($options);

            $dompdf->loadHtml($html);
            $dompdf->setPaper('letter', 'portrait');
            $dompdf->render();

            // Enviar el PDF al navegador
            $dompdf->stream("Factura_" . $factura['NUMERO_FACTURA'] . ".pdf", array("Attachment" => false));

        } else {
            die("Error: Datos de conexión incompletos o inválidos para el subdominio '{$subdominio_seleccionado}'.");
        }
    } else {
        die("Error: Configuración de conexión no encontrada o inactiva para el subdominio '{$subdominio_seleccionado}'.");
    }

} catch (\PDOException $e) {
    die("Error de base de datos al generar la factura para '{$subdominio_seleccionado}': " . htmlspecialchars($e->getMessage()));
} catch (Exception $e) {
    die("Error inesperado al generar la factura para '{$subdominio_seleccionado}': " . htmlspecialchars($e->getMessage()));
}