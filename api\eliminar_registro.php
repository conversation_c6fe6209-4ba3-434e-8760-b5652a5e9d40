<?php
include_once '../config/config_interbase.php';

header('Content-Type: application/json; charset=UTF-8');

$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !is_array($input)) {
    echo json_encode(['status' => 'error', 'message' => 'Datos no válidos']);
    exit;
}

$conn = getInterbaseConnection();

foreach ($input as $elim) {
    $tabla = $elim['TABLA_ORIGEN'];
    $clave = $elim['CLAVE'];

    // Eliminar registro en la base de datos Interbase
    $sql = "DELETE FROM $tabla WHERE CLAVE = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('i', $clave);

    if ($stmt->execute()) {
        echo json_encode(['status' => 'success']);
    } else {
        echo json_encode(['status' => 'error', 'message' => $stmt->error]);
    }

    $stmt->close();
}

$conn->close();
