<?php
/**
 * Debug API para sugerencias - versión de diagnóstico
 */

session_start();
require_once __DIR__ . '/../../config/database.php';

// Log de debug
$debug_log = [];
$debug_log[] = "=== DEBUG SUGERENCIA API ===";
$debug_log[] = "Timestamp: " . date('Y-m-d H:i:s');
$debug_log[] = "Método: " . $_SERVER['REQUEST_METHOD'];
$debug_log[] = "Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'No definido');

// Configurar respuesta JSON
header('Content-Type: application/json');

try {
    // Obtener datos del POST
    $raw_input = file_get_contents('php://input');
    $debug_log[] = "Raw input: " . $raw_input;
    
    $input = json_decode($raw_input, true);
    $debug_log[] = "JSON decoded: " . print_r($input, true);
    
    $sugerencia = trim($input['sugerencia'] ?? '');
    $debug_log[] = "Sugerencia extraída: '" . $sugerencia . "'";
    
    // Validar que la sugerencia no esté vacía
    if (empty($sugerencia)) {
        $debug_log[] = "ERROR: Sugerencia vacía";
        echo json_encode([
            'success' => false, 
            'message' => 'Sugerencia vacía',
            'debug' => $debug_log
        ]);
        exit;
    }
    
    // Obtener información del usuario
    $usuario = $_SESSION['usuario'] ?? 'Anónimo';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Desconocida';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Desconocido';
    
    $debug_log[] = "Usuario: " . $usuario;
    $debug_log[] = "IP: " . $ip_address;
    
    // Crear tabla si no existe
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS SUGERENCIAS_REPORTES (
            CLAVE INT AUTO_INCREMENT PRIMARY KEY,
            SUGERENCIA TEXT NOT NULL,
            USUARIO VARCHAR(100) DEFAULT 'Anónimo',
            IP_ADDRESS VARCHAR(45),
            USER_AGENT TEXT,
            FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ESTADO ENUM('PENDIENTE', 'REVISADA', 'IMPLEMENTADA', 'RECHAZADA') DEFAULT 'PENDIENTE',
            PRIORIDAD ENUM('BAJA', 'MEDIA', 'ALTA') DEFAULT 'MEDIA',
            COMENTARIOS_ADMIN TEXT,
            FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTableSQL);
    $debug_log[] = "Tabla creada/verificada";
    
    // Insertar la sugerencia
    $stmt = $pdo->prepare("
        INSERT INTO SUGERENCIAS_REPORTES 
        (SUGERENCIA, USUARIO, IP_ADDRESS, USER_AGENT) 
        VALUES (?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([$sugerencia, $usuario, $ip_address, $user_agent]);
    $sugerencia_id = $pdo->lastInsertId();
    
    $debug_log[] = "Inserción exitosa. ID: " . $sugerencia_id;
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => '¡Sugerencia enviada correctamente!',
        'sugerencia_id' => $sugerencia_id,
        'fecha' => date('d/m/Y H:i:s'),
        'debug' => $debug_log
    ]);
    
} catch (PDOException $e) {
    $debug_log[] = "ERROR PDO: " . $e->getMessage();
    echo json_encode([
        'success' => false,
        'message' => 'Error de base de datos: ' . $e->getMessage(),
        'debug' => $debug_log
    ]);
    
} catch (Exception $e) {
    $debug_log[] = "ERROR General: " . $e->getMessage();
    echo json_encode([
        'success' => false,
        'message' => 'Error general: ' . $e->getMessage(),
        'debug' => $debug_log
    ]);
}
?>
