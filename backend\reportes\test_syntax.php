<?php
// backend/reportes/test_syntax.php
// Script simple para probar la sintaxis del archivo de citas

echo "<h2>Prueba de Sintaxis - Reportes de Citas</h2>";

// Verificar que no hay errores de sintaxis
$archivo = __DIR__ . '/citas_reportes.php';

if (file_exists($archivo)) {
    echo "<p>✅ Archivo encontrado: $archivo</p>";
    
    // Verificar sintaxis PHP
    $output = [];
    $return_var = 0;
    exec("php -l \"$archivo\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<p>✅ Sintaxis PHP correcta</p>";
    } else {
        echo "<p style='color: red;'>❌ Error de sintaxis PHP:</p>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
    
    // Verificar estados definidos
    echo "<h3>Estados de Citas Definidos:</h3>";
    $estados = [
        0 => 'Atendido',
        1 => 'Canceló',
        2 => 'No asistió',
        3 => 'Citado',
        4 => 'Llegó tarde',
        5 => 'Esperando',
        6 => 'Pendiente aprobación'
    ];
    
    echo "<ul>";
    foreach ($estados as $codigo => $descripcion) {
        echo "<li><strong>Estado $codigo:</strong> $descripcion</li>";
    }
    echo "</ul>";
    
    // Verificar que los archivos de validación existen
    $archivos_validacion = [
        'ValidadorReportes.php' => __DIR__ . '/../lib/ValidadorReportes.php',
        'ReportePDF.php' => __DIR__ . '/../lib/ReportePDF.php',
        'validacion_reportes.js' => __DIR__ . '/js/validacion_reportes.js'
    ];
    
    echo "<h3>Archivos de Validación:</h3>";
    foreach ($archivos_validacion as $nombre => $ruta) {
        if (file_exists($ruta)) {
            echo "<p>✅ $nombre encontrado</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ $nombre no encontrado en: $ruta</p>";
        }
    }
    
    echo "<h3>✅ Prueba completada</h3>";
    echo "<p><a href='citas_reportes.php'>🔗 Ir a Reportes de Citas</a></p>";
    
} else {
    echo "<p style='color: red;'>❌ Archivo no encontrado: $archivo</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>
