/* Estilos generales para el cuerpo */
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

/* Estilo para el contenedor principal */
.container {
    width: 80%;
    margin: 50px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Título del formulario */
h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

/* Estilos para los campos del formulario */
form {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

/* Estilo para cada campo de entrada */
input[type="text"], input[type="date"], input[type="email"], select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    box-sizing: border-box;
    font-size: 16px;
}

/* Estilo para los botones */
button {
    background-color: #4CAF50;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #45a049;
}

/* Estilo para los mensajes de error */
.error {
    color: red;
    font-size: 14px;
}

/* Estilo para los campos obligatorios */
label {
    font-weight: bold;
    color: #333;
}

/* Estilos de los campos cuando están enfocados */
input[type="text"]:focus, input[type="date"]:focus, input[type="email"]:focus, select:focus {
    border-color: #4CAF50;
    outline: none;
}

input:focus {
    outline: none; /* Quita el estilo de foco predeterminado */
    border-color: #ff6b6b; /* Opcional: cambia el borde para resaltar */
    box-shadow: 0 0 5px rgba(255, 107, 107, 0.5); /* Opcional: sombra para foco */
}


/* Estilos responsivos para dispositivos móviles */
@media screen and (max-width: 600px) {
    .container {
        width: 95%;
        padding: 15px;
    }

    form {
        grid-template-columns: 1fr;
    }

    button {
        width: 100%;
    }
}
