<?php
include_once '../config/config.php';

header("Content-Type: application/json");

$conn = getDBConnection();

// Consultar registros no sincronizados
$result = $conn->query("SELECT CLAVE , VISITA , REGISTRO , NOMBRES , APELLIDOS , CEDULA , SEXO , NACIONALIDAD ,ESTADOCIVIL , FECHANAC , LUGARNAC ,OCUPACION , RELIGION ,FECHAINGRESO , RH , CALLE ,  PROVINCIA , LOCALIDAD , MUNICIPIO , PAIS , REFERENCIA , TELEFONO , CELULAR , TELTRABAJO ,  FAX , ECORREO , ARS , PLANES , AFILIADO , VIGENCIA , POLIZA , CATEGORIA , ALERGIA , NAF ,  NSS , NOMBRERESP , <PERSON><PERSON><PERSON><PERSON><PERSON>RESP , <PERSON>D<PERSON><PERSON>RESP , TELEFONORESP , FAMILIARPROX , DIRECCIONFAMILIAR ,  TELEFONOFAMILIAR , <PERSON>EMITIDO , OBSERVACIONES , ESTATUS , STEMBARAZO , PESOHABITUAL , NIVELESCOLAR , PROCEDENCIA , SINCEDULA , PROFESION , RECORDCLINICA , LAST_MODIFIED , SINCRONIZADO FROM PACIENTES WHERE SINCRONIZADO = 0");

$datos = [];
while ($row = $result->fetch_assoc()) {
    $datos[] = $row;
}

echo json_encode($datos);
?>
