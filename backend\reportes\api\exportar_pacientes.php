<?php
// backend/reportes/api/exportar_pacientes.php
session_start();

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../lib/ValidadorReportes.php';
date_default_timezone_set('America/Santo_Domingo');

// Crear validador
$validador = new ValidadorReportes();

// Obtener parámetros
$formato = $_GET['formato'] ?? 'excel';
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-01-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$sexo = $_GET['sexo'] ?? '';
$edad_min = $_GET['edad_min'] ?? '';
$edad_max = $_GET['edad_max'] ?? '';
$provincia = $validador->sanitizarTexto($_GET['provincia'] ?? '');

// Validar permisos
if (!$validador->validarPermisos()) {
    echo $validador->respuestaErrorHTML('Error de Autenticación');
    exit;
}

// Validar conexión a base de datos
if (!$validador->validarConexionBD($pdo)) {
    echo $validador->respuestaErrorHTML('Error de Conexión');
    exit;
}

// Validar parámetros de entrada
if (!$validador->validarFormato($formato)) {
    echo $validador->respuestaErrorHTML('Formato Inválido');
    exit;
}

if (!$validador->validarFechas($fecha_inicio, $fecha_fin)) {
    echo $validador->respuestaErrorHTML('Error en Fechas');
    exit;
}

if (!$validador->validarSexo($sexo)) {
    echo $validador->respuestaErrorHTML('Sexo Inválido');
    exit;
}

if (!$validador->validarEdad($edad_min, $edad_max)) {
    echo $validador->respuestaErrorHTML('Rango de Edad Inválido');
    exit;
}

try {
    // Validar que existan datos en el rango especificado
    $condiciones_extra = [];
    if ($sexo) $condiciones_extra['SEXO'] = $sexo;
    if ($provincia) $condiciones_extra['PROVINCIA'] = $provincia;

    $validador->validarExistenciaDatos($pdo, 'PACIENTES', 'FECHAINGRESO', $fecha_inicio, $fecha_fin, $condiciones_extra);

    // Construir consulta
    $where_conditions = ["FECHAINGRESO BETWEEN ? AND ?"];
    $params = [$fecha_inicio, $fecha_fin];

    if ($sexo) {
        $where_conditions[] = "SEXO = ?";
        $params[] = $sexo;
    }
    if ($provincia) {
        $where_conditions[] = "PROVINCIA = ?";
        $params[] = $provincia;
    }

    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
    
    // Agregar filtro de edad
    $edad_clause = "";
    if ($edad_min || $edad_max) {
        if ($edad_min && $edad_max) {
            $edad_clause = " AND TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) BETWEEN $edad_min AND $edad_max";
        } elseif ($edad_min) {
            $edad_clause = " AND TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) >= $edad_min";
        } elseif ($edad_max) {
            $edad_clause = " AND TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) <= $edad_max";
        }
    }

    // Obtener datos
    $sql = "SELECT p.CEDULA, p.NOMBREAPELLIDO, p.SEXO, 
                  TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) as edad,
                  p.PROVINCIA, p.MUNICIPIO, p.TELEFONO, p.CELULAR, p.ECORREO,
                  p.FECHAINGRESO, p.OCUPACION, p.ESTADOCIVIL,
                  (SELECT MAX(c.FECHACON) FROM CITAMEDIC c WHERE c.CLAVEPAC = p.CLAVE) as ultima_cita,
                  (SELECT COUNT(*) FROM CITAMEDIC c WHERE c.CLAVEPAC = p.CLAVE) as total_citas
           FROM PACIENTES p 
           $where_clause $edad_clause 
           ORDER BY p.FECHAINGRESO DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Verificar si se encontraron datos
    if (empty($pacientes)) {
        $validador->limpiar();
        $validador->obtenerErrores()[] = "No se encontraron pacientes para los criterios especificados";
        $validador->obtenerAdvertencias()[] = "Intente ampliar el rango de fechas o modificar los filtros";
        echo $validador->respuestaErrorHTML('Sin Datos Disponibles');
        exit;
    }

    if ($formato === 'excel') {
        // Exportar a Excel (CSV)
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="reporte_pacientes_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // BOM para UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Encabezados
        fputcsv($output, [
            'Cédula',
            'Nombre Completo',
            'Sexo',
            'Edad',
            'Provincia',
            'Municipio',
            'Teléfono',
            'Celular',
            'Email',
            'Fecha Ingreso',
            'Ocupación',
            'Estado Civil',
            'Última Cita',
            'Total Citas'
        ], ';');
        
        // Datos
        foreach ($pacientes as $paciente) {
            fputcsv($output, [
                $paciente['CEDULA'],
                $paciente['NOMBREAPELLIDO'],
                $paciente['SEXO'] == 'M' ? 'Masculino' : 'Femenino',
                $paciente['edad'] ?? 'N/A',
                $paciente['PROVINCIA'] ?? '',
                $paciente['MUNICIPIO'] ?? '',
                $paciente['TELEFONO'] ?? '',
                $paciente['CELULAR'] ?? '',
                $paciente['ECORREO'] ?? '',
                $paciente['FECHAINGRESO'] ? date('d/m/Y', strtotime($paciente['FECHAINGRESO'])) : '',
                $paciente['OCUPACION'] ?? '',
                $paciente['ESTADOCIVIL'] ?? '',
                $paciente['ultima_cita'] ? date('d/m/Y', strtotime($paciente['ultima_cita'])) : 'Nunca',
                $paciente['total_citas']
            ], ';');
        }
        
        fclose($output);
        
    } else {
        // Exportar a PDF usando la clase ReportePDF
        require_once __DIR__ . '/../../lib/ReportePDF.php';

        // Obtener información de la empresa
        $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
        $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
        $nombre_empresa = $empresa ? $empresa['NOMBRE'] : 'Consultorio Médico';

        // Estadísticas
        $total_pacientes = count($pacientes);
        $masculinos = count(array_filter($pacientes, function($p) { return $p['SEXO'] == 'M'; }));
        $femeninos = count(array_filter($pacientes, function($p) { return $p['SEXO'] == 'F'; }));

        // Edad promedio
        $edades = array_filter(array_column($pacientes, 'edad'), function($edad) { return $edad !== null; });
        $edad_promedio = !empty($edades) ? round(array_sum($edades) / count($edades), 1) : 0;

        // Crear el reporte PDF
        $pdf = new ReportePDF('Reporte de Pacientes', $nombre_empresa);

        // Construir período
        $periodo = ReportePDF::formatearFecha($fecha_inicio) . ' - ' . ReportePDF::formatearFecha($fecha_fin);

        // Filtros aplicados
        $filtros = [];
        if ($sexo) $filtros[] = "Sexo: " . ReportePDF::formatearSexo($sexo);
        if ($provincia) $filtros[] = "Provincia: $provincia";
        if ($edad_min || $edad_max) {
            $filtro_edad = "Edad: ";
            if ($edad_min && $edad_max) $filtro_edad .= "$edad_min - $edad_max años";
            elseif ($edad_min) $filtro_edad .= "Desde $edad_min años";
            elseif ($edad_max) $filtro_edad .= "Hasta $edad_max años";
            $filtros[] = $filtro_edad;
        }
        $subtitulo = !empty($filtros) ? 'Filtros: ' . implode(' | ', $filtros) : '';

        $pdf->iniciarHTML()
           ->agregarEncabezado($subtitulo, $periodo);

        // Estadísticas
        $estadisticas = [
            ['valor' => $total_pacientes, 'etiqueta' => 'Total Pacientes'],
            ['valor' => $masculinos, 'etiqueta' => 'Masculinos'],
            ['valor' => $femeninos, 'etiqueta' => 'Femeninos'],
            ['valor' => $edad_promedio . ' años', 'etiqueta' => 'Edad Promedio']
        ];

        $pdf->agregarSeccionEstadisticas('Resumen Demográfico', $estadisticas);

        // Preparar datos para la tabla
        $encabezados = ['Cédula', 'Nombre', 'Sexo', 'Edad', 'Provincia', 'Teléfono', 'Fecha Ingreso', 'Última Cita', 'Total Citas'];
        $datos_tabla = [];

        foreach ($pacientes as $paciente) {
            $clase_sexo = $paciente['SEXO'] == 'M' ? 'sexo-m' : 'sexo-f';
            $sexo_texto = $paciente['SEXO'] == 'M' ? 'M' : 'F';

            $datos_tabla[] = [
                htmlspecialchars($paciente['CEDULA']),
                htmlspecialchars($paciente['NOMBREAPELLIDO']),
                "<span class='$clase_sexo'>$sexo_texto</span>",
                $paciente['edad'] ?? 'N/A',
                htmlspecialchars($paciente['PROVINCIA'] ?? ''),
                htmlspecialchars($paciente['TELEFONO'] ?? $paciente['CELULAR'] ?? ''),
                ReportePDF::formatearFecha($paciente['FECHAINGRESO']),
                $paciente['ultima_cita'] ? ReportePDF::formatearFecha($paciente['ultima_cita']) : 'Nunca',
                $paciente['total_citas']
            ];
        }

        $pdf->agregarTabla('Listado de Pacientes', $encabezados, $datos_tabla);

        // Agregar análisis adicional si hay datos
        if ($total_pacientes > 0) {
            // Análisis por grupos de edad
            $grupos_edad = [
                'Menores de 18' => 0,
                '18-30 años' => 0,
                '31-50 años' => 0,
                '51-70 años' => 0,
                'Mayores de 70' => 0
            ];

            foreach ($pacientes as $paciente) {
                $edad = $paciente['edad'];
                if ($edad !== null) {
                    if ($edad < 18) $grupos_edad['Menores de 18']++;
                    elseif ($edad <= 30) $grupos_edad['18-30 años']++;
                    elseif ($edad <= 50) $grupos_edad['31-50 años']++;
                    elseif ($edad <= 70) $grupos_edad['51-70 años']++;
                    else $grupos_edad['Mayores de 70']++;
                }
            }

            $contenido_adicional = "<div class='stats-section'>
                <h3 style='margin: 0 0 10px 0; color: #2c3e50;'>Distribución por Grupos de Edad</h3>
                <table style='width: 70%; margin: 0 auto;'>";

            foreach ($grupos_edad as $grupo => $cantidad) {
                $porcentaje = $total_pacientes > 0 ? round(($cantidad / $total_pacientes) * 100, 1) : 0;
                $contenido_adicional .= "<tr><td><strong>$grupo:</strong></td><td>$cantidad ($porcentaje%)</td></tr>";
            }

            $contenido_adicional .= "</table></div>";

            $pdf->agregarSeccionPersonalizada($contenido_adicional);
        }

        $pdf->finalizarHTML();

        echo $pdf->generarPDF('reporte_pacientes_' . date('Y-m-d'));
    }

} catch (PDOException $e) {
    $validador->limpiar();
    $validador->obtenerErrores()[] = "Error en la base de datos: " . $e->getMessage();
    $validador->obtenerAdvertencias()[] = "Verifique la conexión a la base de datos y contacte al administrador";
    echo $validador->respuestaErrorHTML('Error de Base de Datos');

    // Log del error para el administrador
    error_log("Error PDO en exportar_pacientes.php: " . $e->getMessage() . " - Usuario: " . ($_SESSION['usuario'] ?? 'desconocido'));

} catch (Exception $e) {
    $validador->limpiar();
    $validador->obtenerErrores()[] = "Error interno del sistema: " . $e->getMessage();
    $validador->obtenerAdvertencias()[] = "Si el problema persiste, contacte al soporte técnico";
    echo $validador->respuestaErrorHTML('Error del Sistema');

    // Log del error para el administrador
    error_log("Error general en exportar_pacientes.php: " . $e->getMessage() . " - Usuario: " . ($_SESSION['usuario'] ?? 'desconocido'));
}
?>
