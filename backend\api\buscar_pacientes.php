<?php
require_once '../config/database.php';
$pdo = include '../config/database.php';

$termino = $_GET['q'] ?? '';

if (strlen($termino) < 2) {
    echo json_encode([]);
    exit;
}

$sql = "SELECT CLAVE, NOMBRES, APELLIDOS, CEDULA
        FROM PACIENTES 
        WHERE NOMBRES LIKE :busqueda OR APELLIDOS LIKE :busqueda OR CEDULA LIKE :busqueda 
        ORDER BY NOMBRES LIMIT 10";

$stmt = $pdo->prepare($sql);
$stmt->execute([':busqueda' => "%$termino%"]);
$resultados = $stmt->fetchAll();

echo json_encode($resultados);
