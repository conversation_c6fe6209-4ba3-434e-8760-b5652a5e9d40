<?php
// backend/reportes/test_estados.php
// Script de prueba para verificar los estados de citas

session_start();
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../lib/ValidadorReportes.php';
require_once __DIR__ . '/../lib/ReportePDF.php';

// Simular sesión para prueba
$_SESSION['usuario'] = 'admin';
$_SESSION['rol'] = 'admin';

echo "<h2>Prueba de Estados de Citas</h2>";

try {
    // Probar validador
    $validador = new ValidadorReportes();
    
    echo "<h3>1. Prueba de Validación de Estados</h3>";
    $estados_prueba = [0, 1, 2, 3, 4, 5, 6, 7, -1, 'abc'];
    
    foreach ($estados_prueba as $estado) {
        $validador->limpiar();
        $resultado = $validador->validarEstadoCita($estado);
        echo "<p>Estado '$estado': " . ($resultado ? "✅ Válido" : "❌ Inválido") . "</p>";
        if (!$resultado) {
            foreach ($validador->obtenerErrores() as $error) {
                echo "<small style='color: red;'>- $error</small><br>";
            }
        }
    }
    
    // Probar formateo de estados
    echo "<h3>2. Prueba de Formateo de Estados</h3>";
    for ($i = 0; $i <= 7; $i++) {
        $estado_texto = ReportePDF::formatearEstadoCita($i);
        echo "<p>Estado $i: $estado_texto</p>";
    }
    
    // Probar consulta de estados
    echo "<h3>3. Prueba de Consulta de Estados en BD</h3>";
    $stmt = $pdo->query("
        SELECT ESTATUS, 
               CASE ESTATUS 
                   WHEN 0 THEN 'Atendido'
                   WHEN 1 THEN 'Canceló'
                   WHEN 2 THEN 'No asistió'
                   WHEN 3 THEN 'Citado'
                   WHEN 4 THEN 'Llegó tarde'
                   WHEN 5 THEN 'Esperando'
                   WHEN 6 THEN 'Pendiente aprobación'
                   ELSE 'Desconocido'
               END as estado_texto,
               COUNT(*) as cantidad
        FROM CITAMEDIC 
        GROUP BY ESTATUS 
        ORDER BY ESTATUS
    ");
    
    $estados_bd = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($estados_bd)) {
        echo "<p>⚠️ No hay datos de citas en la base de datos</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Código</th><th>Estado</th><th>Cantidad</th></tr>";
        foreach ($estados_bd as $estado) {
            echo "<tr>";
            echo "<td>{$estado['ESTATUS']}</td>";
            echo "<td>{$estado['estado_texto']}</td>";
            echo "<td>{$estado['cantidad']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Probar consulta con parámetros
    echo "<h3>4. Prueba de Consulta con Filtros</h3>";
    $fecha_inicio = date('Y-m-01');
    $fecha_fin = date('Y-m-t');
    
    $where_conditions = ["FECHACON BETWEEN ? AND ?"];
    $params = [$fecha_inicio, $fecha_fin];
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
    
    echo "<p>Consulta: SELECT COUNT(*) FROM CITAMEDIC $where_clause</p>";
    echo "<p>Parámetros: " . implode(', ', $params) . "</p>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_clause");
    $stmt->execute($params);
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>Total de citas en el mes actual: $total</p>";
    
    // Probar con filtro de estado
    echo "<h3>5. Prueba con Filtro de Estado</h3>";
    $estatus_filtro = 0; // Atendido
    
    $where_conditions_filtro = ["FECHACON BETWEEN ? AND ?", "ESTATUS = ?"];
    $params_filtro = [$fecha_inicio, $fecha_fin, $estatus_filtro];
    $where_clause_filtro = "WHERE " . implode(" AND ", $where_conditions_filtro);
    
    echo "<p>Consulta con filtro: SELECT COUNT(*) FROM CITAMEDIC $where_clause_filtro</p>";
    echo "<p>Parámetros: " . implode(', ', $params_filtro) . "</p>";
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_clause_filtro");
    $stmt->execute($params_filtro);
    $total_filtrado = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>Total de citas atendidas en el mes actual: $total_filtrado</p>";
    
    echo "<h3>✅ Todas las pruebas completadas exitosamente</h3>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error en las pruebas</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>Archivo: " . $e->getFile() . "</p>";
    echo "<p>Línea: " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<p><a href='citas_reportes.php'>← Volver a Reportes de Citas</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
