<?php
// facturacion.php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Facturación de Paciente</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- Favicon médico -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
  <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">

  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Variables CSS médicas */
    :root {
      --medical-primary: #059669;
      --medical-secondary: #10b981;
      --medical-accent: #0ea5e9;
      --medical-info: #3b82f6;
      --medical-warning: #f59e0b;
      --medical-danger: #ef4444;
      --medical-light: #ffffff;
      --medical-gray-50: #f9fafb;
      --medical-gray-100: #f3f4f6;
      --medical-gray-200: #e5e7eb;
      --medical-dark: #1f2937;
    }

    /* Estilos base */
    body {
      font-family: 'Inter', sans-serif;
      background: var(--medical-light);
      background-image: 
        radial-gradient(circle at 25% 25%, rgba(5, 150, 105, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
      color: var(--medical-dark);
    }

    /* Header médico */
    .medical-header {
      background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
      color: white;
      padding: 2rem 0;
      margin-bottom: 2rem;
      border-radius: 0 0 20px 20px;
      box-shadow: 0 4px 20px rgba(5, 150, 105, 0.2);
    }

    .medical-header h4 {
      font-weight: 700;
      margin: 0;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    /* Container médico */
    .container {
      max-width: 1200px;
    }

    /* Cards médicas */
    .card {
      border: 2px solid var(--medical-gray-100);
      border-radius: 15px;
      box-shadow: 0 4px 20px rgba(5, 150, 105, 0.08);
      transition: all 0.3s ease;
    }

    .card:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 30px rgba(5, 150, 105, 0.15);
      border-color: var(--medical-primary);
    }

    /* Formularios médicos */
    .form-control, .form-select {
      border: 2px solid var(--medical-gray-200);
      border-radius: 10px;
      padding: 0.8rem 1rem;
      transition: all 0.3s ease;
      background: var(--medical-light);
    }

    .form-control:focus, .form-select:focus {
      border-color: var(--medical-primary);
      box-shadow: 0 0 0 0.2rem rgba(5, 150, 105, 0.25);
      background: var(--medical-light);
    }

    .form-label {
      color: var(--medical-dark);
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    /* Botones médicos */
    .btn-primary {
      background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
      border: none;
      border-radius: 12px;
      padding: 0.8rem 2rem;
      font-weight: 600;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
    }

    .btn-primary:hover {
      background: linear-gradient(135deg, var(--medical-secondary), var(--medical-primary));
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, var(--medical-secondary), var(--medical-primary));
      border: none;
      border-radius: 12px;
      color: white;
      font-weight: 600;
      padding: 0.8rem 2rem;
    }

    .btn-success:hover {
      background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
      transform: translateY(-2px);
      color: white;
    }

    .btn-secondary {
      background: var(--medical-gray-100);
      border: 2px solid var(--medical-gray-200);
      color: var(--medical-dark);
      border-radius: 12px;
      font-weight: 600;
      padding: 0.8rem 2rem;
    }

    .btn-warning {
      background: linear-gradient(135deg, var(--medical-warning), #d97706);
      border: none;
      color: white;
      font-weight: 600;
    }

    .btn-danger {
      background: linear-gradient(135deg, var(--medical-danger), #dc2626);
      border: none;
      color: white;
      font-weight: 600;
    }

    /* Tablas médicas */
    .table {
      background: var(--medical-light);
      border-radius: 15px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(5, 150, 105, 0.08);
    }

    .table thead th {
      background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
      color: white;
      border: none;
      font-weight: 600;
      padding: 1rem;
    }

    .table tbody td {
      padding: 1rem;
      border-color: var(--medical-gray-100);
    }

    .table tbody tr:hover {
      background: var(--medical-gray-50);
    }

    /* Autocompletado médico */
    .autocomplete-suggestions {
      position: absolute;
      background: var(--medical-light);
      border: 2px solid var(--medical-primary);
      border-radius: 10px;
      z-index: 1000;
      width: 100%;
      max-height: 200px;
      overflow-y: auto;
      box-shadow: 0 8px 30px rgba(5, 150, 105, 0.2);
    }

    .autocomplete-suggestion {
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 1px solid var(--medical-gray-100);
      transition: all 0.2s ease;
    }

    .autocomplete-suggestion:hover {
      background: var(--medical-gray-50);
      color: var(--medical-primary);
      font-weight: 500;
    }

    .autocomplete-suggestion:last-child {
      border-bottom: none;
    }

    /* Formulario oculto */
    #formNuevaFactura {
      display: none;
      background: var(--medical-light);
      border: 2px solid var(--medical-gray-100);
      border-radius: 20px;
      padding: 2rem;
      box-shadow: 0 8px 30px rgba(5, 150, 105, 0.1);
    }

    #formNuevaFactura h5 {
      color: var(--medical-primary);
      font-weight: 700;
      margin-bottom: 1.5rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid var(--medical-gray-100);
    }

    /* Mensajes */
    #mensaje {
      padding: 1rem;
      border-radius: 10px;
      font-weight: 500;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .medical-header {
        padding: 1.5rem 0;
      }
      
      .container {
        padding: 0 1rem;
      }
    }
  </style>
</head>
<body>
<!-- Header médico -->
<div class="medical-header">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-md-8">
        <h4><i class="fas fa-file-invoice-dollar me-3"></i>Facturación de Paciente</h4>
        <p class="mb-0 opacity-75">Sistema integral de facturación para consultorio médico</p>
      </div>
      <div class="col-md-4 text-end">
        <a href="javascript:history.back()" class="btn btn-light">
          <i class="fas fa-arrow-left me-2"></i>Volver
        </a>
      </div>
    </div>
  </div>
</div>

<div class="container mt-4">
  <!-- 🔍 Buscador -->
  <div class="mb-3 position-relative">
    <label for="buscarPaciente" class="form-label">
      <i class="fas fa-search me-2 text-primary"></i>Buscar paciente (nombre, cédula)
    </label>
    <input type="text" id="buscarPaciente" class="form-control" placeholder="Ej: María Pérez o 00112345678">
    <div id="sugerencias" class="autocomplete-suggestions"></div>
  </div>

  <!-- Datos del paciente -->
  <div id="infoPaciente" class="d-none mb-3"></div>

  <!-- Tabla de facturas -->
  <div id="tablaFacturas" class="d-none mb-3"></div>

  <button id="btnMostrarFormulario" class="btn btn-success d-none mb-3">
    <i class="fas fa-plus me-2"></i>Insertar nueva factura
  </button>

  <!-- Formulario de nueva factura -->
  <form id="formNuevaFactura" class="border p-3 mt-4 bg-light" style="display:none;">
    <h5 class="mb-3"><i class="fas fa-receipt me-2"></i>Concepto y Pago del Paciente</h5>

    <div class="row mb-3">
      <div class="col-md-6">
        <label class="form-label">Concepto</label>
        <input type="text" name="concepto" id="concepto" class="form-control" required>
      </div>
      <div class="col-md-3">
        <label class="form-label">Modo de pago</label>
        <select name="modopago" id="modopago" class="form-select" required>
          <option value="Efectivo">Efectivo</option>
          <option value="Tarjeta">Tarjeta</option>
          <option value="Transferencia">Transferencia</option>
          <option value="Gratis">Gratis</option>
        </select>
      </div>
      <div class="col-md-3">
        <label class="form-label">Fecha Pago</label>
        <input type="date" name="fechapago" id="fechapago" class="form-control" required>
      </div>
    </div>

    <div class="row mb-3 align-items-end">
      <div class="col-md-2">
        <div class="form-check">
          <input type="checkbox" name="acredito" class="form-check-input" id="acredito">
          <label class="form-check-label" for="acredito">A Crédito</label>
        </div>
      </div>
      <div class="row mb-3">
        <div class="col-md-4">
          <label class="form-label">Precio Paciente</label>
          <input type="number" class="form-control" name="precio" id="precio" step="0.01" required>
        </div>
        <div class="col-md-4">
          <label class="form-label">Pagado</label>
          <input type="number" class="form-control" name="pagado" id="pagado" step="0.01" required>
        </div>
        <div class="col-md-4">
          <label class="form-label">Pendiente</label>
          <input type="number" class="form-control bg-light" name="pendiente" id="pendiente" readonly>
        </div>
      </div>

      <!-- A crédito extra -->
      <div id="bloqueCredito" class="row mb-3" style="display:none;">
        <div class="col-md-2">
          <label class="form-label">No. Pago</label>
          <input type="number" name="nopago" class="form-control">
        </div>
        <div class="col-md-2">
          <label class="form-label">Plazo (días)</label>
          <input type="number" name="plazo" class="form-control">
        </div>
        <div class="col-md-2">
          <label class="form-label">Interés (%)</label>
          <input type="number" name="interes" class="form-control" step="0.01">
        </div>
      </div>

      <hr>

      <h5 class="mb-3"><i class="fas fa-hospital me-2"></i>ARS <span id="arsNombreLabel">(seleccionada)</span></h5>
      <div class="row mb-3">
        <div class="col-md-3">
          <label class="form-label">No. Autorización</label>
          <input type="text" name="noautorizacion" id="noautorizacion" class="form-control">
        </div>
        <div class="col-md-5">
          <label class="form-label">Procedimiento Aplicado</label>
          <input type="text" name="procedimiento" id="procedimiento" class="form-control">
        </div>
        <div class="col-md-4">
          <label class="form-label">Valor Reclamado</label>
          <input type="number" step="0.01" name="valorreclamado" id="valorreclamado" class="form-control">
        </div>
      </div>

      <!-- Datos ocultos para envío -->
      <input type="hidden" name="clavepac" id="clavepac">
      <input type="hidden" name="cedula" id="cedula">
      <input type="hidden" name="estatus" value="A">
      <input type="hidden" name="streclama" value="A">
      <input type="hidden" name="usuario" value="1">
      <input type="hidden" name="consultorio" value="1">
      <input type="hidden" name="FARS" id="FARS">
      <input type="hidden" name="clavefactura" id="clavefactura">

      <!-- Acción -->
      <div class="d-flex justify-content-end gap-2 mt-3">
        <button type="submit" class="btn btn-success">
          <i class="fas fa-save me-2"></i>Guardar
        </button>
        <button type="button" class="btn btn-secondary" id="cancelarForm">
          <i class="fas fa-times me-2"></i>Cancelar
        </button>
      </div>
    </div>
  </form>

  <div id="mensaje" class="mt-3"></div>
</div>

<script>
document.addEventListener("DOMContentLoaded", function () {
  const input = document.getElementById("buscarPaciente");
  const sugerencias = document.getElementById("sugerencias");
  const infoPaciente = document.getElementById("infoPaciente");
  const tablaFacturas = document.getElementById("tablaFacturas");
  const formFactura = document.getElementById("formNuevaFactura");
  const btnInsertar = document.getElementById("btnMostrarFormulario");
  const cancelarBtn = document.getElementById("cancelarForm");

  input.addEventListener("input", async function () {
    const query = this.value.trim();
    sugerencias.innerHTML = "";

    if (query.length < 2) return;

    try {
      const res = await fetch(`../api/buscar_pacientes.php?q=${encodeURIComponent(query)}`);
      if (!res.ok) throw new Error("No se pudo buscar pacientes");
      const pacientes = await res.json();

      pacientes.forEach(p => {
        const item = document.createElement("div");
        item.className = "autocomplete-suggestion list-group-item list-group-item-action";
        item.textContent = `${p.NOMBRES} ${p.APELLIDOS} - ${p.CEDULA}`;
        item.dataset.clave = p.CLAVE;

        item.addEventListener("click", () => {
          input.value = item.textContent;
          sugerencias.innerHTML = "";
          cargarPaciente(p.CLAVE);
        });

        sugerencias.appendChild(item);
      });
    } catch (err) {
      console.error("Error buscando pacientes:", err);
    }
  });

  // Verificar si viene una cédula como parámetro "q" en la URL
  const params = new URLSearchParams(window.location.search);
  const cedulaURL = params.get("q");

  if (cedulaURL) {
    input.value = cedulaURL;

    fetch(`../api/buscar_pacientes.php?q=${encodeURIComponent(cedulaURL)}`)
      .then(res => res.json())
      .then(pacientes => {
        if (pacientes.length === 1) {
          const paciente = pacientes[0];
          cargarPaciente(paciente.CLAVE);
        } else {
          console.warn("No se encontró un paciente único para esa cédula.");
        }
      })
      .catch(err => console.error("Error buscando paciente desde la URL:", err));
  }

async function cargarPaciente(clavepac) {
  try {
    const res = await fetch(`../api/get_paciente_info.php?clavepac=${clavepac}`);
    const data = await res.json();

    if (data.status === "success") {
      const p = data.paciente;
      const facturas = data.facturas;

      document.getElementById("clavepac").value = p.CLAVE;
      document.getElementById("cedula").value = p.CEDULA;
      document.getElementById("FARS").value = p.ars_clave;
      infoPaciente.innerHTML = `
        <div class="card">
          <div class="card-body">
            <h6 class="card-title text-primary"><i class="fas fa-user me-2"></i>Información del Paciente</h6>
            <p><strong>Nombre:</strong> ${p.nombre}</p>
            <p><strong>Cédula:</strong> ${p.CEDULA}</p>
            <p><strong>Teléfono:</strong> ${p.telefono || '—'}</p>
            <p><strong>ARS:</strong> ${p.ars_nombre || '—'}</p>
            <p><strong>ARS Clave:</strong> ${p.ars_clave || '—'}</p>
          </div>
        </div>
      `;
      infoPaciente.classList.remove("d-none");

     let tabla = `<table class="table table-bordered mt-3">
  <thead>
    <tr>
      <th><i class="fas fa-hashtag me-1"></i>Factura</th>
      <th><i class="fas fa-calendar me-1"></i>Fecha</th>
      <th><i class="fas fa-file-text me-1"></i>Concepto</th>
      <th><i class="fas fa-dollar-sign me-1"></i>Precio</th>
      <th><i class="fas fa-check-circle me-1"></i>Pagado</th>
      <th><i class="fas fa-hospital me-1"></i>Reclamado</th>
      <th><i class="fas fa-info-circle me-1"></i>EST</th>
      <th><i class="fas fa-cogs me-1"></i>Acciones</th>
    </tr>
  </thead>
  <tbody>`;

if (facturas.length > 0) {
  facturas.forEach(f => {
    tabla += `<tr id="fila-${f.CLAVE}">
      <td>${f.NUMFACT}</td>
      <td>${f.FECHA}</td>
      <td>${f.CONCEPTO}</td>
      <td>$${f.PRECIO}</td>
      <td>$${f.PAGADO}</td>
      <td>$${f.VALORRECLAMADO}</td>
      <td><span class="badge ${f.ESTATUS === 'A' ? 'bg-success' : 'bg-secondary'}">${f.ESTATUS}</span></td>
      <td>
        <button class="btn btn-sm btn-warning me-1" onclick="editarFactura(${f.CLAVE})" title="Editar">
          <i class="fas fa-edit"></i>
        </button>
        <button class="btn btn-sm btn-danger me-1" onclick="eliminarFactura(${f.CLAVE})" title="Eliminar">
          <i class="fas fa-trash"></i>
        </button>
        <a href="factura_print.php?clave=${f.CLAVE}" target="_blank" class="btn btn-sm btn-primary" title="Imprimir">
          <i class="fas fa-print"></i>
        </a>
      </td>
    </tr>`;
  });
} else {
  tabla += `<tr><td colspan="8" class="text-center text-muted">Sin facturas registradas</td></tr>`;
}

        tabla += `</tbody></table>`;

      tablaFacturas.innerHTML = tabla;
      tablaFacturas.classList.remove("d-none");
      btnInsertar.classList.remove("d-none");
      formFactura.style.display = "none";
    }
  } catch (err) {
    console.error("Error al cargar paciente:", err);
  }
}

btnInsertar.addEventListener("click", () => {
    document.getElementById("clavefactura").value = "";
    formFactura.reset();
    formFactura.style.display = "block";
    btnInsertar.classList.add("d-none");
});

  cancelarBtn.addEventListener("click", () => {
    formFactura.style.display = "none";
    btnInsertar.classList.remove("d-none");
  });

  formFactura.addEventListener("submit", async function (e) {
    e.preventDefault();
    const formData = new FormData(formFactura);

    const res = await fetch("insertar_factura.php", {
      method: "POST",
      body: formData
    });

    const result = await res.json();
    const mensaje = document.getElementById("mensaje");
    mensaje.innerHTML = `<div class="alert alert-${result.status === 'success' ? 'success' : 'danger'}">${result.message || '✔️'}</div>`;

    if (result.status === "success") {
      cargarPaciente(formData.get("clavepac"));
      formFactura.reset();
      formFactura.style.display = "none";
      btnInsertar.classList.remove("d-none");
    }
  });
});
</script>

<script>
  document.addEventListener("DOMContentLoaded", () => {
    const precioInput = document.getElementById("precio");
    const pagadoInput = document.getElementById("pagado");
    const pendienteInput = document.getElementById("pendiente");

    function actualizarPendiente() {
      const precio = parseFloat(precioInput.value) || 0;
      const pagado = parseFloat(pagadoInput.value) || 0;
      const pendiente = (precio - pagado).toFixed(2);
      pendienteInput.value = pendiente >= 0 ? pendiente : 0;
    }

    precioInput.addEventListener("input", actualizarPendiente);
    pagadoInput.addEventListener("input", actualizarPendiente);
  });

editarFactura = async function(clave) {
  if (!clave) {
    alert("No se puede editar: clave inválida.");
    return;
  }

  try {
    const res = await fetch(`get_factura.php?clave=${clave}`);
    const data = await res.json();

    if (data.status === "success") {
      const f = data.factura;

      document.getElementById("clavefactura").value = f.CLAVE;
      document.getElementById("concepto").value = f.CONCEPTO;
      document.getElementById("precio").value = f.PRECIO;
      document.getElementById("pagado").value = f.PAGADO;
      document.getElementById("pendiente").value = (f.PRECIO - f.PAGADO).toFixed(2);
      document.getElementById("fechapago").value = f.FECHAPAGO;
      document.getElementById("modopago").value = f.MODOPGO;
      document.getElementById("noautorizacion").value = f.NOAUTORIZACION || '';
      document.getElementById("procedimiento").value = f.PROCEDIMIENTOS || '';
      document.getElementById("valorreclamado").value = f.VALORRECLAMADO || '';
      document.getElementById("FARS").value = f.FARS;

      document.getElementById("formNuevaFactura").style.display = "block";
      document.getElementById("btnMostrarFormulario").classList.add("d-none");
    } else {
      alert("Factura no encontrada");
    }
  } catch (err) {
    console.error("Error al cargar factura:", err);
  }
};

eliminarFactura = async function(clave) {
  if (!confirm("¿Estás seguro de eliminar esta factura?")) return;

  try {
    const res = await fetch(`eliminar_factura.php?clave=${clave}`, {
      method: "DELETE"
    });

    const data = await res.json();
    if (data.status === "success") {
      document.getElementById(`fila-${clave}`).remove();
      const mensaje = document.getElementById("mensaje");
      mensaje.innerHTML = '<div class="alert alert-success">✅ Factura eliminada correctamente.</div>';
    } else {
      alert("❌ Error al eliminar factura: " + data.message);
    }
  } catch (err) {
    console.error("Error al eliminar:", err);
    alert("Error al eliminar factura.");
  }
};
</script>

</body>
</html>
