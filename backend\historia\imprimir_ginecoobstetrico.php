<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? null;

if (!$CLAVEPAC) {
  die("Paciente no especificado.");
}

$stmt = $pdo->prepare("SELECT * FROM ANTGINECOBSTETRICO WHERE CLAVEPAC = :clavepac LIMIT 1");
$stmt->execute(['clavepac' => $CLAVEPAC]);
$datos = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$datos) {
  die("No hay datos registrados.");
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Impresión - Gineco-Obstétrico</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    @media print {
      .no-print { display: none; }
    }
  </style>
</head>
<body class="p-4">
  <div class="d-flex justify-content-between mb-4">
    <h4>🖨️ Impresión - Antecedentes Gineco-Obstétricos</h4>
    <button class="btn btn-primary no-print" onclick="window.print()">Imprimir</button>
  </div>

  <div class="table-responsive">
    <table class="table table-bordered table-sm">
      <?php foreach ($datos as $campo => $valor): ?>
        <tr>
          <th><?= ucfirst(strtolower($campo)) ?></th>
          <td><?= htmlspecialchars($valor) ?></td>
        </tr>
      <?php endforeach; ?>
    </table>
  </div>
</body>
</html>
