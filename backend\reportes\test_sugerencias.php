<?php
/**
 * Página de prueba para verificar el sistema de sugerencias
 */

session_start();
require_once __DIR__ . '/../config/database.php';

echo "<h1>🧪 Test del Sistema de Sugerencias</h1>";

// Información de sesión
echo "<h2>📋 Información de Sesión</h2>";
echo "<ul>";
echo "<li><strong>Usuario:</strong> " . ($_SESSION['usuario'] ?? 'No definido') . "</li>";
echo "<li><strong>Rol:</strong> " . ($_SESSION['rol'] ?? 'No definido') . "</li>";
echo "<li><strong>Sesión activa:</strong> " . (isset($_SESSION['usuario']) ? 'Sí' : 'No') . "</li>";
echo "</ul>";

// Test de conexión a base de datos
echo "<h2>🔌 Test de Conexión a Base de Datos</h2>";
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✅ Conexión a base de datos exitosa</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error de conexión: " . $e->getMessage() . "</p>";
}

// Test de creación de tabla
echo "<h2>🗃️ Test de Creación de Tabla</h2>";
try {
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS SUGERENCIAS_REPORTES (
            CLAVE INT AUTO_INCREMENT PRIMARY KEY,
            SUGERENCIA TEXT NOT NULL,
            USUARIO VARCHAR(100) DEFAULT 'Anónimo',
            IP_ADDRESS VARCHAR(45),
            USER_AGENT TEXT,
            FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ESTADO ENUM('PENDIENTE', 'REVISADA', 'IMPLEMENTADA', 'RECHAZADA') DEFAULT 'PENDIENTE',
            PRIORIDAD ENUM('BAJA', 'MEDIA', 'ALTA') DEFAULT 'MEDIA',
            COMENTARIOS_ADMIN TEXT,
            FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTableSQL);
    echo "<p style='color: green;'>✅ Tabla SUGERENCIAS_REPORTES creada/verificada correctamente</p>";
    
    // Verificar estructura de la tabla
    $stmt = $pdo->query("DESCRIBE SUGERENCIAS_REPORTES");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>📊 Estructura de la Tabla:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error creando tabla: " . $e->getMessage() . "</p>";
}

// Test de consulta de sugerencias
echo "<h2>📝 Test de Consulta de Sugerencias</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM SUGERENCIAS_REPORTES");
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p style='color: green;'>✅ Total de sugerencias en la base de datos: " . $count['total'] . "</p>";
    
    if ($count['total'] > 0) {
        $stmt = $pdo->query("SELECT * FROM SUGERENCIAS_REPORTES ORDER BY FECHA_CREACION DESC LIMIT 5");
        $sugerencias = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>🔍 Últimas 5 Sugerencias:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Usuario</th><th>Sugerencia</th><th>Estado</th><th>Fecha</th></tr>";
        foreach ($sugerencias as $sug) {
            echo "<tr>";
            echo "<td>" . $sug['CLAVE'] . "</td>";
            echo "<td>" . $sug['USUARIO'] . "</td>";
            echo "<td>" . substr($sug['SUGERENCIA'], 0, 50) . "...</td>";
            echo "<td>" . $sug['ESTADO'] . "</td>";
            echo "<td>" . $sug['FECHA_CREACION'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error consultando sugerencias: " . $e->getMessage() . "</p>";
}

// Test de inserción de sugerencia de prueba
echo "<h2>➕ Test de Inserción</h2>";
if (isset($_POST['test_insert'])) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO SUGERENCIAS_REPORTES 
            (SUGERENCIA, USUARIO, IP_ADDRESS, USER_AGENT) 
            VALUES (?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Sugerencia de prueba - ' . date('Y-m-d H:i:s'),
            $_SESSION['usuario'] ?? 'Usuario_Test',
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'Test User Agent'
        ]);
        
        echo "<p style='color: green;'>✅ Sugerencia de prueba insertada correctamente (ID: " . $pdo->lastInsertId() . ")</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error insertando sugerencia de prueba: " . $e->getMessage() . "</p>";
    }
}

echo "<form method='POST'>";
echo "<button type='submit' name='test_insert' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px;'>🧪 Insertar Sugerencia de Prueba</button>";
echo "</form>";

// Enlaces de navegación
echo "<h2>🔗 Enlaces de Navegación</h2>";
echo "<ul>";
echo "<li><a href='personalizados_reportes.php'>📄 Ir a Reportes Personalizados</a></li>";
echo "<li><a href='admin_sugerencias.php'>👨‍💼 Ir a Admin de Sugerencias</a></li>";
echo "<li><a href='api/enviar_sugerencia.php'>🔧 Test API (POST)</a></li>";
echo "</ul>";

// Información del sistema
echo "<h2>ℹ️ Información del Sistema</h2>";
echo "<ul>";
echo "<li><strong>PHP Version:</strong> " . phpversion() . "</li>";
echo "<li><strong>Servidor:</strong> " . $_SERVER['SERVER_SOFTWARE'] ?? 'Desconocido' . "</li>";
echo "<li><strong>Directorio actual:</strong> " . __DIR__ . "</li>";
echo "<li><strong>Archivo de configuración:</strong> " . (__DIR__ . '/../config/database.php') . "</li>";
echo "<li><strong>Archivo existe:</strong> " . (file_exists(__DIR__ . '/../config/database.php') ? 'Sí' : 'No') . "</li>";
echo "</ul>";

echo "<hr>";
echo "<p><small>🕒 Generado el: " . date('Y-m-d H:i:s') . "</small></p>";
?>
