<?php
// backend/reportes/personalizados_reportes.php
session_start();
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: ../../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

$usuario = htmlspecialchars($_SESSION['usuario']);
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reportes Personalizados - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .header-section {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .template-card {
            border: 2px dashed #dee2e6;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
            transition: all 0.3s;
        }
        .template-card:hover {
            border-color: #dc3545;
            background-color: #f8f9fa;
        }
        .coming-soon {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
            border-radius: 15px;
            padding: 3rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-magic me-3"></i>Reportes Personalizados</h1>
                    <p class="mb-0">Crea reportes específicos según tus necesidades</p>
                </div>
                <div class="col-md-4 text-end">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-end">
                            <li class="breadcrumb-item"><a href="../../../index.php" class="text-white">Inicio</a></li>
                            <li class="breadcrumb-item"><a href="../../reportes.php" class="text-white">Reportes</a></li>
                            <li class="breadcrumb-item active text-white-50">Personalizados</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Funcionalidad Próximamente -->
        <div class="coming-soon">
            <i class="fas fa-tools fa-4x mb-4"></i>
            <h2>Funcionalidad en Desarrollo</h2>
            <p class="lead">Los reportes personalizados estarán disponibles próximamente</p>
            <p>Esta sección permitirá crear reportes específicos con filtros avanzados, campos personalizables y programación automática.</p>
        </div>

        <!-- Plantillas de Reportes Futuros -->
        <div class="row">
            <div class="col-md-4">
                <div class="template-card">
                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                    <h5>Constructor de Reportes</h5>
                    <p class="text-muted">Arrastra y suelta campos para crear reportes personalizados</p>
                    <button class="btn btn-outline-danger" disabled>
                        <i class="fas fa-lock me-2"></i>Próximamente
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="template-card">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h5>Reportes Programados</h5>
                    <p class="text-muted">Programa reportes automáticos por email</p>
                    <button class="btn btn-outline-danger" disabled>
                        <i class="fas fa-lock me-2"></i>Próximamente
                    </button>
                </div>
            </div>
            <div class="col-md-4">
                <div class="template-card">
                    <i class="fas fa-download fa-3x text-muted mb-3"></i>
                    <h5>Exportación Avanzada</h5>
                    <p class="text-muted">Exporta en múltiples formatos con plantillas</p>
                    <button class="btn btn-outline-danger" disabled>
                        <i class="fas fa-lock me-2"></i>Próximamente
                    </button>
                </div>
            </div>
        </div>

        <!-- Características Futuras -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-star me-2"></i>Características Planificadas</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>Constructor Visual</h6>
                        <ul>
                            <li>Interfaz drag & drop</li>
                            <li>Selección de campos personalizada</li>
                            <li>Filtros avanzados</li>
                            <li>Agrupaciones y totales</li>
                        </ul>

                        <h6><i class="fas fa-check-circle text-success me-2"></i>Formatos de Exportación</h6>
                        <ul>
                            <li>PDF con plantillas personalizadas</li>
                            <li>Excel con gráficos</li>
                            <li>CSV para análisis</li>
                            <li>Impresión directa</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>Automatización</h6>
                        <ul>
                            <li>Reportes programados</li>
                            <li>Envío automático por email</li>
                            <li>Alertas por condiciones</li>
                            <li>Integración con calendario</li>
                        </ul>

                        <h6><i class="fas fa-check-circle text-success me-2"></i>Análisis Avanzado</h6>
                        <ul>
                            <li>Gráficos interactivos</li>
                            <li>Comparativas temporales</li>
                            <li>Tendencias y proyecciones</li>
                            <li>Dashboard personalizable</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sugerencias de Reportes -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb me-2"></i>Sugerencias de Reportes Personalizados</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="list-group">
                            <div class="list-group-item">
                                <h6 class="mb-1">Reporte de Seguimiento de Pacientes</h6>
                                <p class="mb-1">Pacientes con citas pendientes, historial de visitas y próximas citas programadas.</p>
                            </div>
                            <div class="list-group-item">
                                <h6 class="mb-1">Análisis de Productividad Médica</h6>
                                <p class="mb-1">Número de pacientes atendidos por doctor, tiempo promedio de consulta, ingresos generados.</p>
                            </div>
                            <div class="list-group-item">
                                <h6 class="mb-1">Reporte de Medicamentos Recetados</h6>
                                <p class="mb-1">Medicamentos más recetados, dosificaciones, interacciones y seguimiento.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="list-group">
                            <div class="list-group-item">
                                <h6 class="mb-1">Análisis de Rentabilidad por Servicio</h6>
                                <p class="mb-1">Servicios más rentables, costos asociados, márgenes de ganancia por procedimiento.</p>
                            </div>
                            <div class="list-group-item">
                                <h6 class="mb-1">Reporte de Satisfacción del Paciente</h6>
                                <p class="mb-1">Encuestas de satisfacción, tiempo de espera, calificaciones y comentarios.</p>
                            </div>
                            <div class="list-group-item">
                                <h6 class="mb-1">Control de Inventario Médico</h6>
                                <p class="mb-1">Stock de medicamentos, equipos médicos, fechas de vencimiento y reposición.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contacto para Sugerencias -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-envelope me-2"></i>¿Tienes una Idea de Reporte?</h5>
            </div>
            <div class="card-body">
                <p>Si tienes una idea específica para un reporte personalizado, nos encantaría escucharla. Tu feedback nos ayuda a priorizar las funcionalidades más importantes.</p>
                <div class="row">
                    <div class="col-md-8">
                        <textarea id="sugerenciaTexto" class="form-control" rows="3" placeholder="Describe el reporte que necesitas..." maxlength="1000"></textarea>
                        <small class="form-text text-muted">
                            <span id="contadorCaracteres">0</span>/1000 caracteres
                        </small>
                    </div>
                    <div class="col-md-4">
                        <button id="btnEnviarSugerencia" class="btn btn-danger w-100" onclick="enviarSugerencia()">
                            <i class="fas fa-paper-plane me-2"></i>Enviar Sugerencia
                        </button>
                    </div>
                </div>

                <!-- Área de mensajes -->
                <div id="mensajeSugerencia" class="mt-3" style="display: none;"></div>
            </div>
        </div>

        <!-- Accesos Rápidos a Otros Reportes -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-external-link-alt me-2"></i>Mientras Tanto, Explora Otros Reportes</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="citas_reportes.php" class="btn btn-outline-primary w-100 mb-2">
                            <i class="fas fa-calendar-alt me-2"></i>Reportes de Citas
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="pacientes_reportes.php" class="btn btn-outline-success w-100 mb-2">
                            <i class="fas fa-users me-2"></i>Reportes de Pacientes
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="medicos_reportes.php" class="btn btn-outline-info w-100 mb-2">
                            <i class="fas fa-notes-medical me-2"></i>Reportes Médicos
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="financieros_reportes.php" class="btn btn-outline-warning w-100 mb-2">
                            <i class="fas fa-chart-pie me-2"></i>Reportes Financieros
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Contador de caracteres
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('sugerenciaTexto');
            const contador = document.getElementById('contadorCaracteres');

            textarea.addEventListener('input', function() {
                const longitud = this.value.length;
                contador.textContent = longitud;

                // Cambiar color según la longitud
                if (longitud > 900) {
                    contador.style.color = '#dc3545'; // Rojo
                } else if (longitud > 700) {
                    contador.style.color = '#ffc107'; // Amarillo
                } else {
                    contador.style.color = '#6c757d'; // Gris
                }
            });
        });

        async function enviarSugerencia() {
            const textarea = document.getElementById('sugerenciaTexto');
            const boton = document.getElementById('btnEnviarSugerencia');
            const mensajeDiv = document.getElementById('mensajeSugerencia');
            const sugerencia = textarea.value.trim();

            // Validar que no esté vacía
            if (!sugerencia) {
                mostrarMensaje('Por favor, escribe tu sugerencia antes de enviar.', 'warning');
                return;
            }

            // Validar longitud mínima
            if (sugerencia.length < 10) {
                mostrarMensaje('La sugerencia debe tener al menos 10 caracteres.', 'warning');
                return;
            }

            // Deshabilitar botón y mostrar loading
            boton.disabled = true;
            boton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Enviando...';

            try {
                const response = await fetch('api/enviar_sugerencia.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        sugerencia: sugerencia
                    })
                });

                const data = await response.json();

                // Debug: mostrar información en consola
                console.log('Respuesta de la API:', data);

                if (data.success) {
                    mostrarMensaje(data.message, 'success');
                    textarea.value = '';
                    document.getElementById('contadorCaracteres').textContent = '0';

                    // Mostrar debug si existe
                    if (data.debug) {
                        console.log('Debug info:', data.debug);
                    }
                } else {
                    let errorMsg = data.message || 'Error al enviar la sugerencia.';

                    // Agregar información de debug si existe
                    if (data.debug) {
                        console.error('Debug info:', data.debug);
                        errorMsg += ' (Ver consola para más detalles)';
                    }

                    mostrarMensaje(errorMsg, 'danger');
                }

            } catch (error) {
                console.error('Error:', error);
                mostrarMensaje('Error de conexión. Por favor, inténtalo más tarde.', 'danger');
            } finally {
                // Restaurar botón
                boton.disabled = false;
                boton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Enviar Sugerencia';
            }
        }

        function mostrarMensaje(mensaje, tipo) {
            const mensajeDiv = document.getElementById('mensajeSugerencia');
            mensajeDiv.innerHTML = `
                <div class="alert alert-${tipo} alert-dismissible fade show" role="alert">
                    ${mensaje}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            mensajeDiv.style.display = 'block';

            // Auto-ocultar después de 5 segundos para mensajes de éxito
            if (tipo === 'success') {
                setTimeout(() => {
                    mensajeDiv.style.display = 'none';
                }, 5000);
            }
        }

        // Animación para las tarjetas de plantillas
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
