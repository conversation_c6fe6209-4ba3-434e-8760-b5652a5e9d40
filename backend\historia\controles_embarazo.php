<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CLAVEEMB = $_GET['CLAVEEMB'] ?? '';

$stmt = $pdo->prepare("SELECT * FROM OBSTETRICIA WHERE CLAVEPAC = :CLAVEPAC AND CLAVEEMB = :CLAVEEMB ORDER BY FECHA_CAP DESC");
$stmt->execute(['CLAVEPAC' => $CLAVEPAC, 'CLAVEEMB' => $CLAVEEMB]);
$controles = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Controles del embarazo</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="p-3">
  <div class="container">
    <h4>📅 Controles del embarazo</h4>
    <table class="table table-bordered table-sm">
      <thead>
        <tr>
          <th>Fecha</th><th>EG</th><th>AU</th><th>Peso</th><th>FCF</th><th>TA</th>
        </tr>
      </thead>
      <tbody>
        <?php foreach ($controles as $r): ?>
          <tr>
            <td><?= $r['FECHA_CAP'] ?></td>
            <td><?= $r['EG'] ?></td>
            <td><?= $r['AU'] ?></td>
            <td><?= $r['PESO'] ?></td>
            <td><?= $r['FCF'] ?></td>
            <td><?= $r['TA'] ?></td>
          </tr>
        <?php endforeach; ?>
      </tbody>
    </table>
    <a href="embarazos.php?CLAVEPAC=<?= $CLAVEPAC ?>" class="btn btn-secondary">⬅ Volver a embarazos</a>
  </div>
</body>
</html>
