<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON><PERSON></title>
    <style>
        #formulario_registro {
            display: none;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1><PERSON><PERSON><PERSON></h1>

    <!-- Campo de búsqueda -->
    <label for="buscar_paciente">Buscar paciente:</label>
    <input type="text" id="buscar_paciente" placeholder="Nombre o Cédula">
    <button id="btn_buscar_paciente">Buscar</button>

    <!-- Resultado de la búsqueda -->
    <div id="resultado_busqueda"></div>

    <!-- Formulario de registro de paciente (oculto inicialmente) -->
    <div id="formulario_registro">
        <h2>Registrar Paciente</h2>
        <form id="form_registro">
            <label for="nombre">Nombre:</label>
            <input type="text" id="nombre" name="NOMBRES" required><br><br>

            <label for="cedula">Cédula:</label>
            <input type="text" id="cedula" name="CEDULA" required><br><br>

            <label for="telefono">Teléfono:</label>
            <input type="text" id="telefono" name="TELEFONO" required><br><br>

            <label for="fechanac">Fecha de Nacimiento:</label>
            <input type="date" id="fechanac" name="FECHANAC" required><br><br>

            <button type="submit">Registrar Paciente</button>
        </form>
    </div>

    <script>
        // Acción de búsqueda de paciente
document.getElementById('btn_buscar_paciente').addEventListener('click', function() {
    const inputPaciente = document.getElementById('buscar_paciente').value;
    const resultado = document.getElementById('resultado_busqueda');
              
   
    if (inputPaciente.trim() === '') {
        resultado.textContent = 'Por favor ingrese un nombre o cédula.';
        return;
    }

    // Petición AJAX
    fetch('verificar_paciente.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: `paciente=${encodeURIComponent(inputPaciente)}`
})

    .then(response => response.json())
    .then(data => {
        
       if (data.pacientes && data.pacientes.length > 0) {
    const nombres = data.pacientes.map(p => p.NOMBRES).join(', ');
    resultado.textContent = `Paciente(s) encontrado(s): ${nombres}`;
}
 else {
            resultado.innerHTML = `Paciente no encontrado. <button id="btn_registrar">Registrar Paciente</button>`;
            agregarEventoRegistro(); // Activar el botón de registro
        }
    })
    .catch(error => console.error('Error:', error));
});


        // Mostrar formulario de registro
        function agregarEventoRegistro() {
            document.getElementById('btn_registrar').addEventListener('click', function() {
                document.getElementById('formulario_registro').style.display = 'block';  // Muestra el formulario
                document.getElementById('resultado_busqueda').textContent = '';  // Limpiar el resultado de búsqueda
            });
        }

        // Acción para enviar el formulario de registro
        document.getElementById('form_registro').addEventListener('submit', function(event) {
            event.preventDefault();  // Prevenir que se recargue la página

            const nombre = document.getElementById('nombre').value;
            const cedula = document.getElementById('cedula').value;
            const telefono = document.getElementById('telefono').value;
            const fechanac = document.getElementById('fechanac').value;

            // Validación simple
            if (!nombre || !cedula || !telefono || !fechanac) {
                alert('Todos los campos son obligatorios.');
                return;
            }

            // Enviar el formulario de registro de paciente
            fetch('registrar_paciente.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `nombre=${encodeURIComponent(nombre)}&cedula=${encodeURIComponent(cedula)}&telefono=${encodeURIComponent(telefono)}&fechanac=${encodeURIComponent(fechanac)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Paciente registrado con éxito.');
                    document.getElementById('formulario_registro').reset(); // Limpiar el formulario
                } else {
                    alert('Error al registrar el paciente.');
                }
            })
            .catch(error => console.error('Error:', error));
        });
    </script>
</body>
</html>


