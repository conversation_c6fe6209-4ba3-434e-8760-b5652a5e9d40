<?php
// debug_session.php - Script temporal para depurar problemas de sesión
session_start();

echo "<h2>🔍 Debug de Sesión - Secretaria</h2>";
echo "<hr>";

echo "<h3>📋 Variables de Sesión:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>🔐 Verificaciones de Rol:</h3>";
echo "<p><strong>Usuario:</strong> " . ($_SESSION['usuario'] ?? 'NO DEFINIDO') . "</p>";
echo "<p><strong>Rol:</strong> " . ($_SESSION['rol'] ?? 'NO DEFINIDO') . "</p>";
echo "<p><strong>Rol (con trim):</strong> '" . trim($_SESSION['rol'] ?? '') . "'</p>";
echo "<p><strong>Longitud del rol:</strong> " . strlen($_SESSION['rol'] ?? '') . " caracteres</p>";

echo "<h3>✅ Pruebas de Comparación:</h3>";
$rol = $_SESSION['rol'] ?? '';
echo "<p>¿Rol === 'secretaria'? " . ($rol === 'secretaria' ? 'SÍ' : 'NO') . "</p>";
echo "<p>¿trim(Rol) === 'secretaria'? " . (trim($rol) === 'secretaria' ? 'SÍ' : 'NO') . "</p>";
echo "<p>¿Rol == 'secretaria'? " . ($rol == 'secretaria' ? 'SÍ' : 'NO') . "</p>";

echo "<h3>🎯 Redirecciones Sugeridas:</h3>";
if (!isset($_SESSION['usuario'])) {
    echo "<p style='color: red;'>❌ No hay usuario en sesión - Debería ir a login.php</p>";
} elseif (!isset($_SESSION['rol'])) {
    echo "<p style='color: orange;'>⚠️ No hay rol definido - Debería ir a index.php</p>";
} elseif (trim($_SESSION['rol']) === 'secretaria') {
    echo "<p style='color: green;'>✅ Rol de secretaria correcto - Debería ir a secretaria_panel.php</p>";
} elseif (trim($_SESSION['rol']) === 'doctor') {
    echo "<p style='color: blue;'>👨‍⚕️ Rol de doctor - Debería ir a doctor_panel_v2.php</p>";
} elseif (trim($_SESSION['rol']) === 'paciente') {
    echo "<p style='color: purple;'>👤 Rol de paciente - Debería ir a paciente_panel.php</p>";
} else {
    echo "<p style='color: red;'>❓ Rol desconocido: '" . htmlspecialchars($rol) . "' - Debería ir a index.php</p>";
}

echo "<hr>";
echo "<p><a href='login.php'>🔙 Volver al Login</a></p>";
echo "<p><a href='logout.php'>🚪 Cerrar Sesión</a></p>";
?>
