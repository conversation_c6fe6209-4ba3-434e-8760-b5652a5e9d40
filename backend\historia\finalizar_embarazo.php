<?php
$pdo = require_once __DIR__ . '/../config/database.php';

$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CLAVE    = $_POST['CLAVE']   ?? null;
if (!$CLAVE) {
  exit("❌ CLAVE requerido.");
}

// Validar existencia del embarazo
$verif = $pdo->prepare("SELECT 1 FROM EMBARAZO WHERE CLAVE = :CLAVE");
$verif->execute(['CLAVE' => $CLAVE]);
if (!$verif->fetch()) {
  exit("❌ Embarazo no encontrado.");
}

// 1) Finalizar el embarazo (con SINCRONIZADO = 0)
$sql = "
  UPDATE EMBARAZO
     SET FECHAFINEMB = CURRENT_DATE,
         ESTATUSEMB  = 'F',
         SINCRONIZADO = 0
   WHERE CLAVE = :CLAVE
";
$stmt = $pdo->prepare($sql);
$stmt->execute(['CLAVE' => $CLAVE]);

// 2) Finalizar registros de OBSTETRICIA (también SINCRONIZADO = 0)
$sql2 = "
  UPDATE OBSTETRICIA
     SET ESTATUSEMB = 'F',
         SINCRONIZADO = 0
   WHERE CLAVEPAC  = :CLAVEPAC
     AND CLAVEEMB  = :CLAVE
";
$stmt2 = $pdo->prepare($sql2);
$stmt2->execute([
  'CLAVEPAC' => $CLAVEPAC,
  'CLAVE'    => $CLAVE
]);

// 3) Marcar en PACIENTES como no embarazada
$upd = $pdo->prepare("
  UPDATE PACIENTES
     SET STEmbarazo = '0',
         SINCRONIZADO = 0
   WHERE CLAVE = :CLAVEPAC
");
$upd->execute(['CLAVEPAC' => $CLAVEPAC]);

// 4) Redirigir
header("Location: obstetricia.php?CLAVEPAC=$CLAVEPAC");
exit;