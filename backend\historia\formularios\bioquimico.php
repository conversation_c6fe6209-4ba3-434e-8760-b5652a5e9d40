<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Bioquímico" en Resultados de Laboratorio
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Función para obtener los exámenes bioquímicos del paciente
function getExamenesBioquimicos($pdo, $clavePac) {
    $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha 
            FROM BIOQUIMICO WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Función para obtener un examen bioquímico específico
function getExamenBioquimico($pdo, $id) {
    $sql = "SELECT * FROM BIOQUIMICO WHERE CLAVE = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$id]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    if ($action === 'getExamenesBioquimicos') {
        echo json_encode(getExamenesBioquimicos($pdo, $clavePac));
        exit;
    }
    else if ($action === 'getExamenBioquimico') {
        $id = intval($_POST['id']);
        echo json_encode(getExamenBioquimico($pdo, $id));
        exit;
    }
    else if ($action === 'saveExamenBioquimico') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $glucosa = $_POST['glucosa'];
        $ureanitro = $_POST['ureanitro'];
        $creatnina = $_POST['creatnina'];
        $ureatotal = $_POST['ureatotal'];
        $acidourico = $_POST['acidourico'];
        $calcio = $_POST['calcio'];
        $fosforo = $_POST['fosforo'];
        $cloruro = $_POST['cloruro'];
        $sodio = $_POST['sodio'];
        $potacio = $_POST['potacio'];
        $proteinast = $_POST['proteinast'];
        $albumina = $_POST['albumina'];
        $globulina = $_POST['globulina'];
        $relacionag = $_POST['relacionag'];
        $bilirrubinad = $_POST['bilirrubinad'];
        $bilirrubinai = $_POST['bilirrubinai'];
        $bilirrubinat = $_POST['bilirrubinat'];
        $colesterolt = $_POST['colesterolt'];
        $colesterolhdl = $_POST['colesterolhdl'];
        $colesterolldl = $_POST['colesterolldl'];
        $trigliceridos = $_POST['trigliceridos'];
        
        try {
            if ($id > 0) {
                // Actualizar registro existente
                $sql = "UPDATE BIOQUIMICO SET 
                        GLUCOSA = ?, UREANITRO = ?, CREATNINA = ?, UREATOTAL = ?, 
                        ACIDOURICO = ?, CALCIO = ?, FOSFORO = ?, CLORURO = ?, 
                        SODIO = ?, POTACIO = ?, PROTEINAST = ?, ALBUMINA = ?, 
                        GLOBULINA = ?, RELACIONAG = ?, BILIRRUBINAD = ?, BILIRRUBINAI = ?, 
                        BILIRRUBINAT = ?, COLESTEROLT = ?, COLESTEROLHDL = ?, 
                        COLESTEROLLDL = ?, TRIGLICERIDOS = ? 
                        WHERE CLAVE = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $glucosa, $ureanitro, $creatnina, $ureatotal, 
                    $acidourico, $calcio, $fosforo, $cloruro, 
                    $sodio, $potacio, $proteinast, $albumina, 
                    $globulina, $relacionag, $bilirrubinad, $bilirrubinai, 
                    $bilirrubinat, $colesterolt, $colesterolhdl, 
                    $colesterolldl, $trigliceridos, $id
                ]);
            } else {
                // Insertar nuevo registro
                $sql = "INSERT INTO BIOQUIMICO (
                        CLAVEPAC, GLUCOSA, UREANITRO, CREATNINA, UREATOTAL, 
                        ACIDOURICO, CALCIO, FOSFORO, CLORURO, 
                        SODIO, POTACIO, PROTEINAST, ALBUMINA, 
                        GLOBULINA, RELACIONAG, BILIRRUBINAD, BILIRRUBINAI, 
                        BILIRRUBINAT, COLESTEROLT, COLESTEROLHDL, 
                        COLESTEROLLDL, TRIGLICERIDOS) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $clavePac, $glucosa, $ureanitro, $creatnina, $ureatotal, 
                    $acidourico, $calcio, $fosforo, $cloruro, 
                    $sodio, $potacio, $proteinast, $albumina, 
                    $globulina, $relacionag, $bilirrubinad, $bilirrubinai, 
                    $bilirrubinat, $colesterolt, $colesterolhdl, 
                    $colesterolldl, $trigliceridos
                ]);
                
                $id = $pdo->lastInsertId();
            }
            
            echo json_encode(['success' => true, 'id' => $id]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteExamenBioquimico') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "DELETE FROM BIOQUIMICO WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Obtener lista de exámenes bioquímicos
$examenes = getExamenesBioquimicos($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Examen Bioquímico</h4>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    Registros
                </div>
                <div class="card-body">
                    <div class="lista-registros" id="bioquimicoList">
                        <?php if (count($examenes) > 0): ?>
                            <ul class="list-unstyled m-2">
                                <?php foreach ($examenes as $item): ?>
                                    <li><a href="#" class="bioquimico-item" data-id="<?php echo $item['id']; ?>"><?php echo $item['fecha']; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-center">No hay registros</p>
                        <?php endif; ?>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" id="btnNuevoBioquimico">Nuevo</button>
                        <button class="btn btn-sm btn-info" id="btnEditarBioquimico">Editar</button>
                        <button class="btn btn-sm btn-danger" id="btnEliminarBioquimico">Eliminar</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    Datos del Examen Bioquímico
                </div>
                <div class="card-body">
                    <form id="formBioquimico">
                        <input type="hidden" id="bioquimicoId" name="id" value="0">
                        
                        <ul class="nav nav-tabs" id="bioquimicoTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="glucidos-tab" data-toggle="tab" href="#glucidos" role="tab">Glucosa y Función Renal</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="electrolitos-tab" data-toggle="tab" href="#electrolitos" role="tab">Electrolitos y Proteínas</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="lipidos-tab" data-toggle="tab" href="#lipidos" role="tab">Lípidos y Bilirrubina</a>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-3" id="bioquimicoTabContent">
                            <!-- Glucosa y Función Renal -->
                            <div class="tab-pane fade show active" id="glucidos" role="tabpanel" aria-labelledby="glucidos-tab">
                                <div class="form-row">
                                    <div class="form-group col-md-4">
                                        <label for="glucosa">Glucosa (mg/dL)</label>
                                        <input type="text" class="form-control" id="glucosa" name="glucosa">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="ureanitro">Nitrógeno Ureico (mg/dL)</label>
                                        <input type="text" class="form-control" id="ureanitro" name="ureanitro">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="creatnina">Creatinina (mg/dL)</label>
                                        <input type="text" class="form-control" id="creatnina" name="creatnina">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group col-md-4">
                                        <label for="ureatotal">Urea Total (mg/dL)</label>
                                        <input type="text" class="form-control" id="ureatotal" name="ureatotal">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="acidourico">Ácido Úrico (mg/dL)</label>
                                        <input type="text" class="form-control" id="acidourico" name="acidourico">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Electrolitos y Proteínas -->
                            <div class="tab-pane fade" id="electrolitos" role="tabpanel" aria-labelledby="electrolitos-tab">
                                <div class="form-row">
                                    <div class="form-group col-md-3">
                                        <label for="calcio">Calcio (mg/dL)</label>
                                        <input type="text" class="form-control" id="calcio" name="calcio">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="fosforo">Fósforo (mg/dL)</label>
                                        <input type="text" class="form-control" id="fosforo" name="fosforo">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="cloruro">Cloruro (mEq/L)</label>
                                        <input type="text" class="form-control" id="cloruro" name="cloruro">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="sodio">Sodio (mEq/L)</label>
                                        <input type="text" class="form-control" id="sodio" name="sodio">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group col-md-3">
                                        <label for="potacio">Potasio (mEq/L)</label>
                                        <input type="text" class="form-control" id="potacio" name="potacio">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="proteinast">Proteínas Totales (g/dL)</label>
                                        <input type="text" class="form-control" id="proteinast" name="proteinast">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="albumina">Albúmina (g/dL)</label>
                                        <input type="text" class="form-control" id="albumina" name="albumina">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="globulina">Globulina (g/dL)</label>
                                        <input type="text" class="form-control" id="globulina" name="globulina">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group col-md-3">
                                        <label for="relacionag">Relación A/G</label>
                                        <input type="text" class="form-control" id="relacionag" name="relacionag">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Lípidos y Bilirrubina -->
                            <div class="tab-pane fade" id="lipidos" role="tabpanel" aria-labelledby="lipidos-tab">
                                <div class="form-row">
                                    <div class="form-group col-md-4">
                                        <label for="bilirrubinad">Bilirrubina Directa (mg/dL)</label>
                                        <input type="text" class="form-control" id="bilirrubinad" name="bilirrubinad">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="bilirrubinai">Bilirrubina Indirecta (mg/dL)</label>
                                        <input type="text" class="form-control" id="bilirrubinai" name="bilirrubinai">
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="bilirrubinat">Bilirrubina Total (mg/dL)</label>
                                        <input type="text" class="form-control" id="bilirrubinat" name="bilirrubinat">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group col-md-3">
                                        <label for="colesterolt">Colesterol Total (mg/dL)</label>
                                        <input type="text" class="form-control" id="colesterolt" name="colesterolt">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="colesterolhdl">Colesterol HDL (mg/dL)</label>
                                        <input type="text" class="form-control" id="colesterolhdl" name="colesterolhdl">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="colesterolldl">Colesterol LDL (mg/dL)</label>
                                        <input type="text" class="form-control" id="colesterolldl" name="colesterolldl">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="trigliceridos">Triglicéridos (mg/dL)</label>
                                        <input type="text" class="form-control" id="trigliceridos" name="trigliceridos">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-right mt-3">
                            <button type="button" class="btn btn-secondary" id="btnCancelarBioquimico">Cancelar</button>
                            <button type="button" class="btn btn-primary" id="btnGuardarBioquimico">Guardar</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentBioquimicoId = 0;
    let editMode = false;
    
    // Inicializar formulario
    disableForm(true);
    $('#btnEditarBioquimico').prop('disabled', true);
    $('#btnEliminarBioquimico').prop('disabled', true);
    $('#btnCancelarBioquimico').hide();
    $('#btnGuardarBioquimico').hide();
    
    // Si hay registros, seleccionar el primero
    if ($('.bioquimico-item').length > 0) {
        $('.bioquimico-item:first').click();
    }
    
    // Cargar examen bioquímico al hacer clic en un elemento de la lista
    $(document).on('click', '.bioquimico-item', function(e) {
        e.preventDefault();
        
        $('.bioquimico-item').removeClass('font-weight-bold');
        $(this).addClass('font-weight-bold');
        
        const id = $(this).data('id');
        currentBioquimicoId = id;
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'getExamenBioquimico',
                id: id
            },
            dataType: 'json',
            success: function(data) {
                if (data) {
                    $('#bioquimicoId').val(data.CLAVE);
                    $('#glucosa').val(data.GLUCOSA);
                    $('#ureanitro').val(data.UREANITRO);
                    $('#creatnina').val(data.CREATNINA);
                    $('#ureatotal').val(data.UREATOTAL);
                    $('#acidourico').val(data.ACIDOURICO);
                    $('#calcio').val(data.CALCIO);
                    $('#fosforo').val(data.FOSFORO);
                    $('#cloruro').val(data.CLORURO);
                    $('#sodio').val(data.SODIO);
                    $('#potacio').val(data.POTACIO);
                    $('#proteinast').val(data.PROTEINAST);
                    $('#albumina').val(data.ALBUMINA);
                    $('#globulina').val(data.GLOBULINA);
                    $('#relacionag').val(data.RELACIONAG);
                    $('#bilirrubinad').val(data.BILIRRUBINAD);
                    $('#bilirrubinai').val(data.BILIRRUBINAI);
                    $('#bilirrubinat').val(data.BILIRRUBINAT);
                    $('#colesterolt').val(data.COLESTEROLT);
                    $('#colesterolhdl').val(data.COLESTEROLHDL);
                    $('#colesterolldl').val(data.COLESTEROLLDL);
                    $('#trigliceridos').val(data.TRIGLICERIDOS);
                    
                    disableForm(true);
                    $('#btnEditarBioquimico').prop('disabled', false);
                    $('#btnEliminarBioquimico').prop('disabled', false);
                    $('#btnCancelarBioquimico').hide();
                    $('#btnGuardarBioquimico').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al cargar examen bioquímico:", error);
                alert("Error al cargar examen bioquímico. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Nuevo examen bioquímico
    $('#btnNuevoBioquimico').click(function() {
        clearForm();
        disableForm(false);
        editMode = false;
        
        $('#btnEditarBioquimico').prop('disabled', true);
        $('#btnEliminarBioquimico').prop('disabled', true);
        $('#btnCancelarBioquimico').show();
        $('#btnGuardarBioquimico').show();
    });
    
    // Editar examen bioquímico
    $('#btnEditarBioquimico').click(function() {
        if (currentBioquimicoId > 0) {
            disableForm(false);
            editMode = true;
            
            $('#btnNuevoBioquimico').prop('disabled', true);
            $('#btnEliminarBioquimico').prop('disabled', true);
            $('#btnCancelarBioquimico').show();
            $('#btnGuardarBioquimico').show();
        }
    });
    
    // Eliminar examen bioquímico
    $('#btnEliminarBioquimico').click(function() {
        if (currentBioquimicoId > 0) {
            if (confirm("¿Está seguro de eliminar este examen bioquímico?")) {
                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: {
                        action: 'deleteExamenBioquimico',
                        id: currentBioquimicoId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert("Examen bioquímico eliminado correctamente.");
                            location.reload(); // Recargar para actualizar la lista
                        } else {
                            alert("Error al eliminar examen bioquímico: " + response.error);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error al eliminar examen bioquímico:", error);
                        alert("Error al eliminar examen bioquímico. Consulte la consola para más detalles.");
                    }
                });
            }
        }
    });
    
    // Cancelar
    $('#btnCancelarBioquimico').click(function() {
        if (editMode && currentBioquimicoId > 0) {
            // Volver a cargar el examen actual
            $('.bioquimico-item[data-id="' + currentBioquimicoId + '"]').click();
        } else {
            clearForm();
        }
        
        disableForm(true);
        $('#btnNuevoBioquimico').prop('disabled', false);
        $('#btnEditarBioquimico').prop('disabled', currentBioquimicoId === 0);
        $('#btnEliminarBioquimico').prop('disabled', currentBioquimicoId === 0);
        $('#btnCancelarBioquimico').hide();
        $('#btnGuardarBioquimico').hide();
    });
    
    // Guardar examen bioquímico
    $('#btnGuardarBioquimico').click(function() {
        const formData = {
            action: 'saveExamenBioquimico',
            id: editMode ? currentBioquimicoId : 0,
            glucosa: $('#glucosa').val(),
            ureanitro: $('#ureanitro').val(),
            creatnina: $('#creatnina').val(),
            ureatotal: $('#ureatotal').val(),
            acidourico: $('#acidourico').val(),
            calcio: $('#calcio').val(),
            fosforo: $('#fosforo').val(),
            cloruro: $('#cloruro').val(),
            sodio: $('#sodio').val(),
            potacio: $('#potacio').val(),
            proteinast: $('#proteinast').val(),
            albumina: $('#albumina').val(),
            globulina: $('#globulina').val(),
            relacionag: $('#relacionag').val(),
            bilirrubinad: $('#bilirrubinad').val(),
            bilirrubinai: $('#bilirrubinai').val(),
            bilirrubinat: $('#bilirrubinat').val(),
            colesterolt: $('#colesterolt').val(),
            colesterolhdl: $('#colesterolhdl').val(),
            colesterolldl: $('#colesterolldl').val(),
            trigliceridos: $('#trigliceridos').val()
        };
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert("Examen bioquímico guardado correctamente.");
                    location.reload(); // Recargar para actualizar la lista
                } else {
                    alert("Error al guardar examen bioquímico: " + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al guardar examen bioquímico:", error);
                alert("Error al guardar examen bioquímico. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Funciones auxiliares
    function clearForm() {
        $('#formBioquimico')[0].reset();
        $('#bioquimicoId').val(0);
        currentBioquimicoId = 0;
    }
    
    function disableForm(disabled) {
        $('#formBioquimico input, #formBioquimico textarea').prop('disabled', disabled);
    }
});
</script>
