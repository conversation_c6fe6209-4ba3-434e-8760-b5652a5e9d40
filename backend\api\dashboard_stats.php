<?php
/**
 * API para obtener estadísticas del dashboard en tiempo real
 * 
 * <AUTHOR> de Consultorio
 * @version 1.0
 */

session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'No autorizado']);
    exit;
}

// Configuración
require_once __DIR__ . '/../config/database.php';
header('Content-Type: application/json');

try {
    // Obtener estadísticas actualizadas
    
    // Citas de hoy
    $stmt_citas_hoy = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE()");
    $citas_hoy = $stmt_citas_hoy->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Pacientes registrados
    $stmt_pacientes = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
    $total_pacientes = $stmt_pacientes->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Citas pendientes (futuras y estado citado)
    $stmt_pendientes = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON >= CURDATE() AND ESTATUS = 3");
    $citas_pendientes = $stmt_pendientes->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Citas atendidas hoy
    $stmt_atendidas = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 0");
    $citas_atendidas = $stmt_atendidas->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Estadísticas adicionales
    $stmt_canceladas = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 1");
    $citas_canceladas = $stmt_canceladas->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt_no_asistio = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 2");
    $citas_no_asistio = $stmt_no_asistio->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Próximas citas (próximos 7 días)
    $stmt_proximas = $pdo->query("
        SELECT COUNT(*) as total 
        FROM CITAMEDIC 
        WHERE FECHACON BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        AND ESTATUS IN (3, 5, 6)
    ");
    $proximas_citas = $stmt_proximas->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Pacientes nuevos este mes
    $stmt_nuevos_pacientes = $pdo->query("
        SELECT COUNT(*) as total 
        FROM PACIENTES 
        WHERE MONTH(FECHAREGISTRO) = MONTH(CURDATE()) 
        AND YEAR(FECHAREGISTRO) = YEAR(CURDATE())
    ");
    $nuevos_pacientes = $stmt_nuevos_pacientes->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Distribución de citas por estado (hoy)
    $stmt_distribucion = $pdo->query("
        SELECT 
            ESTATUS,
            CASE ESTATUS 
                WHEN 0 THEN 'Atendido'
                WHEN 1 THEN 'Canceló'
                WHEN 2 THEN 'No asistió'
                WHEN 3 THEN 'Citado'
                WHEN 4 THEN 'Llegó tarde'
                WHEN 5 THEN 'Esperando'
                WHEN 6 THEN 'Pendiente aprobación'
                ELSE 'Otro'
            END as estado_texto,
            COUNT(*) as cantidad
        FROM CITAMEDIC 
        WHERE FECHACON = CURDATE()
        GROUP BY ESTATUS
        ORDER BY ESTATUS
    ");
    $distribucion_estados = $stmt_distribucion->fetchAll(PDO::FETCH_ASSOC);
    
    // Citas por hora (hoy) - para gráfico
    $stmt_por_hora = $pdo->query("
        SELECT 
            HOUR(HORACON) as hora,
            COUNT(*) as cantidad
        FROM CITAMEDIC 
        WHERE FECHACON = CURDATE()
        GROUP BY HOUR(HORACON)
        ORDER BY hora
    ");
    $citas_por_hora = $stmt_por_hora->fetchAll(PDO::FETCH_ASSOC);
    
    // Respuesta exitosa
    $response = [
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => [
            // Estadísticas principales
            'citas_hoy' => (int)$citas_hoy,
            'citas_atendidas' => (int)$citas_atendidas,
            'citas_pendientes' => (int)$citas_pendientes,
            'total_pacientes' => (int)$total_pacientes,
            
            // Estadísticas adicionales
            'citas_canceladas' => (int)$citas_canceladas,
            'citas_no_asistio' => (int)$citas_no_asistio,
            'proximas_citas' => (int)$proximas_citas,
            'nuevos_pacientes' => (int)$nuevos_pacientes,
            
            // Distribuciones
            'distribucion_estados' => $distribucion_estados,
            'citas_por_hora' => $citas_por_hora,
            
            // Porcentajes
            'porcentaje_atendidas' => $citas_hoy > 0 ? round(($citas_atendidas / $citas_hoy) * 100, 1) : 0,
            'porcentaje_canceladas' => $citas_hoy > 0 ? round(($citas_canceladas / $citas_hoy) * 100, 1) : 0,
            'porcentaje_no_asistio' => $citas_hoy > 0 ? round(($citas_no_asistio / $citas_hoy) * 100, 1) : 0,
        ]
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Error interno del servidor',
        'message' => $e->getMessage()
    ]);
}
?>
