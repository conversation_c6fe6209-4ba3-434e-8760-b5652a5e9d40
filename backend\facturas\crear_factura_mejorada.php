<?php
/**
 * Sistema de Facturación Mejorado - MarcSoftware Solutions
 * Generador de facturas para servicios de desarrollo de software
 * 
 * <AUTHOR> Solutions
 * @version 2.0
 * @date 2024
 */

session_start();

// Verificar autenticación (opcional - ajustar según tu sistema)
// if (!isset($_SESSION['usuario'])) {
//     header('Location: ../../login.php');
//     exit;
// }

require_once __DIR__ . '/../config/database.php';

// Configuración de errores para desarrollo
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Variables para mensajes
$success_message = '';
$error_message = '';
$warning_message = '';

// 1) Obtener datos del consultorio
try {
    $stmt = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
    $empresa = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$empresa) {
        $warning_message = "⚠️ No se encontraron datos del consultorio. Algunos campos pueden estar vacíos.";
    }
} catch (Exception $e) {
    $empresa = null;
    $error_message = "❌ Error al cargar datos del consultorio: " . $e->getMessage();
}

// 2) Procesar envío de formulario
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Validar y limpiar datos
    $concepto = trim($_POST['concepto'] ?? '');
    $precio = floatval($_POST['precio'] ?? 0);
    $moneda = $_POST['moneda'] ?? 'USD';
    $modopago = $_POST['modopago'] ?? 'Transferencia';
    $fechapago = $_POST['fechapago'] ?? date('Y-m-d');

    // Campos bancarios
    $banco_destino = $_POST['banco_destino'] ?? '';
    $cuenta_marcsoftware = trim($_POST['cuenta_marcsoftware'] ?? '');
    $beneficiario_marcsoftware = $_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions';

    // Validaciones básicas
    $errores = [];
    if (empty($concepto)) {
        $errores[] = "El concepto es obligatorio";
    }
    if ($precio <= 0) {
        $errores[] = "El precio debe ser mayor a 0";
    }
    if ($modopago === 'Transferencia' && empty($banco_destino)) {
        $errores[] = "Debe seleccionar un banco para transferencias";
    }
    if ($modopago === 'Transferencia' && empty($cuenta_marcsoftware)) {
        $errores[] = "Debe especificar la cuenta de destino";
    }

    if (empty($errores)) {
        try {
            // Datos del cliente desde EMPRESA
            $cliente_nombre = $empresa['NOMBRE'] ?? 'Cliente Sin Nombre';
            $cliente_rnc = $empresa['RNC'] ?? '';
            $cliente_telefono = $empresa['TELEFONO'] ?? '';
            $cliente_email = $empresa['CORREOE'] ?? '';
            $cliente_direccion = trim(($empresa['CALLE'] ?? '') . ', ' .
                                    ($empresa['MUNICIPIO'] ?? '') . ', ' .
                                    ($empresa['PROVINCIA'] ?? ''), ', ');
            $cliente_especialidad = $empresa['ESPECIALIDAD'] ?? '';
            $cliente_subdominio = $empresa['SUBDOMINIO'] ?? '';

            // 2.1) Verificar o crear cliente
            $stmt = $pdo->prepare("SELECT CLAVE FROM CLIENTES_SOFTWARE WHERE RNC = ? OR NOMBRE_COMPLETO = ?");
            $stmt->execute([$cliente_rnc, $cliente_nombre]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$row) {
                // Crear tabla clientes si no existe
                $pdo->exec("CREATE TABLE IF NOT EXISTS CLIENTES_SOFTWARE (
                    CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                    NOMBRE_COMPLETO VARCHAR(200) NOT NULL,
                    RNC VARCHAR(20),
                    TELEFONO VARCHAR(20),
                    EMAIL VARCHAR(100),
                    DIRECCION VARCHAR(200),
                    CIUDAD VARCHAR(100),
                    PROVINCIA VARCHAR(100),
                    ESPECIALIDAD VARCHAR(100),
                    SUBDOMINIO VARCHAR(50),
                    FECHA_REGISTRO DATE DEFAULT (CURDATE()),
                    ESTATUS CHAR(1) DEFAULT 'A',
                    INDEX idx_rnc (RNC),
                    INDEX idx_nombre (NOMBRE_COMPLETO)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

                $ins = $pdo->prepare("INSERT INTO CLIENTES_SOFTWARE
                    (NOMBRE_COMPLETO,RNC,TELEFONO,EMAIL,DIRECCION,CIUDAD,PROVINCIA,ESPECIALIDAD,SUBDOMINIO,FECHA_REGISTRO)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())");
                $ins->execute([
                    $cliente_nombre,
                    $cliente_rnc,
                    $cliente_telefono,
                    $cliente_email,
                    $empresa['CALLE'] ?? '',
                    $empresa['MUNICIPIO'] ?? '',
                    $empresa['PROVINCIA'] ?? '',
                    $cliente_especialidad,
                    $cliente_subdominio
                ]);
                $cliente_clave = $pdo->lastInsertId();
            } else {
                $cliente_clave = $row['CLAVE'];
            }

            // 2.2) Crear tabla facturas si no existe
            $pdo->exec("CREATE TABLE IF NOT EXISTS FACTURAS_SOFTWARE (
                CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                CLIENTE_CLAVE INT NOT NULL,
                NUMERO_FACTURA VARCHAR(20) UNIQUE NOT NULL,
                CONCEPTO TEXT NOT NULL,
                PRECIO DECIMAL(10,2) NOT NULL,
                MONEDA VARCHAR(5) DEFAULT 'USD',
                MODO_PAGO VARCHAR(20) DEFAULT 'Transferencia',
                BANCO_DESTINO VARCHAR(50),
                CUENTA_DESTINO VARCHAR(30),
                BENEFICIARIO VARCHAR(100),
                FECHA_FACTURA DATE NOT NULL,
                FECHA_VENCIMIENTO DATE,
                ESTATUS VARCHAR(15) DEFAULT 'PENDIENTE',
                FECHA_CREACION DATETIME DEFAULT CURRENT_TIMESTAMP,
                FECHA_ACTUALIZACION DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                USUARIO_CREACION INT,
                NOTAS TEXT,
                FOREIGN KEY (CLIENTE_CLAVE) REFERENCES CLIENTES_SOFTWARE(CLAVE),
                INDEX idx_numero (NUMERO_FACTURA),
                INDEX idx_cliente (CLIENTE_CLAVE),
                INDEX idx_fecha (FECHA_FACTURA),
                INDEX idx_estatus (ESTATUS)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");

            // 2.3) Generar número automático
            $stmt_max = $pdo->query("SELECT MAX(CAST(SUBSTRING(NUMERO_FACTURA,4) AS UNSIGNED)) AS max_num 
                                   FROM FACTURAS_SOFTWARE 
                                   WHERE NUMERO_FACTURA LIKE 'MS-%'");
            $max_result = $stmt_max->fetch(PDO::FETCH_ASSOC);
            $max_num = $max_result['max_num'] ?? 1000;
            $numero_factura = 'MS-' . str_pad($max_num + 1, 4, '0', STR_PAD_LEFT);

            // 2.4) Incluir datos de transferencia en el concepto si aplica
            $concepto_completo = $concepto;
            if ($modopago === 'Transferencia' && $banco_destino) {
                $concepto_completo .= "\n\n" . str_repeat("=", 40) . "\n";
                $concepto_completo .= "DATOS PARA TRANSFERENCIA:\n";
                $concepto_completo .= str_repeat("=", 40) . "\n";
                $concepto_completo .= "🏦 Banco: {$banco_destino}\n";
                $concepto_completo .= "💳 Cuenta: {$cuenta_marcsoftware}\n";
                $concepto_completo .= "👤 Beneficiario: {$beneficiario_marcsoftware}\n";
                $concepto_completo .= str_repeat("=", 40);
            }

            // 2.5) Calcular fecha de vencimiento
            $fecha_vencimiento = date('Y-m-d', strtotime('+30 days', strtotime($fechapago)));

            // 2.6) Insertar la factura
            $stmt_insert = $pdo->prepare("INSERT INTO FACTURAS_SOFTWARE
                (CLIENTE_CLAVE, NUMERO_FACTURA, CONCEPTO, PRECIO, MONEDA, MODO_PAGO,
                 BANCO_DESTINO, CUENTA_DESTINO, BENEFICIARIO, FECHA_FACTURA, FECHA_VENCIMIENTO,
                 FECHA_CREACION, USUARIO_CREACION)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)");
            
            $usuario_id = $_SESSION['usuario_id'] ?? 1; // Ajustar según tu sistema de usuarios
            
            $stmt_insert->execute([
                $cliente_clave, 
                $numero_factura, 
                $concepto_completo, 
                $precio, 
                $moneda, 
                $modopago,
                $banco_destino, 
                $cuenta_marcsoftware, 
                $beneficiario_marcsoftware,
                $fechapago, 
                $fecha_vencimiento,
                $usuario_id
            ]);

            // 3) Mensaje de éxito
            $factura_id = $pdo->lastInsertId();
            $success_message = "
                <div class='alert alert-success alert-dismissible fade show' role='alert'>
                    <h4 class='alert-heading'>✅ ¡Factura Creada Exitosamente!</h4>
                    <hr>
                    <p class='mb-1'><strong>📄 Número:</strong> {$numero_factura}</p>
                    <p class='mb-1'><strong>👤 Cliente:</strong> {$cliente_nombre}</p>
                    <p class='mb-1'><strong>💰 Monto:</strong> {$moneda} " . number_format($precio, 2) . "</p>
                    <p class='mb-1'><strong>📅 Vence:</strong> " . date('d/m/Y', strtotime($fecha_vencimiento)) . "</p>
                    <p class='mb-1'><strong>💳 Pago:</strong> {$modopago}</p>
                    <hr>
                    <div class='d-grid gap-2 d-md-flex justify-content-md-start'>
                        <a href='imprimir_factura.php?id={$factura_id}' target='_blank' class='btn btn-primary'>
                            🖨️ Imprimir Factura
                        </a>
                        <a href='ver_factura.php?id={$factura_id}' class='btn btn-info'>
                            👁️ Ver Detalles
                        </a>
                        <a href='lista_facturas.php' class='btn btn-secondary'>
                            📋 Ver Todas las Facturas
                        </a>
                    </div>
                    <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
                </div>";

            // Limpiar formulario después del éxito
            $_POST = [];

        } catch (Exception $e) {
            $error_message = "❌ Error al crear la factura: " . htmlspecialchars($e->getMessage());
        }
    } else {
        $error_message = "❌ Errores en el formulario:<br>• " . implode("<br>• ", $errores);
    }
}
?>
