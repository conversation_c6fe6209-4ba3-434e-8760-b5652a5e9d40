<?php
// PHP Configuration for error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Database connection
// Adjust the path as necessary to your database.php file
$pdo = require_once '../config/database.php';

// Get patient details from URL parameters
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA']   ?? ''; // Keep CEDULA for display in the interface

$mensaje  = ''; // Message to display to the user
$messageType = ''; // Type of message (success, danger, info, warning)
$registros = []; // Array to store fetched file records

$tableName = 'RESULTADOS_PACIENTES'; // Name of your files table

// --- 1) Handle file deletion via secure POST request ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && !empty($_POST['CLAVE_ELIMINAR'])) {
    $claveEliminar = $_POST['CLAVE_ELIMINAR'];
    
    try {
        // First, retrieve the unique file name and CLAVEPAC for physical file deletion and authorization check
        $stmt = $pdo->prepare("SELECT ARCHIVO, CLAVEPAC FROM " . $tableName . " WHERE CLAVE = ?");
        $stmt->execute([$claveEliminar]);
        $resultado_eliminacion = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($resultado_eliminacion) {
            $archivo_a_eliminar = $resultado_eliminacion['ARCHIVO'];
            $clavePacDelArchivo = $resultado_eliminacion['CLAVEPAC'];

            // Optional: Additional security check if the CLAVEPAC from the URL matches the file's CLAVEPAC
            // This prevents deleting a file for another patient by URL manipulation if CLAVE_ELIMINAR is guessed.
            if ($clavePacDelArchivo != $CLAVEPAC) {
                // If there's a mismatch, log it and prevent deletion
                error_log("Security alert: Attempted to delete file (CLAVE: $claveEliminar) for CLAVEPAC $clavePacDelArchivo by user with CLAVEPAC $CLAVEPAC.");
                die("❌ Acceso no autorizado para eliminar este archivo.");
            }

            // Define the upload directory. This path MUST match the one in upload_resultados.php and descargar_archivo_paciente.php
            // IMPORTANT: Adjust this path if necessary.
            // Example if 'historia' folder is at mi_consultorio/backend/historia/
            // and 'upload' is a subfolder of 'historia':
            $uploadDir = __DIR__ . '/upload/'; 
            $rutaFisicaArchivo = $uploadDir . $archivo_a_eliminar;

            // Delete the physical file if it exists
            if (file_exists($rutaFisicaArchivo)) {
                unlink($rutaFisicaArchivo);
            }
            
            // Delete the record from the database
            $stmt = $pdo->prepare("DELETE FROM " . $tableName . " WHERE CLAVE = ?");
            $stmt->execute([$claveEliminar]);

            $mensaje = "✅ Archivo eliminado exitosamente.";
            $messageType = 'success';
        } else {
            $mensaje = "❌ Archivo no encontrado en la base de datos.";
            $messageType = 'danger';
        }
        // Redirect to reload the page and reflect the changes
        header("Location: archivos_subidos.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar el archivo: " . $e->getMessage();
        $messageType = 'danger';
    }
}

// --- 2) Load historical file records for the patient ---
// Now using CLAVEPAC for the primary query as it's more direct
if ($CLAVEPAC) { 
    $stmt = $pdo->prepare("SELECT CLAVE, NOMBRE_ARCHIVO_ORIGINAL, DESCRIPCION, FECHA_SUBIDA FROM " . $tableName . " WHERE CLAVEPAC = ? ORDER BY FECHA_SUBIDA DESC");
    $stmt->execute([$CLAVEPAC]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
} else {
    $mensaje = 'Por favor, seleccione un paciente para ver sus archivos.';
    $messageType = 'info';
}

?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Archivos Subidos por Paciente</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .form-container { padding: 20px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 15px rgba(0,0,0,0.1); margin-top: 20px; }
        .list-group-item.active { background-color: #0d6efd !important; border-color: #0d6efd !important; }
        .list-group-item.active a { color: #fff !important; }
    </style>
</head>
<body class="p-4">
    <div class="row">
        <div class="col-12 form-container">
            <h4 class="mb-3">Archivos Subidos por el Paciente</h4>
            <p class="text-muted">Paciente: <strong><?= htmlspecialchars($CLAVEPAC) ?></strong> (CI: <strong><?= htmlspecialchars($CEDULA) ?></strong>)</p>

            <?php if ($mensaje): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($mensaje) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!empty($CLAVEPAC)): ?> <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead>
                            <tr>
                                <th>Archivo</th>
                                <th>Fecha Subida</th>
                                <th>Descripción</th>
                                <th>Acciones</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($registros)): ?>
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No hay archivos subidos por este paciente.</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($registros as $r): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($r['NOMBRE_ARCHIVO_ORIGINAL']) ?></td>
                                        <td><?= htmlspecialchars((new DateTime($r['FECHA_SUBIDA']))->format('Y-m-d H:i')) ?></td>
                                        <td><?= htmlspecialchars($r['DESCRIPCION']) ?></td>
                                        <td>
                                            <a href="../api/descargar_archivo_paciente.php?clave_pac=<?= htmlspecialchars($CLAVEPAC) ?>&clave_archivo=<?= htmlspecialchars($r['CLAVE']) ?>" 
   target="_blank" class="btn btn-sm btn-info me-1" title="Visualizar">
    👁️ Visualizar
</a>
<a href="../api/descargar_archivo_paciente.php?clave_pac=<?= htmlspecialchars($CLAVEPAC) ?>&clave_archivo=<?= htmlspecialchars($r['CLAVE']) ?>&mode=download" 
   class="btn btn-sm btn-primary me-1" title="Descargar">
    ⬇️ Descargar
</a>
                                            
                                            <form action="" method="post" class="d-inline">
                                                <input type="hidden" name="CLAVE_ELIMINAR" value="<?= htmlspecialchars($r['CLAVE']) ?>">
                                                <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                                                <input type="hidden" name="CEDULA" value="<?= htmlspecialchars($CEDULA) ?>">
                                                <button type="submit" name="eliminar" class="btn btn-sm btn-danger" title="Eliminar" onclick="return confirm('¿Está seguro de que desea eliminar este archivo? Esto también lo borrará del servidor.');">
                                                    🗑️ Eliminar
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-warning" role="alert">
                    No se ha seleccionado un paciente. Por favor, seleccione uno para ver sus archivos.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>