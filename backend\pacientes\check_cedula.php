<?php
require_once '../config/database.php';
header('Content-Type: application/json');

// Configuración de errores
ini_set('display_errors', 0);
error_reporting(E_ALL);

try {
    // Verifica que se haya enviado la cédula
    if (!isset($_GET['CEDULA']) || empty(trim($_GET['CEDULA']))) {
        echo json_encode([
            'exists' => false,
            'error' => 'No se proporcionó una cédula válida'
        ]);
        exit;
    }

    $cedula = trim($_GET['CEDULA']);

    // Validar formato básico de cédula (solo números y longitud)
    if (!preg_match('/^\d{11}$/', $cedula)) {
        echo json_encode([
            'exists' => false,
            'error' => 'Formato de cédula inválido. Debe contener 11 dígitos.'
        ]);
        exit;
    }

    // Consulta mejorada para buscar el paciente por cédula
    $stmt = $pdo->prepare("SELECT CLAVE, NOMBRES, APELLIDOS, NOMBREAPELLIDO, CEDULA FROM PACIENTES WHERE CEDULA = ?");
    $stmt->execute([$cedula]);
    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($paciente) {
        // Construir nombre completo
        $nombreCompleto = trim($paciente['NOMBREAPELLIDO']) ?:
                         trim($paciente['NOMBRES'] . ' ' . $paciente['APELLIDOS']);

        echo json_encode([
            'exists' => true,
            'CLAVE' => $paciente['CLAVE'],
            'NOMBRE_COMPLETO' => $nombreCompleto,
            'CEDULA' => $paciente['CEDULA'],
            'message' => 'Paciente encontrado: ' . $nombreCompleto
        ]);
    } else {
        echo json_encode([
            'exists' => false,
            'message' => 'Cédula disponible para registro'
        ]);
    }

} catch (PDOException $e) {
    error_log("Error de base de datos en check_cedula.php: " . $e->getMessage());
    echo json_encode([
        'exists' => false,
        'error' => 'Error al consultar la base de datos'
    ]);
} catch (Exception $e) {
    error_log("Error general en check_cedula.php: " . $e->getMessage());
    echo json_encode([
        'exists' => false,
        'error' => 'Error interno del servidor'
    ]);
}
?>
