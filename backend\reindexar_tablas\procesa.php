<?php

// Mostrar todos los errores en pantalla (solo en desarrollo)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// procesa.php
require 'funciones.php';
$pdo     = getPDO();
$tabla   = $_POST['tabla']   ?? '';
$valida  = isset($_POST['validaCed']);
$reindex = isset($_POST['reindex']);

$reportes = [];

// ——— Validación de cédulas en PACIENTES —————————————
if ($valida && strtoupper($tabla) === 'PACIENTES') {
    $stmt = $pdo->query("SELECT CLAVE, CEDULA, EDAD FROM PACIENTES");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $upd = $pdo->prepare(
      "UPDATE PACIENTES
         SET SINCEDULA = :sin
       WHERE CLAVE      = :clave"
    );

    $contVa = $contInva = $contMenor = 0;
    foreach ($rows as $r) {
      $sin  = '0';
      $ced  = trim($r['CEDULA']);
      $edad = (int)$r['EDAD'];

      if (strlen($ced) === 11 && ctype_digit($ced)) {
        if (validaCedula($ced)) {
          $sin = '1'; $contVa++;
        }
        elseif ($edad < 18) {
          $sin = '2'; $contMenor++;
        }
        else {
          $sin = '0'; $contInva++;
        }
      } else {
        $sin = '0'; $contInva++;
      }

      $upd->execute([
        ':sin'   => $sin,
        ':clave' => $r['CLAVE']
      ]);
    }

    $total = $contVa + $contInva + $contMenor;
    $reportes[] = [
      'tabla'     => 'PACIENTES',
      'registros' => $total,
      'estado'    => "Válidas={$contVa}, Inválidas={$contInva}, Menores={$contMenor}"
    ];
}


if (isset($_POST['reindex'])) {
    $res = reindexTabla($pdo, $tabla);
    $count = $res['status']==='ok'
      ? (int)$pdo->query("SELECT COUNT(*) FROM `$tabla`")->fetchColumn()
      : 0;
    $reportes[] = [
      'tabla'     => $tabla,
      'registros' => $count,
      'estado'    => $res['status'] . ': ' . $res['msg']
    ];
}



// ——— Salida HTML ——————————————————————————————————
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Resultados</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
        rel="stylesheet">
</head>
<body class="p-4">
  <h1>Resultados de la operación</h1>
  <table class="table table-striped mt-4">
    <thead>
      <tr>
        <th>Tabla</th>
        <th>Registros</th>
        <th>Estado</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach($reportes as $r): ?>
      <tr>
        <td><?= htmlspecialchars($r['tabla'])?></td>
        <td><?= htmlspecialchars($r['registros'])?></td>
        <td><?= htmlspecialchars($r['estado'])?></td>
      </tr>
      <?php endforeach ?>
    </tbody>
  </table>
  <a href="index.php" class="btn btn-secondary mt-3">← Volver</a>
</body>
</html>
