<?php
// Habilitar la visualización de errores para depuración (QUITAR EN PRODUCCIÓN)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Incluimos tu configuración de conexión para usar la función getMasterPdo()
require_once __DIR__ . '/../config/database.php';

// 1. Obtener el ID de la factura y el subdominio de la URL
$id_factura = $_GET['id'] ?? null;
$subdominio = $_GET['subdominio'] ?? null;
$factura = null; // Variable para almacenar los datos de la factura
$error = null;
$success = null;

if (!$id_factura || !$subdominio) {
    $error = "Error: ID de factura o subdominio no especificado.";
} else {
    try {
        // Conexión a la base de datos MAESTRA para obtener la configuración del tenant
        $masterPdo = getMasterPdo();
        $stmt_master = $masterPdo->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio = ? AND Estado = 'Activo'");
        $stmt_master->execute([$subdominio]);
        $cfg = $stmt_master->fetch(PDO::FETCH_ASSOC);

        if ($cfg && !empty($cfg['CadenaConexionDB'])) {
            // Parsear la cadena de conexión del tenant
            $dsnString = trim($cfg['CadenaConexionDB']);
            $parts = [];
            $regex = '/^mysql:host=(.*?);dbname=(.*?);user=(.*?);password=****$/';
            if (preg_match($regex, $dsnString, $matches)) {
                $parts['host'] = $matches[1];
                $parts['dbname'] = $matches[2];
                $parts['user'] = $matches[3];
                $parts['password'] = $matches[4];
            }

            if (isset($parts['host']) && isset($parts['dbname'])) {
                // Conectar al TENANT específico
                $dsn = "mysql:host={$parts['host']};dbname={$parts['dbname']};charset=utf8mb4";
                $pdo = new PDO($dsn, $parts['user'], $parts['password'], [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]);

                // 2. Procesar la actualización del formulario si se envió por POST
                if ($_SERVER["REQUEST_METHOD"] === "POST") {
                    $concepto = trim($_POST['concepto']);
                    $precio = floatval($_POST['precio']);
                    $moneda = $_POST['moneda'] ?? 'USD';
                    $modopago = $_POST['modopago'] ?? 'Transferencia';
                    $estatus = $_POST['estatus'] ?? 'PENDIENTE';
                    $fechapago = $_POST['fechapago'] ?? date('Y-m-d');
                    $fecha_vencimiento = date('Y-m-d', strtotime('+30 days', strtotime($fechapago)));

                    $banco_destino = $_POST['banco_destino'] ?? '';
                    $cuenta_marcsoftware = trim($_POST['cuenta_marcsoftware'] ?? '');
                    $beneficiario_marcsoftware = $_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions';
                    
                    $fullConcept = $concepto;
                    if ($modopago === 'Transferencia' && $banco_destino) {
                        $fullConcept .= "\n\nDATOS PARA TRANSFERENCIA:\n"
                                      . "Banco: {$banco_destino}\n"
                                      . "Cuenta: {$cuenta_marcsoftware}\n"
                                      . "Beneficiario: {$beneficiario_marcsoftware}";
                    }

                    $stmt_update = $pdo->prepare("UPDATE FACTURAS_SOFTWARE SET
                        CONCEPTO = ?, PRECIO = ?, MONEDA = ?, MODO_PAGO = ?,
                        ESTATUS = ?, FECHA_FACTURA = ?, FECHA_VENCIMIENTO = ?,
                        BANCO_DESTINO = ?, CUENTA_DESTINO = ?, BENEFICIARIO = ?
                        WHERE CLAVE = ?");
                    $stmt_update->execute([
                        $fullConcept, $precio, $moneda, $modopago,
                        $estatus, $fechapago, $fecha_vencimiento,
                        $banco_destino, $cuenta_marcsoftware, $beneficiario_marcsoftware,
                        $id_factura
                    ]);

                    $success = "Factura {$id_factura} actualizada exitosamente.";
                }

                // 3. Cargar los datos de la factura para mostrar el formulario
                $stmt_factura = $pdo->prepare("SELECT fs.*, cs.NOMBRE_COMPLETO
                                               FROM FACTURAS_SOFTWARE fs
                                               JOIN CLIENTES_SOFTWARE cs ON fs.CLIENTE_CLAVE = cs.CLAVE
                                               WHERE fs.CLAVE = ?");
                $stmt_factura->execute([$id_factura]);
                $factura = $stmt_factura->fetch(PDO::FETCH_ASSOC);

                if (!$factura) {
                    $error = "Factura con ID {$id_factura} no encontrada.";
                }

            } else {
                $error = "Error al conectar con el consultorio. La cadena de conexión no es válida.";
            }
        } else {
            $error = "Configuración del consultorio no encontrada o inactiva.";
        }
    } catch (PDOException $e) {
        $error = "Error de conexión con la base de datos maestra: " . htmlspecialchars($e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Editar Factura - MarcSoftware Solutions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2>✏️ Editar Factura #<?= htmlspecialchars($factura['NUMERO_FACTURA'] ?? 'N/A') ?></h2>
    <p class="text-muted">Cliente: <?= htmlspecialchars($factura['NOMBRE_COMPLETO'] ?? 'N/A') ?></p>

    <?php if ($error): ?>
        <div class="alert alert-danger"><?= $error ?></div>
        <a href="historico_facturas.php" class="btn btn-primary">Volver al Histórico</a>
    <?php elseif ($factura): ?>
        <?php if ($success): ?>
            <div class="alert alert-success">
                <?= $success ?>
                <a href="historico_facturas.php?subdominio=<?= urlencode($subdominio) ?>" class="btn btn-sm btn-light ms-2">Volver</a>
                <a href="generar_factura_pdf.php?id_factura=<?= $id_factura ?>&subdominio=<?= urlencode($subdominio) ?>" target="_blank" class="btn btn-sm btn-info ms-2">
                    🖨️ Ver PDF
                </a>
            </div>
        <?php endif; ?>

        <form method="POST">
            <div class="card mb-4">
                <div class="card-header"><h5>💰 Detalles de Facturación</h5></div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Concepto/Servicio:</label>
                        <textarea name="concepto" class="form-control" rows="3" required><?= htmlspecialchars($factura['CONCEPTO']) ?></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Precio:</label>
                            <input type="number" name="precio" step="0.01" class="form-control" value="<?= htmlspecialchars($factura['PRECIO']) ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Moneda:</label>
                            <select name="moneda" class="form-control">
                                <option value="USD" <?= ($factura['MONEDA'] === 'USD') ? 'selected' : '' ?>>USD (Dólares)</option>
                                <option value="DOP" <?= ($factura['MONEDA'] === 'DOP') ? 'selected' : '' ?>>RD$ (Pesos)</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Modo de Pago:</label>
                            <select name="modopago" class="form-control" id="modopago" onchange="toggleBankFields()">
                                <option value="Transferencia" <?= ($factura['MODO_PAGO'] === 'Transferencia') ? 'selected' : '' ?>>Transferencia</option>
                                <option value="Efectivo" <?= ($factura['MODO_PAGO'] === 'Efectivo') ? 'selected' : '' ?>>Efectivo</option>
                                <option value="Cheque" <?= ($factura['MODO_PAGO'] === 'Cheque') ? 'selected' : '' ?>>Cheque</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                         <div class="col-md-6 mb-3">
                            <label class="form-label">Estado:</label>
                            <select name="estatus" class="form-control">
                                <option value="PENDIENTE" <?= ($factura['ESTATUS'] === 'PENDIENTE') ? 'selected' : '' ?>>PENDIENTE</option>
                                <option value="PAGADO" <?= ($factura['ESTATUS'] === 'PAGADO') ? 'selected' : '' ?>>PAGADO</option>
                                <option value="CANCELADO" <?= ($factura['ESTATUS'] === 'CANCELADO') ? 'selected' : '' ?>>CANCELADO</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Fecha de Factura:</label>
                            <input type="date" name="fechapago" class="form-control" value="<?= htmlspecialchars($factura['FECHA_FACTURA']) ?>">
                        </div>
                    </div>
                </div>
            </div>

            <div id="bank-fields" class="card mb-4" style="display: none;">
                <div class="card-header"><h5>🏦 Datos Bancarios - MarcSoftware Solutions</h5></div>
                <div class="card-body">
                    <p class="text-info">Estos datos se guardarán en la factura si el modo de pago es Transferencia.</p>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Banco Destino:</label>
                            <select name="banco_destino" class="form-control">
                                <option value="">Seleccionar Banco</option>
                                <option <?= ($factura['BANCO_DESTINO'] === 'Banco Popular') ? 'selected' : '' ?>>Banco Popular</option>
                                <option <?= ($factura['BANCO_DESTINO'] === 'Banreservas') ? 'selected' : '' ?>>Banreservas</option>
                                <option <?= ($factura['BANCO_DESTINO'] === 'Banco BHD León') ? 'selected' : '' ?>>Banco BHD León</option>
                                <option <?= ($factura['BANCO_DESTINO'] === 'Banco Scotiabank') ? 'selected' : '' ?>>Banco Scotiabank</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Cuenta MarcSoftware:</label>
                            <input type="text" name="cuenta_marcsoftware" class="form-control" placeholder="Número de cuenta" value="<?= htmlspecialchars($factura['CUENTA_DESTINO']) ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Beneficiario:</label>
                            <input type="text" name="beneficiario_marcsoftware" class="form-control" value="<?= htmlspecialchars($factura['BENEFICIARIO']) ?>">
                        </div>
                    </div>
                </div>
            </div>

            <button type="submit" class="btn btn-success btn-lg mb-4">
                💾 Guardar Cambios
            </button>
            <a href="historico_facturas.php?subdominio=<?= urlencode($subdominio) ?>" class="btn btn-secondary btn-lg mb-4">
                Cancelar
            </a>
        </form>
    <?php endif; ?>
</div>

<script>
function toggleBankFields() {
  const modo = document.getElementById('modopago').value;
  document.getElementById('bank-fields').style.display = modo === 'Transferencia' ? 'block' : 'none';
}
document.addEventListener('DOMContentLoaded', toggleBankFields);
</script>
</body>
</html>