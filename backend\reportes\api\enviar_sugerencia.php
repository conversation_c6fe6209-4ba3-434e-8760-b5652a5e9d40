<?php
/**
 * API para enviar sugerencias de reportes personalizados
 * Guarda las sugerencias en la base de datos y opcionalmente envía por email
 */

session_start();
require_once __DIR__ . '/../../config/database.php';

// Configurar respuesta JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Solo permitir método POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit;
}

try {
    // Obtener datos del POST
    $input = json_decode(file_get_contents('php://input'), true);
    $sugerencia = trim($input['sugerencia'] ?? '');
    
    // Validar que la sugerencia no esté vacía
    if (empty($sugerencia)) {
        echo json_encode([
            'success' => false, 
            'message' => 'Por favor, escribe tu sugerencia antes de enviar.'
        ]);
        exit;
    }
    
    // Validar longitud mínima
    if (strlen($sugerencia) < 10) {
        echo json_encode([
            'success' => false, 
            'message' => 'La sugerencia debe tener al menos 10 caracteres.'
        ]);
        exit;
    }
    
    // Obtener información del usuario (si está logueado)
    $usuario = $_SESSION['usuario'] ?? 'Anónimo';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Desconocida';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Desconocido';
    
    // Crear tabla si no existe
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS SUGERENCIAS_REPORTES (
            CLAVE INT AUTO_INCREMENT PRIMARY KEY,
            SUGERENCIA TEXT NOT NULL,
            USUARIO VARCHAR(100) DEFAULT 'Anónimo',
            IP_ADDRESS VARCHAR(45),
            USER_AGENT TEXT,
            FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ESTADO ENUM('PENDIENTE', 'REVISADA', 'IMPLEMENTADA', 'RECHAZADA') DEFAULT 'PENDIENTE',
            PRIORIDAD ENUM('BAJA', 'MEDIA', 'ALTA') DEFAULT 'MEDIA',
            COMENTARIOS_ADMIN TEXT,
            FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($createTableSQL);
    
    // Insertar la sugerencia
    $stmt = $pdo->prepare("
        INSERT INTO SUGERENCIAS_REPORTES 
        (SUGERENCIA, USUARIO, IP_ADDRESS, USER_AGENT) 
        VALUES (?, ?, ?, ?)
    ");
    
    $stmt->execute([$sugerencia, $usuario, $ip_address, $user_agent]);
    $sugerencia_id = $pdo->lastInsertId();
    
    // Opcional: Enviar notificación por email al administrador
    $enviar_email = true; // Cambiar a false si no quieres emails
    
    if ($enviar_email) {
        try {
            $to = '<EMAIL>'; // Cambiar por el email del administrador
            $subject = '💡 Nueva Sugerencia de Reporte - ID: ' . $sugerencia_id;
            $message = "
                <html>
                <head>
                    <title>Nueva Sugerencia de Reporte</title>
                </head>
                <body>
                    <h2>💡 Nueva Sugerencia de Reporte Personalizado</h2>
                    <p><strong>ID:</strong> {$sugerencia_id}</p>
                    <p><strong>Usuario:</strong> {$usuario}</p>
                    <p><strong>Fecha:</strong> " . date('d/m/Y H:i:s') . "</p>
                    <p><strong>IP:</strong> {$ip_address}</p>
                    
                    <h3>Sugerencia:</h3>
                    <div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0;'>
                        " . nl2br(htmlspecialchars($sugerencia)) . "
                    </div>
                    
                    <hr>
                    <p><small>Este mensaje fue generado automáticamente por el Sistema de Consultorio.</small></p>
                </body>
                </html>
            ";
            
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                'From: Sistema Consultorio <<EMAIL>>',
                'Reply-To: ' . $usuario . ' <<EMAIL>>',
                'X-Mailer: PHP/' . phpversion()
            ];
            
            // Intentar enviar email (no bloquear si falla)
            @mail($to, $subject, $message, implode("\r\n", $headers));
            
        } catch (Exception $e) {
            // Log del error pero no fallar la operación
            error_log("Error enviando email de sugerencia: " . $e->getMessage());
        }
    }
    
    // Respuesta exitosa
    echo json_encode([
        'success' => true,
        'message' => '¡Gracias por tu sugerencia! La hemos recibido y la evaluaremos para futuras actualizaciones.',
        'sugerencia_id' => $sugerencia_id,
        'fecha' => date('d/m/Y H:i:s')
    ]);
    
} catch (PDOException $e) {
    error_log("Error de base de datos en sugerencias: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor. Por favor, inténtalo más tarde.'
    ]);
    
} catch (Exception $e) {
    error_log("Error general en sugerencias: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Error procesando la sugerencia. Por favor, inténtalo más tarde.'
    ]);
}
?>
