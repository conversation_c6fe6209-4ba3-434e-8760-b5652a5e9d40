<?php
session_start();
require_once '../config/database.php';
$config = include '../config/config.php'; // Ruta al archivo de configuración

// Verificar autenticación
if (!isset($_SESSION['usuario']) || !isset($_SESSION['rol'])) {
    header('Location: ../../login.php');
    exit;
}

// Función para obtener la URL de regreso según el rol
function getBackUrl($rol) {
    switch ($rol) {
        case 'secretaria':
            return '../../secretaria_panel.php';
        case 'doctor':
            return '../../doctor_panel_v2.php';
        case 'admin':
            return '../../index.php';
        default:
            return '../../index.php';
    }
}

// Función para obtener el texto del botón según el rol
function getBackText($rol) {
    switch ($rol) {
        case 'secretaria':
            return 'Panel de Secretaría';
        case 'doctor':
            return 'Panel del Doctor';
        case 'admin':
            return 'Panel de Administración';
        default:
            return 'Inicio';
    }
}

$backUrl = getBackUrl($_SESSION['rol']);
$backText = getBackText($_SESSION['rol']);

// Opcional: Cargar el paciente si se pasa el ID en la URL
$paciente = null;
if (isset($_GET['clave'])) {
    $paciente_id = $_GET['clave'];
    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
    $stmt->execute([$paciente_id]);
    $paciente = $stmt->fetch();
}

$activeTab = 'registrar'; // Valor por defecto
if (isset($_GET['tab'])) {
    $activeTab = $_GET['tab'];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Pacientes</title>

    <!-- Favicon médico -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
    <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
    <!-- Bootstrap y Font Awesome -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="../../public/assets/css/custom.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- DataTables CSS moderno -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">

    <style>
        :root {
            /* Paleta médica profesional - Actualizada */
            --medical-primary: #059669;
            --medical-secondary: #10b981;
            --medical-light: #ffffff;
            --medical-accent: #0ea5e9;
            --medical-dark: #1f2937;

            /* Colores de estado médico */
            --success-medical: #10b981;
            --warning-medical: #f59e0b;
            --danger-medical: #ef4444;
            --info-medical: #3b82f6;

            /* Colores neutros médicos */
            --medical-white: #ffffff;
            --medical-gray-50: #f8fafc;
            --medical-gray-100: #f1f5f9;
            --medical-gray-200: #e2e8f0;
            --medical-gray-300: #cbd5e1;
            --medical-gray-400: #94a3b8;
            --medical-gray-500: #64748b;
            --medical-gray-600: #475569;
            --medical-gray-700: #334155;
            --medical-gray-800: #1e293b;
            --medical-gray-900: #0f172a;

            /* Configuración de diseño */
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --box-shadow-medical: 0 10px 25px -5px rgba(30, 64, 175, 0.1), 0 8px 10px -6px rgba(30, 64, 175, 0.1);
            --box-shadow-hover: 0 20px 25px -5px rgba(30, 64, 175, 0.15), 0 10px 10px -5px rgba(30, 64, 175, 0.1);
            --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 25%, #0ea5e9 50%, #06b6d4 75%, #10b981 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: var(--medical-gray-800);
            position: relative;
        }

        /* Patrón médico de fondo */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            background-size: 100px 100px;
            pointer-events: none;
            z-index: -1;
        }

        /* Container principal médico */
        .medical-container {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(248, 250, 252, 0.95) 50%,
                rgba(219, 234, 254, 0.9) 100%);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--box-shadow-medical);
            border: 1px solid rgba(30, 64, 175, 0.1);
            margin: 2rem auto;
            padding: 2.5rem;
            position: relative;
            overflow: hidden;
        }

        .medical-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                var(--medical-primary) 0%,
                var(--medical-secondary) 25%,
                var(--medical-accent) 50%,
                var(--info-medical) 75%,
                var(--success-medical) 100%);
        }

        /* Título médico */
        .medical-title {
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
        }

        .medical-title h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--medical-primary);
            text-shadow: 0 2px 4px rgba(30, 64, 175, 0.1);
            margin-bottom: 0.5rem;
        }

        .medical-title .subtitle {
            color: var(--medical-gray-600);
            font-size: 1.1rem;
            font-weight: 400;
        }

        .medical-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, var(--medical-primary), var(--medical-accent));
            border-radius: 2px;
        }

        /* Container de pestañas médicas ultra modernas */
        .medical-tabs-container {
            position: relative;
            padding: 1rem;
        }

        .tabs-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                rgba(30, 64, 175, 0.08) 0%,
                rgba(59, 130, 246, 0.05) 25%,
                rgba(14, 165, 233, 0.08) 50%,
                rgba(6, 182, 212, 0.05) 75%,
                rgba(16, 185, 129, 0.08) 100%);
            border-radius: 24px;
            border: 2px solid rgba(30, 64, 175, 0.1);
            box-shadow:
                inset 0 2px 10px rgba(30, 64, 175, 0.05),
                0 8px 25px rgba(30, 64, 175, 0.1);
        }

        .medical-nav-pills {
            position: relative;
            z-index: 2;
            background: transparent !important;
            padding: 0.75rem;
        }

        .medical-tab {
            border: none !important;
            border-radius: 20px !important;
            padding: 1.5rem 2rem !important;
            font-weight: 600 !important;
            color: var(--medical-gray-600) !important;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.9) 0%,
                rgba(248, 250, 252, 0.8) 100%) !important;
            backdrop-filter: blur(15px);
            transition: var(--transition) !important;
            position: relative;
            overflow: hidden;
            margin: 0.5rem !important;
            border: 2px solid rgba(255, 255, 255, 0.3) !important;
            box-shadow: 0 6px 20px rgba(30, 64, 175, 0.1);
            display: flex !important;
            align-items: center;
            gap: 1rem;
            min-height: 80px;
        }

        .tab-icon-container {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            background: linear-gradient(135deg,
                rgba(30, 64, 175, 0.1) 0%,
                rgba(59, 130, 246, 0.05) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
            flex-shrink: 0;
        }

        .tab-icon-container i {
            font-size: 1.5rem;
            color: var(--medical-gray-500);
            transition: var(--transition);
        }

        .tab-content-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            text-align: left;
        }

        .tab-title {
            font-size: 1.1rem;
            font-weight: 700;
            line-height: 1.2;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .tab-subtitle {
            font-size: 0.85rem;
            font-weight: 400;
            opacity: 0.8;
            margin-top: 0.25rem;
            text-transform: none;
            letter-spacing: normal;
        }

        .medical-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent);
            transition: left 0.6s ease;
        }

        .medical-tab::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 3px;
            background: linear-gradient(90deg,
                var(--medical-primary),
                var(--medical-accent));
            border-radius: 2px;
            transition: width 0.4s ease;
        }

        .medical-tab:hover::before {
            left: 100%;
        }

        .medical-tab:hover::after {
            width: 80%;
        }

        .medical-tab:hover {
            color: var(--medical-primary) !important;
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.98) 0%,
                rgba(219, 234, 254, 0.9) 100%) !important;
            transform: translateY(-6px) scale(1.03);
            border-color: rgba(30, 64, 175, 0.3) !important;
            box-shadow: 0 12px 30px rgba(30, 64, 175, 0.2);
        }

        .medical-tab:hover .tab-icon-container {
            background: linear-gradient(135deg,
                rgba(30, 64, 175, 0.15) 0%,
                rgba(59, 130, 246, 0.1) 100%);
            transform: scale(1.1) rotate(5deg);
        }

        .medical-tab:hover .tab-icon-container i {
            color: var(--medical-primary);
            transform: scale(1.2);
        }

        .medical-tab:hover .tab-title {
            color: var(--medical-primary);
        }

        .medical-tab.active {
            color: var(--medical-white) !important;
            background: linear-gradient(135deg,
                var(--medical-primary) 0%,
                var(--medical-secondary) 30%,
                var(--medical-accent) 70%,
                var(--info-medical) 100%) !important;
            transform: translateY(-8px) scale(1.05);
            border-color: var(--medical-white) !important;
            box-shadow:
                0 15px 40px rgba(30, 64, 175, 0.4),
                0 0 30px rgba(30, 64, 175, 0.2);
            position: relative;
            z-index: 10;
        }

        .medical-tab.active .tab-icon-container {
            background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.2) 0%,
                rgba(255, 255, 255, 0.1) 100%);
            transform: scale(1.15);
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
        }

        .medical-tab.active .tab-icon-container i {
            color: var(--medical-white);
            transform: scale(1.3);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .medical-tab.active .tab-title,
        .medical-tab.active .tab-subtitle {
            color: var(--medical-white);
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .medical-tab.active::before {
            background: linear-gradient(90deg,
                transparent,
                rgba(255, 255, 255, 0.2),
                transparent);
            left: 100%;
            animation: shimmer 2s infinite;
        }

        .medical-tab.active::after {
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg,
                rgba(255, 255, 255, 0.8),
                rgba(255, 255, 255, 0.4),
                rgba(255, 255, 255, 0.8));
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .medical-tab i {
            transition: var(--transition);
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        .medical-tab:hover i {
            transform: scale(1.2) rotate(5deg);
            color: var(--medical-accent);
        }

        .medical-tab.active i {
            transform: scale(1.3);
            color: var(--medical-white);
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            animation: pulse-icon 2s infinite;
        }

        /* Animaciones para las pestañas */
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes pulse-icon {
            0%, 100% { transform: scale(1.3); }
            50% { transform: scale(1.4); }
        }

        /* Efecto de resplandor para pestaña activa */
        .medical-tab.active {
            animation: glow-tab 3s ease-in-out infinite alternate;
        }

        @keyframes glow-tab {
            from {
                box-shadow: 0 12px 35px rgba(30, 64, 175, 0.4);
            }
            to {
                box-shadow: 0 12px 35px rgba(30, 64, 175, 0.6),
                           0 0 20px rgba(30, 64, 175, 0.3);
            }
        }

        /* Contenido de pestañas médicas */
        .medical-tab-content {
            margin-top: 2rem;
        }

        .medical-tab-panel {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 2rem;
            border: 1px solid rgba(30, 64, 175, 0.1);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.05);
        }

        /* Estilos para DataTables médico */
        .dataTables_wrapper {
            font-family: 'Inter', sans-serif;
        }

        .dataTables_wrapper .dataTables_length select,
        .dataTables_wrapper .dataTables_filter input {
            border: 2px solid var(--medical-gray-200);
            border-radius: 8px;
            padding: 0.5rem;
            color: var(--medical-gray-700);
            transition: var(--transition);
        }

        .dataTables_wrapper .dataTables_length select:focus,
        .dataTables_wrapper .dataTables_filter input:focus {
            border-color: var(--medical-primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            border-radius: 8px !important;
            margin: 0 2px !important;
            padding: 0.5rem 1rem !important;
            border: 1px solid var(--medical-gray-200) !important;
            color: var(--medical-gray-600) !important;
            transition: var(--transition) !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: var(--medical-primary) !important;
            color: var(--medical-white) !important;
            border-color: var(--medical-primary) !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%) !important;
            color: var(--medical-white) !important;
            border-color: var(--medical-primary) !important;
        }

        /* Tabla médica */
        .table {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.05);
        }

        .table thead th {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: var(--medical-white);
            border: none;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        .table tbody tr {
            transition: var(--transition);
        }

        .table tbody tr:hover {
            background-color: rgba(30, 64, 175, 0.05);
            transform: scale(1.01);
        }

        .table tbody td {
            border-color: var(--medical-gray-200);
            vertical-align: middle;
        }

        /* Botones médicos */
        .btn-medical-primary {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            border: none;
            color: var(--medical-white);
            border-radius: 12px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.2);
        }

        .btn-medical-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
            color: var(--medical-white);
        }

        .btn-medical-success {
            background: linear-gradient(135deg, var(--success-medical) 0%, #34d399 100%);
            border: none;
            color: var(--medical-white);
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.2);
        }

        .btn-medical-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
            color: var(--medical-white);
        }

        .btn-medical-warning {
            background: linear-gradient(135deg, var(--warning-medical) 0%, #fbbf24 100%);
            border: none;
            color: var(--medical-white);
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.2);
        }

        .btn-medical-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
            color: var(--medical-white);
        }

        .btn-medical-danger {
            background: linear-gradient(135deg, var(--danger-medical) 0%, #f87171 100%);
            border: none;
            color: var(--medical-white);
            border-radius: 12px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: var(--transition);
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
        }

        .btn-medical-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
            color: var(--medical-white);
        }

        /* Formularios médicos */
        .form-control {
            border: 2px solid var(--medical-gray-200);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            transition: var(--transition);
            font-family: 'Inter', sans-serif;
        }

        .form-control:focus {
            border-color: var(--medical-primary);
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
        }

        .form-label {
            color: var(--medical-gray-700);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        /* Animaciones */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* Botón de regresar médico */
        .medical-back-btn {
            background: linear-gradient(135deg,
                var(--medical-primary) 0%,
                var(--medical-secondary) 100%) !important;
            border: none !important;
            color: white !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 15px !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3) !important;
            text-decoration: none !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
        }

        .medical-back-btn:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4) !important;
            color: white !important;
        }

        .medical-back-btn:active {
            transform: translateY(0) !important;
        }

        .medical-back-btn i {
            transition: transform 0.3s ease !important;
        }

        .medical-back-btn:hover i {
            transform: translateX(-3px) !important;
        }

        /* Responsive para pestañas médicas */
        @media (max-width: 768px) {
            .medical-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .medical-title h1 {
                font-size: 2rem;
            }

            .medical-tabs-container {
                padding: 0.5rem;
            }

            .medical-nav-pills {
                padding: 0.5rem;
            }

            .medical-tab {
                padding: 1rem 1.5rem !important;
                margin: 0.25rem !important;
                min-height: 70px;
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .tab-icon-container {
                width: 40px;
                height: 40px;
                border-radius: 12px;
            }

            .tab-icon-container i {
                font-size: 1.2rem;
            }

            .tab-content-text {
                align-items: center;
            }

            .tab-title {
                font-size: 0.95rem;
            }

            .tab-subtitle {
                font-size: 0.75rem;
                display: none; /* Ocultar subtítulo en móvil */
            }

            .medical-tab-panel {
                padding: 1.5rem;
            }
        }

        @media (max-width: 576px) {
            .medical-tab {
                padding: 0.75rem 1rem !important;
                min-height: 60px;
            }

            .tab-icon-container {
                width: 35px;
                height: 35px;
                border-radius: 10px;
            }

            .tab-icon-container i {
                font-size: 1rem;
            }

            .tab-title {
                font-size: 0.85rem;
            }
        }

        /* Estilos para herramientas de mantenimiento */
        .maintenance-tool {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: var(--medical-light);
            border: 2px solid var(--medical-gray-100);
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .maintenance-tool:hover {
            border-color: var(--medical-primary);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.15);
        }

        .tool-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .tool-content h6 {
            color: var(--medical-primary);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .tool-content p {
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navegación de regreso -->
    <nav class="navbar navbar-expand-lg" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(15px); border-bottom: 2px solid rgba(30, 64, 175, 0.1);">
        <div class="container">
            <a class="navbar-brand" href="<?php echo $backUrl; ?>" style="color: var(--medical-primary); font-weight: 600;">
                <i class="fas fa-arrow-left me-2"></i>
                <i class="fas fa-stethoscope me-2"></i>
                Volver al <?php echo $backText; ?>
            </a>
        </div>
    </nav>

    <div class="container">
        <div class="medical-container">
            <!-- Botón de regresar y título médico -->
            <div class="d-flex align-items-center justify-content-between mb-4">
                <a href="<?php echo $backUrl; ?>" class="btn btn-outline-primary btn-lg medical-back-btn">
                    <i class="fas fa-arrow-left me-2"></i>
                    Regresar al <?php echo $backText; ?>
                </a>
            </div>

            <!-- Título médico -->
            <div class="medical-title">
                <h1><i class="fas fa-users me-3"></i>Gestión de Pacientes</h1>
                <p class="subtitle">
                    <i class="fas fa-heartbeat me-2"></i>
                    Sistema integral de administración de pacientes médicos
                </p>
            </div>

            <!-- Pestañas médicas ultra modernas -->
            <div class="medical-tabs-container mb-5">
                <div class="tabs-background"></div>
                <ul class="nav nav-pills nav-fill medical-nav-pills" id="medicalTab" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link medical-tab <?= $activeTab == 'registrar' ? 'active' : '' ?>"
                                id="registrar-tab"
                                data-bs-toggle="pill"
                                data-bs-target="#registrar"
                                type="button"
                                role="tab"
                                aria-controls="registrar"
                                aria-selected="<?= $activeTab == 'registrar' ? 'true' : 'false' ?>">
                            <div class="tab-icon-container">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="tab-content-text">
                                <span class="tab-title">Registrar/Actualizar</span>
                                <span class="tab-subtitle">Gestionar información del paciente</span>
                            </div>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link medical-tab <?= $activeTab == 'buscar' ? 'active' : '' ?>"
                                id="buscar-tab"
                                data-bs-toggle="pill"
                                data-bs-target="#buscar"
                                type="button"
                                role="tab"
                                aria-controls="buscar"
                                aria-selected="<?= $activeTab == 'buscar' ? 'true' : 'false' ?>">
                            <div class="tab-icon-container">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="tab-content-text">
                                <span class="tab-title">Buscar Pacientes</span>
                                <span class="tab-subtitle">Consultar base de datos</span>
                            </div>
                        </button>
                    </li>

                    <!-- Pestaña Mantenimiento -->
                    <li class="nav-item" role="presentation">
                        <button class="nav-link medical-tab <?= $activeTab == 'mantenimiento' ? 'active' : '' ?>"
                                id="mantenimiento-tab"
                                data-bs-toggle="pill"
                                data-bs-target="#mantenimiento"
                                type="button"
                                role="tab"
                                aria-controls="mantenimiento"
                                aria-selected="<?= $activeTab == 'mantenimiento' ? 'true' : 'false' ?>">
                            <div class="tab-icon-container">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="tab-content-text">
                                <span class="tab-title">Mantenimiento</span>
                                <span class="tab-subtitle">Herramientas del sistema</span>
                            </div>
                        </button>
                    </li>
                </ul>
            </div>

            <!-- Contenido de las pestañas -->
            <div class="tab-content medical-tab-content" id="medicalTabContent">
                <!-- Pestaña Buscar -->
                <div class="tab-pane fade <?= $activeTab == 'buscar' ? 'show active' : '' ?>"
                     id="buscar"
                     role="tabpanel"
                     aria-labelledby="buscar-tab">
                    <div class="medical-tab-panel">
                        <?php include 'buscar_pacientes.php'; ?>
                    </div>
                </div>

                <!-- Pestaña Registrar/Actualizar -->
                <div class="tab-pane fade <?= $activeTab == 'registrar' ? 'show active' : '' ?>"
                     id="registrar"
                     role="tabpanel"
                     aria-labelledby="registrar-tab">
                    <div class="medical-tab-panel">
                        <?php include 'crear_paciente.php'; ?>
                    </div>
                </div>

                <!-- Pestaña Mantenimiento -->
                <div class="tab-pane fade <?= $activeTab == 'mantenimiento' ? 'show active' : '' ?>"
                     id="mantenimiento"
                     role="tabpanel"
                     aria-labelledby="mantenimiento-tab">
                    <div class="medical-tab-panel">
                        <div class="row">
                            <div class="col-12">
                                <div class="medical-card">
                                    <div class="medical-card-header">
                                        <h5><i class="fas fa-tools me-2"></i>Herramientas de Mantenimiento</h5>
                                        <p class="text-muted mb-0">Utilidades para mantener el sistema en óptimas condiciones</p>
                                    </div>
                                    <div class="medical-card-body">
                                        <div class="row g-4">
                                            <!-- Corrección de Rutas de Fotos -->
                                            <div class="col-md-6">
                                                <div class="maintenance-tool">
                                                    <div class="tool-icon">
                                                        <i class="fas fa-image"></i>
                                                    </div>
                                                    <div class="tool-content">
                                                        <h6>Corregir Rutas de Fotografías</h6>
                                                        <p class="text-muted">Normaliza las rutas de fotografías inconsistentes en la base de datos.</p>
                                                        <a href="corregir_rutas_fotos.php" target="_blank" class="btn btn-warning">
                                                            <i class="fas fa-wrench me-2"></i>Ejecutar Corrección
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Placeholder para futuras herramientas -->
                                            <div class="col-md-6">
                                                <div class="maintenance-tool">
                                                    <div class="tool-icon">
                                                        <i class="fas fa-database"></i>
                                                    </div>
                                                    <div class="tool-content">
                                                        <h6>Optimización de Base de Datos</h6>
                                                        <p class="text-muted">Próximamente: Herramientas para optimizar el rendimiento de la BD.</p>
                                                        <button class="btn btn-secondary" disabled>
                                                            <i class="fas fa-clock me-2"></i>Próximamente
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts modernos -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables JS moderno -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        $(document).ready(function() {
            // Animación de entrada
            $('.medical-container').addClass('fade-in-up');

            // Función para inicializar DataTable médico
            function initMedicalDataTable() {
                if ($('#tablaPacientes').length && !$.fn.DataTable.isDataTable('#tablaPacientes')) {
                    $('#tablaPacientes').DataTable({
                        paging: true,
                        ordering: true,
                        searching: true,
                        responsive: true,
                        pageLength: 10,
                        lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "Todos"]],
                        language: {
                            lengthMenu: "Mostrar _MENU_ pacientes por página",
                            search: "Buscar paciente:",
                            searchPlaceholder: "Nombre, cédula, teléfono...",
                            zeroRecords: "No se encontraron pacientes",
                            emptyTable: "No hay pacientes registrados",
                            loadingRecords: "Cargando pacientes...",
                            processing: "Procesando...",
                            info: "Mostrando _START_ a _END_ de _TOTAL_ pacientes",
                            infoEmpty: "Mostrando 0 a 0 de 0 pacientes",
                            infoFiltered: "(filtrado de _MAX_ pacientes totales)",
                            paginate: {
                                first: "Primero",
                                last: "Último",
                                next: "Siguiente",
                                previous: "Anterior"
                            }
                        },
                        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                             '<"row"<"col-sm-12"tr>>' +
                             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        columnDefs: [
                            {
                                targets: -1,
                                orderable: false,
                                className: 'text-center'
                            }
                        ],
                        initComplete: function() {
                            // Personalizar el campo de búsqueda
                            $('.dataTables_filter input').addClass('form-control');
                            $('.dataTables_length select').addClass('form-select');

                            // Agregar placeholder al campo de búsqueda
                            $('.dataTables_filter input').attr('placeholder', 'Buscar paciente...');
                        }
                    });
                }
            }

            // Si la pestaña Buscar está activa al cargar la página
            if ($('#medicalTabContent .tab-pane.active').attr('id') === 'buscar') {
                setTimeout(initMedicalDataTable, 200);
            }

            // Cuando se cambie de pestaña
            $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function (e) {
                const targetId = $(e.target).attr('data-bs-target');

                if (targetId === '#buscar') {
                    setTimeout(initMedicalDataTable, 200);
                }

                // Animación de entrada para el contenido de la pestaña
                $(targetId + ' .medical-tab-panel').addClass('fade-in-up');
            });

            // Efectos hover mejorados para botones
            $('.btn-medical-primary, .btn-medical-success, .btn-medical-warning, .btn-medical-danger').hover(
                function() {
                    $(this).css('transform', 'translateY(-2px) scale(1.02)');
                },
                function() {
                    $(this).css('transform', 'translateY(0) scale(1)');
                }
            );

            // Mejorar la experiencia de los formularios
            $('.form-control').on('focus', function() {
                $(this).parent().addClass('focused');
            }).on('blur', function() {
                $(this).parent().removeClass('focused');
            });

            // Notificaciones toast para acciones
            function showMedicalToast(message, type = 'info') {
                const toastContainer = $('#medical-toast-container').length ?
                    $('#medical-toast-container') :
                    $('<div id="medical-toast-container" class="toast-container position-fixed bottom-0 end-0 p-3"></div>').appendTo('body');

                const toastId = 'toast-' + Date.now();
                const bgClass = type === 'success' ? 'bg-success' :
                               type === 'error' ? 'bg-danger' :
                               type === 'warning' ? 'bg-warning' : 'bg-info';

                const toast = $(`
                    <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                                ${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `);

                toastContainer.append(toast);
                const bsToast = new bootstrap.Toast(toast[0]);
                bsToast.show();

                toast.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

            // Hacer la función disponible globalmente
            window.showMedicalToast = showMedicalToast;

            // Atajos de teclado
            $(document).on('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '1':
                            e.preventDefault();
                            $('#registrar-tab').click();
                            break;
                        case '2':
                            e.preventDefault();
                            $('#buscar-tab').click();
                            break;
                    }
                }
            });

            // Mostrar información de atajos
            const shortcutsInfo = $(`
                <div class="position-fixed bottom-0 start-0 p-3 text-muted small" style="z-index: 1000;">
                    <div class="bg-white rounded p-2 shadow-sm border" style="opacity: 0.8;">
                        <strong>Atajos:</strong> Ctrl+1 (Registrar) | Ctrl+2 (Buscar)
                    </div>
                </div>
            `);

            $('body').append(shortcutsInfo);

            // Ocultar atajos después de 5 segundos
            setTimeout(() => {
                shortcutsInfo.css('opacity', '0.3');
            }, 5000);

            shortcutsInfo.hover(
                () => shortcutsInfo.css('opacity', '1'),
                () => shortcutsInfo.css('opacity', '0.3')
            );
        });
    </script>

</body>
</html>
