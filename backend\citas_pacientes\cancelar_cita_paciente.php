<?php
// Mostrar errores para depuración (puedes quitar en producción)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// ✅ Ruta correcta al archivo de conexión
require_once __DIR__ . '/../config/database.php';

// Validar que el ID de la cita fue recibido correctamente
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo "❌ Cita no especificada.";
    exit;
}

$id = (int) $_GET['id'];

try {
    // Verificar que la cita existe y está en estado pendiente (estatus = 6)
    $stmt = $pdo->prepare("SELECT ESTATUS FROM CITAMEDIC WHERE CLAVE = ?");
    $stmt->execute([$id]);
    $cita = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$cita) {
        echo "❌ Cita no encontrada.";
        exit;
    }

    if ((int)$cita['ESTATUS'] !== 6) {
        echo "⚠️ Solo puedes cancelar citas en estado 'Pendiente de aprobación'.";
        exit;
    }

    // Cambiar el estatus a "1" = Cancelado
    $update = $pdo->prepare("UPDATE CITAMEDIC SET ESTATUS = 1 WHERE CLAVE = ?");
    $update->execute([$id]);

    // Redirigir al panel del paciente
    header("Location: ../../paciente_panel.php");
    exit;
} catch (PDOException $e) {
    echo "❌ Error al cancelar la cita: " . htmlspecialchars($e->getMessage());
}
