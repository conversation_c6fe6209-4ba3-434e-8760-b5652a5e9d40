<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;

require 'libs/phpmailer/src/PHPMailer.php';
require 'libs/phpmailer/src/SMTP.php';
require 'libs/phpmailer/src/Exception.php';

// Si el formulario se ha enviado
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Conectar a la base de datos
    require_once 'backend/config/database.php';
    
    // Obtener el correo electrónico del formulario
    $email = $_POST['email'];

    // Buscar al usuario por su correo electrónico
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();

    // Si el usuario existe
    if ($user) {
        // Generar un token único para el restablecimiento de contraseña
        $token = bin2hex(random_bytes(50));
        
        // Guardar el token en la base de datos (puedes añadir un campo 'reset_token' en tu tabla 'usuarios')
        $stmt = $pdo->prepare("UPDATE usuarios SET reset_token = ? WHERE email = ?");
        $stmt->execute([$token, $email]);

        // Enviar el enlace de restablecimiento por correo electrónico (puedes usar PHPMailer aquí)
        $resetLink = "https://www.marcsoftware.com/mi_consultorio/reset-password.php?token=" . $token;
        
           // --- Configurar y enviar correo con PHPMailer ---
        $mail = new PHPMailer(true);

        try {
            // Configuración del servidor SMTP
            $mail->isSMTP();
    $mail->Host       = 'mail.marcsoftware.com';
    $mail->SMTPAuth   = true;
    $mail->Username   = '<EMAIL>';
    $mail->Password   = 'Sauce32!';
    $mail->SMTPSecure = 'tls';
    $mail->Port       = 587;

    $mail->setFrom('<EMAIL>', 'Consultorio Médico Agustín');
    $mail->addAddress($email); // correo del usuario que solicitó el reset

    $mail->addReplyTo('<EMAIL>', 'No responder');

    $mail->isHTML(true);
    $mail->addCustomHeader('X-Mailer', 'Mi Consultorio - Recuperación');

    $mail->CharSet = 'UTF-8';
    $mail->Encoding = 'base64';

    $mail->Subject = 'Recuperación de Contraseña - Mi Consultorio';
   $mail->Body = "
    <html>
    <body>
        <p>Hola,</p>
        <p>Has solicitado recuperar tu contraseña.</p>
        <p>Haz clic en el siguiente enlace para restablecerla:</p>
        <p><a href='$resetLink'>$resetLink</a></p>
        <p>Si no hiciste esta solicitud, ignora este correo.</p>
        <br><p>Saludos,<br><strong>Consultorio Médico Agustín</strong></p>
        <br><small style='color: #555;'>Este correo fue enviado automáticamente. No respondas a este mensaje.</small>
    </body>
    </html>
";
    $mail->send();
    echo "✔️ Enlace enviado a tu correo electrónico.";
        } catch (Exception $e) {
            $error = "Error al enviar el correo: {$mail->ErrorInfo}";
        }

    } else {
        $error = "El correo electrónico no está registrado.";
    }
}
        
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Recuperar Contraseña - Mi Consultorio</title>
    <link rel="stylesheet" href="public/assets/css/styles.css"> <!-- Estilos CSS -->
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <h2 class="login-title">Recuperar Contraseña</h2>
            <form action="forgot-password.php" method="POST">
                <div class="form-group">
                    <label for="email">Correo Electrónico:</label>
                    <input type="email" id="email" name="email" placeholder="Ingresa tu correo" required>
                </div>
                <button type="submit" class="btn-login">Enviar enlace de recuperación</button>
                <?php if (isset($error)) echo "<p style='color:red'>$error</p>"; ?>
            </form>
        </div>
    </div>
</body>
</html>
