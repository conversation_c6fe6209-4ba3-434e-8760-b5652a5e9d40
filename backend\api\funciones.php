<?php
// Función para validar la cédula
function validaCedula($cedula) {
    if (strlen($cedula) != 11) {
        return false;
    }

    $sImp = 0;
    $sPar = 0;

    for ($i = 0; $i < 10; $i++) {
        $digito = (int)$cedula[$i];

        if ($i % 2 == 0) {
            $sImp += $digito;
        } else {
            $doble = $digito * 2;
            if ($doble > 9) {
                $sPar += ($doble - 9);
            } else {
                $sPar += $doble;
            }
        }
    }

    $sumaTotal = $sImp + $sPar;
    $digitoControl = $sumaTotal % 10;

    if ($digitoControl == 0 && (int)$cedula[10] == 0) {
        return true;
    }

    if ($digitoControl == 10 - (int)$cedula[10]) {
        return true;
    }

    return false;
}
?>
