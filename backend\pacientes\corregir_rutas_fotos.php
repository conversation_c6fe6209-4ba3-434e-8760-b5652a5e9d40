<?php
/**
 * Script para corregir rutas de fotografías inconsistentes en la base de datos
 * Ejecutar una sola vez para limpiar datos existentes
 */

require_once '../config/database.php';

// Configuración
ini_set('display_errors', 1);
error_reporting(E_ALL);

/**
 * Función para normalizar rutas de fotografías
 */
function normalizarRutaFoto($ruta) {
    if (empty($ruta)) {
        return 'fotografias/NOFOTO.BMP';
    }

    $ruta = trim($ruta);

    // Corregir rutas mal formadas comunes
    if ($ruta === 'FotografiasNOFOTO.BMP' || $ruta === 'NOFOTO.BMP') {
        return 'fotografias/NOFOTO.BMP';
    }

    // Convertir barras invertidas a barras normales
    $ruta = str_replace('\\', '/', $ruta);

    // Corregir doble directorio: fotografias/Fotografias/archivo.bmp
    if (strpos($ruta, 'fotografias/Fotografias/') === 0) {
        $ruta = str_replace('fotografias/Fotografias/', 'fotografias/', $ruta);
    }

    // Corregir directorio con mayúscula al inicio
    if (strpos($ruta, 'Fotografias/') === 0) {
        $ruta = str_replace('Fotografias/', 'fotografias/', $ruta);
    }

    // Corregir casos donde hay Fotografias en el medio
    $ruta = str_replace('/Fotografias/', '/fotografias/', $ruta);

    // Si no tiene directorio, agregarlo
    if (!strpos($ruta, '/') && $ruta !== 'fotografias/NOFOTO.BMP') {
        $ruta = 'fotografias/' . $ruta;
    }

    // Limpiar caracteres especiales que pueden causar problemas de URL
    $ruta = preg_replace('/[^\x20-\x7E]/', '', $ruta); // Remover caracteres no ASCII

    return $ruta;
}

try {
    echo "<h2>🔧 Corrección de Rutas de Fotografías</h2>";
    echo "<p>Iniciando corrección de rutas inconsistentes...</p>";
    
    // Obtener todas las fotografías con rutas problemáticas
    $stmt = $pdo->query("SELECT CLAVE, ARCHIVO, CLAVEPAC FROM FOTOGRAFIAS WHERE ARCHIVO IS NOT NULL AND ARCHIVO != ''");
    $fotografias = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $corregidas = 0;
    $sinCambios = 0;
    
    echo "<h3>📋 Procesando " . count($fotografias) . " registros...</h3>";
    echo "<ul>";
    
    foreach ($fotografias as $foto) {
        $rutaOriginal = $foto['ARCHIVO'];
        $rutaNormalizada = normalizarRutaFoto($rutaOriginal);
        
        if ($rutaOriginal !== $rutaNormalizada) {
            // Actualizar la ruta en la base de datos
            $stmtUpdate = $pdo->prepare("UPDATE FOTOGRAFIAS SET ARCHIVO = ? WHERE CLAVE = ?");
            $stmtUpdate->execute([$rutaNormalizada, $foto['CLAVE']]);
            
            echo "<li>✅ <strong>Corregida:</strong> '{$rutaOriginal}' → '{$rutaNormalizada}' (Paciente: {$foto['CLAVEPAC']})</li>";
            $corregidas++;
        } else {
            $sinCambios++;
        }
    }
    
    echo "</ul>";
    
    echo "<h3>📊 Resumen:</h3>";
    echo "<ul>";
    echo "<li><strong>Total procesados:</strong> " . count($fotografias) . "</li>";
    echo "<li><strong>Rutas corregidas:</strong> {$corregidas}</li>";
    echo "<li><strong>Sin cambios:</strong> {$sinCambios}</li>";
    echo "</ul>";
    
    if ($corregidas > 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
        echo "<strong>✅ Corrección completada exitosamente!</strong><br>";
        echo "Se corrigieron {$corregidas} rutas de fotografías en la base de datos.";
        echo "</div>";
    } else {
        echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
        echo "<strong>ℹ️ No se encontraron rutas que corregir.</strong><br>";
        echo "Todas las rutas ya están en el formato correcto.";
        echo "</div>";
    }
    
    // Verificar rutas problemáticas restantes
    $stmtCheck = $pdo->query("SELECT COUNT(*) as total FROM FOTOGRAFIAS WHERE ARCHIVO LIKE '%Fotografias%' OR ARCHIVO LIKE '%NOFOTO.BMP' AND ARCHIVO NOT LIKE 'fotografias/%'");
    $problemasRestantes = $stmtCheck->fetch()['total'];
    
    if ($problemasRestantes > 0) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
        echo "<strong>⚠️ Advertencia:</strong> Aún quedan {$problemasRestantes} rutas con posibles problemas.";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
    echo "<strong>❌ Error de base de datos:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 1rem; border-radius: 5px; margin: 1rem 0;'>";
    echo "<strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage());
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Nota:</strong> Este script se puede ejecutar múltiples veces de forma segura.</p>";
echo "<p><a href='gestion_pacientes.php'>← Volver a Gestión de Pacientes</a></p>";
?>
