<?php
require_once '../config/database.php';

// Obtener los datos del formulario
$data = json_decode(file_get_contents('php://input'), true);

 // Validar y asegurar valores predeterminados
$data['SINCRONIZADO'] = isset($data['SINCRONIZADO']) ? $data['SINCRONIZADO'] : 0;

// Actualizar los campos según los datos recibidos
$stmt = $pdo->prepare("
    UPDATE PACIENTES
    SET 
        CEDULA = ?, 
        NOMBRES = ?,  
        APELLIDOS = ?, 
        FECHANAC = ?, 
        SEXO = ?, 
        NACIONALIDAD = ?, 
        TELEFONO = ?, 
        RH = ?, 
        ECORREO = ?, 
        SINCRONIZADO = ? 
    WHERE CLAVE = ?
");

// Ejecutar la consulta con los datos recibidos
$success = $stmt->execute([
    $data['CEDULA'],             // Cédula
    $data['NOMBRES'],            // Nombres
    $data['APELLIDOS'],          // Apellidos
    $data['FECHANAC'],           // Fecha de Nacimiento
    $data['SEXO'],               // Sexo
    $data['NACIONALIDAD'],       // Nacionalidad
    $data['TELEFONO'],           // Teléfono
    $data['RH'],                 // Tipo de Sangre (RH)
    $data['ECORREO'],            // Correo Electrónico
    $data['SINCRONIZADO'],       // Sincronizado (0 o el valor enviado desde el formulario)
    $data['CLAVE']               // Clave del paciente (para actualizar el registro correspondiente)
]);

// Responder en formato JSON
echo json_encode(['success' => $success]);
?>
