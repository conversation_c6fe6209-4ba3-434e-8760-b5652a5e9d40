<?php
session_start();

// Verificar si el usuario tiene privilegios de administrador
if (!isset($_SESSION['usuario']) || $_SESSION['role'] != 'admin') {
    header('Location: login.php');
    exit();
}

require_once 'backend/config/database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $usuario = $_POST['usuario'];
    $email = $_POST['email'];
    $password = password_hash($_POST['password'], PASSWORD_BCRYPT); // Encriptar contraseña
    $role = $_POST['role'];

    try {
        $stmt = $pdo->prepare("INSERT INTO usuarios (usuario, email, password, role, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([$usuario, $email, $password, $role]);
        $success = "Usuario registrado con éxito.";
    } catch (PDOException $e) {
        $error = "Error al registrar el usuario: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Usuario</title>
    <link rel="stylesheet" href="public/assets/css/styles.css">
</head>
<body>
    <div class="container">
        <h1>Registrar Nuevo Usuario</h1>
        <?php if (isset($success)) echo "<p style='color: green;'>$success</p>"; ?>
        <?php if (isset($error)) echo "<p style='color: red;'>$error</p>"; ?>
        <form action="registro_usuario.php" method="POST">
            <label for="usuario">Usuario:</label>
            <input type="text" id="usuario" name="usuario" required>
            
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" required>
            
            <label for="password">Contraseña:</label>
            <input type="password" id="password" name="password" required>
            
            <label for="role">Rol:</label>
            <select id="role" name="role">
                <option value="admin">Administrador</option>
                <option value="user">Usuario</option>
            </select>
            
            <button type="submit">Registrar Usuario</button>
        </form>
        <a href="index.php">Volver al inicio</a>
    </div>
</body>
</html>
