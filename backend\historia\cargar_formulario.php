<?php
// Este archivo se encargará de cargar dinámicamente los formularios

// Iniciar sesión
session_start();

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Database connection
require_once '../config/database.php';

// Verificar que se recibió el parámetro formulario
if (!isset($_GET['formulario']) || empty($_GET['formulario'])) {
    echo '<div class="alert alert-danger">Formulario no especificado</div>';
    exit;
}

// Validar el nombre del formulario para evitar inyección de ruta
$formulario = basename($_GET['formulario']);
$rutaFormulario = "formularios/{$formulario}.php";

// Verificar que el archivo existe
if (!file_exists($rutaFormulario)) {
    echo '<div class="alert alert-danger">Formulario no encontrado: ' . htmlspecialchars($formulario) . '</div>';
    exit;
}

// Incluir el formulario solicitado
include($rutaFormulario);
?>
