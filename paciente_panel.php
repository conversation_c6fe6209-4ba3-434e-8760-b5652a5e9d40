<?php
// paciente_panel
session_start();
header("Cache-Control: no-cache, must-revalidate");
header("Expires: Sat, 1 Jan 2000 00:00:00 GMT");
header("Pragma: no-cache");

header('Content-Type: text/html; charset=UTF-8');

// Conexión base de datos
require_once __DIR__ . '/backend/config/database.php';

// Mostrar errores de PHP para depuración (¡DESHABILITAR EN PRODUCCIÓN!)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


$cedula = $_SESSION['cedula'] ?? '';
$mensajeEmail = '';



// --- Control de Acceso y Redirección ---

if ($_SESSION['rol'] === 'paciente') {
    // Buscar si el paciente tiene ECORREO registrado
    $stmt = $pdo->prepare("SELECT ECORREO FROM PACIENTES WHERE CEDULA = ?");
    $stmt->execute([$cedula]);
    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);

    if (empty($paciente['ECORREO'])) {
        $mensajeEmail = "⚠️ Aún no tienes un correo registrado. <a href='actualizar_email.php'>Haz clic aquí para actualizarlo</a>.";
    }
}


// Solo permite el acceso a usuarios con rol 'paciente'
if (!isset($_SESSION['usuario']) || !isset($_SESSION['rol']) || $_SESSION['rol'] !== 'paciente') {
    header('Location: no_autorizado.php'); // Ruta corregida
    exit;
}

// Verificar si hay citas recién aprobadas
$stmt = $pdo->prepare("SELECT FECHACON FROM CITAMEDIC
                        WHERE NSS = ? AND ESTATUS = 3 AND FECHACON >= CURDATE()
                        ORDER BY FECHACON DESC LIMIT 1");
                        $stmt->execute([$cedula]);
                         $aprobada = $stmt->fetch(PDO::FETCH_ASSOC);


    // Mostrar notificación solo si no se ha mostrado antes
             $mensaje_aprobacion = '';
             if ($aprobada && (!isset($_SESSION['ultima_cita_notificada']) || $_SESSION['ultima_cita_notificada'] !== $aprobada['FECHACON'])) {
                $fecha_formateada = date('d/m/Y', strtotime($aprobada['FECHACON']));
                 $mensaje_aprobacion = "Tu cita del <strong>$fecha_formateada</strong> ha sido aprobada.";
                 $_SESSION['ultima_cita_notificada'] = $aprobada['FECHACON'];
             }


// Obtener cédula y nombre del paciente si no están en sesión
if (!isset($_SESSION['cedula']) || !isset($_SESSION['nombre_paciente'])) {
    $stmt = $pdo->prepare("SELECT CEDULA, NOMBREAPELLIDO FROM PACIENTES WHERE CEDULA = ?");
    $stmt->execute([$_SESSION['cedula']]);
    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($paciente) {
        $_SESSION['cedula'] = $paciente['CEDULA'];
        $_SESSION['nombre_paciente'] = $paciente['NOMBREAPELLIDO'];
    } else {
        $_SESSION['cedula'] = '';
        $_SESSION['nombre_paciente'] = '';
    }
}

$cedulaPaciente = $_SESSION['cedula'] ?? '';


?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Área del Paciente - Mi Consultorio</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Fuente Google Fonts - Inter -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Variables de color y fuentes para consistencia con el tema médico */
        :root {
            --primary-color: #285499; /* Azul más oscuro y profesional */
            --secondary-color: #4CAF50; /* Verde más vibrante para acciones de éxito */
            --accent-color: #FFC107; /* Amarillo para advertencias */
            --danger-color: #DC3545; /* Rojo estándar de Bootstrap */
            --light-bg: #f0f2f5; /* Fondo general más suave */
            --dark-text: #343a40; /* Texto oscuro */
            --gray-text: #6c757d; /* Texto gris */
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); /* Sombra suave para tarjetas */
            --border-radius-lg: 0.75rem; /* Bordes redondeados */
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--light-bg);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .navbar {
            background-color: #ffffff !important; /* Fondo blanco para la barra de navegación */
            border-bottom: 1px solid #e0e0e0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            display: flex;
            align-items: center;
        }
        .navbar-brand .bi {
            font-size: 1.5rem;
            margin-right: 0.5rem;
        }

        .btn-logout {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .btn-logout:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-2px);
        }

        .main-content {
            flex-grow: 1; /* Permite que el contenido principal ocupe el espacio restante */
            padding: 2rem 0;
            display: flex;
            flex-direction: column; /* Cambiado a columna para centrar contenido verticalmente */
            align-items: center; /* Centra horizontalmente */
            justify-content: center; /* Centra verticalmente */
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 3rem;
            animation: fadeIn 0.8s ease-out;
            width: 100%; /* Asegura que ocupe el ancho completo */
        }
        .welcome-section h1 {
            font-size: 2.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        .welcome-section p {
            font-size: 1.15rem;
            color: var(--gray-text);
        }

        .feature-card {
            background-color: #ffffff;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow);
            padding: 2.5rem;
            margin-bottom: 1.5rem; /* Espacio entre tarjetas en móvil */
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            text-align: center;
            height: 100%; /* Asegura que las tarjetas tengan la misma altura en la fila */
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
        }
        .feature-card .icon-wrapper {
            font-size: 4.5rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
            display: block;
            line-height: 1; /* Evita espacio extra en el icono */
        }
        .feature-card h5 {
            font-size: 1.8rem;
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 1rem;
        }
        .feature-card p {
            color: var(--gray-text);
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            flex-grow: 1; /* Permite que el párrafo ocupe espacio y empuje el botón hacia abajo */
        }
        .feature-card .btn-feature {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            padding: 0.8rem 1.8rem;
            font-size: 1.05rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            display: inline-flex; /* Para alinear icono y texto */
            align-items: center;
            gap: 0.75rem;
        }
        .btn-feature:hover {
            background-color: #204176;
            border-color: #1a3c6d;
            transform: translateY(-2px);
        }
        /* Estilo para el contenedor de dos botones */
        .btn-group-vertical-custom {
            display: flex;
            flex-direction: column;
            gap: 1rem; /* Espacio entre los botones */
            width: 100%; /* Ocupa el ancho completo de la tarjeta */
        }
        .btn-group-vertical-custom .btn {
            width: 100%; /* Asegura que los botones ocupen el ancho completo */
        }

        /* Estilo para la tabla de citas */
        .table-responsive {
            margin-top: 3rem;
            background-color: #ffffff;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--card-shadow);
            padding: 1.5rem;
        }
        .table-responsive h4 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .table-bordered {
            border-color: #e0e0e0;
        }
        .table-light th {
            background-color: #f8f9fa;
            color: var(--dark-text);
            font-weight: 600;
        }
        .table td, .table th {
            vertical-align: middle;
        }
        .badge {
            padding: 0.5em 0.8em;
            border-radius: 0.5rem;
            font-size: 0.85em;
        }
        .alert {
            border-radius: 0.5rem;
            font-size: 0.95rem;
            padding: 0.75rem 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .alert .bi, .alert .fas {
            font-size: 1.2rem;
        }


        /* Animaciones */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .welcome-section h1 {
                font-size: 2.2rem;
            }
            .welcome-section p {
                font-size: 1rem;
            }
            .feature-card {
                padding: 2rem;
            }
            .feature-card .icon-wrapper {
                font-size: 3.5rem;
            }
            .feature-card h5 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="bi bi-person-circle"></i> Área del Paciente
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                       <a class="btn btn-logout" href="logout.php">
                            <i class="bi bi-box-arrow-right"></i> Cerrar sesión
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container main-content">
        <div class="row w-100 justify-content-center">
            <div class="col-12 welcome-section">
                <h1>Bienvenido, <span style="color: var(--secondary-color);"><?php echo htmlspecialchars($_SESSION['nombre_paciente']); ?></span></h1>
                <p>Desde este panel puedes solicitar una cita médica y subir los resultados de tus estudios.</p>
            </div>

            <?php if ($mensajeEmail): ?>
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                    <?php echo $mensajeEmail; ?>
                </div>
            <?php endif; ?>

            <?php if ($mensaje_aprobacion): ?>
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-bell-fill"></i>
                    <?php echo $mensaje_aprobacion; ?>
                </div>
            <?php endif; ?>

            <div class="col-md-6 col-lg-5 d-flex">
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="bi bi-calendar-plus"></i>
                    </div>
                    <h5>Solicitar Cita</h5>
                    <p>Agenda una nueva cita médica de forma rápida y sencilla.</p>
                    <a href="backend/citas_pacientes/crear_cita_paciente.php" class="btn btn-feature">
                        <i class="bi bi-plus-circle-fill"></i> Crear Cita
                    </a>
                </div>
            </div>

            <div class="col-md-6 col-lg-5 d-flex">
                <div class="feature-card">
                    <div class="icon-wrapper">
                        <i class="bi bi-upload"></i>
                    </div>
                    <h5>Subir Resultados</h5>
                    <p>Sube los resultados de tus análisis, radiografías, tomografías, etc.</p>
                    <form action="backend/resultados/upload_resultados.php" method="post" enctype="multipart/form-data">
                        <input type="hidden" name="CEDULA" value="<?php echo htmlspecialchars($cedulaPaciente, ENT_QUOTES, 'UTF-8'); ?>">
                        <div class="mb-3">
                            <label for="archivo" class="form-label">Selecciona el archivo</label>
                            <input type="file" name="archivo" id="archivo" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label for="descripcion" class="form-label">Descripción</label>
                            <input type="text" name="descripcion" id="descripcion" class="form-control" placeholder="Ej: Radiografía de tórax">
                        </div>
                        <button type="submit" class="btn btn-feature">
                            <i class="bi bi-cloud-arrow-up-fill"></i> Subir Archivo
                        </button>
                    </form>
                </div>
            </div>

            <?php
            try {
                // Esta consulta parece redundante con la siguiente para "Tus Próximas Citas"
                // y su resultado no se usa para mostrar citas en la tabla.
                // Se mantiene por si tiene un propósito específico de notificación.
                $stmt = $pdo->prepare("
                    SELECT FECHACON FROM CITAMEDIC
                    WHERE NSS = ? AND ESTATUS = 3 AND FECHACON >= CURDATE()
                    ORDER BY FECHACON DESC LIMIT 1
                ");
                $stmt->execute([$cedula]);
                $aprobada = $stmt->fetch(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                // Error handling for the 'aprobada' query
                // echo "<p style='color:red;'>Error al cargar la cita aprobada: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>

            <?php
            try {
                $sql = "SELECT CLAVE, FECHACON, HORACON, ESTATUS
                        FROM CITAMEDIC
                        WHERE NSS = ?
                        AND ESTATUS IN (3, 5, 6)
                        ORDER BY FECHACON ASC";

                $stmt = $pdo->prepare($sql);
                $stmt->execute([$cedulaPaciente]);
                $citas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                echo "<p style='color:red;'>Error al obtener las citas: " . htmlspecialchars($e->getMessage()) . "</p>";
                $citas = [];
            }
            ?>

            <?php if (!empty($citas)): ?>
                <div class="col-12 table-responsive">
                    <h4><i class="bi bi-calendar-check-fill"></i> Tus Próximas Citas</h4>
                    <table class="table table-bordered mt-3">
                        <thead class="table-light">
                            <tr>
                                <th>Fecha</th>
                                <th>Hora</th>
                                <th>Estado</th>
                                <th>Acción</th>
                            </tr>
                        </thead>
                        <tbody>
                        <?php foreach ($citas as $cita): ?>
                            <?php
                                $estatus = (int)$cita['ESTATUS'];
                                $estadoTexto = '';
                                $badgeClass = '';
                                $iconClass = '';

                                switch ($estatus) {
                                    case 0:
                                        $estadoTexto = 'Atendido';
                                        $badgeClass = 'bg-info text-dark';
                                        $iconClass = 'bi bi-check-all';
                                        break;
                                    case 1:
                                        $estadoTexto = 'Canceló';
                                        $badgeClass = 'bg-danger';
                                        $iconClass = 'bi bi-x-circle-fill';
                                        break;
                                    case 2:
                                        $estadoTexto = 'No asistió';
                                        $badgeClass = 'bg-secondary';
                                        $iconClass = 'bi bi-person-slash';
                                        break;
                                    case 3:
                                        $estadoTexto = 'Citado';
                                        $badgeClass = 'bg-success';
                                        $iconClass = 'bi bi-check-circle-fill';
                                        break;
                                    case 4:
                                        $estadoTexto = 'Llegó tarde';
                                        $badgeClass = 'bg-warning text-dark';
                                        $iconClass = 'bi bi-hourglass-split';
                                        break;
                                    case 5:
                                        $estadoTexto = 'Esperando';
                                        $badgeClass = 'bg-primary';
                                        $iconClass = 'bi bi-clock-fill';
                                        break;
                                    case 6:
                                        $estadoTexto = 'Pendiente de aprobación';
                                        $badgeClass = 'bg-warning text-dark';
                                        $iconClass = 'bi bi-hourglass-split';
                                        break;
                                    default:
                                        $estadoTexto = 'Desconocido';
                                        $badgeClass = 'bg-light text-dark';
                                        $iconClass = 'bi bi-question-circle-fill';
                                }
                            ?>
                            <tr>
                                <td><?php echo date('Y-m-d', strtotime($cita['FECHACON'])); ?></td>
                                <td><?php echo date('h:i a', strtotime($cita['HORACON'])); ?></td>
                                <td><span class="badge <?php echo $badgeClass; ?>"><i class="<?php echo $iconClass; ?>"></i> <?php echo $estadoTexto; ?></span></td>
                                <td class="text-center">
                                    <?php if ($estatus === 6): ?>
                                        <a href="backend/citas_pacientes/cancelar_cita_paciente.php?id=<?php echo $cita['CLAVE']; ?>"
                                            onclick="return confirm('¿Estás seguro de cancelar esta cita?')"
                                            title="Cancelar cita" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash-fill"></i> Cancelar
                                        </a>
                                    <?php else: ?>
                                        <button class="btn btn-sm btn-outline-secondary" disabled title="No puedes cancelar esta cita">
                                            <i class="bi bi-lock-fill"></i> No disponible
                                        </button>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="col-12 mt-4 text-center">
                    <p class="alert alert-info"><i class="bi bi-info-circle-fill"></i> No tienes citas futuras activas.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
