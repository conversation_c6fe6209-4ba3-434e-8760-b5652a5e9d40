<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Capturar Imagen con Cámara</title>
  <style>
    #video, #canvas {
      width: 320px;
      height: 240px;
      border: 1px solid black;
      margin-bottom: 10px;
    }
  </style>
</head>
<body>
  <h2>Captura de imagen desde la cámara</h2>
  
  <!-- Sección para capturar con la cámara -->
  <div>
    <video id="video" autoplay></video><br>
    <button id="capturar">Capturar Imagen</button>
  </div>
  
  <!-- Canvas para mostrar la imagen capturada (opcional) -->
  <canvas id="canvas" style="display: none;"></canvas>
  
  <h3>O selecciona un archivo:</h3>
  <!-- Sección para subir archivo de imagen -->
  <input type="file" id="archivoImagen" accept="image/*">
  
  <!-- Formulario que enviará la imagen capturada o seleccionada -->
  <form id="formulario" action="procesar_imagen.php" method="POST">
    <!-- Campo oculto para almacenar la imagen capturada (en base64) -->
    <input type="hidden" name="imagenCapturada" id="imagenCapturada">
    <!-- Campo oculto para almacenar la imagen seleccionada vía archivo (opcional) -->
    <input type="hidden" name="imagenArchivo" id="imagenArchivo">
    <!-- Otros campos del formulario se agregarían aquí -->
    <br>
    <button type="submit">Enviar Imagen</button>
  </form>
  
  <script>
    // Elementos para la captura con la cámara
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas');
    const capturarButton = document.getElementById('capturar');
    const imagenCapturadaInput = document.getElementById('imagenCapturada');
    
    // Elemento para subir archivo
    const archivoInput = document.getElementById('archivoImagen');
    const imagenArchivoInput = document.getElementById('imagenArchivo');
    
    // Intentar acceder a la cámara mediante getUserMedia
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      navigator.mediaDevices.getUserMedia({ video: true })
        .then(stream => {
          video.srcObject = stream;
          video.play();
        })
        .catch(err => {
          console.error("Error al acceder a la cámara:", err);
        });
    } else {
      alert("Tu navegador no soporta la API getUserMedia");
    }
    
    // Capturar la imagen desde el video
    capturarButton.addEventListener('click', () => {
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const contexto = canvas.getContext('2d');
      contexto.drawImage(video, 0, 0, canvas.width, canvas.height);
      // Convertir el contenido del canvas a base64 (en formato JPEG)
      const dataURL = canvas.toDataURL('image/jpeg');
      imagenCapturadaInput.value = dataURL;
      alert("Imagen capturada desde la cámara. Si lo deseas, también puedes seleccionar un archivo.");
    });
    
    // Si se selecciona un archivo, convertirlo a base64
    archivoInput.addEventListener('change', function() {
      const archivo = this.files[0];
      if (archivo) {
        const lector = new FileReader();
        lector.onload = function(e) {
          imagenArchivoInput.value = e.target.result; // Contendrá el data URL del archivo
        };
        lector.readAsDataURL(archivo);
      }
    });
  </script>
</body>
</html>
