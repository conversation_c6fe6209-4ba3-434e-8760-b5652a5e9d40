<?php 
ob_start(); // Evita errores de encabezado
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
date_default_timezone_set('America/Santo_Domingo');

include('../config/database.php');

// ✅ Obtener los datos de la cita si se envió la clave
$cita = null;
if (isset($_GET['clave'])) {
    $claveCita = $_GET['clave'];

    $sqlCita = "SELECT c.CLAVE, c.CLAVEPAC, c.FECHACON, c.HORACON, c.TIPOC<PERSON>A, c.MODOASISTENCIA, 
                       c.ESTATUS, c.REMITIDOPOR, c.O<PERSON>ERVACION, c.NUMDOCTOR, p.CEDULA, p.NOMBREAPELLIDO, p.TELEFONO
                FROM CITAMEDIC c
                JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
                WHERE c.CLAVE = ?";
    $stmtCita = $pdo->prepare($sqlCita);
    $stmtCita->execute([$claveCita]);
    $cita = $stmtCita->fetch(PDO::FETCH_ASSOC);
    
    
}

// ✅ Procesar actualización si se envía el formulario
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        $pdo->beginTransaction();

        // 🔹 Validar y asignar valores
        $claveCita = $_POST['CLAVE'];
        $modoAsistencia = isset($_POST['MODOASISTENCIA']) ? $_POST['MODOASISTENCIA'] : '1'; 
        $estatus = isset($_POST['ESTATUS']) ? $_POST['ESTATUS'] : '5';
        $tipocita = $_POST['TIPOCITA'];
        $telefono = $_POST['TELEFONO'];
        $remitidoPor = $_POST['REMITIDOPOR'];
        $observacion = $_POST['OBSERVACION'];
        $clavePaciente = $_POST['CLAVEPAC'];
        
 
 // Obtener valores del formulario
$estatus = $_POST['ESTATUS'];  
$modoAsistencia = null; // Lo ajustaremos automáticamente

// Obtener la fecha de la cita y la fecha actual
$fechaCita = $_POST['FECHACON'];
$fechaHoy = date('Y-m-d');

// 📌 **Lógica para determinar el MODO DE ASISTENCIA automáticamente**
if ($fechaCita > $fechaHoy) {
    $estatus = 3;  // Si la cita es futura, se marca como "Citado"
}

if (in_array($estatus, [1, 2, 3,6])) {
    $modoAsistencia = 0; // Paciente NO presente
} else {
    $modoAsistencia = 1; // Paciente presente
}
 

        // 🔹 Actualizar datos permitidos de la cita
        $sqlCita = "UPDATE CITAMEDIC 
                    SET TIPOCITA = ?, REMITIDOPOR = ?, 
                        OBSERVACION = ?, MODOASISTENCIA = ?, TELEFONO = ?, ESTATUS = ?, SINCRONIZADO = 0
                    WHERE CLAVE = ?";
        $stmtCita = $pdo->prepare($sqlCita);
        $stmtCita->execute([$tipocita, $remitidoPor, $observacion, $modoAsistencia, $telefono, $estatus, $claveCita]);

        // 🔹 Actualizar teléfono del paciente si ha cambiado
        $sqlUpdatePaciente = "UPDATE PACIENTES SET TELEFONO = ? WHERE CLAVE = ?";
        $stmtUpdatePaciente = $pdo->prepare($sqlUpdatePaciente);
        $stmtUpdatePaciente->execute([$telefono, $clavePaciente]);

        $pdo->commit();

        // ✅ Redirigir a la pantalla de citas con mensaje de éxito
        header("Location: crear_cita.php?mensaje=editada");
        exit;
    } catch (PDOException $e) {
        $pdo->rollBack();
        echo "<p class='error-message'>Error al actualizar la cita: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

ob_end_flush(); // Finaliza la captura de salida
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Cita</title>
    <link rel="stylesheet" href="../../public/assets/css/stylescitas.css">
</head>
<body>
    <h1>Editar Cita Médica</h1>

    <form action="editar_cita.php" method="POST">
        <input type="hidden" name="CLAVE" value="<?php echo htmlspecialchars($cita['CLAVE']); ?>">
        <input type="hidden" name="CLAVEPAC" value="<?php echo htmlspecialchars($cita['CLAVEPAC']); ?>">

        <label for="NOMBREAPELLIDO">Nombre del Paciente:</label>
        <input type="text" id="NOMBREAPELLIDO" name="NOMBREAPELLIDO" value="<?php echo htmlspecialchars($cita['NOMBREAPELLIDO']); ?>" readonly>

        <label for="TELEFONO">Teléfono:</label>
        <input type="text" id="TELEFONO" name="TELEFONO" value="<?php echo htmlspecialchars($cita['TELEFONO']); ?>" required>

        <label for="FECHACON">Fecha de la Cita:</label>
        <input type="date" id="FECHACON" name="FECHACON" value="<?php echo htmlspecialchars($cita['FECHACON']); ?>" readonly>

        <label for="HORACON">Hora de la Cita:</label>
        <input type="text" id="HORACON" name="HORACON" value="<?php echo htmlspecialchars($cita['HORACON']); ?>" readonly>

        <label for="TIPOCITA">Tipo de Cita:</label>
        <select id="TIPOCITA" name="TIPOCITA">
            <option value="1" <?php echo ($cita['TIPOCITA'] === '1') ? 'selected' : ''; ?>>Control</option>
            <option value="2" <?php echo ($cita['TIPOCITA'] === '2') ? 'selected' : ''; ?>>Seguimiento</option>
            <option value="3" <?php echo ($cita['TIPOCITA'] === '3') ? 'selected' : ''; ?>>Consulta</option>
            <option value="4" <?php echo ($cita['TIPOCITA'] === '4') ? 'selected' : ''; ?>>Resultados</option>
        </select>

        <label for="NUMDOCTOR">Número del Doctor:</label>
        <input type="text" id="NUMDOCTOR" name="NUMDOCTOR" value="<?php echo htmlspecialchars($cita['NUMDOCTOR']); ?>" readonly>

        <label for="REMITIDOPOR">Remitido por:</label>
       <input type="text" id="REMITIDOPOR" name="REMITIDOPOR" value="<?php echo htmlspecialchars($cita['REMITIDOPOR'] ?? '', ENT_QUOTES, 'UTF-8'); ?>">

       
       <label for="ESTATUS">Estatus:</label>
<select id="ESTATUS" name="ESTATUS" >
    <option value="0" <?php echo ($cita['ESTATUS'] == 0) ? 'selected' : ''; ?>>Atendido</option>
    <option value="1" <?php echo ($cita['ESTATUS'] == 1) ? 'selected' : ''; ?>>Canceló</option>
    <option value="2" <?php echo ($cita['ESTATUS'] == 2) ? 'selected' : ''; ?>>No asistió</option>
    <option value="3" <?php echo ($cita['ESTATUS'] == 3) ? 'selected' : ''; ?>>Citado</option>
    <option value="4" <?php echo ($cita['ESTATUS'] == 4) ? 'selected' : ''; ?>>Llegó tarde</option>
    <option value="5" <?php echo ($cita['ESTATUS'] == 5) ? 'selected' : ''; ?>>Esperando</option>
    <option value="6" <?php echo ($cita['ESTATUS'] == 6) ? 'selected' : ''; ?>>Pendiente aprobació</option>
</select>


        
        <label for="OBSERVACION">Observación:</label>
            <textarea id="OBSERVACION" name="OBSERVACION" class="observacion" rows="2"  ><?php echo htmlspecialchars($cita['OBSERVACION'] ?? '', ENT_QUOTES, 'UTF-8'); ?></textarea>

        <button type="submit">Guardar Cambios</button>
    </form>
</body>
</html>
