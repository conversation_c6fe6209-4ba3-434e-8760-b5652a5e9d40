<?php
// Habilitar la visualización de errores para depuración (QUITAR EN PRODUCCIÓN)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Conexión a la base de datos MAESTRA
require_once __DIR__ . '/../config/database.php';
$masterPdo = getMasterPdo();

// Obtener parámetros desde GET o POST
$subdominio_seleccionado = $_GET['subdominio'] ?? $_POST['subdominio'] ?? '';
$id_factura_a_editar = $_GET['id_factura'] ?? $_POST['id_factura'] ?? null;
$pdo = null;
$empresa = null;
$error_tenant_connection = null;
$factura_a_editar = null;
$mensaje_status = null;

// Conectar al tenant usando el subdominio
if ($subdominio_seleccionado && $id_factura_a_editar) {
    try {
        $stmt_master = $masterPdo->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio = ? AND Estado = 'Activo'");
        $stmt_master->execute([$subdominio_seleccionado]);
        $cfg = $stmt_master->fetch(PDO::FETCH_ASSOC);

        if ($cfg && !empty($cfg['CadenaConexionDB'])) {
            preg_match('/host=([^;]+)/', $cfg['CadenaConexionDB'], $matches_host); $host = $matches_host[1] ?? '';
            preg_match('/dbname=([^;]+)/', $cfg['CadenaConexionDB'], $matches_dbname); $dbname = $matches_dbname[1] ?? '';
            preg_match('/user=([^;]+)/', $cfg['CadenaConexionDB'], $matches_user); $user = $matches_user[1] ?? '';
            preg_match('/password=([^;]+)/', $cfg['CadenaConexionDB'], $matches_pass); $password = $matches_pass[1] ?? '';

            if ($host && $dbname && $user !== '') {
                $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
                $pdo = new PDO($dsn, $user, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]);

                // Obtener datos del consultorio (EMPRESA) del tenant actual
                $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
                $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

            } else {
                $error_tenant_connection = "Datos de conexión incompletos para el subdominio '{$subdominio_seleccionado}'.";
            }
        } else {
            $error_tenant_connection = "Configuración de conexión no encontrada para el subdominio '{$subdominio_seleccionado}'.";
        }
    } catch (PDOException $e) {
        $error_tenant_connection = "Error al conectar o consultar la base de datos del consultorio: " . htmlspecialchars($e->getMessage());
    }
} else {
    $error_tenant_connection = "Faltan parámetros para editar la factura. Por favor, selecciona una factura desde el Histórico.";
}

// Lógica para procesar la actualización de la factura (solo en POST)
if ($_SERVER["REQUEST_METHOD"] === "POST" && $pdo && $id_factura_a_editar) {
    // Recoger los datos del formulario de edición
    $concepto_puro = trim($_POST['concepto'] ?? '');
    $precio = floatval($_POST['precio'] ?? 0);
    $moneda = $_POST['moneda'] ?? 'USD';
    $modopago = $_POST['modopago'] ?? 'Transferencia';
    $fechapago = $_POST['fechapago'] ?? date('Y-m-d');
    $fechavencimiento = $_POST['fechavencimiento'] ?? '';

    // Campos bancarios
    $banco_destino = $_POST['banco_destino'] ?? '';
    $cuenta_marcsoftware = trim($_POST['cuenta_marcsoftware'] ?? '');
    $beneficiario_marcsoftware = $_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions';
    
    // === LÓGICA CLAVE: RECONSTRUIR EL CAMPO 'CONCEPTO' DE LA BD ===
    $concepto_final = $concepto_puro;
    if ($modopago === 'Transferencia') {
        $concepto_final .= "\n\nDATOS PARA TRANSFERENCIA:\n"
                         . "Banco: {$banco_destino}\n"
                         . "Cuenta: {$cuenta_marcsoftware}\n"
                         . "Beneficiario: {$beneficiario_marcsoftware}";
    }
    
    try {
        $stmt_update = $pdo->prepare("UPDATE FACTURAS_SOFTWARE SET 
            CONCEPTO = ?, PRECIO = ?, MONEDA = ?, MODO_PAGO = ?, FECHA_FACTURA = ?, FECHA_VENCIMIENTO = ?,
            BANCO_DESTINO = ?, CUENTA_DESTINO = ?, BENEFICIARIO = ?
            WHERE CLAVE = ?");

        $stmt_update->execute([
            $concepto_final, $precio, $moneda, $modopago, $fechapago, $fechavencimiento,
            $banco_destino, $cuenta_marcsoftware, $beneficiario_marcsoftware,
            $id_factura_a_editar
        ]);

        // Redireccionar para evitar reenviar el formulario y para mostrar el estado
        header("Location: editar_factura_cliente.php?id_factura=" . urlencode($id_factura_a_editar) . "&subdominio=" . urlencode($subdominio_seleccionado) . "&status=updated");
        exit();
    } catch (Exception $e) {
        $mensaje_status = "❌ Error al actualizar la factura: " . htmlspecialchars($e->getMessage());
    }
}

// Lógica para obtener la factura a editar (se ejecuta siempre en GET y después del POST fallido)
if ($pdo && $id_factura_a_editar) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM FACTURAS_SOFTWARE WHERE CLAVE = ?");
        $stmt->execute([$id_factura_a_editar]);
        $factura_a_editar = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$factura_a_editar) {
            $error_tenant_connection = "No se encontró la factura con el ID proporcionado.";
        } else {
             // Limpiar el concepto para mostrar solo la parte que el usuario escribió
            $full_concepto_db = $factura_a_editar['CONCEPTO'];
            $bank_details_start = "\n\nDATOS PARA TRANSFERENCIA:\n";
            
            if (strpos($full_concepto_db, $bank_details_start) !== false) {
                $parts = explode($bank_details_start, $full_concepto_db, 2);
                $factura_a_editar['CONCEPTO_LIMPIO'] = trim($parts[0]);
            } else {
                $factura_a_editar['CONCEPTO_LIMPIO'] = $full_concepto_db;
            }
        }
    } catch (Exception $e) {
        $error_tenant_connection = "Error al obtener la factura: " . htmlspecialchars($e->getMessage());
    }
}

// Mensaje de estado al recargar la página
if (isset($_GET['status']) && $_GET['status'] === 'updated') {
    $mensaje_status = "✅ Factura actualizada exitosamente.";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Editar Factura - <?= htmlspecialchars($subdominio_seleccionado) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h2>✏️ Editar Factura para <?= htmlspecialchars($empresa['NOMBRE'] ?? 'Consultorio') ?></h2>

    <?php if ($mensaje_status): ?>
        <div class="alert alert-success mt-3"><?= $mensaje_status ?></div>
    <?php endif; ?>

    <?php if ($error_tenant_connection): ?>
        <div class="alert alert-danger"><?= $error_tenant_connection ?></div>
        <a href="crear_factura_cliente.php?subdominio=<?= urlencode($subdominio_seleccionado) ?>" class="btn btn-primary">Volver al Histórico</a>
    <?php elseif ($factura_a_editar): ?>
        <p class="text-muted">Editando Factura No. <?= htmlspecialchars($factura_a_editar['NUMERO_FACTURA']) ?></p>

        <form method="POST">
            <input type="hidden" name="id_factura" value="<?= htmlspecialchars($factura_a_editar['CLAVE']) ?>">
            <input type="hidden" name="subdominio" value="<?= urlencode($subdominio_seleccionado) ?>">

            <div class="card mb-4">
                <div class="card-header"><h5>💰 Detalles de Facturación</h5></div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Concepto/Servicio:</label>
                        <textarea name="concepto" class="form-control" rows="3" required
                        ><?= htmlspecialchars($factura_a_editar['CONCEPTO_LIMPIO'] ?? '') ?></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Precio:</label>
                            <input type="number" name="precio" step="0.01" class="form-control" value="<?= htmlspecialchars($factura_a_editar['PRECIO']) ?>" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Moneda:</label>
                            <select name="moneda" class="form-control">
                                <option value="USD" <?= ($factura_a_editar['MONEDA'] === 'USD') ? 'selected' : '' ?>>USD (Dólares)</option>
                                <option value="DOP" <?= ($factura_a_editar['MONEDA'] === 'DOP') ? 'selected' : '' ?>>RD$ (Pesos)</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Modo de Pago:</label>
                            <select name="modopago" class="form-control" id="modopago" onchange="toggleBankFields()">
                                <option value="Transferencia" <?= ($factura_a_editar['MODO_PAGO'] === 'Transferencia') ? 'selected' : '' ?>>Transferencia</option>
                                <option value="Efectivo" <?= ($factura_a_editar['MODO_PAGO'] === 'Efectivo') ? 'selected' : '' ?>>Efectivo</option>
                                <option value="Cheque" <?= ($factura_a_editar['MODO_PAGO'] === 'Cheque') ? 'selected' : '' ?>>Cheque</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div id="bank-fields" class="card mb-4" style="display:none;">
                <div class="card-header"><h5>🏦 Datos Bancarios - MarcSoftware Solutions</h5></div>
                <div class="card-body">
                    <p class="text-info">El cliente transferirá a estas cuentas:</p>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Banco Destino:</label>
                            <select name="banco_destino" class="form-control">
                                <option value="">Seleccionar Banco</option>
                                <option <?= ($factura_a_editar['BANCO_DESTINO'] === 'Banco Popular') ? 'selected' : '' ?>>Banco Popular</option>
                                <option <?= ($factura_a_editar['BANCO_DESTINO'] === 'Banreservas') ? 'selected' : '' ?>>Banreservas</option>
                                <option <?= ($factura_a_editar['BANCO_DESTINO'] === 'Banco BHD León') ? 'selected' : '' ?>>Banco BHD León</option>
                                <option <?= ($factura_a_editar['BANCO_DESTINO'] === 'Banco Scotiabank') ? 'selected' : '' ?>>Banco Scotiabank</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Cuenta MarcSoftware:</label>
                            <input type="text" name="cuenta_marcsoftware" class="form-control" placeholder="Número de cuenta" value="<?= htmlspecialchars($factura_a_editar['CUENTA_DESTINO']) ?>">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Beneficiario:</label>
                            <input type="text" name="beneficiario_marcsoftware" class="form-control" value="<?= htmlspecialchars($factura_a_editar['BENEFICIARIO']) ?>">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Fecha de Factura:</label>
                    <input type="date" name="fechapago" class="form-control" value="<?= htmlspecialchars($factura_a_editar['FECHA_FACTURA']) ?>">
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Fecha de Vencimiento:</label>
                    <input type="date" name="fechavencimiento" class="form-control" value="<?= htmlspecialchars($factura_a_editar['FECHA_VENCIMIENTO']) ?>">
                </div>
            </div>

            <button type="submit" class="btn btn-success btn-lg mb-4">
                💾 Guardar Cambios
            </button>
            <a href="crear_factura_cliente.php?subdominio=<?= urlencode($subdominio_seleccionado) ?>" class="btn btn-secondary btn-lg mb-4">
                Cancelar
            </a>
        </form>
    <?php endif; ?>

</div>

<script>
function toggleBankFields() {
    const modo = document.getElementById('modopago').value;
    const bankFields = document.getElementById('bank-fields');
    if (bankFields) {
        bankFields.style.display = modo === 'Transferencia' ? 'block' : 'none';
    }
}
document.addEventListener('DOMContentLoaded', toggleBankFields);
</script>
</body>
</html>
