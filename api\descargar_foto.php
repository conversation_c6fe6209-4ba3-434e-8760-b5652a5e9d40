<?php
// Ruta donde se almacenan las fotos en el servidor
$directorioFotos = __DIR__ . '/../backend/pacientes/fotografias/';

if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['cedula'])) {
    // Limpiar y validar el valor recibido
    $cedula = preg_replace('/[^0-9]/', '', $_GET['cedula']); // Solo permitir números para evitar ataques de path traversal
    $archivo = $directorioFotos . $cedula . '.bmp';

    if (file_exists($archivo) && is_readable($archivo)) {
        // Definir el tipo de contenido correcto (image/bmp en lugar de application/octet-stream)
        header('Content-Type: image/bmp');
        header('Content-Disposition: inline; filename="' . basename($archivo) . '"'); // inline permite abrir en navegador
        header('Content-Length: ' . filesize($archivo));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Leer y enviar el archivo al navegador
        readfile($archivo);
        exit;
    } else {
        http_response_code(404);
        echo json_encode([
            "status" => "error",
            "message" => "Archivo de foto no encontrado o no accesible"
        ]);
        exit;
    }
} else {
    http_response_code(400);
    echo json_encode([
        "status" => "error",
        "message" => "Solicitud inválida o parámetro 'cedula' faltante"
    ]);
    exit;
}