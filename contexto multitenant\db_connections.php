<?php
// backend/config/db_connections.php

// --- Configuración de la Base de Datos Maestra ---
// ¡IMPORTANTE! Reemplaza estos valores con tus credenciales reales para la Base de Datos Maestra
// Estas credenciales deben tener permisos para:
// 1. <PERSON>r la tabla 'Consultorios' (para obtener cadenas de conexión).
// 2. Leer la tabla 'Usuarios' (para autenticar usuarios).
define('DB_MASTER_HOST', 'bh8942.banahosting.com'); // Ej: 'localhost', '127.0.0.1', o el host de tu servicio de DB en la nube
define('DB_MASTER_NAME', 'gthavrvf_consultorio_maestra'); // El nombre de la DB Maestra que creaste (ej. 'mi_app_consultorio_maestra')
define('DB_MASTER_USER', 'gthavrvf_consultorio_maestra'); // Usuario con permisos para leer la DB Maestra
define('DB_MASTER_PASS', 'tol2zt,Z<PERSON>'); // Contraseña del usuario de la DB Maestra

// Función para conectar a la Base de Datos Maestra
function connectMasterDB() {
    try {
        $pdoMaster = new PDO(
            "mysql:host=" . DB_MASTER_HOST . ";dbname=" . DB_MASTER_NAME,
            DB_MASTER_USER,
            DB_MASTER_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, // Lanzar excepciones en caso de error
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC, // Por defecto, obtener resultados como arrays asociativos
                PDO::ATTR_EMULATE_PREPARES => false, // Deshabilitar emulación de preparaciones para seguridad y rendimiento
            ]
        );
        return $pdoMaster;
    } catch (PDOException $e) {
        // Es crucial manejar este error adecuadamente en producción.
        // Registra el error en un log y muestra un mensaje genérico al usuario.
        error_log("Error al conectar a la Base de Datos Maestra: " . $e->getMessage());
        die("Lo sentimos, estamos experimentando problemas técnicos. Por favor, inténtelo más tarde. (Error DB Maestra)");
    }
}

// Función para conectar a la Base de Datos del Consultorio Específico
// Esta función asume que $_SESSION['cadena_conexion_consultorio'] ya está establecida.
// y contiene una cadena de conexión PDO DSN completa (ej. "mysql:host=***;dbname=***;user=***;password=***")
function connectConsultorioDB() {
    // Verificar si la cadena de conexión del consultorio está disponible en la sesión
    if (!isset($_SESSION['cadena_conexion_consultorio'])) {
        error_log("Error: No se encontró la cadena de conexión del consultorio en la sesión para connectConsultorioDB.");
        // Si no está, es un error de sesión o acceso no autorizado, redirigir al login.
        header('Location: login.php?error=sesion_expirada_o_invalida');
        exit();
    }

    $cadenaConexion = $_SESSION['cadena_conexion_consultorio'];

    try {
        // Crear una nueva conexión PDO usando la cadena de conexión almacenada en la sesión.
        // Los parámetros de usuario y contraseña se pueden omitir aquí si ya están incluidos en el DSN.
        $pdoConsultorio = new PDO($cadenaConexion, null, null, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        return $pdoConsultorio;
    } catch (PDOException $e) {
        // Registrar el error detallado en el log del servidor
        error_log("Error al conectar a la Base de Datos del Consultorio: " . $e->getMessage());
        // Mostrar un mensaje genérico al usuario
        die("Lo sentimos, no pudimos conectar a la base de datos de su consultorio. Por favor, contacte a soporte.");
    }
}
?>
