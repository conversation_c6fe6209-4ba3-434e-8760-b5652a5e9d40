<?php
/**
 * Panel de administración para gestionar sugerencias de reportes
 * Solo accesible para administradores
 */

session_start();
require_once __DIR__ . '/../config/database.php';

// Debug: Mostrar información de sesión (temporal)
$debug_mode = true; // Cambiar a false en producción

if ($debug_mode) {
    echo "<!-- DEBUG INFO: ";
    echo "Usuario: " . ($_SESSION['usuario'] ?? 'No definido') . " | ";
    echo "Rol: " . ($_SESSION['rol'] ?? 'No definido') . " | ";
    echo "Sesión activa: " . (isset($_SESSION['usuario']) ? 'Sí' : 'No');
    echo " -->";
}

// Verificación de acceso más flexible (temporal para testing)
if (!isset($_SESSION['usuario'])) {
    // Si no hay sesión, redirigir al login
    header('Location: ../../login.php');
    exit;
}

// Nota: Comentamos temporalmente la verificación de rol admin para testing
// if (($_SESSION['rol'] ?? '') !== 'admin') {
//     header('Location: ../../login.php');
//     exit;
// }

// Procesar acciones
if ($_POST['accion'] ?? '' === 'actualizar_estado') {
    $clave = $_POST['clave'] ?? 0;
    $estado = $_POST['estado'] ?? 'PENDIENTE';
    $comentarios = $_POST['comentarios'] ?? '';
    $prioridad = $_POST['prioridad'] ?? 'MEDIA';
    
    $stmt = $pdo->prepare("
        UPDATE SUGERENCIAS_REPORTES 
        SET ESTADO = ?, COMENTARIOS_ADMIN = ?, PRIORIDAD = ?
        WHERE CLAVE = ?
    ");
    $stmt->execute([$estado, $comentarios, $prioridad, $clave]);
    
    $mensaje = "Sugerencia actualizada correctamente.";
}

// Obtener filtros
$filtro_estado = $_GET['estado'] ?? '';
$filtro_prioridad = $_GET['prioridad'] ?? '';
$busqueda = $_GET['busqueda'] ?? '';

// Construir consulta
$where_conditions = [];
$params = [];

if ($filtro_estado) {
    $where_conditions[] = "ESTADO = ?";
    $params[] = $filtro_estado;
}

if ($filtro_prioridad) {
    $where_conditions[] = "PRIORIDAD = ?";
    $params[] = $filtro_prioridad;
}

if ($busqueda) {
    $where_conditions[] = "(SUGERENCIA LIKE ? OR USUARIO LIKE ?)";
    $params[] = "%$busqueda%";
    $params[] = "%$busqueda%";
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Crear tabla si no existe
try {
    $createTableSQL = "
        CREATE TABLE IF NOT EXISTS SUGERENCIAS_REPORTES (
            CLAVE INT AUTO_INCREMENT PRIMARY KEY,
            SUGERENCIA TEXT NOT NULL,
            USUARIO VARCHAR(100) DEFAULT 'Anónimo',
            IP_ADDRESS VARCHAR(45),
            USER_AGENT TEXT,
            FECHA_CREACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ESTADO ENUM('PENDIENTE', 'REVISADA', 'IMPLEMENTADA', 'RECHAZADA') DEFAULT 'PENDIENTE',
            PRIORIDAD ENUM('BAJA', 'MEDIA', 'ALTA') DEFAULT 'MEDIA',
            COMENTARIOS_ADMIN TEXT,
            FECHA_ACTUALIZACION TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $pdo->exec($createTableSQL);

    // Verificar si la tabla existe antes de consultar
    $table_exists = false;
    try {
        $pdo->query("SELECT 1 FROM SUGERENCIAS_REPORTES LIMIT 1");
        $table_exists = true;
    } catch (PDOException $e) {
        // La tabla no existe, se creará arriba
    }

    if ($table_exists) {
        // Obtener sugerencias
        $stmt = $pdo->prepare("
            SELECT * FROM SUGERENCIAS_REPORTES
            $where_clause
            ORDER BY FECHA_CREACION DESC
        ");
        $stmt->execute($params);
        $sugerencias = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Estadísticas
        $stats = $pdo->query("
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN ESTADO = 'PENDIENTE' THEN 1 ELSE 0 END) as pendientes,
                SUM(CASE WHEN ESTADO = 'REVISADA' THEN 1 ELSE 0 END) as revisadas,
                SUM(CASE WHEN ESTADO = 'IMPLEMENTADA' THEN 1 ELSE 0 END) as implementadas,
                SUM(CASE WHEN ESTADO = 'RECHAZADA' THEN 1 ELSE 0 END) as rechazadas
            FROM SUGERENCIAS_REPORTES
        ")->fetch(PDO::FETCH_ASSOC);
    } else {
        // Si la tabla no existe, valores por defecto
        $sugerencias = [];
        $stats = ['total' => 0, 'pendientes' => 0, 'revisadas' => 0, 'implementadas' => 0, 'rechazadas' => 0];
    }

} catch (PDOException $e) {
    // Si hay error con la base de datos, mostrar mensaje amigable
    $error_db = "Error de base de datos: " . $e->getMessage();
    $sugerencias = [];
    $stats = ['total' => 0, 'pendientes' => 0, 'revisadas' => 0, 'implementadas' => 0, 'rechazadas' => 0];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administrar Sugerencias de Reportes</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .sugerencia-card {
            transition: all 0.3s ease;
            border-left: 4px solid #dee2e6;
        }
        .sugerencia-card.pendiente { border-left-color: #ffc107; }
        .sugerencia-card.revisada { border-left-color: #0dcaf0; }
        .sugerencia-card.implementada { border-left-color: #198754; }
        .sugerencia-card.rechazada { border-left-color: #dc3545; }
        
        .badge-prioridad.alta { background-color: #dc3545; }
        .badge-prioridad.media { background-color: #ffc107; }
        .badge-prioridad.baja { background-color: #6c757d; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">
                    <i class="fas fa-lightbulb text-warning me-2"></i>
                    Administrar Sugerencias de Reportes
                </h1>
                <p class="text-muted">Gestiona las ideas y sugerencias de los usuarios</p>
            </div>
            <div class="col-auto">
                <a href="personalizados_reportes.php" class="btn btn-outline-primary">
                    <i class="fas fa-arrow-left me-2"></i>Volver a Reportes
                </a>
            </div>
        </div>

        <!-- Estadísticas -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 class="mb-1"><?= $stats['total'] ?></h3>
                        <p class="mb-0">Total Sugerencias</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-warning">
                    <div class="card-body text-center">
                        <h3 class="mb-1 text-warning"><?= $stats['pendientes'] ?></h3>
                        <p class="mb-0">Pendientes</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-success">
                    <div class="card-body text-center">
                        <h3 class="mb-1 text-success"><?= $stats['implementadas'] ?></h3>
                        <p class="mb-0">Implementadas</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card border-info">
                    <div class="card-body text-center">
                        <h3 class="mb-1 text-info"><?= $stats['revisadas'] ?></h3>
                        <p class="mb-0">En Revisión</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Estado</label>
                        <select name="estado" class="form-select">
                            <option value="">Todos los estados</option>
                            <option value="PENDIENTE" <?= $filtro_estado === 'PENDIENTE' ? 'selected' : '' ?>>Pendiente</option>
                            <option value="REVISADA" <?= $filtro_estado === 'REVISADA' ? 'selected' : '' ?>>Revisada</option>
                            <option value="IMPLEMENTADA" <?= $filtro_estado === 'IMPLEMENTADA' ? 'selected' : '' ?>>Implementada</option>
                            <option value="RECHAZADA" <?= $filtro_estado === 'RECHAZADA' ? 'selected' : '' ?>>Rechazada</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Prioridad</label>
                        <select name="prioridad" class="form-select">
                            <option value="">Todas las prioridades</option>
                            <option value="ALTA" <?= $filtro_prioridad === 'ALTA' ? 'selected' : '' ?>>Alta</option>
                            <option value="MEDIA" <?= $filtro_prioridad === 'MEDIA' ? 'selected' : '' ?>>Media</option>
                            <option value="BAJA" <?= $filtro_prioridad === 'BAJA' ? 'selected' : '' ?>>Baja</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Buscar</label>
                        <input type="text" name="busqueda" class="form-control" 
                               placeholder="Buscar en sugerencias o usuario..." 
                               value="<?= htmlspecialchars($busqueda) ?>">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>Filtrar
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Mensaje de éxito -->
        <?php if (isset($mensaje)): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <?= $mensaje ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Mensaje de error de base de datos -->
        <?php if (isset($error_db)): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <strong>Error:</strong> <?= $error_db ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Lista de Sugerencias -->
        <div class="row">
            <?php if (empty($sugerencias)): ?>
                <div class="col-12">
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5>No hay sugerencias</h5>
                            <p class="text-muted">No se encontraron sugerencias con los filtros aplicados.</p>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <?php foreach ($sugerencias as $sugerencia): ?>
                    <div class="col-12 mb-3">
                        <div class="card sugerencia-card <?= strtolower($sugerencia['ESTADO']) ?>">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>ID: <?= $sugerencia['CLAVE'] ?></strong>
                                    <span class="badge bg-secondary ms-2"><?= $sugerencia['USUARIO'] ?></span>
                                    <span class="badge badge-prioridad <?= strtolower($sugerencia['PRIORIDAD']) ?> ms-1">
                                        <?= $sugerencia['PRIORIDAD'] ?>
                                    </span>
                                </div>
                                <div>
                                    <small class="text-muted">
                                        <?= date('d/m/Y H:i', strtotime($sugerencia['FECHA_CREACION'])) ?>
                                    </small>
                                    <span class="badge bg-<?= 
                                        $sugerencia['ESTADO'] === 'PENDIENTE' ? 'warning' : 
                                        ($sugerencia['ESTADO'] === 'IMPLEMENTADA' ? 'success' : 
                                        ($sugerencia['ESTADO'] === 'REVISADA' ? 'info' : 'danger')) 
                                    ?> ms-2">
                                        <?= $sugerencia['ESTADO'] ?>
                                    </span>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="mb-3"><?= nl2br(htmlspecialchars($sugerencia['SUGERENCIA'])) ?></p>
                                
                                <?php if ($sugerencia['COMENTARIOS_ADMIN']): ?>
                                    <div class="alert alert-info">
                                        <strong>Comentarios del administrador:</strong><br>
                                        <?= nl2br(htmlspecialchars($sugerencia['COMENTARIOS_ADMIN'] ?? '')) ?>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Formulario de actualización -->
                                <form method="POST" class="row g-2">
                                    <input type="hidden" name="accion" value="actualizar_estado">
                                    <input type="hidden" name="clave" value="<?= $sugerencia['CLAVE'] ?>">
                                    
                                    <div class="col-md-3">
                                        <select name="estado" class="form-select form-select-sm">
                                            <option value="PENDIENTE" <?= $sugerencia['ESTADO'] === 'PENDIENTE' ? 'selected' : '' ?>>Pendiente</option>
                                            <option value="REVISADA" <?= $sugerencia['ESTADO'] === 'REVISADA' ? 'selected' : '' ?>>Revisada</option>
                                            <option value="IMPLEMENTADA" <?= $sugerencia['ESTADO'] === 'IMPLEMENTADA' ? 'selected' : '' ?>>Implementada</option>
                                            <option value="RECHAZADA" <?= $sugerencia['ESTADO'] === 'RECHAZADA' ? 'selected' : '' ?>>Rechazada</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select name="prioridad" class="form-select form-select-sm">
                                            <option value="BAJA" <?= $sugerencia['PRIORIDAD'] === 'BAJA' ? 'selected' : '' ?>>Baja</option>
                                            <option value="MEDIA" <?= $sugerencia['PRIORIDAD'] === 'MEDIA' ? 'selected' : '' ?>>Media</option>
                                            <option value="ALTA" <?= $sugerencia['PRIORIDAD'] === 'ALTA' ? 'selected' : '' ?>>Alta</option>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <input type="text" name="comentarios" class="form-control form-control-sm" 
                                               placeholder="Comentarios del administrador..." 
                                               value="<?= htmlspecialchars($sugerencia['COMENTARIOS_ADMIN'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary btn-sm w-100">
                                            <i class="fas fa-save me-1"></i>Actualizar
                                        </button>
                                    </div>
                                </form>
                                
                                <small class="text-muted mt-2 d-block">
                                    IP: <?= $sugerencia['IP_ADDRESS'] ?> | 
                                    Actualizado: <?= date('d/m/Y H:i', strtotime($sugerencia['FECHA_ACTUALIZACION'])) ?>
                                </small>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
