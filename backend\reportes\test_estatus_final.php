<?php
// backend/reportes/test_estatus_final.php
// Script final para verificar que todo funciona con ESTATUS

session_start();
require_once __DIR__ . '/../config/database.php';

// Simular sesión para prueba
$_SESSION['usuario'] = 'admin';
$_SESSION['rol'] = 'admin';

echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Prueba Final - Campo ESTATUS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
        .highlight { background-color: #ffffcc; font-weight: bold; }
    </style>
</head>
<body>";

echo "<h1>🔍 Prueba Final - Campo ESTATUS para Reportes</h1>";

try {
    // 1. Verificar contexto de crear_cita.php
    echo "<div class='section'>";
    echo "<h2>1. Contexto Confirmado de crear_cita.php</h2>";
    
    echo "<h3>Estados de ESTATUS (Campo Principal para Reportes):</h3>";
    echo "<table>";
    echo "<tr><th>Código</th><th>Estado</th><th>Descripción</th></tr>";
    
    $estados_estatus = [
        0 => ['nombre' => 'Atendido', 'desc' => 'Paciente fue atendido'],
        1 => ['nombre' => 'Canceló', 'desc' => 'Paciente canceló la cita'],
        2 => ['nombre' => 'No asistió', 'desc' => 'Paciente no se presentó'],
        3 => ['nombre' => 'Citado', 'desc' => 'Cita programada/confirmada'],
        4 => ['nombre' => 'Llegó tarde', 'desc' => 'Paciente llegó tarde'],
        5 => ['nombre' => 'Esperando', 'desc' => 'Paciente en sala de espera'],
        6 => ['nombre' => 'Pendiente aprobación', 'desc' => 'Cita pendiente de aprobación']
    ];
    
    foreach ($estados_estatus as $codigo => $info) {
        echo "<tr>";
        echo "<td class='highlight'>$codigo</td>";
        echo "<td class='highlight'>{$info['nombre']}</td>";
        echo "<td>{$info['desc']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Estados de MODOASISTENCIA (Campo Auxiliar):</h3>";
    echo "<table>";
    echo "<tr><th>Código</th><th>Estado</th><th>Descripción</th></tr>";
    echo "<tr><td>0</td><td>Ausente</td><td>Paciente no presente físicamente</td></tr>";
    echo "<tr><td>1</td><td>Presente</td><td>Paciente presente físicamente</td></tr>";
    echo "</table>";
    
    echo "<p class='success'>✅ <strong>ESTATUS</strong> es el campo correcto para filtrar y reportar estados de citas</p>";
    echo "<p class='warning'>⚠️ <strong>MODOASISTENCIA</strong> es un campo auxiliar que indica presencia física</p>";
    
    echo "</div>";
    
    // 2. Verificar datos reales en ESTATUS
    echo "<div class='section'>";
    echo "<h2>2. Datos Reales en el Campo ESTATUS</h2>";
    
    $stmt = $pdo->query("
        SELECT ESTATUS, 
               CASE ESTATUS 
                   WHEN 0 THEN 'Atendido'
                   WHEN 1 THEN 'Canceló'
                   WHEN 2 THEN 'No asistió'
                   WHEN 3 THEN 'Citado'
                   WHEN 4 THEN 'Llegó tarde'
                   WHEN 5 THEN 'Esperando'
                   WHEN 6 THEN 'Pendiente aprobación'
                   ELSE 'Desconocido'
               END as estado_texto,
               COUNT(*) as cantidad
        FROM CITAMEDIC 
        GROUP BY ESTATUS 
        ORDER BY ESTATUS
    ");
    
    $datos_estatus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($datos_estatus)) {
        echo "<p class='warning'>⚠️ No hay datos en la tabla CITAMEDIC</p>";
    } else {
        echo "<table>";
        echo "<tr><th>Código ESTATUS</th><th>Estado</th><th>Cantidad</th><th>Porcentaje</th></tr>";
        
        $total_registros = array_sum(array_column($datos_estatus, 'cantidad'));
        
        foreach ($datos_estatus as $dato) {
            $porcentaje = round(($dato['cantidad'] / $total_registros) * 100, 1);
            echo "<tr>";
            echo "<td class='highlight'>{$dato['ESTATUS']}</td>";
            echo "<td>{$dato['estado_texto']}</td>";
            echo "<td>{$dato['cantidad']}</td>";
            echo "<td>{$porcentaje}%</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Total de registros:</strong> $total_registros</p>";
    }
    echo "</div>";
    
    // 3. Prueba de consultas de reportes
    echo "<div class='section'>";
    echo "<h2>3. Prueba de Consultas de Reportes</h2>";
    
    $fecha_inicio = date('Y-m-01');
    $fecha_fin = date('Y-m-t');
    
    echo "<h4>Período de prueba: $fecha_inicio a $fecha_fin</h4>";
    
    // Consulta base
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as total 
        FROM CITAMEDIC 
        WHERE FECHACON BETWEEN ? AND ?
    ");
    $stmt->execute([$fecha_inicio, $fecha_fin]);
    $total_mes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p><strong>Total de citas en el mes actual:</strong> $total_mes</p>";
    
    if ($total_mes > 0) {
        // Prueba de filtros individuales
        echo "<h4>Prueba de Filtros por ESTATUS:</h4>";
        echo "<table>";
        echo "<tr><th>Estado</th><th>Consulta</th><th>Cantidad</th><th>Estado</th></tr>";
        
        foreach ($estados_estatus as $codigo => $info) {
            try {
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as total 
                    FROM CITAMEDIC 
                    WHERE FECHACON BETWEEN ? AND ? AND ESTATUS = ?
                ");
                $stmt->execute([$fecha_inicio, $fecha_fin, $codigo]);
                $cantidad = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
                
                echo "<tr>";
                echo "<td>{$info['nombre']} ($codigo)</td>";
                echo "<td><code>ESTATUS = $codigo</code></td>";
                echo "<td>$cantidad</td>";
                echo "<td class='success'>✅ OK</td>";
                echo "</tr>";
                
            } catch (Exception $e) {
                echo "<tr>";
                echo "<td>{$info['nombre']} ($codigo)</td>";
                echo "<td><code>ESTATUS = $codigo</code></td>";
                echo "<td>-</td>";
                echo "<td class='error'>❌ Error</td>";
                echo "</tr>";
            }
        }
        echo "</table>";
        
        // Prueba de consulta compleja (simulando reporte)
        echo "<h4>Prueba de Consulta Compleja (Simulando Reporte):</h4>";
        
        $where_conditions = ["FECHACON BETWEEN ? AND ?"];
        $params = [$fecha_inicio, $fecha_fin];
        
        // Simular filtro de doctor
        $doctor = '9999999999999';
        if ($doctor) {
            $where_conditions[] = "NUMDOCTOR = ?";
            $params[] = $doctor;
        }
        
        // Simular filtro de estado
        $estatus_filtro = '0'; // Atendido
        if ($estatus_filtro !== '') {
            $where_conditions[] = "ESTATUS = ?";
            $params[] = $estatus_filtro;
        }
        
        $where_clause = "WHERE " . implode(" AND ", $where_conditions);
        
        echo "<pre>";
        echo "<strong>Consulta SQL:</strong>\n";
        echo "SELECT COUNT(*) FROM CITAMEDIC $where_clause\n\n";
        echo "<strong>Parámetros:</strong>\n";
        echo "- Fecha inicio: $fecha_inicio\n";
        echo "- Fecha fin: $fecha_fin\n";
        echo "- Doctor: $doctor\n";
        echo "- Estatus: $estatus_filtro (Atendido)\n\n";
        echo "<strong>Verificación:</strong>\n";
        echo "- Número de parámetros: " . count($params) . "\n";
        echo "- Número de placeholders (?): " . substr_count($where_clause, '?') . "\n";
        echo "</pre>";
        
        try {
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_clause");
            $stmt->execute($params);
            $resultado = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<p class='success'>✅ <strong>Consulta ejecutada correctamente!</strong></p>";
            echo "<p><strong>Resultado:</strong> $resultado citas encontradas</p>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ <strong>Error en la consulta:</strong> " . $e->getMessage() . "</p>";
        }
    }
    
    echo "</div>";
    
    // 4. Verificar que las APIs usen ESTATUS
    echo "<div class='section'>";
    echo "<h2>4. Verificación de APIs Actualizadas</h2>";
    
    $apis_verificar = [
        'exportar_citas.php' => 'API de exportación de citas',
        'reporte_rapido.php' => 'API de reporte rápido',
        'estadisticas_tiempo_real.php' => 'API de estadísticas en tiempo real'
    ];
    
    foreach ($apis_verificar as $archivo => $descripcion) {
        $ruta = __DIR__ . "/api/$archivo";
        if (file_exists($ruta)) {
            $contenido = file_get_contents($ruta);
            
            $usa_estatus = strpos($contenido, 'ESTATUS') !== false;
            $usa_modoasistencia = strpos($contenido, 'MODOASISTENCIA') !== false;
            
            echo "<p>";
            echo "<strong>$descripcion:</strong> ";
            
            if ($usa_estatus && !$usa_modoasistencia) {
                echo "<span class='success'>✅ Usa ESTATUS correctamente</span>";
            } elseif ($usa_estatus && $usa_modoasistencia) {
                echo "<span class='warning'>⚠️ Usa ambos campos (verificar)</span>";
            } elseif (!$usa_estatus && $usa_modoasistencia) {
                echo "<span class='error'>❌ Usa MODOASISTENCIA (incorrecto)</span>";
            } else {
                echo "<span class='error'>❌ No usa ningún campo</span>";
            }
            echo "</p>";
        } else {
            echo "<p><strong>$descripcion:</strong> <span class='error'>❌ Archivo no encontrado</span></p>";
        }
    }
    
    echo "</div>";
    
    // 5. Resumen final
    echo "<div class='section'>";
    echo "<h2>5. ✅ Resumen Final</h2>";
    
    echo "<h3>Configuración Correcta:</h3>";
    echo "<ul>";
    echo "<li class='success'>✅ <strong>ESTATUS</strong> es el campo principal para reportes de estados de citas</li>";
    echo "<li class='success'>✅ Estados 0-6 correctamente definidos según crear_cita.php</li>";
    echo "<li class='success'>✅ Consultas SQL actualizadas para usar ESTATUS</li>";
    echo "<li class='success'>✅ APIs de exportación actualizadas</li>";
    echo "<li class='success'>✅ Validación de parámetros SQL corregida</li>";
    echo "</ul>";
    
    echo "<h3>Campos y su Propósito:</h3>";
    echo "<ul>";
    echo "<li><strong>ESTATUS:</strong> Estado de la cita (0=Atendido, 1=Canceló, etc.) - <em>Para reportes</em></li>";
    echo "<li><strong>MODOASISTENCIA:</strong> Presencia física (0=Ausente, 1=Presente) - <em>Campo auxiliar</em></li>";
    echo "</ul>";
    
    echo "<p class='success'><strong>🎉 El sistema de reportes debería funcionar correctamente ahora!</strong></p>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ Error durante la prueba: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>🔗 Enlaces de Prueba</h2>";
echo "<p><a href='citas_reportes.php' target='_blank'>🔗 Probar Reportes de Citas</a></p>";
echo "<p><a href='api/exportar_citas.php?formato=excel&fecha_inicio=" . date('Y-m-01') . "&fecha_fin=" . date('Y-m-t') . "' target='_blank'>🔗 Probar Exportación Excel</a></p>";
echo "<p><a href='diagnostico.php' target='_blank'>🔗 Ejecutar Diagnóstico Completo</a></p>";
echo "</div>";

echo "</body></html>";
?>
