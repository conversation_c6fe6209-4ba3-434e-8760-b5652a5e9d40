<?php
$pdo = require_once __DIR__ . '/../config/database.php';
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA = $_GET['CEDULA'] ?? '';

$stmt = $pdo->prepare("SELECT * FROM EMBARAZO WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHAINIEMB DESC");
$stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
$embarazos = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Embarazos anteriores</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="p-3">
  <div class="container">
    <h4>📋 Historial de embarazos</h4>
    <?php foreach ($embarazos as $emb): ?>
      <div class="card mb-3">
        <div class="card-header d-flex justify-content-between align-items-center">
          <strong>#<?= $emb['NUMEMB'] ?> - Inicio: <?= $emb['FECHAINIEMB'] ?> <?= $emb['ESTATUSEMB'] === 'A' ? '🟢 Activo' : '🔴 Finalizado' ?></strong>
          <a href="controles_embarazo.php?CLAVEPAC=<?= $CLAVEPAC ?>&CLAVEEMB=<?= $emb['CLAVE'] ?>" class="btn btn-outline-primary btn-sm">
            Ver controles
          </a>
        </div>
        <div class="card-body">
          <p><strong>Patología:</strong> <?= htmlspecialchars($emb['PATOLOGIA'] ?: 'Sin especificar') ?></p>
          <?php if ($emb['FECHAFINEMB']): ?>
            <p><strong>Fin:</strong> <?= $emb['FECHAFINEMB'] ?></p>
          <?php endif; ?>
        </div>
      </div>
    <?php endforeach; ?>

    <a href="obstetricia.php?CLAVEPAC=<?= $CLAVEPAC ?>&CEDULA=<?= $CEDULA ?>" class="btn btn-secondary">⬅ Volver</a>
  </div>
</body>
</html>