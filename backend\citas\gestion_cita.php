<?php
require_once '../config/database.php'; // Ajusta la ruta a tu archivo de conexión
// Establecer la zona horaria
date_default_timezone_set('America/Santo_Domingo');
// --------------------------------------
// 1) Determinar el filtro (Hoy/Pasadas/Futuras)
// --------------------------------------
$filter = isset($_GET['filtro']) ? $_GET['filtro'] : 'hoy';  // Valor por defecto: hoy

// Obtener la fecha actual
$fechaHoy = date('Y-m-d');

// Dependiendo del filtro, se arma la condición WHERE
switch ($filter) {
    case 'pasadas':
        // Citas cuya fecha sea menor a hoy
        $condicion = "c.FECHACON < '$fechaHoy'";
        break;
    case 'futuras':
        // Citas cuya fecha sea mayor a hoy
        $condicion = "c.FECHACON > '$fechaHoy'";
        break;
    case 'hoy':
    default:
        // Citas cuya fecha sea exactamente hoy
        $condicion = "c.FECHACON = '$fechaHoy'";
        break;
}

// --------------------------------------
// 2) Consulta para obtener las citas según el filtro
// --------------------------------------
$sqlCitas = "
    SELECT 
        c.CLAVE AS ID,
        p.NOMBREAPELLIDO AS PACIENTE,
        c.FECHACON AS FECHA,
        c.HORACON AS HORA,

         CASE c.ESTATUS 
                            WHEN 0 THEN 'Atendido'
                            WHEN 1 THEN 'Canceló'
                            WHEN 2 THEN 'No asistió'                          
                            WHEN 3 THEN 'Citado'
                            WHEN 4 THEN 'Llegó tarde' 
                            WHEN 5 THEN 'Esperando'
                            WHEN 6 THEN 'Pendiente aprobación'
                           
                           END AS ESTATUS
    FROM CITAMEDIC c
    JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
    WHERE $condicion
    ORDER BY c.FECHACON, c.HORACON
";

// Ejecutar la consulta
$stmt = $pdo->prepare($sqlCitas);
$stmt->execute();
$citas = $stmt->fetchAll(PDO::FETCH_ASSOC);

/**
 * Devuelve la clase CSS correspondiente según el estatus de la cita.
 */
function getStatusClass($estatus) {
    // Convertir el estatus a minúsculas para comparación
    $estatusLower = strtolower(trim($estatus));
    
    // Puedes ajustar los nombres según cómo se guarden los datos en la base
    switch ($estatusLower) {
        case 'atendido':
            return 'status-atendido';
        case 'esperando':
            return 'status-esperando';
        case 'citado':
            return 'status-citado';
        case 'canceló':
        case 'cancelado':
            return 'status-cancelo';
        case 'no asistió':
        case 'no asistio':
            return 'status-no-asistio';
         case 'pendiente aprobació':
            return 'status-pendiente-aprobacion';
            
        default:
            return ''; // Sin clase adicional si no coincide
    }
}

?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Gestión de Citas</title>
    <!-- Puedes usar Bootstrap u otro framework, aquí un ejemplo mínimo -->
     <link rel="stylesheet" href="../../public/assets/css/stylescitas.css">
    <link rel="stylesheet" href="../../public/assets/css/stylescitas2.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>

<div class="container mt-4">

    <h1 class="text-center">Gestión de Citas</h1>

    <!-- Botones de filtro -->
    <div class="mb-3">
        
    
        <a href="gestion_cita.php?filtro=hoy" class="fas fa-calendar-day 
            <?php echo ($filter === 'hoy') ? 'active' : '' ?>">
            Citas de Hoy
        </a>
        <a href="gestion_cita.php?filtro=pasadas" class="btn btn-info 
            <?php echo ($filter === 'pasadas') ? 'active' : '' ?>">
            Citas Pasadas
        </a>
        <a href="gestion_cita.php?filtro=futuras" class="btn btn-primary 
            <?php echo ($filter === 'futuras') ? 'active' : '' ?>">
            Citas Futuras
        </a>
        
        <!-- Acción para Insertar Pacientes -->
<td>
  <a href="crear_cita.php" class="btn btn-warning btn">
    <i class="fas fa-calendar-plus"></i> Crear Cita
</a>
</td>
    
        
    </div>

    <!-- Tabla de Citas -->
    <h3>
        <?php 
          if ($filter === 'hoy') {
              echo "Citas de Hoy";
          } elseif ($filter === 'pasadas') {
              echo "Citas Pasadas";
          } else {
              echo "Citas Futuras";
          }
        ?>
    </h3>

    <?php if (!empty($citas)): ?>
        <table class="table table-bordered table-hover ">
            <thead class="thead-light">
                <tr>
                    <th>ID</th>
                    <th>Paciente</th>
                    <th>Fecha</th>
                    <th>Hora</th>
                     <th>Estatus</th>
                    <th>Acciones  <a href="../pacientes/gestion_pacientes.php" class="btn btn-warning btn-sm ms-2">
                    <i class="fas fa-user-plus"></i> Pacientes
                </a></th>
                
                </tr>
            </thead>
            <tbody>
                <?php foreach ($citas as $cita): ?>
                <tr>
                   
                <tr class="<?php echo getStatusClass($cita['ESTATUS']); ?>">
                <td><?php echo htmlspecialchars($cita['ID']); ?></td>
                <td><?php echo htmlspecialchars($cita['PACIENTE']); ?></td>
                <td><?php echo htmlspecialchars($cita['FECHA']); ?></td>
                <td><?php echo htmlspecialchars($cita['HORA']); ?></td>
                 <td><?php echo htmlspecialchars($cita['ESTATUS']); ?></td>
                    
                    <!-- Acción para Citas -->
<td>
  <!-- Botón para Marcar como Atendido -->
  <a href="crear_cita.php?atender=<?php echo $cita['ID']; ?>" class="btn btn-success btn-sm">
    <i class="fas fa-check-circle"></i> Atender
  </a>

  <!-- Botón para Editar Cita -->
  <a href="editar_cita.php?clave=<?php echo $cita['ID']; ?>" class=" class="btn-editar">
    <i class="fas fa-edit"></i> Ed
  </a>

  <!-- Botón para Eliminar Cita -->
  <a href="eliminar_cita.php?atender=<?php echo $cita['ID']; ?>" class="btn-eliminar" onclick="return confirm('¿Estás seguro de eliminar esta cita?');">
    <i class="fas fa-trash-alt"></i> El
  </a>

</td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <p class="alert alert-info">
            No hay citas 
            <?php 
              if ($filter === 'hoy') {
                  echo "para hoy.";
              } elseif ($filter === 'pasadas') {
                  echo "pasadas.";
              } else {
                  echo "futuras.";
              }
            ?>
        </p>
    <?php endif; ?>

</div><!-- /.container -->


</body>
</html>
