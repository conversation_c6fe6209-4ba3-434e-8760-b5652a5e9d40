<?php
// Conectar a la base de datos (asegúrate de usar tus credenciales adecuadas)
include('../config/database.php');

// Función para validar la cédula en RD
function validaCedula($cedula) {
    if (strlen($cedula) != 11) {
        return false;
    }

    $sImp = 0;
    $sPar = 0;

    for ($i = 0; $i < 10; $i++) {
        $digito = (int)$cedula[$i];

        if ($i % 2 == 0) {
            $sImp += $digito;
        } else {
            $doble = $digito * 2;
            if ($doble > 9) {
                $sPar += ($doble - 9);
            } else {
                $sPar += $doble;
            }
        }
    }

    $sumaTotal = $sImp + $sPar;
    $digitoControl = $sumaTotal % 10;

    if ($digitoControl == 0 && (int)$cedula[10] == 0) {
        return true;
    }

    if ($digitoControl == 10 - (int)$cedula[10]) {
        return true;
    }

    return false;
}

// Verificar si la cédula existe en la base de datos
if (isset($_GET['cedula'])) {
    $cedula = $_GET['cedula'];

    // Validar que solo contiene números
    if (!ctype_digit($cedula)) {
        echo json_encode(["error" => "La cédula solo debe contener números."]);
        exit;
    }

    // Validar la longitud de la cédula (debe ser de 11 caracteres)
    if (strlen($cedula) != 11) {
        echo json_encode(["error" => "La cédula debe tener exactamente 11 dígitos."]);
        exit;
    }

    // Validar la cédula según el algoritmo RD
    if (!validaCedula($cedula)) {
        echo json_encode(["error" => "La cédula no es válida!!!."]);
        exit;
    }

    // Verificar si la cédula existe en la base de datos
    $sql = "SELECT * FROM PACIENTES WHERE CEDULA = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$cedula]);

    $paciente = $stmt->fetch(PDO::FETCH_ASSOC);

    // Si encuentras al paciente
    if ($paciente) {
        echo json_encode([
            "cedula" => $paciente['CEDULA'],
            "nombre" => $paciente['NOMBREAPELLIDO'],
            "sexo" => $paciente['SEXO'],
            "estadoCivil" => $paciente['ESTADOCIVIL'],
            "telefono" => $paciente['TELEFONO'],
            "fechaNac" => $paciente['FECHANAC'],
            "email" => $paciente['ECORREO'],
            "nacionalidad" => $paciente['NACIONALIDAD'],
            "edad" => ($paciente['EDAD']),
            "tipoSangre" => $paciente['RH'],
            "peso" => $paciente['PESOHABITUAL'] ?? 'No especificado',
            "ocupacion" => $paciente['OCUPACION'],
            "calle" => $paciente['CALLE'] ?? 'No especificado',
            "localidad" => $paciente['LOCALIDAD'] ?? 'No especificado',
            "municipio" => $paciente['MUNICIPIO'] ?? 'No especificado',
            "provincia" => $paciente['PROVINCIA'] ?? 'No especificado',
             "pais" => $paciente['PAIS'] ?? 'No especificado',
             "ars"=> $paciente['ARS'] ?? '1',
             
             
             
             
        ]);
    } else {
        echo json_encode(["error" => "No se encontró un paciente con esa cédula."]);
    }
    exit;
}
?>

<?php
// Mantén el mismo código PHP que antes (sin cambios)
// Incluye la lógica para conexión, validación de cédula y manejo de errores.
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Información del Paciente</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            background-color: #f4f4f9;
            color: #333;
        }
        .container {
            background: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            width: 400px;
            margin: 20px auto; /* Centra horizontalmente */
        }
        h1 {
            text-align: center;
             color: #0056b3;
            margin-bottom: 20px;
        }
        label {
            font-weight: bold;
            display: block;
            margin-bottom: 8px;
        }
        input {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        #resultado {
            padding: 10px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            color: #333;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
    <script>
        function validarCedula() {
            const cedulaInput = document.getElementById('CEDULA');
            const cedula = cedulaInput.value.trim();
            const resultadoDiv = document.getElementById('resultado');

            resultadoDiv.innerHTML = "Validando cédula...";
            resultadoDiv.className = "";

            // Validar que solo contiene números
            if (!/^\d{11}$/.test(cedula)) {
                resultadoDiv.textContent = "La cédula debe tener exactamente 11 dígitos y contener solo números.";
                resultadoDiv.className = "error";
                return;
            }

            // Hacer la solicitud AJAX para verificar la cédula
            fetch(`prueba.php?cedula=${cedula}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    resultadoDiv.textContent = data.error;
                    resultadoDiv.className = "error";
                } else {
                    resultadoDiv.innerHTML = `
                        <b>Cédula:</b> ${data.cedula}<br>
                        <b>Paciente:</b> ${data.nombre}<br>
                        <b>Sexo:</b> ${data.sexo}<br>
                        <b>Estado Civil:</b> ${data.estadoCivil}<br>
                        <b>Teléfono:</b> ${data.telefono}<br>
                        <b>Fecha de Nacimiento:</b> ${data.fechaNac}<br>
                        <b>Correo Electrónico:</b> ${data.email}<br>
                        <b>Nacionalidad:</b> ${data.nacionalidad}<br>
                        <b>Edad:</b> ${data.edad}<br>
                        <b>Tipo de Sangre:</b> ${data.tipoSangre}<br>
                        <b>Peso Habitual:</b> ${data.peso}<br>
                        <b>Ocupación:</b> ${data.ocupacion}<br>
                        <b>Calle:</b> ${data.calle}<br>
                        <b>Localidad:</b> ${data.localidad}<br>
                        <b>Municipio:</b> ${data.municipio}<br>
                        <b>Provincia:</b> ${data.provincia}<br>
                        <b>País:</b> ${data.pais}<br>
                    `;
                    resultadoDiv.className = "success";
                }
            })
            .catch(() => {
                resultadoDiv.textContent = "Hubo un error al verificar la cédula.";
                resultadoDiv.className = "error";
            });
        }
    </script>
</head>
<body>
    <div class="container">
        <h1>Información del Paciente</h1>
        <form id="form-cita">
            <label for="CEDULA">Cédula:</label>
            <input 
                type="text" 
                id="CEDULA" 
                name="CEDULA" 
                maxlength="11" 
                placeholder="Ingresa la cédula"
                oninput="this.value = this.value.replace(/\D/g, '');" 
                onblur="validarCedula()">
            <div id="resultado"></div>
        </form>
    </div>
</body>
</html>
