<?php
// backend/reportes/pacientes_reportes.php
session_start();
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: ../../../login.php');
    exit;
}

require_once __DIR__ . '/../config/database.php';
date_default_timezone_set('America/Santo_Domingo');

$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

$usuario = htmlspecialchars($_SESSION['usuario']);

// Procesar filtros
$fecha_inicio = $_GET['fecha_inicio'] ?? date('Y-01-01');
$fecha_fin = $_GET['fecha_fin'] ?? date('Y-m-d');
$sexo = $_GET['sexo'] ?? '';
$edad_min = $_GET['edad_min'] ?? '';
$edad_max = $_GET['edad_max'] ?? '';
$provincia = $_GET['provincia'] ?? '';
$reporte_tipo = $_GET['tipo'] ?? 'demografia';
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Reportes de Pacientes - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio'; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { background-color: #f8f9fa; }
        .header-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .report-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-users me-3"></i>Reportes de Pacientes</h1>
                    <p class="mb-0">Análisis demográfico y estadísticas de pacientes</p>
                </div>
                <div class="col-md-4 text-end">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb justify-content-end">
                            <li class="breadcrumb-item"><a href="../../../index.php" class="text-white">Inicio</a></li>
                            <li class="breadcrumb-item"><a href="../../reportes.php" class="text-white">Reportes</a></li>
                            <li class="breadcrumb-item active text-white-50">Pacientes</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filtros -->
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-filter me-2"></i>Filtros de Reporte</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-2">
                        <label for="fecha_inicio" class="form-label">Fecha Inicio</label>
                        <input type="date" class="form-control" id="fecha_inicio" name="fecha_inicio" value="<?php echo $fecha_inicio; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="fecha_fin" class="form-label">Fecha Fin</label>
                        <input type="date" class="form-control" id="fecha_fin" name="fecha_fin" value="<?php echo $fecha_fin; ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="sexo" class="form-label">Sexo</label>
                        <select class="form-select" id="sexo" name="sexo">
                            <option value="">Todos</option>
                            <option value="M" <?php echo $sexo == 'M' ? 'selected' : ''; ?>>Masculino</option>
                            <option value="F" <?php echo $sexo == 'F' ? 'selected' : ''; ?>>Femenino</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label for="edad_min" class="form-label">Edad Min</label>
                        <input type="number" class="form-control" id="edad_min" name="edad_min" value="<?php echo $edad_min; ?>" min="0" max="120">
                    </div>
                    <div class="col-md-1">
                        <label for="edad_max" class="form-label">Edad Max</label>
                        <input type="number" class="form-control" id="edad_max" name="edad_max" value="<?php echo $edad_max; ?>" min="0" max="120">
                    </div>
                    <div class="col-md-2">
                        <label for="provincia" class="form-label">Provincia</label>
                        <select class="form-select" id="provincia" name="provincia">
                            <option value="">Todas</option>
                            <?php
                            $stmt = $pdo->query("SELECT DISTINCT PROVINCIA FROM PACIENTES WHERE PROVINCIA IS NOT NULL AND PROVINCIA != '' ORDER BY PROVINCIA");
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                $selected = $provincia == $row['PROVINCIA'] ? 'selected' : '';
                                echo "<option value='{$row['PROVINCIA']}' $selected>{$row['PROVINCIA']}</option>";
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="tipo" class="form-label">Tipo de Reporte</label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="demografia" <?php echo $reporte_tipo == 'demografia' ? 'selected' : ''; ?>>Demografía</option>
                            <option value="actividad" <?php echo $reporte_tipo == 'actividad' ? 'selected' : ''; ?>>Actividad</option>
                            <option value="registros" <?php echo $reporte_tipo == 'registros' ? 'selected' : ''; ?>>Registros</option>
                            <option value="detallado" <?php echo $reporte_tipo == 'detallado' ? 'selected' : ''; ?>>Detallado</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-search me-2"></i>Generar Reporte
                        </button>
                        <button type="button" class="btn btn-primary" onclick="exportarExcel()">
                            <i class="fas fa-file-excel me-2"></i>Exportar Excel
                        </button>
                        <button type="button" class="btn btn-danger" onclick="exportarPDF()">
                            <i class="fas fa-file-pdf me-2"></i>Exportar PDF
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Estadísticas Generales -->
        <?php
        // Construir WHERE clause
        $where_conditions = ["FECHAINGRESO BETWEEN ? AND ?"];
        $params = [$fecha_inicio, $fecha_fin];

        if ($sexo) {
            $where_conditions[] = "SEXO = ?";
            $params[] = $sexo;
        }
        if ($provincia) {
            $where_conditions[] = "PROVINCIA = ?";
            $params[] = $provincia;
        }
        
        $where_clause = "WHERE " . implode(" AND ", $where_conditions);
        
        // Agregar filtro de edad si se especifica
        $edad_clause = "";
        if ($edad_min || $edad_max) {
            if ($edad_min && $edad_max) {
                $edad_clause = " AND TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) BETWEEN $edad_min AND $edad_max";
            } elseif ($edad_min) {
                $edad_clause = " AND TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) >= $edad_min";
            } elseif ($edad_max) {
                $edad_clause = " AND TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) <= $edad_max";
            }
        }

        // Total de pacientes
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM PACIENTES $where_clause $edad_clause");
        $stmt->execute($params);
        $total_pacientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

        // Pacientes por sexo
        $stmt = $pdo->prepare("SELECT SEXO, COUNT(*) as cantidad FROM PACIENTES $where_clause $edad_clause GROUP BY SEXO");
        $stmt->execute($params);
        $por_sexo = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Edad promedio
        $stmt = $pdo->prepare("SELECT AVG(TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE())) as edad_promedio FROM PACIENTES $where_clause $edad_clause AND FECHANAC IS NOT NULL");
        $stmt->execute($params);
        $edad_promedio = round($stmt->fetch(PDO::FETCH_ASSOC)['edad_promedio'], 1);

        // Nuevos pacientes este mes
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM PACIENTES WHERE MONTH(FECHAINGRESO) = MONTH(CURDATE()) AND YEAR(FECHAINGRESO) = YEAR(CURDATE())");
        $stmt->execute();
        $nuevos_mes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        ?>

        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h4><?php echo $total_pacientes; ?></h4>
                        <p class="mb-0">Total Pacientes</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-birthday-cake fa-2x mb-2"></i>
                        <h4><?php echo $edad_promedio; ?> años</h4>
                        <p class="mb-0">Edad Promedio</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-user-plus fa-2x mb-2"></i>
                        <h4><?php echo $nuevos_mes; ?></h4>
                        <p class="mb-0">Nuevos Este Mes</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="fas fa-venus-mars fa-2x mb-2"></i>
                        <h4>
                            <?php 
                            $masculinos = 0;
                            $femeninos = 0;
                            foreach ($por_sexo as $sexo_data) {
                                if ($sexo_data['SEXO'] == 'M') $masculinos = $sexo_data['cantidad'];
                                if ($sexo_data['SEXO'] == 'F') $femeninos = $sexo_data['cantidad'];
                            }
                            echo $masculinos . 'M / ' . $femeninos . 'F';
                            ?>
                        </h4>
                        <p class="mb-0">Distribución por Sexo</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>Distribución por Sexo</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="sexoChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Grupos de Edad</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="edadChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-map-marker-alt me-2"></i>Distribución por Provincia</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="provinciaChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card report-card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>Registros por Mes</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="registrosChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabla Detallada -->
        <?php if ($reporte_tipo == 'detallado'): ?>
        <div class="card report-card">
            <div class="card-header">
                <h5><i class="fas fa-table me-2"></i>Listado Detallado de Pacientes</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="tablaPacientes">
                        <thead>
                            <tr>
                                <th>Cédula</th>
                                <th>Nombre Completo</th>
                                <th>Sexo</th>
                                <th>Edad</th>
                                <th>Provincia</th>
                                <th>Teléfono</th>
                                <th>Fecha Ingreso</th>
                                <th>Última Cita</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $sql = "SELECT p.CEDULA, p.NOMBREAPELLIDO, p.SEXO, 
                                          TIMESTAMPDIFF(YEAR, p.FECHANAC, CURDATE()) as edad,
                                          p.PROVINCIA, p.TELEFONO, p.FECHAINGRESO,
                                          (SELECT MAX(c.FECHACON) FROM CITAMEDIC c WHERE c.CLAVEPAC = p.CLAVE) as ultima_cita
                                   FROM PACIENTES p 
                                   $where_clause $edad_clause 
                                   ORDER BY p.FECHAINGRESO DESC";
                            
                            $stmt = $pdo->prepare($sql);
                            $stmt->execute($params);
                            $pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            foreach ($pacientes as $paciente):
                            ?>
                            <tr>
                                <td><?php echo htmlspecialchars($paciente['CEDULA']); ?></td>
                                <td><?php echo htmlspecialchars($paciente['NOMBREAPELLIDO']); ?></td>
                                <td>
                                    <span class="badge bg-<?php echo $paciente['SEXO'] == 'M' ? 'primary' : 'pink'; ?>">
                                        <?php echo $paciente['SEXO'] == 'M' ? 'Masculino' : 'Femenino'; ?>
                                    </span>
                                </td>
                                <td><?php echo $paciente['edad'] ?? 'N/A'; ?> años</td>
                                <td><?php echo htmlspecialchars($paciente['PROVINCIA'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($paciente['TELEFONO'] ?? 'N/A'); ?></td>
                                <td><?php echo $paciente['FECHAINGRESO'] ? date('d/m/Y', strtotime($paciente['FECHAINGRESO'])) : 'N/A'; ?></td>
                                <td><?php echo $paciente['ultima_cita'] ? date('d/m/Y', strtotime($paciente['ultima_cita'])) : 'Nunca'; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Datos para gráficos
        <?php
        // Datos para gráfico de sexo
        $labels_sexo = [];
        $data_sexo = [];
        foreach ($por_sexo as $sexo_data) {
            $labels_sexo[] = $sexo_data['SEXO'] == 'M' ? 'Masculino' : 'Femenino';
            $data_sexo[] = $sexo_data['cantidad'];
        }

        // Datos para gráfico de edad
        $stmt = $pdo->prepare("
            SELECT 
                CASE 
                    WHEN TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) < 18 THEN 'Menor de 18'
                    WHEN TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) BETWEEN 18 AND 30 THEN '18-30'
                    WHEN TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) BETWEEN 31 AND 50 THEN '31-50'
                    WHEN TIMESTAMPDIFF(YEAR, FECHANAC, CURDATE()) BETWEEN 51 AND 70 THEN '51-70'
                    ELSE 'Mayor de 70'
                END as grupo_edad,
                COUNT(*) as cantidad
            FROM PACIENTES 
            $where_clause $edad_clause AND FECHANAC IS NOT NULL
            GROUP BY grupo_edad
        ");
        $stmt->execute($params);
        $datos_edad = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $labels_edad = [];
        $data_edad = [];
        foreach ($datos_edad as $edad_data) {
            $labels_edad[] = $edad_data['grupo_edad'];
            $data_edad[] = $edad_data['cantidad'];
        }

        // Datos para gráfico de provincia
        $stmt = $pdo->prepare("
            SELECT PROVINCIA, COUNT(*) as cantidad 
            FROM PACIENTES 
            $where_clause $edad_clause AND PROVINCIA IS NOT NULL AND PROVINCIA != ''
            GROUP BY PROVINCIA 
            ORDER BY cantidad DESC 
            LIMIT 10
        ");
        $stmt->execute($params);
        $datos_provincia = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $labels_provincia = [];
        $data_provincia = [];
        foreach ($datos_provincia as $prov_data) {
            $labels_provincia[] = $prov_data['PROVINCIA'];
            $data_provincia[] = $prov_data['cantidad'];
        }

        // Datos para registros por mes
        $stmt = $pdo->prepare("
            SELECT DATE_FORMAT(FECHAINGRESO, '%Y-%m') as mes, COUNT(*) as cantidad 
            FROM PACIENTES 
            WHERE FECHAINGRESO >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
            GROUP BY mes 
            ORDER BY mes
        ");
        $stmt->execute();
        $datos_registros = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $labels_registros = [];
        $data_registros = [];
        foreach ($datos_registros as $reg_data) {
            $labels_registros[] = date('M Y', strtotime($reg_data['mes'] . '-01'));
            $data_registros[] = $reg_data['cantidad'];
        }
        ?>

        // Gráfico de sexo
        const ctx1 = document.getElementById('sexoChart').getContext('2d');
        new Chart(ctx1, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode($labels_sexo); ?>,
                datasets: [{
                    data: <?php echo json_encode($data_sexo); ?>,
                    backgroundColor: ['#007bff', '#e83e8c']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });

        // Gráfico de edad
        const ctx2 = document.getElementById('edadChart').getContext('2d');
        new Chart(ctx2, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($labels_edad); ?>,
                datasets: [{
                    label: 'Pacientes',
                    data: <?php echo json_encode($data_edad); ?>,
                    backgroundColor: '#28a745'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Gráfico de provincia
        const ctx3 = document.getElementById('provinciaChart').getContext('2d');
        new Chart(ctx3, {
            type: 'horizontalBar',
            data: {
                labels: <?php echo json_encode($labels_provincia); ?>,
                datasets: [{
                    label: 'Pacientes',
                    data: <?php echo json_encode($data_provincia); ?>,
                    backgroundColor: '#17a2b8'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y'
            }
        });

        // Gráfico de registros
        const ctx4 = document.getElementById('registrosChart').getContext('2d');
        new Chart(ctx4, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($labels_registros); ?>,
                datasets: [{
                    label: 'Nuevos Registros',
                    data: <?php echo json_encode($data_registros); ?>,
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        function exportarExcel() {
            window.location.href = 'api/exportar_pacientes.php?formato=excel&' + new URLSearchParams(window.location.search);
        }

        function exportarPDF() {
            window.location.href = 'api/exportar_pacientes.php?formato=pdf&' + new URLSearchParams(window.location.search);
        }
    </script>
</body>
</html>
