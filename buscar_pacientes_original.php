<?php

// buscar_pacientes.php
session_start();
require_once '../config/database.php';

// Cargar listado de aseguradoras
$stmt_ars = $pdo->query("SELECT CLAVE, NOMBRE FROM ASEGURADORA ORDER BY NOMBRE");
$arsList = $stmt_ars->fetchAll(PDO::FETCH_ASSOC);

// Cargar listado de categorías (solo aquellas de tipo 'C')
$stmt_categ = $pdo->query("SELECT CLAVE, NOMBRE FROM CATEGORIAS WHERE TIPO = 'C' ORDER BY CLAVE");
$categList = $stmt_categ->fetchAll(PDO::FETCH_ASSOC);

$pacientes = [];


// Consulta: Traer todos los pacientes ordenados por NOMBRES
$stmt = $pdo->query("SELECT * FROM PACIENTES ORDER BY NOMBRES ASC");
$pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Si se recibe una petición POST, devolvemos el JSON y salimos
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo json_encode($pacientes);
    exit;

}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
 <title>Buscar Pacientes</title>
  <!-- Favicon médico -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
  <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">


 <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css">
  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .container {
      margin-top: 2rem;
    }
  </style>

</head>
<body>


<script>
    var categList = <?php echo json_encode($categList); ?>;
      var arsList = <?php echo json_encode($arsList); ?>;
</script>


<div class="container">
 
  <!-- Aquí se mostrará la tabla con los datos -->
  <div id="resultadosBusqueda"></div>
</div>

<!-- jQuery (versión completa) -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.min.js"></script>

<script>
// Función para cargar la tabla de pacientes con DataTables
function cargarPacientes() {
  // Realizamos la petición POST para obtener los datos
  fetch('buscar_pacientes.php', {
    method: 'POST',
    body: JSON.stringify({}), // En este caso no enviamos filtros
    headers: { 'Content-Type': 'application/json' }
  })
  .then(response => response.json())
  .then(data => {
    // Verificamos que data sea un array
    if (!Array.isArray(data)) {
      console.error("La respuesta no es un array:", data);
      return;
    }
    
    let html = `
      <table id="tablaPacientes" class="table table-striped table-bordered table-hover">
        <thead>
          <tr>
            <th>Cédula</th>
            <th>Registro</th>
            <th>Nombres</th>
            <th>Apellidos</th>
            <th>Edad</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
    `;
    data.forEach(paciente => {
      html += `
        <tr>
          <td>${paciente.CEDULA}</td>
          <td>${paciente.REGISTRO}</td>
          <td>${paciente.NOMBRES}</td>
          <td>${paciente.APELLIDOS}</td>
          <td>${paciente.EDAD}</td>
          <td>
            <a href="#" onclick="editarPaciente(${paciente.CLAVE})" title="Editar">
              <i class="fas fa-pencil-alt"></i>
            </a>
            <a href="#" onclick="eliminarPaciente(${paciente.CLAVE})" title="Eliminar">
              <i class="fas fa-trash"></i>
            </a>
                   
           <a href="../citas/crear_cita.php?pre=${encodeURIComponent(paciente.CEDULA)}" class="btn btn-transparent" title="Crear Cita">
        <i class="fas fa-calendar-plus"></i>
      </a>

        </td>
        </tr>
      `;
    });
    html += `
        </tbody>
      </table>
    `;
    document.getElementById('resultadosBusqueda').innerHTML = html;
    
    // Inicializar DataTables (después de insertar la tabla en el DOM)
    $('#tablaPacientes').DataTable({
      paging: true,
      ordering: true,
      order: [[2, 'asc']], // Ordena por NOMBRES de forma ascendente
      searching: true,
      language: {
        lengthMenu: "Mostrar _MENU_ entradas",
        search: "Buscar:",
        zeroRecords: "No se encontraron resultados",
         info: "Mostrando _START_ a _END_ de _TOTAL_ pacientes",
        emptyTable: "No hay pacientes disponibles",
        infoEmpty: "No hay pacientes disponibles",
          paginate: {
            first: "Primero",
            last: "Último",
            next: "Siguiente",
            previous: "Anterior"
          }
       
      }
  
    });
  })
  .catch(error => {
    console.error('Error al cargar los pacientes:', error);
  });
}

// Cargar la tabla de pacientes cuando el documento esté listo
$(document).ready(function() {
  cargarPacientes();
});

// Funciones de validación (puedes ajustarlas según sea necesario)
function validarTelefono(input) {
    const telefono = input.value;
    if (telefono !== "" && !/^[0-9]{10}$/.test(telefono)) {
        input.setCustomValidity("El teléfono debe contener exactamente 10 dígitos numéricos.");
    } else {
        input.setCustomValidity("");
    }
}

function validarCorreo(input) {
    const correo = input.value;
    if (correo !== "" && !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(correo)) {
        input.setCustomValidity("Por favor, ingresa un correo electrónico válido.");
    } else {
        input.setCustomValidity("");
    }
}


// Función para editar un paciente (genera el formulario completo de edición con pestañas)
function editarPaciente(clave) {
    fetch('obtener_paciente.php?CLAVE=' + clave, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
    })
    .then(response => response.json())
    .then(paciente => {
        if (paciente.error) {
            console.error(paciente.error);
            return;
        }
     
      // Si el paciente tiene una foto, usarla; de lo contrario, usar NOFOTO.BMP
         let rutaFoto = (paciente.ARCHIVO && paciente.ARCHIVO.trim() !== "") 
            ? `${paciente.ARCHIVO}?v=${new Date().getTime()}`
            : "fotografias/NOFOTO.BMP";
      
        // Construir el bloque de la foto (ahora se muestra debajo del encabezado, justificado a la izquierda)
      
        let fotoHTML = `
          <div style="margin-bottom: 1rem;">
            <p>Foto del Paciente:</p>
            <img src="${rutaFoto}" alt="Foto del Paciente" style="max-width:150px;">
          </div>
          
        `;
 
  
   // Generar dinámicamente las opciones de Estado Civil según el sexo
    let estados = [];
    if (paciente.SEXO === 'Masculino') {
      estados = ["Soltero", "Casado", "Viudo", "Divorciado", "Separado", "Unión Libre"];
    } else if (paciente.SEXO === 'Femenino') {
      estados = ["Soltera", "Casada", "Viuda", "Divorciada", "Separada", "Unión Libre"];
    } else {
      estados = ["Soltero(a)", "Casado(a)", "Viudo(a)", "Divorciado(a)", "Separado(a)", "Unión Libre"];
    }
    
     let estadoOptions = `<option value="">Seleccionar</option>`;
    estados.forEach(estado => {
      estadoOptions += `<option value="${estado}" ${paciente.ESTADOCIVIL === estado ? 'selected' : ''}>${estado}</option>`;
    });
      
      // Generar las opciones del select de Categoría usando categList
    let categoriaOptions = `<option value="">Seleccionar Categoría</option>` +
     categList.map(cat => `<option value="${cat.CLAVE}" ${paciente.CATEGORIA == cat.CLAVE ? 'selected' : ''}>${cat.NOMBRE}</option>`).join('');

      
        // Generar el formulario de edición completo (con pestañas)
        let form = `
         <form id="editarPacienteForm" enctype="multipart/form-data">
        
         ${fotoHTML}
        

  <div style="margin-bottom: 1rem;">
    <label for="nuevaFoto">Cambiar Foto:</label>
    <input type="file" class="form-control" id="nuevaFoto" name="nuevaFoto" accept=".jpg, .jpeg, .png, .bmp">
  </div>
      
          <input type="hidden" name="CLAVE" value="${paciente.CLAVE}">
          <input type="hidden" name="SINCRONIZADO" value="0">
          <h3>Datos Principales</h3>
               
               <div class="form-group" style="margin-bottom: 0.5rem;">
  <label for="CEDULA" style="margin-bottom: 0.2rem; display: block;">Cédula:</label>
  <input type="text" class="form-control" id="CEDULA" name="CEDULA" value="${paciente.CEDULA}" maxlength="11" required oninput="validarCedula(event)">
</div>
               
          <div class="form-group">
             <label for="NOMBRES">Nombres:</label>
             <input type="text" class="form-control" id="NOMBRES" name="NOMBRES" value="${paciente.NOMBRES}" required maxlength="35">
            </div>
            <div class="form-group">
             <label for="APELLIDOS">Apellidos:</label>
             <input type="text" class="form-control" id="APELLIDOS" name="APELLIDOS" value="${paciente.APELLIDOS}" required maxlength="35">
            </div>
          <div class="form-group">
            <label for="FECHANAC">Fecha de Nacimiento:</label>
            <input type="date" class="form-control" id="FECHANAC" name="FECHANAC" value="${paciente.FECHANAC}" required oninput="validarFechaNacimiento(event)">
          </div>
          <div class="form-group">
            <label for="SEXO">Sexo:</label>
            <select class="form-control" id="SEXO" name="SEXO" required>
              <option value="">Seleccionar</option>
              <option value="Masculino" ${paciente.SEXO === 'Masculino' ? 'selected' : ''}>Masculino</option>
              <option value="Femenino" ${paciente.SEXO === 'Femenino' ? 'selected' : ''}>Femenino</option>
              <option value="Indefinido" ${paciente.SEXO === 'Indefinido' ? 'selected' : ''}>Indefinido</option>
            </select>
          </div>
          
            <div class="form-group">
             <label for="RH">Tipo de Sangre (RH):</label>
             <select class="form-control" id="RH" name="RH" required>
              <option value="" disabled>Seleccione una opción</option>
                <option value="O+" <?php echo (trim($paciente['RH'] ?? '') === 'O+' ? 'selected' : ''); ?>>O+</option>
               <option value="O-" <?php echo (trim($paciente['RH'] ?? '') === 'O-' ? 'selected' : ''); ?>>O-</option>
               <option value="A+" <?php echo (trim($paciente['RH'] ?? '') === 'A+' ? 'selected' : ''); ?>>A+</option>
                <option value="A-" <?php echo (trim($paciente['RH'] ?? '') === 'A-' ? 'selected' : ''); ?>>A-</option>
                  <option value="B+" <?php echo (trim($paciente['RH'] ?? '') === 'B+' ? 'selected' : ''); ?>>B+</option>
              <option value="B-" <?php echo (trim($paciente['RH'] ?? '') === 'B-' ? 'selected' : ''); ?>>B-</option>
             <option value="AB+" <?php echo (trim($paciente['RH'] ?? '') === 'AB+' ? 'selected' : ''); ?>>AB+</option>
               <option value="AB-" <?php echo (trim($paciente['RH'] ?? '') === 'AB-' ? 'selected' : ''); ?>>AB-</option>
              </select>
            </div>

    <div class="form-group">
            <label for="PESOHABITUAL">Peso Habitual:</label>
            <input type="number" step="0.01" class="form-control" id="PESOHABITUAL" name="PESOHABITUAL" value="${paciente.PESOHABITUAL}">
          </div>      
          
          <div class="form-group">
            <label for="NACIONALIDAD">Nacionalidad:</label>
            <input type="text" class="form-control" id="NACIONALIDAD" name="NACIONALIDAD" value="${paciente.NACIONALIDAD}" required maxlength="35">
          </div>
          
          <h3>Información Adicional</h3>
          
           <div class="form-group">
          <label for="ESTADOCIVIL">Estado Civil:</label>
          <select class="form-control" id="ESTADOCIVIL" name="ESTADOCIVIL">
            ${estadoOptions}
          </select>
        </div>
                <div class="form-group">
             <label for="LUGARNAC">Lugar de Nacimiento:</label>
            <input type="text" class="form-control" id="LUGARNAC" name="LUGARNAC" value="${paciente.LUGARNAC}" maxlength="35">
            </div>
          <div class="form-group">
            <label for="OCUPACION">Ocupación:</label>
            <input type="text" class="form-control" id="OCUPACION" name="OCUPACION" value="${paciente.OCUPACION}" maxlength="35">
          </div>
          <div class="form-group">
            <label for="RELIGION">Religión:</label>
            <select class="form-control" id="RELIGION" name="RELIGION">
              <option value="">Seleccionar</option>
              <option value="Católico" ${paciente.RELIGION === "Católico" ? 'selected' : ''}>Católico</option>
              <option value="Cristiano Evangelice" ${paciente.RELIGION === "Cristiano Evangelice" ? 'selected' : ''}>Cristiano Evangelice</option>
              <option value="Testigo" ${paciente.RELIGION === "Testigo" ? 'selected' : ''}>Testigo</option>
              <option value="Ninguna" ${paciente.RELIGION === "Ninguna" ? 'selected' : ''}>Ninguna</option>
              <option value="Otra" ${paciente.RELIGION === "Otra" ? 'selected' : ''}>Otra</option>
            </select>
          </div>
          
          <!-- Pestañas para agrupar la información -->
          <ul class="nav nav-tabs" id="infoTab" role="tablist">
            <li class="nav-item">
              <a class="nav-link active" id="domicilio-tab" data-toggle="tab" href="#domicilioForm" role="tab" aria-controls="domicilioForm" aria-selected="true">Domicilio</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="contacto-tab" data-toggle="tab" href="#contactoForm" role="tab" aria-controls="contactoForm" aria-selected="false">Contacto</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="poliza-tab" data-toggle="tab" href="#polizaForm" role="tab" aria-controls="polizaForm" aria-selected="false">Póliza</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="responsable-tab" data-toggle="tab" href="#responsableForm" role="tab" aria-controls="responsableForm" aria-selected="false">Responsable</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" id="familiar-tab" data-toggle="tab" href="#familiarForm" role="tab" aria-controls="familiarForm" aria-selected="false">Familiar</a>
            </li>
          </ul>
          
          <div class="tab-content pt-3" id="infoTabContent">
            <!-- Pestaña DOMICILIO -->
            <div class="tab-pane fade show active" id="domicilioForm" role="tabpanel" aria-labelledby="domicilio-tab">
              <div class="form-group">
                <label for="CALLE">Calle:</label>
                <input type="text" class="form-control" id="CALLE" name="CALLE" value="${paciente.CALLE}" maxlength="80">
              </div>
              <div class="form-group">
                <label for="PROVINCIA">Provincia:</label>
                <input type="text" class="form-control" id="PROVINCIA" name="PROVINCIA" value="${paciente.PROVINCIA}" maxlength="30">
              </div>
              <div class="form-group">
                <label for="LOCALIDAD">Localidad:</label>
                <input type="text" class="form-control" id="LOCALIDAD" name="LOCALIDAD" value="${paciente.LOCALIDAD}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="MUNICIPIO">Municipio:</label>
                <input type="text" class="form-control" id="MUNICIPIO" name="MUNICIPIO" value="${paciente.MUNICIPIO}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="PAIS">País:</label>
                <input type="text" class="form-control" id="PAIS" name="PAIS" value="${paciente.PAIS}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="REFERENCIA">Referencia:</label>
                <input type="text" class="form-control" id="REFERENCIA" name="REFERENCIA" value="${paciente.REFERENCIA}" maxlength="30">
              </div>
            </div>
            
            <!-- Pestaña CONTACTO -->
            <div class="tab-pane fade" id="contactoForm" role="tabpanel" aria-labelledby="contacto-tab">
              <div class="form-group">
            
            
            
            <label for="CELULAR">Teléfono Celular:</label>
             <input type="text" class="form-control" id="CELULAR" name="CELULAR" value="${paciente.CELULAR}"
              maxlength="10"  pattern="[0-9]{10}"  title="Ingrese exactamente 10 dígitos numéricos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
              
              
              
            </div>
           <div class="form-group">
  <label for="TELEFONO">Teléfono de Casa:</label>
  <input type="text" class="form-control" id="TELEFONO" name="TELEFONO" value="${paciente.TELEFONO}"
         maxlength="10" pattern="[0-9]{10}"  maxlength="10"  title="Ingrese exactamente 10 dígitos numéricos"
         oninput="this.value = this.value.replace(/[^0-9]/g, '');">
</div>
        <div class="form-group">
         <label for="TELTRABAJO">Teléfono del Trabajo:</label>
        <input type="text" class="form-control" id="TELTRABAJO" name="TELTRABAJO" value="${paciente.TELTRABAJO}"
        maxlength="10"  pattern="[0-9]{10}"  maxlength="10"   title="Ingrese exactamente 10 dígitos numéricos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
        </div>
            <div class="form-group">
             <label for="FAX">Fax:</label>
             <input type="text" class="form-control" id="FAX" name="FAX" value="${paciente.FAX}"
             maxlength="10"  pattern="[0-9]{10}"   maxlength="10"  title="Ingrese exactamente 10 dígitos numéricos" oninput="this.value = this.value.replace(/[^0-9]/g, '');">
            </div>

              <div class="form-group">
                <label for="ECORREO">Correo Electrónico:</label>
                <input type="email" class="form-control" id="ECORREO" name="ECORREO" value="${paciente.ECORREO}" maxlength="50">
              </div>
            </div>
            
            <!-- Pestaña PÓLIZA (se utiliza NSS en lugar de POLIZA) -->
            <div class="tab-pane fade" id="polizaForm" role="tabpanel" aria-labelledby="poliza-tab">
             
             
              <div class="form-group">
             <label for="ARS">ARS:</label>
              <select class="form-control" id="ARS" name="ARS" required>
             <option value="">Seleccionar ARS</option>
             ${arsList.map(item => `<option value="${item.CLAVE}" ${paciente.ARS == item.CLAVE ? 'selected' : ''}>${item.NOMBRE}</option>`).join('')}
             </select>
            </div>
             
              <div class="form-group">
                <label for="PLANES">Planes:</label>
                <input type="text" class="form-control" id="PLANES" name="PLANES" value="${paciente.PLANES}" maxlength="20">
              </div>
              <div class="form-group">
                <label for="AFILIADO">Afiliado:</label>
                <input type="text" class="form-control" id="AFILIADO" name="AFILIADO" value="${paciente.AFILIADO}" maxlength="18">
              </div>
              <div class="form-group">
                <label for="VIGENCIA">Vigencia:</label>
                <input type="date" class="form-control" id="VIGENCIA" name="VIGENCIA" value="${paciente.VIGENCIA ? new Date(paciente.VIGENCIA).toISOString().substr(0,10) : ''}">
              </div>
              <div class="form-group">
                <label for="NSS">Número de Seguro Social (NSS):</label>
                <input type="text" class="form-control" id="NSS" name="NSS" readonly value="${paciente.CEDULA}">
              </div>
              
              <div class="form-group">
             <label for="CATEGORIA">Categoría:</label>
             <select class="form-control" id="CATEGORIA" name="CATEGORIA" required>
               <option value="">Seleccionar Categoría</option>${categList.map(cat => `<option value="${cat.CLAVE}" ${paciente.CATEGORIA==cat.CLAVE ? 'selected' : ''}>${cat.NOMBRE}</option>`).join('')}
             </select>
            </div>

            </div>
            
            <!-- Pestaña RESPONSABLE -->
            <div class="tab-pane fade" id="responsableForm" role="tabpanel" aria-labelledby="responsable-tab">
              <div class="form-group">
                <label for="NOMBRERESP">Nombre del Responsable:</label>
                <input type="text" class="form-control" id="NOMBRERESP" name="NOMBRERESP" value="${paciente.NOMBRERESP}" maxlength="50">
              </div>
              <div class="form-group">
                <label for="DIRECCIONRESP">Dirección del Responsable:</label>
                <input type="text" class="form-control" id="DIRECCIONRESP" name="DIRECCIONRESP" value="${paciente.DIRECCIONRESP}" maxlength="50">
              </div>
                 <div class="form-group">
             <label for="CEDULARESP">Cédula del Responsable:</label>
             <input type="text" class="form-control" id="CEDULARESP" name="CEDULARESP" 
              value="${paciente.CEDULARESP}" 
                 maxlength="11" pattern="\d{10}" title="Ingrese exactamente 11 dígitos numéricos"
              oninput="this.value = this.value.replace(/[^0-9]/g, '');">
                </div>
                <div class="form-group">
             <label for="TELEFONORESP">Teléfono del Responsable:</label>
              <input type="text" class="form-control" id="TELEFONORESP" name="TELEFONORESP" 
               value="${paciente.TELEFONORESP}" 
              maxlength="10" pattern="[0-9]{10}" title="Ingrese exactamente 10 dígitos numéricos"
                oninput="this.value = this.value.replace(/[^0-9]/g, '');">
                </div>

            </div>
            
            <!-- Pestaña FAMILIAR -->
            <div class="tab-pane fade" id="familiarForm" role="tabpanel" aria-labelledby="familiar-tab">
              <div class="form-group">
                <label for="FAMILIARPROX">Nombre del Familiar Próximo:</label>
                <input type="text" class="form-control" id="FAMILIARPROX" name="FAMILIARPROX" value="${paciente.FAMILIARPROX}" maxlength="35">
              </div>
              <div class="form-group">
                <label for="DIRECCIONFAMILIAR">Dirección del Familiar Próximo:</label>
                <input type="text" class="form-control" id="DIRECCIONFAMILIAR" name="DIRECCIONFAMILIAR" value="${paciente.DIRECCIONFAMILIAR}" maxlength="35">
              </div>
                <div class="form-group">
         <label for="TELEFONOFAMILIAR">Teléfono del Familiar Próximo:</label>
         <input type="text" class="form-control" id="TELEFONOFAMILIAR" name="TELEFONOFAMILIAR" 
         value="${paciente.TELEFONOFAMILIAR}" 
         maxlength="10" pattern="[0-9]{10}"title="Ingrese exactamente 10 dígitos numéricos"
         oninput="this.value = this.value.replace(/[^0-9]/g, '');">
            </div>
            </div>
          </div>
          
          <!-- Otros campos fuera de pestañas -->
         <div class="form-group mt-3">
        <label for="OBSERVACIONES">Observaciones (por la secretaria):</label>
        <textarea class="form-control" id="OBSERVACIONES" name="OBSERVACIONES" maxlength="60">${paciente.OBSERVACIONES ? paciente.OBSERVACIONES : ''}</textarea>   
          
            </div>
         
         
          <div class="form-group">
            <label>Nivel Escolar:</label><br>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelPrimario" value="0" ${paciente.NIVELESCOLAR == "0" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelPrimario">Primario</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelSecundario" value="1" ${paciente.NIVELESCOLAR == "1" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelSecundario">Secundario</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelUniversitario" value="2" ${paciente.NIVELESCOLAR == "2" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelUniversitario">Universitario</label>
            </div>
           
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="NIVELESCOLAR" id="nivelNinguno" value="3" ${paciente.NIVELESCOLAR == "3" ? 'checked' : ''}>
              <label class="form-check-label" for="nivelNinguno">Ninguno</label>
            </div>
          </div>
         
          <div class="form-group">
            <label>Procedencia:</label><br>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaUrbano" value="1" ${paciente.PROCEDENCIA == "1" ? 'checked' : ''}>
              <label class="form-check-label" for="procedenciaUrbano">Urbano</label>
            </div>
            <div class="form-check form-check-inline">
              <input class="form-check-input" type="radio" name="PROCEDENCIA" id="procedenciaRural" value="0" ${paciente.PROCEDENCIA == "0" ? 'checked' : ''}>
              <label class="form-check-label" for="procedenciaRural">Rural</label>
            </div>
          </div>
          <div class="form-group">
            <label for="RECORDCLINICA">Record Clínico:</label>
            <input type="text" class="form-control" id="RECORDCLINICA" name="RECORDCLINICA" value="${paciente.RECORDCLINICA}">
          </div>
          
          <button type="submit" class="btn btn-primary">Guardar Cambios</button>
        </form>
        `;

        document.getElementById('resultadosBusqueda').innerHTML = form;

        // Manejar el envío del formulario de edición mediante AJAX
    const editarForm = document.getElementById('editarPacienteForm');
    editarForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // 1) Validación nativa HTML5 (incluye tu pattern de 10 dígitos)
      if (!this.checkValidity()) {
        this.reportValidity();
        return;
      }

      // 2) FormData y envío
      const formData = new FormData(this);
      fetch('actualizar_paciente.php', {
        method: 'POST',
        body: formData
      })
      .then(response => response.json())
      .then(responseData => {
        if (responseData.success) {
          alert('Paciente actualizado con éxito');
          window.location.href = 'gestion_pacientes.php?tab=buscar';
        } else {
          alert('Error al actualizar paciente');
        }
      })
      .catch(error => console.error('Error al actualizar paciente:', error));
    });
  })
  .catch(error => console.error('Error al obtener datos del paciente:', error));
}  // fin de function editarPaciente

        
// Función para eliminar un paciente (similar a la implementada previamente)
function eliminarPaciente(clave) {
    if (confirm('¿Estás seguro de eliminar este paciente?')) {
        fetch('eliminar_paciente.php', {
            method: 'POST',
            body: JSON.stringify({ CLAVE: clave }),
            headers: { 'Content-Type': 'application/json' }
        })
        .then(response => response.json())
        .then(responseData => {
            if (responseData.success) {
                alert('Paciente eliminado con éxito');
                window.location.href = 'gestion_pacientes.php?tab=buscar';
            } else {
                alert('Error al eliminar paciente');
            }
        })
        .catch(error => console.error('Error al eliminar paciente:', error));
    }
}
</script>

<!-- Incluir jQuery, Popper.js y Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>


</body>
</html>

