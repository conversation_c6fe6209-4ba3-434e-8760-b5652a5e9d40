<?php
/**
 * Webhook para recibir mensajes de WhatsApp desde Twilio
 * Este archivo debe ser accesible públicamente para que Twilio pueda enviar los mensajes
 */

// Headers para CORS y JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Manejar preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../services/WhatsAppService.php';

// Log para debugging (opcional)
function logWebhook($message) {
    $logFile = __DIR__ . '/../logs/whatsapp_webhook.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

try {
    // Verificar que sea una petición POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        http_response_code(405);
        echo json_encode(['error' => 'Método no permitido']);
        exit;
    }
    
    // Obtener datos del webhook
    $webhookData = $_POST;
    
    // Log de datos recibidos (para debugging)
    logWebhook("Webhook recibido: " . json_encode($webhookData));
    
    // Verificar que tenemos los datos necesarios
    if (empty($webhookData['From']) || empty($webhookData['Body'])) {
        logWebhook("Datos incompletos en webhook");
        http_response_code(400);
        echo json_encode(['error' => 'Datos incompletos']);
        exit;
    }
    
    // Verificar que WhatsApp esté habilitado
    if (!$config['whatsapp']['enabled']) {
        logWebhook("WhatsApp no está habilitado");
        http_response_code(503);
        echo json_encode(['error' => 'Servicio no disponible']);
        exit;
    }
    
    // Crear instancia del servicio WhatsApp
    $whatsappService = new WhatsAppService($config, $pdo);
    
    // Procesar el mensaje recibido
    $response = $whatsappService->processIncomingMessage($webhookData);
    
    logWebhook("Mensaje procesado correctamente");
    
    // Responder a Twilio (TwiML vacío significa que no hay respuesta automática)
    echo '<?xml version="1.0" encoding="UTF-8"?><Response></Response>';
    
} catch (Exception $e) {
    logWebhook("Error procesando webhook: " . $e->getMessage());
    
    // No mostrar errores internos a Twilio
    http_response_code(200);
    echo '<?xml version="1.0" encoding="UTF-8"?><Response></Response>';
}
?>
