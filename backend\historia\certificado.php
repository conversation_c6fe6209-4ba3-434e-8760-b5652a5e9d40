<?php
// certificado.php
$pdo = require_once __DIR__ . '/../config/database.php';
$mensaje = "";
$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA'] ?? '';
$CLAVE    = $_GET['CLAVE'] ?? null;
$nuevo    = isset($_GET['nuevo']);
$datos    = null;
$registros = [];

if (!$CLAVEPAC) {
    die("Paciente no especificado.");
}

// --- Lógica de Robustez: Resolver CLAVEPAC usando CEDULA ---
$clav_pac_real = $CLAVEPAC; // Valor por defecto si no se encuentra o no hay CEDULA

if (!empty($CEDULA)) {
    try {
        $stmtPaciente = $pdo->prepare("SELECT CLAVE FROM PACIENTES WHERE CEDULA = :cedula");
        $stmtPaciente->execute(['cedula' => $CEDULA]);
        $rowPaciente = $stmtPaciente->fetch(PDO::FETCH_ASSOC);

        if ($rowPaciente) {
            $clav_pac_real = $rowPaciente['CLAVE']; // Asigna la CLAVEPAC encontrada en remoto
        } else {
            $mensaje = "⚠️ Advertencia: Paciente con CÉDULA: " . htmlspecialchars($CEDULA) . " no encontrado en PACIENTES. Usando CLAVEPAC original.";
        }
    } catch (PDOException $e) {
        $mensaje = "❌ Error al buscar paciente en PACIENTES: " . $e->getMessage();
    }
}
// --- FIN Lógica de Robustez CLAVEPAC ---

// 1) Eliminar certificado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['eliminar']) && isset($_POST['CLAVE_ELIMINAR'])) {
    $claveEliminar = $_POST['CLAVE_ELIMINAR'];
    try {
        $stmt = $pdo->prepare("DELETE FROM CERTIFICADO WHERE CLAVE = ?");
        $stmt->execute([$claveEliminar]);

        $mensaje = "✅ Certificado eliminado correctamente.";
        // Redireccionar al listado, posiblemente al primer registro o a una vista vacía
        header("Location: certificado.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
        exit;
    } catch (PDOException $e) {
        $mensaje = "❌ Error al eliminar: " . $e->getMessage();
    }
}

// 2) Obtener todos los certificados del paciente
if ($clav_pac_real) { // Usamos la CLAVEPAC real o la original si no se encontró
    $stmt = $pdo->prepare("SELECT CLAVE, FECHA_CAP FROM CERTIFICADO WHERE CLAVEPAC = :CLAVEPAC ORDER BY FECHA_CAP DESC");
    $stmt->execute(['CLAVEPAC' => $clav_pac_real]);
    $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// 3) Obtener datos si hay CLAVE o cargar el primer registro automáticamente
if ($nuevo) {
    $datos = null; // Reiniciar datos para un nuevo certificado
} elseif ($CLAVE) {
    $stmt = $pdo->prepare("SELECT * FROM CERTIFICADO WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $datos = $stmt->fetch(PDO::FETCH_ASSOC);
} elseif (!empty($registros)) { // Si no hay CLAVE y no es nuevo, carga el primero
    $CLAVE = $registros[0]['CLAVE'];
    $stmt = $pdo->prepare("SELECT * FROM CERTIFICADO WHERE CLAVE = :CLAVE");
    $stmt->execute(['CLAVE' => $CLAVE]);
    $datos = $stmt->fetch(PDO::FETCH_ASSOC);
}


// 4) Guardar o actualizar certificado
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['eliminar'])) {
    $diag  = trim($_POST['DIAGCERT'] ?? '');
    $recom = trim($_POST['RECOMENDACION'] ?? '');
    $fecha = $_POST['FECHA_CAP'] ?? date('Y-m-d'); // Usa la fecha del POST o la actual

    if ($diag === '' || $recom === '') {
        $mensaje = '❌ Debe completar el diagnóstico y la recomendación.';
    } else {
        try {
            if (!empty($_POST['CLAVE'])) { // Es una actualización
                $stmt = $pdo->prepare("UPDATE CERTIFICADO SET 
                    DIAGCERT = :diag, 
                    RECOMENDACION = :recom, 
                    FECHA_CAP = :fecha, 
                    SINCRONIZADO = 0 
                    WHERE CLAVE = :CLAVE"
                );
                $stmt->execute([
                    'diag' => $diag,
                    'recom' => $recom,
                    'fecha' => $fecha,
                    'CLAVE' => $_POST['CLAVE']
                ]);
                $CLAVE = $_POST['CLAVE'];
                $mensaje = "✅ Certificado actualizado correctamente.";
            } else { // Es una nueva inserción
                $stmt = $pdo->prepare("INSERT INTO CERTIFICADO (
                    CLAVEPAC, CEDULA, CLAVEDOC, DIAGCERT, RECOMENDACION, FECHA_CAP, SINCRONIZADO
                ) VALUES (
                    :CLAVEPAC, :CEDULA, :CLAVEDOC, :diag, :recom, :fecha, 0
                )");
                $stmt->execute([
                    'CLAVEPAC' => $clav_pac_real, // Usar la CLAVEPAC real
                    'CEDULA' => $CEDULA,
                    'CLAVEDOC' => 1, // Manteniendo el valor hardcodeado por ahora
                    'diag' => $diag,
                    'recom' => $recom,
                    'fecha' => $fecha
                ]);
                $CLAVE = $pdo->lastInsertId();
                $mensaje = "✅ Certificado insertado correctamente.";
            }
            // Redirigir para cargar el certificado recién guardado/actualizado y limpiar el POST
            header("Location: certificado.php?CLAVEPAC={$CLAVEPAC}&CEDULA={$CEDULA}&CLAVE={$CLAVE}");
            exit;
        } catch (PDOException $e) {
            $mensaje = "❌ Error al guardar: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Certificado Médico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .form-label {
            font-weight: bold;
        }
        .card-header h5 {
            margin-bottom: 0;
        }
    </style>
</head>
<body class="p-4 bg-light">
<div class="row">
    <div class="col-md-3 border-end">
        <h5 class="mb-3">FECHAS DE CERTIFICADOS</h5>
        <ul class="list-group">
            <?php if (!empty($registros)): ?>
                <?php foreach ($registros as $r): ?>
                    <?php $active = isset($CLAVE) && $CLAVE == $r['CLAVE']; ?>
                    <li class="list-group-item <?= $active ? 'active' : '' ?>">
                        <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($r['CLAVE']) ?>"
                           class="text-decoration-none <?= $active ? 'text-white' : '' ?>">
                            <?= htmlspecialchars($r['FECHA_CAP']) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <li class="list-group-item text-muted">No hay certificados registrados.</li>
            <?php endif; ?>
            <li class="list-group-item mt-3">
                <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&nuevo=1" class="btn btn-sm btn-primary w-100">➕ Nuevo Certificado</a>
            </li>
        </ul>
    </div>

    <div class="col-md-9">
        <h4 class="mb-4">Detalles del Certificado Médico</h4>
        <?php if ($mensaje): ?><div class="alert alert-info"><?= htmlspecialchars($mensaje) ?></div><?php endif; ?>

        <form method="post" novalidate>
            <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
            <input type="hidden" name="CEDULA"   value="<?= htmlspecialchars($CEDULA) ?>">
            <?php if (!empty($CLAVE) && !$nuevo): // Solo incluir CLAVE si es una modificación de un registro existente ?>
                <input type="hidden" name="CLAVE" value="<?= htmlspecialchars($CLAVE) ?>">
            <?php endif; ?>

            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Información del Certificado</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="DIAGCERT" class="form-label">Diagnóstico:</label>
                        <textarea name="DIAGCERT" id="DIAGCERT" class="form-control" maxlength="500" rows="3"><?= htmlspecialchars($datos['DIAGCERT'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="RECOMENDACION" class="form-label">Recomendación:</label>
                        <textarea name="RECOMENDACION" id="RECOMENDACION" class="form-control" maxlength="400" rows="3"><?= htmlspecialchars($datos['RECOMENDACION'] ?? '') ?></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="FECHA_CAP" class="form-label">Fecha del Certificado:</label>
                        <input type="date" name="FECHA_CAP" id="FECHA_CAP" class="form-control" value="<?= htmlspecialchars($datos['FECHA_CAP'] ?? date('Y-m-d')) ?>">
                    </div>
                </div>
            </div>

            <div class="d-flex gap-2 mt-4">
                <button type="submit" name="accion" value="guardar" class="btn btn-success btn-lg">💾 Guardar</button>
                <a href="?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&nuevo=1" class="btn btn-secondary btn-lg">🆕 Nuevo</a>
                
                <?php if (!empty($CLAVE) && !$nuevo): ?>
                    <form method="post" action="" onsubmit="return confirm('¿Deseas eliminar este certificado de forma permanente?');" style="display:inline;">
                        <input type="hidden" name="CLAVE_ELIMINAR" value="<?= htmlspecialchars($CLAVE) ?>">
                        <input type="hidden" name="CLAVEPAC" value="<?= htmlspecialchars($CLAVEPAC) ?>">
                        <input type="hidden" name="CEDULA" value="<?= htmlspecialchars($CEDULA) ?>">
                        <button type="submit" name="eliminar" class="btn btn-danger btn-lg">🗑️ Eliminar</button>
                    </form>
                    
                    <div class="dropdown">
                        <button class="btn btn-primary btn-lg dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-print me-2"></i>Imprimir Certificado
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="imprimir_certificado.php?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($CLAVE) ?>" target="_blank">
                                    <i class="fas fa-certificate me-2 text-primary"></i>
                                    <div>
                                        <div class="fw-bold">Certificado Oficial</div>
                                        <small class="text-muted">Formato oficial con membrete</small>
                                    </div>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="imprimir_indicaciones.php?tipo=individual&TABLA=certificado&CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($CLAVE) ?>" target="_blank">
                                    <i class="fas fa-file-alt me-2 text-info"></i>
                                    <div>
                                        <div class="fw-bold">Formato Simple</div>
                                        <small class="text-muted">Formato de indicación médica</small>
                                    </div>
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="imprimir_certificado.php?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=<?= htmlspecialchars($CLAVE) ?>&auto_print=1" target="_blank">
                                    <i class="fas fa-print me-2 text-success"></i>
                                    <div>
                                        <div class="fw-bold">Imprimir Directo</div>
                                        <small class="text-muted">Abre e imprime automáticamente</small>
                                    </div>
                                </a>
                            </li>
                        </ul>
                    </div>
                <?php endif; ?>
                <a href="javascript:history.back()" class="btn btn-outline-secondary btn-lg ms-auto">Volver</a>
            </div>
        </form>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Atajos de teclado para certificados
document.addEventListener('keydown', function(e) {
    // Ctrl+P para imprimir certificado oficial
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        const claveParam = '<?= htmlspecialchars($CLAVE ?? '') ?>';
        if (claveParam) {
            window.open(`imprimir_certificado.php?CLAVEPAC=<?= htmlspecialchars($CLAVEPAC) ?>&CEDULA=<?= htmlspecialchars($CEDULA) ?>&CLAVE=${claveParam}`, '_blank');
        }
    }

    // Ctrl+S para guardar
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        document.querySelector('button[name="accion"][value="guardar"]').click();
    }
});

// Mostrar información de atajos
const shortcutsInfo = document.createElement('div');
shortcutsInfo.className = 'position-fixed bottom-0 end-0 p-3 text-muted small';
shortcutsInfo.style.cssText = 'z-index: 1000; opacity: 0.7;';
shortcutsInfo.innerHTML = `
    <div class="bg-white rounded p-2 shadow-sm border">
        <strong>Atajos Certificado:</strong><br>
        <small>
            Ctrl+S: Guardar<br>
            Ctrl+P: Imprimir
        </small>
    </div>
`;

document.body.appendChild(shortcutsInfo);

// Ocultar después de 8 segundos
setTimeout(() => {
    shortcutsInfo.style.opacity = '0.3';
}, 8000);

shortcutsInfo.addEventListener('mouseenter', () => {
    shortcutsInfo.style.opacity = '1';
});

shortcutsInfo.addEventListener('mouseleave', () => {
    shortcutsInfo.style.opacity = '0.3';
});
</script>

</body>
</html>