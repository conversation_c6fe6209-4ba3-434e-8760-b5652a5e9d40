<?php
$pdo = require_once __DIR__ . '/../config/database.php';

$CLAVEPAC = $_GET['CLAVEPAC'] ?? '';
$CEDULA   = $_GET['CEDULA'] ?? '';

$embarazoActivo = false;
$datosEmbarazo = [];

if ($CLAVEPAC) {
  $stmt = $pdo->prepare("SELECT * FROM EMBARAZO WHERE CLAVEPAC = :CLAVEPAC AND ESTATUSEMB = 'A'");
  $stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
  $datosEmbarazo = $stmt->fetch(PDO::FETCH_ASSOC);
  $embarazoActivo = $datosEmbarazo !== false;
}

if ($_SERVER["REQUEST_METHOD"] === "POST") {
  $fum = $_POST['FUM'] ?? null;
  $accion = $_POST['accion'] ?? '';
  
  if ($accion === 'iniciar' && $fum) {
    $stmt = $pdo->prepare("INSERT INTO EMBARAZO (CLAVEPAC, CEDULA, UUID, FECHAINIEMB, FUM, NUMEMB, ESTATUSEMB)
                           VALUES (:CLAVEPAC, :CEDULA, lower(hex(randomblob(16))), :FECHAINIEMB, :FUM,
                           (SELECT IFNULL(MAX(NUMEMB), 0)+1 FROM EMBARAZO WHERE CLAVEPAC = :CLAVEPAC), 'A')");
    $stmt->execute([
      'CLAVEPAC' => $CLAVEPAC,
      'CEDULA' => $CEDULA,
      'FECHAINIEMB' => date('Y-m-d'),
      'FUM' => $fum
    ]);
  }

  if ($accion === 'finalizar' && $embarazoActivo) {
    $stmt = $pdo->prepare("UPDATE EMBARAZO SET ESTATUSEMB = 'F', FECHAFINEMB = :hoy WHERE CLAVE = :CLAVE");
    $stmt->execute([
      'hoy' => date('Y-m-d'),
      'CLAVE' => $datosEmbarazo['CLAVE']
    ]);
  }

  header("Location: embarazo.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
  exit;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Gestión de Embarazo</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="p-4">
  <h4>Seguimiento de Embarazo</h4>

  <?php if ($embarazoActivo): ?>
    <div class="alert alert-success">
      <strong>Embarazo activo</strong><br>
      <b>FUM:</b> <?= $datosEmbarazo['FUM'] ?><br>
      <b>Edad Gestacional:</b> <span id="eg"></span> semanas<br>
      <b>FPP:</b> <span id="fpp"></span>
    </div>
    <form method="post">
      <input type="hidden" name="accion" value="finalizar">
      <button type="submit" class="btn btn-danger">Finalizar embarazo</button>
    </form>
    <script>
      const fum = new Date("<?= $datosEmbarazo['FUM'] ?>");
      const hoy = new Date();
      const diff = (hoy - fum) / (1000 * 60 * 60 * 24);
      const eg = (diff / 7).toFixed(2);
      const fpp = new Date(fum.getTime() + 280 * 24 * 60 * 60 * 1000);
      document.getElementById("eg").textContent = eg;
      document.getElementById("fpp").textContent = fpp.toISOString().slice(0, 10);
    </script>
  <?php else: ?>
    <form method="post" class="mt-4">
      <div class="mb-3">
        <label for="FUM" class="form-label">Fecha Última Menstruación (FUM)</label>
        <input type="date" name="FUM" id="FUM" class="form-control" required>
      </div>
      <input type="hidden" name="accion" value="iniciar">
      <button type="submit" class="btn btn-success">Iniciar Embarazo</button>
    </form>
  <?php endif; ?>
</body>
</html>
