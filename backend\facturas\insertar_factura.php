<?php
header('Content-Type: application/json; charset=UTF-8');
require_once '../config/database.php';
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);



/**
 * Función para numerar las facturas pares.
 */
// Función para obtener el siguiente número de factura (remoto = pares)
function obtenerSiguienteNumFactRemoto(PDO $pdo): int {
    $stmt = $pdo->query("SELECT MAX(NUMFACT) AS maxfact FROM FACTURAS");
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $maxNum = $row && $row['maxfact'] ? (int)$row['maxfact'] : 1000;

    // En remoto generamos pares
    return ($maxNum % 2 === 0) ? $maxNum + 2 : $maxNum + 1;
}


/**
 * Función para insertar un comprobante.
 */
function insertarComprobante(PDO $pdo, int $clavepac, int $clavefac, string $tipo = 'F', int $caja = 1) {
    try {
        // Obtener el próximo número de comprobante
        $stmt = $pdo->prepare("SELECT MAX(NUMERO) FROM COMPROBANTES WHERE TIPO = ?");
        $stmt->execute([$tipo]);
        $max = $stmt->fetchColumn();
        $nuevoNumero = ($max ?? 0) + 1;

        // Obtener NCF disponible
        $ncfData = $pdo->prepare("SELECT CLAVE, NCF, TIPOCOMPROB FROM COMPROBANTEFISCAL 
                                  WHERE ESTATUS = 'A' 
                                  ORDER BY SECUENCIA ASC LIMIT 1");
        $ncfData->execute();
        $row = $ncfData->fetch(PDO::FETCH_ASSOC);

        if (!$row) {
            return ['status' => 'error', 'mensaje' => 'No hay NCF disponible'];
        }

        // Marcar el NCF como usado
        $update = $pdo->prepare("UPDATE COMPROBANTEFISCAL SET ESTATUS = 'U' WHERE CLAVE = ?");
        $update->execute([$row['CLAVE']]);

        // Insertar en COMPROBANTES
        $insert = $pdo->prepare("INSERT INTO COMPROBANTES 
            (CLAVEPAC, CLAVEFAC, TIPO, NUMERO, FECHA, ESTATUS, CAJA, NCF, TIPOCOMPFISCAL) 
            VALUES (?, ?, ?, ?, CURDATE(), 'A', ?, ?, ?)");
        $insert->execute([
            $clavepac, 
            $clavefac, 
            $tipo, 
            $nuevoNumero, 
            $caja, 
            $row['NCF'], 
            $row['TIPOCOMPROB']
        ]);

        return [
            'status'  => 'success',
            'mensaje' => "Comprobante con NCF generado: {$row['NCF']}",
            'ncf'     => $row['NCF'],
            'numero'  => $nuevoNumero
        ];
    } catch (PDOException $e) {
        return ['status' => 'error', 'mensaje' => 'Error al generar comprobante: ' . $e->getMessage()];
    }
}

try {
    $pdo = include '../config/database.php';

    // Recibir datos del formulario
    $clavepac         = $_POST['clavepac'] ?? null;
    $cedula           = $_POST['cedula'] ?? '';
    $concepto         = $_POST['concepto'] ?? '';
    $modopago         = $_POST['modopago'] ?? '';
    $fechapago        = $_POST['fechapago'] ?? date('Y-m-d');
    $precio           = $_POST['precio'] ?? 0;
    $pagado           = $_POST['pagado'] ?? 0;
    $nopago           = isset($_POST['nopago']) ? (int)$_POST['nopago'] : 1;
    $noautorizacion   = $_POST['noautorizacion'] ?? '';
    $procedimiento    = $_POST['procedimiento'] ?? '';
    $valorreclamado   = $_POST['valorreclamado'] ?? 0;
    $fars             = isset($_POST['FARS']) && trim($_POST['FARS']) !== '' ? $_POST['FARS'] : null;
    $clavefactura     = $_POST['clavefactura'] ?? '';

   
    $fechaproc=date('Y-m-d');
   
    // Variables para cuenta por cobrar (se espera que vengan del formulario)
    // Si el modo de pago es "efectivo" o "gratis", forzamos estos valores a 0
    $modopago_lower = strtolower($modopago);
    if ($modopago_lower == 'efectivo' || $modopago_lower == 'gratis') {
        $plazo = 0;
        $interes = 0;
        $tipointeres = 'F';
        $tipoplazo = 0;
        $intmoratorio = 0;
    } else {
        $plazo = isset($_POST['plazo']) ? (int)$_POST['plazo'] : 0;
        $interes = isset($_POST['interes']) ? (float)$_POST['interes'] : 0;
        $tipointeres = $_POST['tipointeres'] ?? 'F';
        $tipoplazo = isset($_POST['tipoplazo']) ? (int)$_POST['tipoplazo'] : 0;
        $intmoratorio = isset($_POST['intmoratorio']) ? (float)$_POST['intmoratorio'] : 0;
    }

    if (!$clavepac || !$concepto || !$modopago) {
        echo json_encode(["status" => "error", "message" => "Datos obligatorios faltantes."]);
        exit;
    }

    if (!empty($clavefactura)) {
        // Actualizar factura existente (no se generan comprobantes ni se modifica la cuenta por cobrar)
        $sql = "UPDATE FACTURAS 
                SET CLAVEPAC = ?,
                    CEDULA = ?,
                    PRECIO = ?,
                    CONCEPTO = ?,
                    FECHAPAGO = ?,
                    MODOPGO = ?,
                    PAGADO = ?,
                    NOPAGO = ?,
                    PLAZO = ?,
                    INTERES = ?, 
                    NOAUTORIZACION = ?,
                    VALORRECLAMADO = ?,
                    PROCEDIMIENTOS = ?,
                    FECHA = CURDATE(),
                    HORA = CURTIME(),
                    USUARIO = 1,
                    CONSULTORIO = 1,
                    SINCRONIZADO = 0
                WHERE CLAVE = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $clavepac,
            $cedula,
            $precio,
            $concepto,
            $fechapago,
            $modopago,
            $pagado,
            $nopago,
            $plazo,
            $interes,
            $noautorizacion,
            $valorreclamado,
            $procedimiento,
            $clavefactura
        ]);

        echo json_encode([
            "status" => "success",
            "message" => "Factura actualizada exitosamente."
        ]);
    } else {
       
       
       
       
        // Insertar nueva factura
        // Obtener el próximo número de factura
        $numfact = obtenerSiguienteNumFactRemoto($pdo);
        $sql = "INSERT INTO FACTURAS 
            (CLAVEPAC,CEDULA, NUMFACT, FARS, PRECIO, CONCEPTO, FECHAPROC, FECHAPAGO, MODOPGO, ESTATUS, PAGADO, NOPAGO,PLAZO,INTERES, NOAUTORIZACION, VALORRECLAMADO, PROCEDIMIENTOS,STRECLAMA, FECHA, HORA, USUARIO, CONSULTORIO)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'A', ?, ?, ?, ?, ?, ?, ?,'A', CURDATE(), CURTIME(), 1, 1)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $clavepac,
            $cedula,
            $numfact,
            $fars,
            $precio,
            $concepto,
            $fechaproc,
            $fechapago,
            $modopago,
            $pagado,
            $nopago,
            $plazo,
            $interes,
            $noautorizacion,
            $valorreclamado,
            $procedimiento
        ]);

        $clavefac = $pdo->lastInsertId(); // Clave de la factura recién insertada

        // Generar el comprobante relacionado
        $comprobante = insertarComprobante($pdo, $clavepac, $clavefac);

        // Generar la cuenta por cobrar solo si hay deuda (precio > pagado) y el modo de pago no es 'Gratis'
        if ($precio > $pagado && $modopago_lower != 'gratis') {
            $deuda = $precio - $pagado;
            $nopago = max(1, $nopago); // Evitar división entre cero
            $importeCuota = round($deuda / $nopago, 2);
            $sumaImporte = 0;
            for ($k = 1; $k <= $nopago; $k++) {
                $sumaImporte += $importeCuota;
                // Ajustar la última cuota para compensar redondeo
                if ($k == $nopago) {
                    $importeInst = $deuda - ($sumaImporte - $importeCuota);
                } else {
                    $importeInst = $importeCuota;
                }
                // Calcular la fecha del próximo pago: sumar (k * plazo) días a la fecha de pago
                $proximoPago = date('Y-m-d', strtotime($fechapago . " + " . ($k * $plazo) . " days"));
                $sqlXC = "INSERT INTO XCOBRAR 
                    (CLAVEPAC, CLAVEFAC, IMPORTE, INTERES, PLAZO, PROXIMOPAGO, ESTATUS, FECHA, NUMPAGO, TIPOINTERES, TIPOPLAZO, INTMORATORIO, CANTINTMORAT, CANTINTERES, PAGADO, ORDEN)
                    VALUES (?, ?, ?, ?, ?, ?, 'A', CURDATE(), ?, ?, ?, ?, 0, 0, 0, ?)";
                $stmtXC = $pdo->prepare($sqlXC);
                // Formatear el número de pago como "k/nopago"
                $numPagoStr = $k . '/' . $nopago;
                $stmtXC->execute([
                    $clavepac,
                    $clavefac,
                    $importeInst,
                    $interes,
                    $plazo,
                    $proximoPago,
                    $numPagoStr,
                    $tipointeres,
                    $tipoplazo,
                    $intmoratorio,
                    $k  // Orden secuencial
                ]);
            }
        }

        echo json_encode([
            "status" => "success",
            "message" => "Factura guardada exitosamente.",
            "comprobante" => $comprobante
        ]);
    }
} catch (Exception $e) {
    echo json_encode(["status" => "error", "message" => "Error: " . $e->getMessage()]);
}
?>
