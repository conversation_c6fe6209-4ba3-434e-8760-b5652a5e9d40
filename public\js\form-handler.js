document.getElementById('pacienteForm').addEventListener('submit', async function(e) {
    e.preventDefault();  // Previene el comportamiento por defecto del formulario (enviar)

    // Recopila los datos del formulario
    const formData = {
        nombres: document.getElementById('NOMBRES').value,
        apellidos: document.getElementById('APELLIDOS').value,
        cedula: document.getElementById('CEDULA').value,
        sexo: document.getElementById('SEXO').value,
        telefono: document.getElementById('TELEFONO').value,
        celular: document.getElementById('CELULAR').value,  // Asegúrate de que el campo sea 'CELULAR' si existe en tu formulario
        nacionalidad: document.getElementById('NACIONALIDAD').value,
        rh: document.getElementById('RH').value,
        ecorreo: document.getElementById('ECORREO').value
    };

    try {
        // Realiza una solicitud POST al servidor con los datos del formulario
        const response = await fetch('../backend/api/pacientes.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)  // Convierte los datos del formulario en JSON
        });

        // Respuesta del servidor
        const result = await response.json();
        alert(result.message);  // Muestra el mensaje de respuesta
    } catch (error) {
        alert('Error al registrar paciente: ' + error.message);  // Muestra el error si ocurre
    }
});
