<?php

require_once '/backend/config/database.php'; // Asegúrate de tener esta configuración de base de datos

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Obtener datos del formulario
    $usuario = $_POST['usuario'];
    $password = $_POST['password'];
    $email = $_POST['email'];

     echo $usuario;
  
    // Asegurarse de que el campo usuario no está vacío
    if (empty($usuario) || empty($password) || empty($email)) {
        echo "Todos los campos son requeridos.";
    } else {
        // Hashear la contraseña antes de guardarla
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        // Insertar el usuario en la base de datos
        $stmt = $pdo->prepare("INSERT INTO usuarios (usuario, password, email) VALUES (?, ?, ?)");
        $stmt->execute([$usuario, $hashed_password, $email]);

        echo "Usuario registrado con éxito.";
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Usuario</title>
</head>
<body>

<h2>Registro de Usuario</h2>

<form action="registro.php" method="POST">
    <label for="usuario">Usuario:</label>
    <input type="text" name="usuario" required><br>

    <label for="password">Contraseña:</label>
    <input type="password" name="password" required><br>

    <label for="email">Correo Electrónico:</label>
    <input type="email" name="email" required><br>

    <button type="submit">Registrar</button>
</form>

</body>
</html>
