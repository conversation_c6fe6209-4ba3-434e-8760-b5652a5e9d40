<?php
/**
 * Panel del Doctor - Sistema de Consultorio Médico
 */

// Configuración de errores para debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();

// Test simple para verificar que PHP funciona
// echo "PHP está funcionando correctamente"; // Descomenta esta línea para test

// Verificar autenticación y rol
if (!isset($_SESSION['usuario']) || $_SESSION['rol'] !== 'doctor') {
    header('Location: login.php');
    exit;
}

// Configuración de la aplicación
require_once __DIR__ . '/backend/config/database.php';
date_default_timezone_set('America/Santo_Domingo');

// Obtener información del doctor
$usuario = htmlspecialchars($_SESSION['usuario']);
$nombreDoctor = $_SESSION['nombre_completo'] ?? $usuario;

// Obtener información del consultorio - COPIA EXACTA DEL ADMIN
try {
    $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
    $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $empresa = null;
}

// Detectar si es doctora para el saludo
$saludoDoctor = 'Bienvenida';

// Obtener estadísticas del día para el doctor
try {
    // Citas de hoy
    $stmt_citas_hoy = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE()");
    $citas_hoy = $stmt_citas_hoy->fetch()['total'] ?? 0;

    // Citas atendidas hoy
    $stmt_atendidas = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 0");
    $citas_atendidas = $stmt_atendidas->fetch()['total'] ?? 0;

    // Citas pendientes hoy
    $stmt_pendientes = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS IN (3, 5)");
    $citas_pendientes = $stmt_pendientes->fetch()['total'] ?? 0;

    // Total de pacientes registrados
    $stmt_pacientes = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
    $total_pacientes = $stmt_pacientes->fetch()['total'] ?? 0;

    // Próximas citas (siguientes 7 días)
    $stmt_proximas = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON BETWEEN CURDATE() + INTERVAL 1 DAY AND CURDATE() + INTERVAL 7 DAY");
    $proximas_citas = $stmt_proximas->fetch()['total'] ?? 0;

    // Citas de hoy con detalles
    $stmt_citas_detalle = $pdo->query("
        SELECT c.CLAVE, c.HORACON, c.CLAVEPAC, p.NOMBREAPELLIDO, p.CEDULA, c.ESTATUS, c.MODOASISTENCIA
        FROM CITAMEDIC c
        LEFT JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
        WHERE c.FECHACON = CURDATE()
        ORDER BY c.HORACON ASC
    ");
    $citas_detalle = $stmt_citas_detalle->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    $citas_hoy = $citas_atendidas = $citas_pendientes = $total_pacientes = $proximas_citas = 0;
    $citas_detalle = [];
}

// Formatear fecha actual
$fechaActual = date('l, j \d\e F \d\e Y');
$fechaActual = str_replace(
    ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'],
    ['Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado', 'Domingo'],
    $fechaActual
);
$fechaActual = str_replace(
    ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'],
    ['enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio', 'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'],
    $fechaActual
);

// Estados de citas
function getEstadoCita($estatus) {
    $estados = [
        0 => ['texto' => 'Atendido', 'clase' => 'success', 'icono' => 'check-circle-fill'],
        1 => ['texto' => 'Canceló', 'clase' => 'warning', 'icono' => 'x-circle-fill'],
        2 => ['texto' => 'No asistió', 'clase' => 'danger', 'icono' => 'exclamation-triangle-fill'],
        3 => ['texto' => 'Citado', 'clase' => 'primary', 'icono' => 'calendar-check'],
        4 => ['texto' => 'Llegó tarde', 'clase' => 'warning', 'icono' => 'clock-fill'],
        5 => ['texto' => 'Esperando', 'clase' => 'info', 'icono' => 'hourglass-split'],
        6 => ['texto' => 'Pendiente', 'clase' => 'secondary', 'icono' => 'clock']
    ];
    return $estados[$estatus] ?? ['texto' => 'Desconocido', 'clase' => 'secondary', 'icono' => 'question-circle'];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel del Doctor - Consultorio Médico</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --medical-primary: #1e40af;
            --medical-secondary: #3b82f6;
            --medical-success: #10b981;
            --medical-info: #0ea5e9;
            --medical-warning: #f59e0b;
            --medical-danger: #ef4444;
            --medical-light: #f8fafc;
            --medical-dark: #1e293b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            min-height: 100vh;
            color: var(--medical-dark);
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(30, 64, 175, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 600;
            color: var(--medical-primary) !important;
            font-size: 1.3rem;
        }

        .btn-logout {
            background: linear-gradient(135deg, var(--medical-danger) 0%, #dc2626 100%);
            color: white;
            border: none;
            padding: 0.5rem 1.2rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-logout:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
            color: white;
        }

        .main-content {
            padding: 2rem 0;
            max-width: 1400px;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }

        .welcome-section h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .welcome-section p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0.5rem;
        }

        .date-display {
            font-size: 1rem;
            opacity: 0.8;
            font-weight: 300;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--medical-dark);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #64748b;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .feature-card h5 {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--medical-dark);
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #64748b;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .btn-feature {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 10px;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.25rem;
        }

        .btn-feature:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
            color: white;
        }

        .btn-feature.success {
            background: linear-gradient(135deg, var(--medical-success) 0%, #059669 100%);
        }

        .btn-feature.success:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }

        .btn-feature.info {
            background: linear-gradient(135deg, var(--medical-info) 0%, #0284c7 100%);
        }

        .btn-feature.info:hover {
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
        }

        .btn-feature.warning {
            background: linear-gradient(135deg, var(--medical-warning) 0%, #d97706 100%);
        }

        .btn-feature.warning:hover {
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
        }

        .citas-hoy {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .citas-hoy h4 {
            color: var(--medical-primary);
            margin-bottom: 1.5rem;
            font-weight: 600;
        }

        .cita-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            border-left: 4px solid var(--medical-primary);
            transition: all 0.3s ease;
        }

        .cita-item:hover {
            background: rgba(248, 250, 252, 1);
            transform: translateX(5px);
        }

        .cita-hora {
            font-weight: 600;
            color: var(--medical-primary);
            min-width: 80px;
        }

        .cita-paciente {
            flex: 1;
            margin-left: 1rem;
            font-weight: 500;
        }

        @media (max-width: 768px) {
            .welcome-section h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navegación -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-user-md"></i> Panel del Doctor
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="btn btn-logout" href="logout.php">
                            <i class="bi bi-box-arrow-right"></i> Cerrar Sesión
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Contenido Principal -->
    <div class="container main-content">
        <!-- Sección de Bienvenida -->
        <div class="welcome-section">
            <h1><?php echo $saludoDoctor; ?>, <span style="color: #fbbf24;"><?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Sistema de Consultorio'; ?></span></h1>
            <p>Panel de control médico para gestión de pacientes y consultas</p>
            <div class="date-display"><?php echo $fechaActual; ?></div>
        </div>

        <!-- Estadísticas Rápidas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-number"><?php echo $citas_hoy; ?></div>
                <div class="stat-label">Citas Hoy</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-success), #059669);">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?php echo $citas_atendidas; ?></div>
                <div class="stat-label">Atendidas</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-warning), #d97706);">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo $citas_pendientes; ?></div>
                <div class="stat-label">Pendientes</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, var(--medical-info), #0284c7);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo $total_pacientes; ?></div>
                <div class="stat-label">Total Pacientes</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-number"><?php echo $proximas_citas; ?></div>
                <div class="stat-label">Próximas Citas</div>
            </div>
        </div>

        <!-- Funcionalidades Principales -->
        <div class="feature-grid">
            <!-- Historia Clínica -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));">
                    <i class="fas fa-file-medical"></i>
                </div>
                <h5>Historia Clínica</h5>
                <p>Accede y gestiona las historias clínicas completas de tus pacientes, incluyendo antecedentes, diagnósticos y tratamientos.</p>
                <a href="backend/historia/buscar_paciente.php" class="btn btn-feature">
                    <i class="fas fa-search"></i> Buscar Paciente
                </a>
            </div>

            <!-- Gestión de Citas -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-success), #059669);">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <h5>Gestión de Citas</h5>
                <p>Administra tus citas médicas, revisa horarios, actualiza estados y organiza tu agenda diaria.</p>
                <div>
                    <a href="backend/citas/gestion_citas.php" class="btn btn-feature success">
                        <i class="fas fa-calendar-alt"></i> Ver Agenda
                    </a>
                    <a href="backend/citas/crear_cita.php" class="btn btn-feature success">
                        <i class="fas fa-plus"></i> Nueva Cita
                    </a>
                </div>
            </div>

            <!-- Pacientes -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-info), #0284c7);">
                    <i class="fas fa-users"></i>
                </div>
                <h5>Gestión de Pacientes</h5>
                <p>Consulta información de pacientes, actualiza datos personales y revisa historial médico completo.</p>
                <a href="backend/pacientes/gestion_pacientes.php" class="btn btn-feature info">
                    <i class="fas fa-user-friends"></i> Ver Pacientes
                </a>
            </div>

            <!-- Prescripciones -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-prescription-bottle-alt"></i>
                </div>
                <h5>Prescripciones</h5>
                <p>Genera recetas médicas, prescripciones y certificados médicos de forma rápida y profesional.</p>
                <a href="backend/historia/buscar_paciente.php?seccion=prescripcion" class="btn btn-feature success">
                    <i class="fas fa-pills"></i> Prescribir
                </a>
            </div>

            <!-- Reportes Médicos -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, var(--medical-warning), #d97706);">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h5>Reportes Médicos</h5>
                <p>Genera reportes estadísticos, análisis de consultas y seguimiento de tratamientos de tus pacientes.</p>
                <a href="reportes.php" class="btn btn-feature warning">
                    <i class="fas fa-file-chart-line"></i> Ver Reportes
                </a>
            </div>

            <!-- Laboratorio -->
            <div class="feature-card">
                <div class="feature-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-flask"></i>
                </div>
                <h5>Resultados de Laboratorio</h5>
                <p>Revisa y analiza resultados de laboratorio, estudios complementarios y pruebas diagnósticas.</p>
                <a href="backend/historia/buscar_paciente.php?seccion=laboratorio" class="btn btn-feature" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-microscope"></i> Ver Resultados
                </a>
            </div>
        </div>

        <!-- Citas de Hoy -->
        <div class="row">
            <div class="col-12">
                <div class="citas-hoy">
                    <h4><i class="fas fa-calendar-day"></i> Citas de Hoy</h4>

                    <?php if (empty($citas_detalle)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times" style="font-size: 3rem; color: #cbd5e1; margin-bottom: 1rem;"></i>
                            <p class="text-muted">No hay citas programadas para hoy</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($citas_detalle as $cita): ?>
                            <?php $estado = getEstadoCita($cita['ESTATUS']); ?>
                            <div class="cita-item">
                                <div class="cita-hora">
                                    <?php echo date('H:i', strtotime($cita['HORACON'])); ?>
                                </div>
                                <div class="cita-paciente">
                                    <?php echo htmlspecialchars($cita['NOMBREAPELLIDO'] ?? 'Paciente no encontrado'); ?>
                                </div>
                                <div>
                                    <span class="badge bg-<?php echo $estado['clase']; ?>">
                                        <i class="bi bi-<?php echo $estado['icono']; ?>"></i>
                                        <?php echo $estado['texto']; ?>
                                    </span>
                                </div>
                                <div class="ms-2">
                                    <a href="backend/historia/historia_menu.php?CLAVEPAC=<?php echo $cita['CLAVEPAC'] ?? ''; ?>&CEDULA=<?php echo $cita['CEDULA'] ?? ''; ?>"
                                       class="btn btn-sm btn-outline-primary" title="Ver Historia Clínica">
                                        <i class="fas fa-file-medical"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Accesos Rápidos Adicionales -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="citas-hoy">
                    <h4><i class="fas fa-bolt"></i> Accesos Rápidos</h4>
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="backend/citas/crear_cita.php" class="btn btn-feature w-100">
                                <i class="fas fa-plus-circle"></i><br>
                                <small>Nueva Cita</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="backend/historia/buscar_paciente.php" class="btn btn-feature success w-100">
                                <i class="fas fa-search"></i><br>
                                <small>Buscar Paciente</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="backend/pacientes/gestion_pacientes.php" class="btn btn-feature info w-100">
                                <i class="fas fa-user-plus"></i><br>
                                <small>Nuevo Paciente</small>
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="reportes.php" class="btn btn-feature warning w-100">
                                <i class="fas fa-chart-bar"></i><br>
                                <small>Reportes</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script para actualizar estadísticas en tiempo real -->
    <script>
        // Actualizar estadísticas cada 5 minutos
        setInterval(function() {
            fetch('backend/api/dashboard_stats.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Actualizar contadores si existen los elementos
                        const stats = data.data;
                        updateStatIfExists('citas_hoy', stats.citas_hoy);
                        updateStatIfExists('citas_atendidas', stats.citas_atendidas);
                        updateStatIfExists('citas_pendientes', stats.citas_pendientes);
                        updateStatIfExists('total_pacientes', stats.total_pacientes);
                        updateStatIfExists('proximas_citas', stats.proximas_citas);
                    }
                })
                .catch(error => console.log('Error actualizando estadísticas:', error));
        }, 300000); // 5 minutos

        function updateStatIfExists(className, value) {
            const elements = document.querySelectorAll(`.${className} .stat-number`);
            elements.forEach(element => {
                if (element) {
                    element.textContent = value;
                }
            });
        }

        // Mostrar hora actual
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('es-ES', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            const timeElement = document.querySelector('.current-time');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        }

        // Actualizar cada segundo
        setInterval(updateTime, 1000);
        updateTime(); // Llamar inmediatamente
    </script>
</body>
</html>
