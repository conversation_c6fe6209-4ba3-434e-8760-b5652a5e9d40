/* Estilo general */
body {
    margin: 0;
    font-family: '<PERSON>o', sans-serif;
    background-color: #f4f4f9;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

/* Contenedor del formulario */
.login-container {
    width: 100%;
    max-width: 400px;
    padding: 20px;
    box-sizing: border-box;
}

/* Tarjeta de login */
.login-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

/* Título */
.login-title {
    font-size: 24px;
    color: #333;
    text-align: center;
    margin-bottom: 20px;
}

/* Campos del formulario */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
    color: #666;
}

input {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-sizing: border-box;
    outline: none;
}

input:focus {
    border-color: #0056b3;
    box-shadow: 0 0 5px rgba(0, 86, 179, 0.5);
}

/* Botón de inicio de sesión */
.btn-login {
    width: 100%;
    padding: 10px;
    background-color: #0056b3;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-login:hover {
    background-color: #003f8a;
}

/* Enlaces */
.login-links {
    margin-top: 15px;
    text-align: center;
}

.login-links a {
    text-decoration: none;
    color: #0056b3;
    font-size: 14px;
}

.login-links a:hover {
    text-decoration: underline;
}

table .fas {
    font-size: 18px;
    color: #007bff;
    margin-right: 10px;
    text-decoration: none;
}


.registro-link {
    color: #007bff;
    text-decoration: none;
    font-size: 0.95em;
}

.registro-link:hover {
    text-decoration: underline;
}

table .fas:hover {
    color: #dc3545;
}

/* Estilo para las filas impares */
tr:nth-child(odd) {
    background-color: #f2f2f2; /* Color de fondo para las filas impares (gris claro) */
}

/* Estilo para las filas pares */
tr:nth-child(even) {
    background-color: #ffffff; /* Color de fondo para las filas pares (blanco) */
}

