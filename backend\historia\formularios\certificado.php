<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Certificado Médico" en Indicaciones
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Obtener datos del paciente
$stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
$stmt->execute([$clavePac]);
$paciente = $stmt->fetch(PDO::FETCH_ASSOC);

// Función para obtener los certificados del paciente
function getCertificados($pdo, $clavePac) {
    $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha, 
            MOTIVO as motivo, DIAGNOSTICO as diagnostico, REPOSO as reposo, 
            DIAS_REPOSO as dias_reposo, OBSERVACIONES as observaciones,
            TIPO as tipo, FECHA_INICIO_REPOSO as fecha_inicio_reposo,
            FECHA_FIN_REPOSO as fecha_fin_reposo
            FROM CERTIFICADOS WHERE CLAVEPAC = ? ORDER BY FECHA_CAP DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    if ($action === 'saveCertificado') {
        $motivo = $_POST['motivo'];
        $diagnostico = $_POST['diagnostico'];
        $reposo = $_POST['reposo'];
        $diasReposo = $_POST['diasReposo'] ?: null;
        $observaciones = $_POST['observaciones'];
        $tipo = $_POST['tipo'] ?? 'Médico';
        $fecha_inicio_reposo = $_POST['fecha_inicio_reposo'] ?? null;
        $fecha_fin_reposo = $_POST['fecha_fin_reposo'] ?? null;
        
        try {
            $sql = "INSERT INTO CERTIFICADOS (CLAVEPAC, MOTIVO, DIAGNOSTICO, REPOSO, DIAS_REPOSO, OBSERVACIONES, TIPO, FECHA_INICIO_REPOSO, FECHA_FIN_REPOSO) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$clavePac, $motivo, $diagnostico, $reposo, $diasReposo, $observaciones, $tipo, $fecha_inicio_reposo, $fecha_fin_reposo]);
        
            $id = $pdo->lastInsertId();
        
            echo json_encode([
                'success' => true, 
                'id' => $id,
                'fecha' => date('Y-m-d')
            ]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteCertificado') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "DELETE FROM CERTIFICADOS WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'printCertificado') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "SELECT c.*, p.NOMBRES, p.APELLIDOS, p.CEDULA, p.FECHA_NAC, p.SEXO 
                    FROM CERTIFICADOS c 
                    JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE 
                    WHERE c.CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            $certificado = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($certificado) {
                echo json_encode([
                    'success' => true,
                    'certificado' => $certificado
                ]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Certificado no encontrado']);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Obtener lista de certificados
$certificados = getCertificados($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Certificado Médico</h4>
    
    <div class="row">
        <div class="col-md-5">
            <div class="card">
                <div class="card-header">
                    Nuevo Certificado
                </div>
                <div class="card-body">
                    <form id="formCertificado">
                        <div class="form-group">
                            <label for="motivoCertificado">Motivo del Certificado</label>
                            <input type="text" class="form-control" id="motivoCertificado" placeholder="Ej: Constancia médica">
                        </div>
                        <div class="form-group">
                            <label for="tipoCertificado">Tipo de Certificado</label>
                            <select class="form-control" id="tipoCertificado" name="tipo">
                                <option value="Médico">Médico</option>
                                <option value="Aptitud Física">Aptitud Física</option>
                                <option value="Incapacidad">Incapacidad</option>
                                <option value="Otro">Otro</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="diagnosticoCertificado">Diagnóstico</label>
                            <textarea class="form-control" id="diagnosticoCertificado" rows="2"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Requiere Reposo</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="reposoCertificado" id="reposoSi" value="S">
                                <label class="form-check-label" for="reposoSi">Sí</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="reposoCertificado" id="reposoNo" value="N" checked>
                                <label class="form-check-label" for="reposoNo">No</label>
                            </div>
                        </div>
                        <div class="form-group" id="diasReposoGroup" style="display: none;">
                            <label for="diasReposo">Días de Reposo</label>
                            <input type="number" class="form-control" id="diasReposo" min="1">
                        </div>
                        <div class="form-group" id="fechasReposoGroup" style="display: none;">
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="fechaInicioReposo">Fecha Inicio Reposo</label>
                                    <input type="date" class="form-control" id="fechaInicioReposo" name="fecha_inicio_reposo">
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="fechaFinReposo">Fecha Fin Reposo</label>
                                    <input type="date" class="form-control" id="fechaFinReposo" name="fecha_fin_reposo">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="observacionesCertificado">Observaciones</label>
                            <textarea class="form-control" id="observacionesCertificado" rows="3"></textarea>
                        </div>
                        <button type="button" class="btn btn-primary" id="btnGuardarCertificado">Guardar Certificado</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-7">
            <div class="card">
                <div class="card-header">
                    Certificados Emitidos
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>Fecha</th>
                                    <th>Tipo</th>
                                    <th>Motivo</th>
                                    <th>Diagnóstico</th>
                                    <th>Reposo</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody id="tablaCertificados">
                                <?php if (count($certificados) > 0): ?>
                                    <?php foreach ($certificados as $certificado): ?>
                                        <tr data-id="<?php echo $certificado['id']; ?>">
                                            <td><?php echo $certificado['fecha']; ?></td>
                                            <td><?php echo htmlspecialchars($certificado['tipo'] ?? 'Médico'); ?></td>
                                            <td><?php echo htmlspecialchars($certificado['motivo']); ?></td>
                                            <td><?php echo htmlspecialchars($certificado['diagnostico']); ?></td>
                                            <td>
                                                <?php if ($certificado['reposo'] == 'S'): ?>
                                                    Sí, <?php echo $certificado['dias_reposo']; ?> días
                                                    <?php if (!empty($certificado['fecha_inicio_reposo']) && !empty($certificado['fecha_fin_reposo'])): ?>
                                                        <small class="d-block text-muted">
                                                            Del <?php echo date('d/m/Y', strtotime($certificado['fecha_inicio_reposo'])); ?> 
                                                            al <?php echo date('d/m/Y', strtotime($certificado['fecha_fin_reposo'])); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    No
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-primary btn-imprimir-certificado">Imprimir</button>
                                                <button class="btn btn-sm btn-danger btn-eliminar-certificado">Eliminar</button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="6" class="text-center">No hay certificados emitidos</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal para imprimir certificado -->
    <div class="modal fade" id="modalCertificado" tabindex="-1" role="dialog" aria-labelledby="modalCertificadoLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalCertificadoLabel">Certificado Médico</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="certificadoContent">
                    <!-- El contenido del certificado se cargará aquí -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                    <button type="button" class="btn btn-primary" id="btnImprimirCertificado">Imprimir</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Mostrar/ocultar campo de fechas de reposo
    $('input[name="reposoCertificado"]').change(function() {
        if ($(this).val() === 'S') {
            $('#diasReposoGroup').show();
            $('#fechasReposoGroup').show();
        } else {
            $('#diasReposoGroup').hide();
            $('#fechasReposoGroup').hide();
        }
    });
    
    // Guardar nuevo certificado
    $('#btnGuardarCertificado').click(function() {
        const motivo = $('#motivoCertificado').val();
        const diagnostico = $('#diagnosticoCertificado').val();
        
        if (!motivo || !diagnostico) {
            alert('Por favor complete al menos los campos de Motivo y Diagnóstico');
            return;
        }
        
        const reposo = $('input[name="reposoCertificado"]:checked').val();
        const diasReposo = reposo === 'S' ? $('#diasReposo').val() : '';
        const observaciones = $('#observacionesCertificado').val();
        const tipo = $('#tipoCertificado').val();
        const fecha_inicio_reposo = reposo === 'S' ? $('#fechaInicioReposo').val() : '';
        const fecha_fin_reposo = reposo === 'S' ? $('#fechaFinReposo').val() : '';
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'saveCertificado',
                motivo: motivo,
                diagnostico: diagnostico,
                reposo: reposo,
                diasReposo: diasReposo,
                observaciones: observaciones,
                tipo: tipo,
                fecha_inicio_reposo: fecha_inicio_reposo,
                fecha_fin_reposo: fecha_fin_reposo
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Limpiar formulario
                    $('#formCertificado')[0].reset();
                    $('#diasReposoGroup').hide();
                    $('#fechasReposoGroup').hide();
                    
                    // Agregar a la tabla
                    const reposoText = reposo === 'S' ? `Sí, ${diasReposo} días` : 'No';
                    const fechasReposoText = (reposo === 'S' && fecha_inicio_reposo && fecha_fin_reposo) ? 
                        `<small class="d-block text-muted">Del ${formatDate(fecha_inicio_reposo)} al ${formatDate(fecha_fin_reposo)}</small>` : '';
                    
                    const newRow = `
                        <tr data-id="${response.id}">
                            <td>${response.fecha}</td>
                            <td>${tipo}</td>
                            <td>${motivo}</td>
                            <td>${diagnostico}</td>
                            <td>
                                ${reposoText}
                                ${fechasReposoText}
                            </td>
                            <td>
                                <button class="btn btn-sm btn-primary btn-imprimir-certificado">Imprimir</button>
                                <button class="btn btn-sm btn-danger btn-eliminar-certificado">Eliminar</button>
                            </td>
                        </tr>
                    `;
                    
                    // Si la tabla está vacía, eliminar la fila de "No hay certificados"
                    if ($('#tablaCertificados tr td').length === 1 && $('#tablaCertificados tr td').text().includes('No hay certificados')) {
                        $('#tablaCertificados').empty();
                    }
                    
                    $('#tablaCertificados').prepend(newRow);
                    
                    alert('Certificado guardado correctamente');
                } else {
                    alert('Error al guardar certificado: ' + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al guardar certificado:', error);
                alert('Error al guardar certificado. Consulte la consola para más detalles.');
            }
        });
    });
    
    // Eliminar certificado
    $(document).on('click', '.btn-eliminar-certificado', function() {
        if (confirm('¿Está seguro de eliminar este certificado?')) {
            const row = $(this).closest('tr');
            const id = row.data('id');
            
            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: {
                    action: 'deleteCertificado',
                    id: id
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        row.remove();
                        
                        // Si no quedan filas, mostrar mensaje
                        if ($('#tablaCertificados tr').length === 0) {
                            $('#tablaCertificados').html('<tr><td colspan="5" class="text-center">No hay certificados emitidos</td></tr>');
                        }
                        
                        alert('Certificado eliminado correctamente');
                    } else {
                        alert('Error al eliminar certificado: ' + response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error al eliminar certificado:', error);
                    alert('Error al eliminar certificado. Consulte la consola para más detalles.');
                }
            });
        }
    });
    
    // Imprimir certificado
    $(document).on('click', '.btn-imprimir-certificado', function() {
        const id = $(this).closest('tr').data('id');
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'printCertificado',
                id: id
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    const cert = response.certificado;
                    const fechaEmision = new Date(cert.FECHA_CAP).toLocaleDateString('es-ES');
                    
                    // Calcular edad
                    const fechaNac = new Date(cert.FECHA_NAC);
                    const hoy = new Date();
                    let edad = hoy.getFullYear() - fechaNac.getFullYear();
                    const m = hoy.getMonth() - fechaNac.getMonth();
                    if (m < 0 || (m === 0 && hoy.getDate() < fechaNac.getDate())) {
                        edad--;
                    }
                    
                    // Generar contenido del certificado
                    let certificadoHTML = `
                        <div class="certificado-medico">
                            <div class="text-center mb-4">
                                <h4>CERTIFICADO MÉDICO</h4>
                            </div>
                            
                            <p>Fecha: ${fechaEmision}</p>
                            
                            <p>Por medio de la presente, certifico que el/la paciente:</p>
                            
                            <div class="datos-paciente mb-3">
                                <p><strong>Nombre:</strong> ${cert.NOMBRES} ${cert.APELLIDOS}</p>
                                <p><strong>Cédula:</strong> ${cert.CEDULA}</p>
                                <p><strong>Edad:</strong> ${edad} años</p>
                                <p><strong>Sexo:</strong> ${cert.SEXO === 'M' ? 'Masculino' : 'Femenino'}</p>
                            </div>
                            
                            <p><strong>Motivo:</strong> ${cert.MOTIVO}</p>
                            <p><strong>Diagnóstico:</strong> ${cert.DIAGNOSTICO}</p>
                            
                            ${cert.REPOSO === 'S' ? `
                                <p>Se le indica reposo médico por ${cert.DIAS_REPOSO} día(s).</p>
                            ` : ''}
                            
                            ${cert.OBSERVACIONES ? `
                                <p><strong>Observaciones:</strong> ${cert.OBSERVACIONES}</p>
                            ` : ''}
                            
                            <div class="firma mt-5 text-center">
                                <div class="linea-firma">____________________________</div>
                                <p>Firma y Sello del Médico</p>
                            </div>
                        </div>
                    `;
                    
                    // Mostrar en el modal
                    $('#certificadoContent').html(certificadoHTML);
                    $('#modalCertificado').modal('show');
                } else {
                    alert('Error al cargar certificado: ' + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error al cargar certificado:', error);
                alert('Error al cargar certificado. Consulte la consola para más detalles.');
            }
        });
    });
    
    // Imprimir certificado desde el modal
    $('#btnImprimirCertificado').click(function() {
        const contenido = document.getElementById('certificadoContent').innerHTML;
        const ventanaImpresion = window.open('', '_blank');
        
        ventanaImpresion.document.write(`
            <html>
                <head>
                    <title>Certificado Médico</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .certificado-medico { max-width: 800px; margin: 0 auto; }
                        .linea-firma { width: 200px; margin: 0 auto; border-top: 1px solid #000; }
                    </style>
                </head>
                <body>
                    ${contenido}
                </body>
            </html>
        `);
        
        ventanaImpresion.document.close();
        ventanaImpresion.focus();
        ventanaImpresion.print();
        // ventanaImpresion.close(); // Opcional: cerrar después de imprimir
    });
});

// Función auxiliar para formatear fechas
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES');
}
</script>
