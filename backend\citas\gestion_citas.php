<?php
session_start();

if (!isset($_SESSION['usuario'])) {
    header('Location: ../../login.php');
    exit();
}

require_once '../config/database.php'; // Ajusta la ruta según corresponda
date_default_timezone_set('America/Santo_Domingo');

// 1) Determinar el filtro (hoy, pasadas, futuras)
$filter = isset($_GET['filtro']) ? $_GET['filtro'] : 'hoy';
$fechaHoy = date('Y-m-d');

switch ($filter) {
    case 'pasadas':
        $condicion = "c.FECHACON < '$fechaHoy'";
        break;
    case 'futuras':
        $condicion = "c.FECHACON > '$fechaHoy'";
        break;
    case 'hoy':
    default:
        $condicion = "c.FECHACON = '$fechaHoy'";
        break;
}

// 2) Consulta para obtener las citas según el filtro
$sqlCitas = "
    SELECT 
        c.CLAVE AS ID,
         p.CE<PERSON>,
        p.NOMBREAPELLIDO AS PACIENTE,
      
        c.FECHACON AS FECHA,
        c.HORACON AS HORA,
        CASE c.ESTATUS 
            WHEN 0 THEN 'Atendido'
            WHEN 1 THEN 'Canceló'
            WHEN 2 THEN 'No asistió'
            WHEN 3 THEN 'Citado'
            WHEN 4 THEN 'Llegó tarde'
            WHEN 5 THEN 'Esperando'
            WHEN 6 THEN 'Pendiente aprobación'
            ELSE 'Desconocido'
        END AS ESTATUS
    FROM CITAMEDIC c
    JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
    WHERE $condicion
    ORDER BY c.FECHACON, c.HORACON
";

$stmt = $pdo->prepare($sqlCitas);
$stmt->execute();
$citas = $stmt->fetchAll(PDO::FETCH_ASSOC);

/**
 * Función que devuelve la clase CSS correspondiente según el estatus.
 *
 * @param string $estatus El estatus (texto)
 * @return string La clase CSS definida en tu hoja de estilos
 */
function getStatusClass($estatus) {
    $estatusLower = strtolower(trim($estatus));
    switch ($estatusLower) {
        case 'atendido':
            return 'status-atendido';
        case 'esperando':
            return 'status-esperando';
        case 'citado':
            return 'status-citado';
        case 'canceló':
        case 'cancelado':
            return 'status-cancelo';
        case 'no asistió':
        case 'no asistio':
            return 'status-no-asistio';
      
      case 'Pendiente aprobación':
            return 'status-pendiente-aprobacion';
      
      
        default:
            return '';
    }
}
?>




<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gestión de Citas</title>
  
  <!-- Tus hojas de estilo personalizadas -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
  <!-- Font Awesome para iconos -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <link rel="stylesheet" href="../../public/assets/css/stylescitas.css">
  <link rel="stylesheet" href="../../public/assets/css/stylescitas2.css">
  
  <!-- Font Awesome para iconos -->
 
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
 
  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
  
  <!-- jQuery completo (no la versión slim) -->
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
  <!-- DataTables JS -->
  <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
  
  <script>
  $(document).ready(function() {
      // Inicializar DataTables en la tabla con id "tablaCitas"
      $('#tablaCitas').DataTable({
        ordering: true,
        searching: true,
        paging: true,
        info: true,
        // Para que al hacer clic en cada cabecera se ordene la columna:
        order: [],
        language: {
          search: "Buscar:",
          lengthMenu: "Mostrar _MENU_ entradas",
          zeroRecords: "No se encontraron resultados",
         info: "Mostrando _START_ a _END_ de _TOTAL_ citas",
          emptyTable: "No hay citas disponibles",
          infoEmpty: "No hay pacientes disponibles",
          paginate: {
            first: "Primero",
            last: "Último",
            next: "Siguiente",
            previous: "Anterior"
          }
          
          
        }
      });
  });
  </script>
  
  <style>
    /* Botón de regresar médico */
    .medical-back-btn {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%) !important;
        border: none !important;
        color: white !important;
        padding: 0.75rem 1.5rem !important;
        border-radius: 15px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3) !important;
        text-decoration: none !important;
        display: inline-flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    .medical-back-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4) !important;
        color: white !important;
        text-decoration: none !important;
    }

    .medical-back-btn:active {
        transform: translateY(0) !important;
    }

    .medical-back-btn i {
        transition: transform 0.3s ease !important;
    }

    .medical-back-btn:hover i {
        transform: translateX(-3px) !important;
    }

    /* Si requieres estilos adicionales, agrégalos aquí */
  </style>
</head>
<body>
  <div class="container mt-4">
    <!-- Botón de regresar -->
    <div class="d-flex align-items-center justify-content-between mb-4">
        <a href="../../index.php" class="btn btn-outline-primary btn-lg medical-back-btn">
            <i class="fas fa-arrow-left me-2"></i>
            Regresar al Inicio
        </a>
    </div>

    <h1 class="text-center">Gestión de Citas</h1>
    
    
    
    <div class="filters mb-3 text-center">
  <a href="gestion_citas.php?filtro=hoy" class="btn btn-success <?php echo ($filter === 'hoy') ? 'active' : ''; ?>">
    <i class="fas fa-calendar-day"></i> Citas de Hoy
  </a>
  <a href="gestion_citas.php?filtro=pasadas" class="btn btn-info <?php echo ($filter === 'pasadas') ? 'active' : ''; ?>">
    <i class="fas fa-history"></i> Citas Pasadas
  </a>
  <a href="gestion_citas.php?filtro=futuras" class="btn btn-primary <?php echo ($filter === 'futuras') ? 'active' : ''; ?>">
    <i class="fas fa-arrow-right"></i> Citas Futuras
  </a>
  <a href="../pacientes/gestion_pacientes.php" class="btn btn-warning">
    <i class="fas fa-user-plus"></i> Gestionar Pacientes
  </a>
</div>
    
    <h2 class="mb-3">
      <?php 
        if ($filter === 'hoy') echo "Citas de Hoy";
        elseif ($filter === 'pasadas') echo "Citas Pasadas";
        else echo "Citas Futuras";
      ?>
    </h3>
    
    <?php if (!empty($citas)): ?>
      <table id="tablaCitas" class="table table-bordered table-hover">
        <thead class="thead-light">
          <tr>
            <th>ID</th>
            <th>Paciente</th>
            <th>Fecha</th>
            <th>Hora</th>
            <th>Estatus</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($citas as $cita): ?>
            <tr class="<?php echo getStatusClass($cita['ESTATUS']); ?>">
              <td><?= htmlspecialchars($cita['ID']) ?></td>
              <td><?= htmlspecialchars($cita['PACIENTE']) ?></td>
              <td><?= htmlspecialchars($cita['FECHA']) ?></td>
              <td><?= htmlspecialchars($cita['HORA']) ?></td>
              <td><?= htmlspecialchars($cita['ESTATUS']) ?></td>
              <td>
                <!-- Botón para Marcar como Atendido -->
                <a href="crear_cita.php?atender=<?= htmlspecialchars($cita['ID']) ?>" class="btn btn-success btn-sm">
                  <i class="fas fa-check-circle"></i> Atender
                </a>
               <!-- Botón para Editar Cita (solo ícono) -->
<a href="editar_cita.php?clave=<?= htmlspecialchars($cita['ID']) ?>" 
   class="icon-btn text-info" 
   title="Editar">
  <i class="fas fa-edit"></i>
</a>

<!-- Botón para Eliminar Cita (solo ícono) -->
<a href="eliminar_cita.php?atender=<?= htmlspecialchars($cita['ID']) ?>" 
   class="icon-btn text-danger" 
   title="Eliminar"
   onclick="return confirm('¿Estás seguro de eliminar esta cita?');">
  <i class="fas fa-trash-alt"></i>
</a>

<!-- Botón para Crear Cita (solo ícono) -->
<a href="crear_cita.php?pre=<?= htmlspecialchars($cita['CEDULA']) ?>" 
   class="icon-btn text-warning" 
   title="Crear Cita">
  <i class="fas fa-calendar-plus"></i>
</a>

<!-- Botón para Ir a Facturación -->
<a href="../facturas/facturacion.php?q=<?= urlencode($cita['CEDULA']) ?>" 
   class="icon-btn text-primary" 
   title="Ir a Facturación">
  <i class="fas fa-file-invoice-dollar"></i>
</a>



              </td>
            </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p class="alert alert-info">
        No hay citas <?php echo ($filter === 'hoy') ? "para hoy." : (($filter === 'pasadas') ? "pasadas." : "futuras."); ?>
      </p>
    <?php endif; ?>
  </div>
</body>
</html>
