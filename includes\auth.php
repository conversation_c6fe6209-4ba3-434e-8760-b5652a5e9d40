<?php
// auth.php
session_start(); // Asegurarse de que la sesión esté iniciada

function requireRole($rol) {
    if (!isset($_SESSION['rol']) || $_SESSION['rol'] !== $rol) {
        header('Location: no_autorizado.php');
        exit();
    }
}

// Si se quiere permitir más de un rol:
function requireRoles(array $roles) {
    if (!isset($_SESSION['rol']) || !in_array($_SESSION['rol'], $roles)) {
        header('Location: no_autorizado.php');
        exit();
    }
}
?>