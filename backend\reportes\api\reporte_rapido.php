<?php
// backend/reportes/api/reporte_rapido.php
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario'])) {
    http_response_code(401);
    echo json_encode(['error' => 'No autorizado']);
    exit;
}

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../lib/ReportePDF.php';

// Obtener tipo de reporte
$input = json_decode(file_get_contents('php://input'), true);
$tipo = $input['tipo'] ?? 'resumen_general';

// Obtener información de la empresa
$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);
$nombre_empresa = $empresa ? $empresa['NOMBRE'] : 'Consultorio Médico';

try {
    switch ($tipo) {
        case 'citas_hoy':
            $stmt = $pdo->query("
                SELECT c.HORACON, p.NOMBREAPELLIDO, c.ESTATUS,
                       CASE c.ESTATUS
                           WHEN 0 THEN 'Atendido'
                           WHEN 1 THEN 'Canceló'
                           WHEN 2 THEN 'No asistió'
                           WHEN 3 THEN 'Citado'
                           WHEN 4 THEN 'Llegó tarde'
                           WHEN 5 THEN 'Esperando'
                           WHEN 6 THEN 'Pendiente aprobación'
                           ELSE 'Desconocido'
                       END as estado_texto
                FROM CITAMEDIC c
                JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
                WHERE c.FECHACON = CURDATE()
                ORDER BY c.HORACON
            ");
            $citas = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Crear reporte PDF
            $pdf = new ReportePDF('Citas del Día', $nombre_empresa);
            $pdf->iniciarHTML()
               ->agregarEncabezado('', 'Fecha: ' . date('d/m/Y'));

            // Estadísticas rápidas
            $total_citas = count($citas);
            $atendidas = count(array_filter($citas, function($c) { return $c['ESTATUS'] == 0; }));
            $pendientes = count(array_filter($citas, function($c) { return $c['ESTATUS'] == 3 || $c['ESTATUS'] == 6; }));

            $estadisticas = [
                ['valor' => $total_citas, 'etiqueta' => 'Total Citas'],
                ['valor' => $atendidas, 'etiqueta' => 'Atendidas'],
                ['valor' => $pendientes, 'etiqueta' => 'Pendientes']
            ];

            $pdf->agregarSeccionEstadisticas('Resumen del Día', $estadisticas);

            // Tabla de citas
            $encabezados = ['Hora', 'Paciente', 'Estado'];
            $datos_tabla = [];

            foreach ($citas as $cita) {
                $clase_estado = '';
                switch ($cita['ESTATUS']) {
                    case 0: $clase_estado = 'estado-atendido'; break;
                    case 2: $clase_estado = 'estado-no-asistio'; break;
                    case 3: $clase_estado = 'estado-citado'; break;
                    case 6: $clase_estado = 'estado-pendiente'; break;
                }

                $datos_tabla[] = [
                    ReportePDF::formatearHora($cita['HORACON']),
                    htmlspecialchars($cita['NOMBREAPELLIDO']),
                    "<span class='$clase_estado'>" . $cita['estado_texto'] . "</span>"
                ];
            }

            $pdf->agregarTabla('Agenda del Día', $encabezados, $datos_tabla);
            $pdf->finalizarHTML();

            $resultado = $pdf->generarPDF('citas_hoy_' . date('Y-m-d'));
            break;
            
        case 'pacientes_nuevos':
            $stmt = $pdo->query("
                SELECT NOMBREAPELLIDO, CEDULA, FECHAINGRESO, TELEFONO
                FROM PACIENTES 
                WHERE MONTH(FECHAINGRESO) = MONTH(CURDATE()) 
                AND YEAR(FECHAINGRESO) = YEAR(CURDATE())
                ORDER BY FECHAINGRESO DESC
            ");
            $pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $contenido = "<h2>Pacientes Nuevos - " . date('F Y') . "</h2>";
            $contenido .= "<table>";
            $contenido .= "<tr><th>Nombre</th><th>Cédula</th><th>Fecha Ingreso</th><th>Teléfono</th></tr>";
            
            foreach ($pacientes as $paciente) {
                $contenido .= "<tr>";
                $contenido .= "<td>" . htmlspecialchars($paciente['NOMBREAPELLIDO']) . "</td>";
                $contenido .= "<td>" . htmlspecialchars($paciente['CEDULA']) . "</td>";
                $contenido .= "<td>" . date('d/m/Y', strtotime($paciente['FECHAINGRESO'])) . "</td>";
                $contenido .= "<td>" . htmlspecialchars($paciente['TELEFONO'] ?? 'N/A') . "</td>";
                $contenido .= "</tr>";
            }
            
            $contenido .= "</table>";
            $contenido .= "<p><strong>Total de pacientes nuevos: " . count($pacientes) . "</strong></p>";
            
            // Crear reporte simple para pacientes nuevos
            $pdf = new ReportePDF('Pacientes Nuevos del Mes', $nombre_empresa);
            $pdf->iniciarHTML()
               ->agregarEncabezado('', 'Mes: ' . date('F Y'));

            $encabezados = ['Nombre', 'Cédula', 'Fecha Ingreso', 'Teléfono'];
            $datos_tabla = [];

            foreach ($pacientes as $paciente) {
                $datos_tabla[] = [
                    htmlspecialchars($paciente['NOMBREAPELLIDO']),
                    htmlspecialchars($paciente['CEDULA']),
                    ReportePDF::formatearFecha($paciente['FECHAINGRESO']),
                    htmlspecialchars($paciente['TELEFONO'] ?? 'N/A')
                ];
            }

            $pdf->agregarTabla('Nuevos Pacientes', $encabezados, $datos_tabla);
            $pdf->finalizarHTML();

            $resultado = $pdf->generarPDF('pacientes_nuevos_' . date('Y-m'));
            break;

        case 'ingresos_mes':
            // Simplificado para reporte rápido
            $pdf = new ReportePDF('Ingresos del Mes', $nombre_empresa);
            $pdf->iniciarHTML()
               ->agregarEncabezado('', 'Mes: ' . date('F Y'));

            $stmt = $pdo->query("
                SELECT COALESCE(SUM(PAGADO), 0) as total_ingresos, COUNT(*) as total_facturas
                FROM FACTURAS
                WHERE MONTH(FECHA) = MONTH(CURDATE())
                AND YEAR(FECHA) = YEAR(CURDATE())
                AND ESTATUS = 'A'
            ");
            $resumen = $stmt->fetch(PDO::FETCH_ASSOC);

            $estadisticas = [
                ['valor' => ReportePDF::formatearMoneda($resumen['total_ingresos']), 'etiqueta' => 'Total Ingresos'],
                ['valor' => $resumen['total_facturas'], 'etiqueta' => 'Total Facturas']
            ];

            $pdf->agregarSeccionEstadisticas('Resumen Financiero', $estadisticas);
            $pdf->finalizarHTML();

            $resultado = $pdf->generarPDF('ingresos_mes_' . date('Y-m'));
            break;

        case 'resumen_general':
        default:
            // Estadísticas generales
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
            $total_pacientes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE()");
            $citas_hoy = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            $stmt = $pdo->query("
                SELECT COALESCE(SUM(PAGADO), 0) as total
                FROM FACTURAS
                WHERE MONTH(FECHA) = MONTH(CURDATE())
                AND YEAR(FECHA) = YEAR(CURDATE())
                AND ESTATUS = 'A'
            ");
            $ingresos_mes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            $stmt = $pdo->query("
                SELECT COUNT(*) as total
                FROM EXAMENFISICO
                WHERE MONTH(FECHA_CAP) = MONTH(CURDATE())
                AND YEAR(FECHA_CAP) = YEAR(CURDATE())
            ");
            $examenes_mes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

            // Crear reporte de resumen
            $pdf = new ReportePDF('Resumen General del Sistema', $nombre_empresa);
            $pdf->iniciarHTML()
               ->agregarEncabezado();

            $estadisticas = [
                ['valor' => $total_pacientes, 'etiqueta' => 'Total Pacientes'],
                ['valor' => $citas_hoy, 'etiqueta' => 'Citas Hoy'],
                ['valor' => ReportePDF::formatearMoneda($ingresos_mes), 'etiqueta' => 'Ingresos del Mes'],
                ['valor' => $examenes_mes, 'etiqueta' => 'Exámenes del Mes']
            ];

            $pdf->agregarSeccionEstadisticas('Indicadores Principales', $estadisticas);

            // Estado de citas de hoy
            $stmt = $pdo->query("
                SELECT
                    CASE ESTATUS
                        WHEN 0 THEN 'Atendido'
                        WHEN 1 THEN 'Canceló'
                        WHEN 2 THEN 'No asistió'
                        WHEN 3 THEN 'Citado'
                        WHEN 4 THEN 'Llegó tarde'
                        WHEN 5 THEN 'Esperando'
                        WHEN 6 THEN 'Pendiente aprobación'
                        ELSE 'Otro'
                    END as estado,
                    COUNT(*) as cantidad
                FROM CITAMEDIC
                WHERE FECHACON = CURDATE()
                GROUP BY ESTATUS
            ");
            $estados_citas = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($estados_citas)) {
                $encabezados = ['Estado', 'Cantidad'];
                $datos_tabla = [];

                foreach ($estados_citas as $estado) {
                    $datos_tabla[] = [$estado['estado'], $estado['cantidad']];
                }

                $pdf->agregarTabla('Estado de Citas de Hoy', $encabezados, $datos_tabla);
            }

            $pdf->finalizarHTML();
            $resultado = $pdf->generarPDF('resumen_general_' . date('Y-m-d'));
            break;
    }

    // Devolver el resultado
    echo $resultado;
    
} catch (PDOException $e) {
    http_response_code(500);
    echo "Error en la base de datos: " . $e->getMessage();
} catch (Exception $e) {
    http_response_code(500);
    echo "Error interno: " . $e->getMessage();
}
?>
