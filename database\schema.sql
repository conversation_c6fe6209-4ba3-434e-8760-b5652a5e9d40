CREATE TABLE PACIENTES (
  <PERSON>LAVE INT AUTO_INCREMENT PRIMARY KEY,
  NOMBRES CHAR(35) NOT NULL,
  APELLIDOS CHAR(35) NOT NULL,
  CED<PERSON><PERSON> CHAR(11) NOT NULL,
  <PERSON><PERSON><PERSON> CHAR(10) NOT NULL,
  NACIONAL<PERSON><PERSON> CHAR(35),
  <PERSON><PERSON>AD<PERSON><PERSON><PERSON><PERSON> CHAR(15),
  FECHANAC DATE,
  LUGARNA<PERSON> CHAR(35),
  OCUPACION CHAR(25),
  R<PERSON><PERSON><PERSON> CHAR(25),
  RH CHAR(3),
  CALLE VARCHAR(80),
  PROVINC<PERSON> CHAR(30),
  LOCALIDAD VARCHAR(35),
  <PERSON><PERSON><PERSON><PERSON><PERSON> CHAR(35),
  PAIS CHAR(35),
  REFER<PERSON>CI<PERSON> CHAR(30),
  <PERSON><PERSON><PERSON><PERSON><PERSON> CHAR(10),
  <PERSON>L<PERSON><PERSON>R CHAR(10),
  TELTRABAJO CHAR(10),
  <PERSON><PERSON>RE<PERSON> CHAR(50),
  ARS SMALLINT DEFAULT NULL,
  VIGENCIA DATE,
  <PERSON><PERSON><PERSON><PERSON><PERSON> CHAR(18),
  CATEGORIA SMALLINT DEFAULT NULL,
  <PERSON>MB<PERSON>RE<PERSON> CHAR(50),
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> CHAR(50),
  <PERSON>DU<PERSON>RE<PERSON> CHAR(11),
  <PERSON><PERSON><PERSON><PERSON><PERSON> VARCHAR(35),
  OBSERVACIONES CHAR(60),
  PESOHABITUAL NUMERIC(8, 2),
  NIVELESCOLAR CHAR(1) DEFAULT '0',
  PROCEDENCIA CHAR(1) DEFAULT '0',
  PROFESION CHAR(25),
  LAST_MODIFIED TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  SINCRONIZADO INTEGER DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;