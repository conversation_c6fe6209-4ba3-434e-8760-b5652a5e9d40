<?php
// backend/reportes/api/estadisticas_tiempo_real.php
header('Content-Type: application/json');
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario'])) {
    http_response_code(401);
    echo json_encode(['error' => 'No autorizado']);
    exit;
}

require_once __DIR__ . '/../../config/database.php';

try {
    $estadisticas = [];
    
    // Total de pacientes
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM PACIENTES");
    $estadisticas['total_pacientes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Citas de hoy
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE()");
    $estadisticas['citas_hoy'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Citas atendidas hoy
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 0");
    $estadisticas['citas_atendidas_hoy'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Citas pendientes hoy (incluye citado, esperando y pendiente aprobación)
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS IN (3, 5, 6)");
    $estadisticas['citas_pendientes_hoy'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Ingresos del día
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(PAGADO), 0) as total 
        FROM FACTURAS 
        WHERE DATE(FECHA) = CURDATE() 
        AND ESTATUS = 'A'
    ");
    $estadisticas['ingresos_hoy'] = number_format($stmt->fetch(PDO::FETCH_ASSOC)['total'], 2);
    
    // Ingresos del mes actual
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(PAGADO), 0) as total 
        FROM FACTURAS 
        WHERE MONTH(FECHA) = MONTH(CURDATE()) 
        AND YEAR(FECHA) = YEAR(CURDATE())
        AND ESTATUS = 'A'
    ");
    $estadisticas['ingresos_mes'] = number_format($stmt->fetch(PDO::FETCH_ASSOC)['total'], 2);
    
    // Historias clínicas del mes
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM EXAMENFISICO 
        WHERE MONTH(FECHA_CAP) = MONTH(CURDATE()) 
        AND YEAR(FECHA_CAP) = YEAR(CURDATE())
    ");
    $estadisticas['historias_mes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Facturas pendientes de pago
    $stmt = $pdo->query("
        SELECT COUNT(*) as total, COALESCE(SUM(NOPAGO), 0) as monto_pendiente
        FROM FACTURAS 
        WHERE NOPAGO > 0 
        AND ESTATUS = 'A'
    ");
    $pendientes = $stmt->fetch(PDO::FETCH_ASSOC);
    $estadisticas['facturas_pendientes'] = $pendientes['total'];
    $estadisticas['monto_pendiente'] = number_format($pendientes['monto_pendiente'], 2);
    
    // Nuevos pacientes este mes
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM PACIENTES 
        WHERE MONTH(FECHAINGRESO) = MONTH(CURDATE()) 
        AND YEAR(FECHAINGRESO) = YEAR(CURDATE())
    ");
    $estadisticas['pacientes_nuevos_mes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Próximas citas (próximos 7 días) - incluye citado, esperando y pendiente aprobación
    $stmt = $pdo->query("
        SELECT COUNT(*) as total
        FROM CITAMEDIC
        WHERE FECHACON BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
        AND ESTATUS IN (3, 5, 6)
    ");
    $estadisticas['proximas_citas'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Estado de sincronización (si existe)
    $tablas_sync = ['PACIENTES', 'CITAMEDIC', 'FACTURAS', 'EXAMENFISICO'];
    $total_no_sync = 0;
    $total_registros = 0;
    
    foreach ($tablas_sync as $tabla) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            $total_registros += $total;
            
            $stmt = $pdo->query("SELECT COUNT(*) as no_sync FROM $tabla WHERE SINCRONIZADO = 0");
            $no_sync = $stmt->fetch(PDO::FETCH_ASSOC)['no_sync'];
            $total_no_sync += $no_sync;
        } catch (Exception $e) {
            // Tabla no tiene campo SINCRONIZADO o no existe
        }
    }
    
    $estadisticas['registros_no_sincronizados'] = $total_no_sync;
    $estadisticas['porcentaje_sincronizacion'] = $total_registros > 0 ? 
        round((($total_registros - $total_no_sync) / $total_registros) * 100, 1) : 100;
    
    // Actividad reciente (últimas 24 horas)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM PACIENTES 
        WHERE FECHAINGRESO >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $estadisticas['pacientes_24h'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM CITAMEDIC 
        WHERE FECHACON >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $estadisticas['citas_24h'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM FACTURAS 
        WHERE FECHA >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        AND ESTATUS = 'A'
    ");
    $estadisticas['facturas_24h'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Información del sistema
    $estadisticas['timestamp'] = time();
    $estadisticas['fecha_actualizacion'] = date('d/m/Y H:i:s');
    $estadisticas['usuario_actual'] = $_SESSION['usuario'];
    
    // Calcular tendencias (comparar con período anterior)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM PACIENTES 
        WHERE FECHAINGRESO >= DATE_SUB(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), INTERVAL 1 MONTH)
        AND FECHAINGRESO < DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
    ");
    $pacientes_mes_anterior = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $tendencia_pacientes = 0;
    if ($pacientes_mes_anterior > 0) {
        $tendencia_pacientes = round((($estadisticas['pacientes_nuevos_mes'] - $pacientes_mes_anterior) / $pacientes_mes_anterior) * 100, 1);
    }
    $estadisticas['tendencia_pacientes'] = $tendencia_pacientes;
    
    // Ingresos mes anterior
    $stmt = $pdo->query("
        SELECT COALESCE(SUM(PAGADO), 0) as total 
        FROM FACTURAS 
        WHERE MONTH(FECHA) = MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
        AND YEAR(FECHA) = YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
        AND ESTATUS = 'A'
    ");
    $ingresos_mes_anterior = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $tendencia_ingresos = 0;
    if ($ingresos_mes_anterior > 0) {
        $ingresos_actual = str_replace(',', '', $estadisticas['ingresos_mes']);
        $tendencia_ingresos = round((($ingresos_actual - $ingresos_mes_anterior) / $ingresos_mes_anterior) * 100, 1);
    }
    $estadisticas['tendencia_ingresos'] = $tendencia_ingresos;
    
    // Alertas del sistema
    $alertas = [];
    
    // Alerta: Muchas citas no asistidas hoy
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM CITAMEDIC WHERE FECHACON = CURDATE() AND ESTATUS = 2");
    $no_asistidas_hoy = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    if ($no_asistidas_hoy > 3) {
        $alertas[] = [
            'tipo' => 'warning',
            'mensaje' => "Alto número de no asistencias hoy: $no_asistidas_hoy",
            'icono' => 'fas fa-exclamation-triangle'
        ];
    }
    
    // Alerta: Facturas pendientes altas
    if ($estadisticas['facturas_pendientes'] > 10) {
        $alertas[] = [
            'tipo' => 'danger',
            'mensaje' => "Muchas facturas pendientes: " . $estadisticas['facturas_pendientes'],
            'icono' => 'fas fa-exclamation-circle'
        ];
    }
    
    // Alerta: Problemas de sincronización
    if ($estadisticas['registros_no_sincronizados'] > 50) {
        $alertas[] = [
            'tipo' => 'warning',
            'mensaje' => "Registros pendientes de sincronización: " . $estadisticas['registros_no_sincronizados'],
            'icono' => 'fas fa-sync-alt'
        ];
    }
    
    // Alerta: Crecimiento positivo
    if ($tendencia_pacientes > 10) {
        $alertas[] = [
            'tipo' => 'success',
            'mensaje' => "Crecimiento en pacientes: +{$tendencia_pacientes}%",
            'icono' => 'fas fa-arrow-up'
        ];
    }
    
    $estadisticas['alertas'] = $alertas;
    
    echo json_encode($estadisticas);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error en la base de datos: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error interno: ' . $e->getMessage()]);
}
?>
