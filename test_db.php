<?php
// test_db.php — Colócalo junto a tu index.php de mi_consultorio
ini_set('display_errors', 1);
error_reporting(E_ALL);


// 🔧 Ajusta la ruta al verdadero path de tu database.php:
$pdo = require __DIR__ . '/backend/config/database.php';

// 2) Detecta el subdominio
$host      = $_SERVER['HTTP_HOST'] ?? '';
$subdomain = explode('.', $host)[0];

// 3) Averigua la BD actual
try {
    $currentDb = $pdo->query('SELECT DATABASE()')->fetchColumn();
} catch (Exception $e) {
    $currentDb = '¡Error al leer DATABASE(): ' . $e->getMessage();
}

// 4) Muestra la info en pantalla
?><!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Test de Conexión</title>
  <style> body{font-family:sans-serif;padding:1rem;} .ok{background:#dfd;padding:.5rem;} .ko{background:#fdd;padding:.5rem;} </style>
</head>
<body>
  <h1>🔍 Prueba de database.php</h1>

  <p><strong>Host / SUBDOMINIO leído:</strong> <code><?= htmlspecialchars($host) ?></code></p>
  <p><strong>Subdominio:</strong> <code><?= htmlspecialchars($subdomain) ?></code></p>

  <?php if (strpos($currentDb, 'Error')===false): ?>
    <p class="ok"><strong>✅ Conectado exitosamente a la BD:</strong> <code><?= htmlspecialchars($currentDb) ?></code></p>
  <?php else: ?>
    <p class="ko"><strong>❌ Hubo problema:</strong> <?= htmlspecialchars($currentDb) ?></p>
  <?php endif; ?>

  <p>Si ves el nombre de la BD correcto aquí, tu `database.php` funciona.</p>
</body>
</html>
