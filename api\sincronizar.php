<?php
include_once '../config/config.php';

header("Content-Type: application/json");

// Leer datos JSON de la solicitud
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(["status" => "error", "message" => "JSON inválido"]);
    exit;
}

$conn = getDBConnection();

// Insertar o actualizar los datos en la base
foreach ($data as $paciente) {
    $stmt = $conn->prepare("INSERT INTO PACIENTES (
        VISITA, REGISTRO, NOMBRES, APELLIDOS, CEDULA, SEXO, NACIONALIDAD, 
        ESTADOCIVIL, FECHANAC, LUGARNAC, OCUPACION, RELIGION, FECHAINGRESO, RH, CALLE, 
        PROVINCIA, LOCALIDAD, MUNICIPIO, PAIS, REFERENCIA, TELEFONO, CELULAR, TELTRABAJO, 
        FAX, ECORREO, ARS, PLANES, AFILIADO, VIGENCIA, POLIZA, CATEGORIA, ALERGIA, NAF, 
        NSS, NOMBRERESP, DIRECCIONRESP, CEDULARESP, TELEFONORESP, FAMILIARPROX, 
        DIRECCIONFAMILIAR, TELEFONOFAMILIAR, REMITIDO, OBSERVACIONES, ESTATUS, STEMBARAZO, 
        PESOHABITUAL, NIVELESCOLAR, PROCEDENCIA, SINCEDULA, PROFESION, RECORDCLINICA, 
        LAST_MODIFIED, SINCRONIZADO
    ) 
    VALUES (
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1
    )
    ON DUPLICATE KEY UPDATE 
        VISITA = VALUES(VISITA), 
        REGISTRO = VALUES(REGISTRO), 
        NOMBRES = VALUES(NOMBRES), 
        APELLIDOS = VALUES(APELLIDOS), 
        CEDULA = VALUES(CEDULA), 
        SEXO = VALUES(SEXO), 
        NACIONALIDAD = VALUES(NACIONALIDAD), 
        ESTADOCIVIL = VALUES(ESTADOCIVIL), 
        FECHANAC = VALUES(FECHANAC), 
        LUGARNAC = VALUES(LUGARNAC), 
        OCUPACION = VALUES(OCUPACION), 
        RELIGION = VALUES(RELIGION), 
        FECHAINGRESO = VALUES(FECHAINGRESO), 
        RH = VALUES(RH), 
        CALLE = VALUES(CALLE), 
        PROVINCIA = VALUES(PROVINCIA), 
        LOCALIDAD = VALUES(LOCALIDAD), 
        MUNICIPIO = VALUES(MUNICIPIO), 
        PAIS = VALUES(PAIS), 
        REFERENCIA = VALUES(REFERENCIA), 
        TELEFONO = VALUES(TELEFONO), 
        CELULAR = VALUES(CELULAR), 
        TELTRABAJO = VALUES(TELTRABAJO), 
        FAX = VALUES(FAX), 
        ECORREO = VALUES(ECORREO), 
        ARS = VALUES(ARS), 
        PLANES = VALUES(PLANES), 
        AFILIADO = VALUES(AFILIADO), 
        VIGENCIA = VALUES(VIGENCIA), 
        POLIZA = VALUES(POLIZA), 
        CATEGORIA = VALUES(CATEGORIA), 
        ALERGIA = VALUES(ALERGIA), 
        NAF = VALUES(NAF), 
        NSS = VALUES(NSS), 
        NOMBRERESP = VALUES(NOMBRERESP), 
        DIRECCIONRESP = VALUES(DIRECCIONRESP), 
        CEDULARESP = VALUES(CEDULARESP), 
        TELEFONORESP = VALUES(TELEFONORESP), 
        FAMILIARPROX = VALUES(FAMILIARPROX), 
        DIRECCIONFAMILIAR = VALUES(DIRECCIONFAMILIAR), 
        TELEFONOFAMILIAR = VALUES(TELEFONOFAMILIAR), 
        REMITIDO = VALUES(REMITIDO), 
        OBSERVACIONES = VALUES(OBSERVACIONES), 
        ESTATUS = VALUES(ESTATUS), 
        STEMBARAZO = VALUES(STEMBARAZO), 
        PESOHABITUAL = VALUES(PESOHABITUAL), 
        NIVELESCOLAR = VALUES(NIVELESCOLAR), 
        PROCEDENCIA = VALUES(PROCEDENCIA), 
        SINCEDULA = VALUES(SINCEDULA), 
        PROFESION = VALUES(PROFESION), 
        RECORDCLINICA = VALUES(RECORDCLINICA), 
        LAST_MODIFIED = VALUES(LAST_MODIFIED)");

    $stmt->bind_param(
        "ssssssssssssssssssssssssssssssssssssssssssssssssssss",
        $paciente['VISITA'], $paciente['REGISTRO'], $paciente['NOMBRES'], $paciente['APELLIDOS'], 
        $paciente['CEDULA'], $paciente['SEXO'], $paciente['NACIONALIDAD'], $paciente['ESTADOCIVIL'], 
        $paciente['FECHANAC'], $paciente['LUGARNAC'], $paciente['OCUPACION'], $paciente['RELIGION'], 
        $paciente['FECHAINGRESO'], $paciente['RH'], $paciente['CALLE'], $paciente['PROVINCIA'], 
        $paciente['LOCALIDAD'], $paciente['MUNICIPIO'], $paciente['PAIS'], $paciente['REFERENCIA'], 
        $paciente['TELEFONO'], $paciente['CELULAR'], $paciente['TELTRABAJO'], $paciente['FAX'], 
        $paciente['ECORREO'], $paciente['ARS'], $paciente['PLANES'], $paciente['AFILIADO'], 
        $paciente['VIGENCIA'], $paciente['POLIZA'], $paciente['CATEGORIA'], $paciente['ALERGIA'], 
        $paciente['NAF'], $paciente['NSS'], $paciente['NOMBRERESP'], $paciente['DIRECCIONRESP'], 
        $paciente['CEDULARESP'], $paciente['TELEFONORESP'], $paciente['FAMILIARPROX'], 
        $paciente['DIRECCIONFAMILIAR'], $paciente['TELEFONOFAMILIAR'], $paciente['REMITIDO'], 
        $paciente['OBSERVACIONES'], $paciente['ESTATUS'], $paciente['STEMBARAZO'], 
        $paciente['PESOHABITUAL'], $paciente['NIVELESCOLAR'], $paciente['PROCEDENCIA'], 
        $paciente['SINCEDULA'], $paciente['PROFESION'], $paciente['RECORDCLINICA'], 
        $paciente['LAST_MODIFIED']
    );

    $stmt->execute();
}

echo json_encode(["status" => "success", "message" => "Datos sincronizados correctamente"]);

?>


