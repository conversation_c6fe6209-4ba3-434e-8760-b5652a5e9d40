<?php
require_once 'backend/config/database.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $token = $_POST['token'];
    $password = password_hash($_POST['password'], PASSWORD_BCRYPT);

    // Verificar el token y actualizar la contraseña
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE reset_token = ? AND reset_expira > NOW()");
    $stmt->execute([$token]);
    $user = $stmt->fetch();

    if ($user) {
        $stmt = $pdo->prepare("UPDATE usuarios SET password = ?, reset_token = NULL, reset_expira = NULL WHERE reset_token = ?");
        $stmt->execute([$password, $token]);

        echo "Tu contraseña ha sido actualizada. Ahora puedes iniciar sesión.";
    } else {
        echo "El enlace de recuperación no es válido o ha expirado.";
    }
}
