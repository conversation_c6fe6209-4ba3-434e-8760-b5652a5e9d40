<?php
// Test de guardado para diagnosticar problemas
session_start();
require_once __DIR__ . '/backend/config/database.php';

echo "<h2>Test de Guardado - Módulo Texto Simple</h2>";

// Verificar conexión a base de datos
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p>✅ <strong>Conexión a BD:</strong> OK</p>";
} catch (Exception $e) {
    echo "<p>❌ <strong>Conexión a BD:</strong> ERROR - " . $e->getMessage() . "</p>";
}

// Verificar sesión
echo "<p><strong>Usuario:</strong> " . ($_SESSION['usuario'] ?? 'NO_DEFINIDO') . "</p>";
echo "<p><strong>Rol:</strong> " . ($_SESSION['rol'] ?? 'NO_DEFINIDO') . "</p>";
echo "<p><strong>CLAVEPAC:</strong> " . ($_SESSION['CLAVEPAC'] ?? 'NO_DEFINIDO') . "</p>";

// Test de POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Datos POST Recibidos:</h3>";
    echo "<pre>";
    print_r($_POST);
    echo "</pre>";
    
    if (isset($_POST['test_guardar'])) {
        $texto = trim($_POST['texto_prueba'] ?? '');
        echo "<p><strong>Texto a guardar:</strong> " . htmlspecialchars($texto) . "</p>";
        
        if (!empty($texto)) {
            try {
                // Test de inserción en tabla ESTUDIOS
                $stmt = $pdo->prepare("INSERT INTO ESTUDIOS (CLAVEPAC, CEDULA, FECHA_CAP, ESTUDIOS, SINCRONIZADO) VALUES (?, ?, CURRENT_TIMESTAMP, ?, 0)");
                $result = $stmt->execute([999, '12345678', $texto]);
                
                if ($result) {
                    $newId = $pdo->lastInsertId();
                    echo "<p>✅ <strong>Guardado exitoso!</strong> Nuevo ID: " . $newId . "</p>";
                } else {
                    echo "<p>❌ <strong>Error en execute()</strong></p>";
                }
            } catch (PDOException $e) {
                echo "<p>❌ <strong>Error PDO:</strong> " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p>⚠️ <strong>Texto vacío</strong></p>";
        }
    }
}

// Verificar estructura de tabla ESTUDIOS
try {
    $stmt = $pdo->query("DESCRIBE ESTUDIOS");
    $columns = $stmt->fetchAll();
    echo "<h3>Estructura de tabla ESTUDIOS:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . $col['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p>❌ <strong>Error al verificar tabla:</strong> " . $e->getMessage() . "</p>";
}

// Verificar últimos registros
try {
    $stmt = $pdo->query("SELECT * FROM ESTUDIOS ORDER BY FECHA_CAP DESC LIMIT 5");
    $registros = $stmt->fetchAll();
    echo "<h3>Últimos 5 registros en ESTUDIOS:</h3>";
    if ($registros) {
        echo "<table border='1'>";
        echo "<tr><th>CLAVE</th><th>CLAVEPAC</th><th>CEDULA</th><th>FECHA_CAP</th><th>ESTUDIOS (primeros 50 chars)</th></tr>";
        foreach ($registros as $reg) {
            echo "<tr>";
            echo "<td>" . $reg['CLAVE'] . "</td>";
            echo "<td>" . $reg['CLAVEPAC'] . "</td>";
            echo "<td>" . $reg['CEDULA'] . "</td>";
            echo "<td>" . $reg['FECHA_CAP'] . "</td>";
            echo "<td>" . substr($reg['ESTUDIOS'], 0, 50) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No hay registros en ESTUDIOS</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ <strong>Error al consultar registros:</strong> " . $e->getMessage() . "</p>";
}
?>

<h3>Test de Formulario:</h3>
<form method="POST">
    <div>
        <label>Texto de prueba:</label><br>
        <textarea name="texto_prueba" rows="5" cols="50" placeholder="Escribe algo para probar el guardado..."></textarea>
    </div>
    <br>
    <button type="submit" name="test_guardar">🧪 Probar Guardado</button>
</form>

<h3>Enlaces de Prueba:</h3>
<p><a href="backend/historia/modulo_texto_simple.php?CLAVEPAC=999&CEDULA=12345678&TABLA=estudios">Probar módulo_texto_simple.php con ESTUDIOS</a></p>
<p><a href="backend/historia/modulo_texto_simple.php?CLAVEPAC=999&CEDULA=12345678&TABLA=prescripcion">Probar módulo_texto_simple.php con PRESCRIPCION</a></p>
