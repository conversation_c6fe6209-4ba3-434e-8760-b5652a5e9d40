<?php

session_start(); // Necesario para acceder a $_SESSION['usuario']
require_once '../config/database.php';

header('Content-Type: application/json');


if (!isset($_GET['fecha']) || !isset($_SESSION['usuario'])) {
    echo json_encode([]);
    exit;
}

$fecha = $_GET['fecha'];
$cedula = $_SESSION['cedula'];

// Validar formato de fecha
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $fecha)) {
    echo json_encode([]);
    exit;
}

// Verificar si el paciente ya tiene una cita ese día
$verificarStmt = $pdo->prepare("SELECT COUNT(*) FROM CITAMEDIC WHERE NSS = ? AND FECHACON = ?");
$verificarStmt->execute([$cedula, $fecha]);

if ($verificarStmt->fetchColumn() > 0) {
    echo json_encode([]); // Ya tiene cita, no mostrar horas
    exit;
}

// Obtener configuración
$configQuery = $pdo->query("SELECT HORAINICIO, HORAFIN, DURACION FROM CONFIG LIMIT 1");
$config = $configQuery->fetch(PDO::FETCH_ASSOC);

if (!$config) {
    echo json_encode([]);
    exit;
}

function formatoHora($hhmm) {
    return substr($hhmm, 0, 2) . ':' . substr($hhmm, 2, 2);
}

$inicio = strtotime(formatoHora($config['HORAINICIO']));
$fin = strtotime(formatoHora($config['HORAFIN']));
$duracion = (int)$config['DURACION'];

// Obtener horas ya ocupadas
$stmt = $pdo->prepare("SELECT HORACON FROM CITAMEDIC WHERE FECHACON = ?");
$stmt->execute([$fecha]);
$horasOcupadas = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Normalizar horas ocupadas a formato 12h (como se muestra en pantalla)
$horasOcupadas = array_map(function ($hora) {
    $time = strtotime($hora);
    return str_replace(['am', 'pm'], ['a.m.', 'p.m.'], date('h:i a', $time));
}, $horasOcupadas);

$disponibles = [];

for ($h = $inicio; $h <= $fin; $h += $duracion * 60) {
    $hora12 = date('h:i a', $h);
    $hora12 = str_replace(['am', 'pm'], ['a.m.', 'p.m.'], $hora12);

    // Si la fecha es hoy y ya pasó la hora, saltar
    if ($fecha === date('Y-m-d') && date('H:i', $h) <= date('H:i')) {
        continue;
    }

    if (!in_array($hora12, $horasOcupadas)) {
        $disponibles[] = $hora12;
    }
}

echo json_encode($disponibles);