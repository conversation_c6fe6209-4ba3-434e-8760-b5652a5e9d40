<?php
// backend/lib/ValidadorReportes.php
// Clase para validar datos de entrada en reportes

class ValidadorReportes {
    private $errores = [];
    private $advertencias = [];
    
    public function __construct() {
        $this->errores = [];
        $this->advertencias = [];
    }
    
    /**
     * Validar rango de fechas
     */
    public function validarFechas($fecha_inicio, $fecha_fin) {
        // Validar formato de fecha
        if (!$this->validarFormatoFecha($fecha_inicio)) {
            $this->errores[] = "Formato de fecha de inicio inválido. Use YYYY-MM-DD";
            return false;
        }
        
        if (!$this->validarFormatoFecha($fecha_fin)) {
            $this->errores[] = "Formato de fecha de fin inválido. Use YYYY-MM-DD";
            return false;
        }
        
        // Convertir a timestamps para comparar
        $inicio = strtotime($fecha_inicio);
        $fin = strtotime($fecha_fin);
        $hoy = strtotime(date('Y-m-d'));
        
        // Validar que fecha inicio no sea mayor que fecha fin
        if ($inicio > $fin) {
            $this->errores[] = "La fecha de inicio no puede ser mayor que la fecha de fin";
            return false;
        }
        
        // Validar que las fechas no sean muy antiguas (más de 5 años)
        $hace_5_anos = strtotime('-5 years');
        if ($inicio < $hace_5_anos) {
            $this->advertencias[] = "La fecha de inicio es muy antigua (más de 5 años)";
        }
        
        // Validar que las fechas no sean futuras (más de 1 año)
        $en_1_ano = strtotime('+1 year');
        if ($fin > $en_1_ano) {
            $this->advertencias[] = "La fecha de fin está muy en el futuro";
        }
        
        // Validar rango muy amplio (más de 2 años)
        $diferencia_dias = ($fin - $inicio) / (60 * 60 * 24);
        if ($diferencia_dias > 730) { // 2 años
            $this->advertencias[] = "El rango de fechas es muy amplio (más de 2 años). El reporte puede ser lento";
        }
        
        return true;
    }
    
    /**
     * Validar formato de fecha YYYY-MM-DD
     */
    private function validarFormatoFecha($fecha) {
        if (empty($fecha)) return false;
        
        $d = DateTime::createFromFormat('Y-m-d', $fecha);
        return $d && $d->format('Y-m-d') === $fecha;
    }
    
    /**
     * Validar parámetros de edad
     */
    public function validarEdad($edad_min, $edad_max) {
        if (!empty($edad_min)) {
            if (!is_numeric($edad_min) || $edad_min < 0 || $edad_min > 120) {
                $this->errores[] = "Edad mínima debe ser un número entre 0 y 120";
                return false;
            }
        }
        
        if (!empty($edad_max)) {
            if (!is_numeric($edad_max) || $edad_max < 0 || $edad_max > 120) {
                $this->errores[] = "Edad máxima debe ser un número entre 0 y 120";
                return false;
            }
        }
        
        if (!empty($edad_min) && !empty($edad_max)) {
            if ($edad_min > $edad_max) {
                $this->errores[] = "La edad mínima no puede ser mayor que la edad máxima";
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Validar parámetros de sexo
     */
    public function validarSexo($sexo) {
        if (!empty($sexo) && !in_array($sexo, ['M', 'F'])) {
            $this->errores[] = "Sexo debe ser 'M' (Masculino) o 'F' (Femenino)";
            return false;
        }
        return true;
    }
    
    /**
     * Validar estado de cita
     */
    public function validarEstadoCita($estatus) {
        $estados_validos = [0, 1, 2, 3, 4, 5, 6];
        if (!empty($estatus) && !in_array((int)$estatus, $estados_validos)) {
            $this->errores[] = "Estado de cita inválido. Valores válidos: 0 (Atendido), 1 (Canceló), 2 (No asistió), 3 (Citado), 4 (Llegó tarde), 5 (Esperando), 6 (Pendiente aprobación)";
            return false;
        }
        return true;
    }
    
    /**
     * Validar estado de pago
     */
    public function validarEstadoPago($estado_pago) {
        $estados_validos = ['pagado', 'pendiente', 'parcial'];
        if (!empty($estado_pago) && !in_array($estado_pago, $estados_validos)) {
            $this->errores[] = "Estado de pago inválido. Valores válidos: pagado, pendiente, parcial";
            return false;
        }
        return true;
    }
    
    /**
     * Validar método de pago
     */
    public function validarMetodoPago($metodo_pago) {
        $metodos_validos = ['efectivo', 'tarjeta', 'transferencia', 'cheque'];
        if (!empty($metodo_pago) && !in_array($metodo_pago, $metodos_validos)) {
            $this->errores[] = "Método de pago inválido. Valores válidos: efectivo, tarjeta, transferencia, cheque";
            return false;
        }
        return true;
    }
    
    /**
     * Validar formato de exportación
     */
    public function validarFormato($formato) {
        $formatos_validos = ['excel', 'pdf'];
        if (!in_array($formato, $formatos_validos)) {
            $this->errores[] = "Formato de exportación inválido. Valores válidos: excel, pdf";
            return false;
        }
        return true;
    }
    
    /**
     * Validar que el usuario tenga permisos
     */
    public function validarPermisos($roles_permitidos = ['admin', 'doctor', 'secretaria']) {
        if (!isset($_SESSION['usuario']) || !isset($_SESSION['rol'])) {
            $this->errores[] = "Usuario no autenticado";
            return false;
        }
        
        if (!in_array($_SESSION['rol'], $roles_permitidos)) {
            $this->errores[] = "No tiene permisos para acceder a este reporte";
            return false;
        }
        
        return true;
    }
    
    /**
     * Validar conexión a base de datos
     */
    public function validarConexionBD($pdo) {
        try {
            $stmt = $pdo->query("SELECT 1");
            return true;
        } catch (PDOException $e) {
            $this->errores[] = "Error de conexión a la base de datos: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Validar que existan datos en el rango especificado
     */
    public function validarExistenciaDatos($pdo, $tabla, $campo_fecha, $fecha_inicio, $fecha_fin, $condiciones_extra = []) {
        try {
            $where_conditions = ["$campo_fecha BETWEEN ? AND ?"];
            $params = [$fecha_inicio, $fecha_fin];

            // Agregar condiciones extra
            foreach ($condiciones_extra as $condicion => $valor) {
                if (!empty($valor)) {
                    $where_conditions[] = "$condicion = ?";
                    $params[] = $valor;
                }
            }
            
            $where_clause = "WHERE " . implode(" AND ", $where_conditions);
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM $tabla $where_clause");
            $stmt->execute($params);
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            if ($total == 0) {
                $this->advertencias[] = "No se encontraron datos para el período y filtros especificados";
                return false;
            }
            
            if ($total > 10000) {
                $this->advertencias[] = "Se encontraron muchos registros ($total). El reporte puede tardar en generarse";
            }
            
            return true;
            
        } catch (PDOException $e) {
            $this->errores[] = "Error al validar datos: " . $e->getMessage();
            return false;
        }
    }
    
    /**
     * Sanitizar entrada de texto
     */
    public function sanitizarTexto($texto) {
        if (empty($texto)) return '';
        
        // Remover caracteres peligrosos
        $texto = strip_tags($texto);
        $texto = htmlspecialchars($texto, ENT_QUOTES, 'UTF-8');
        $texto = trim($texto);
        
        // Limitar longitud
        if (strlen($texto) > 255) {
            $texto = substr($texto, 0, 255);
            $this->advertencias[] = "Texto truncado a 255 caracteres";
        }
        
        return $texto;
    }
    
    /**
     * Obtener todos los errores
     */
    public function obtenerErrores() {
        return $this->errores;
    }
    
    /**
     * Obtener todas las advertencias
     */
    public function obtenerAdvertencias() {
        return $this->advertencias;
    }
    
    /**
     * Verificar si hay errores
     */
    public function tieneErrores() {
        return !empty($this->errores);
    }
    
    /**
     * Verificar si hay advertencias
     */
    public function tieneAdvertencias() {
        return !empty($this->advertencias);
    }
    
    /**
     * Limpiar errores y advertencias
     */
    public function limpiar() {
        $this->errores = [];
        $this->advertencias = [];
    }
    
    /**
     * Generar respuesta de error en JSON
     */
    public function respuestaError($codigo_http = 400) {
        http_response_code($codigo_http);
        header('Content-Type: application/json');
        
        $respuesta = [
            'error' => true,
            'errores' => $this->errores,
            'advertencias' => $this->advertencias,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        return json_encode($respuesta);
    }
    
    /**
     * Generar respuesta de error en HTML
     */
    public function respuestaErrorHTML($titulo = 'Error en el Reporte') {
        http_response_code(400);
        header('Content-Type: text/html; charset=utf-8');
        
        $html = "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>$titulo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f8f9fa; }
        .error-container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .error-header { color: #dc3545; border-bottom: 2px solid #dc3545; padding-bottom: 15px; margin-bottom: 20px; }
        .error-list { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 15px 0; }
        .warning-list { background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 15px 0; }
        .error-item { margin: 5px 0; color: #721c24; }
        .warning-item { margin: 5px 0; color: #856404; }
        .back-button { display: inline-block; margin-top: 20px; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .back-button:hover { background-color: #0056b3; }
    </style>
</head>
<body>
    <div class='error-container'>
        <div class='error-header'>
            <h1><i class='fas fa-exclamation-triangle'></i> $titulo</h1>
        </div>";
        
        if (!empty($this->errores)) {
            $html .= "<div class='error-list'>
                <h3>Errores encontrados:</h3>";
            foreach ($this->errores as $error) {
                $html .= "<div class='error-item'>• " . htmlspecialchars($error) . "</div>";
            }
            $html .= "</div>";
        }
        
        if (!empty($this->advertencias)) {
            $html .= "<div class='warning-list'>
                <h3>Advertencias:</h3>";
            foreach ($this->advertencias as $advertencia) {
                $html .= "<div class='warning-item'>• " . htmlspecialchars($advertencia) . "</div>";
            }
            $html .= "</div>";
        }
        
        $html .= "
        <p>Por favor, corrija los errores indicados e intente nuevamente.</p>
        <a href='javascript:history.back()' class='back-button'>Volver</a>
    </div>
</body>
</html>";
        
        return $html;
    }
}
?>
