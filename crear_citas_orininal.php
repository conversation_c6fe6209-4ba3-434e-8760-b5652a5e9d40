<?php
session_start();

if (!isset($_SESSION['usuario'])) {
    header('Location: /mi_consultorio/login.php');
    exit();
}

// Incluir archivo de configuración para la conexión a la base de datos
include('../config/database.php');

//date_default_timezone_set('America/New_York'); // Configura tu zona horaria
date_default_timezone_set('America/Santo_Domingo');

// Mostrar todos los errores, advertencias y avisos
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);


// Obtener la hora del cliente si está disponible
$horaCliente = isset($_POST['horaCliente']) ? $_POST['horaCliente'] : null;

// Consulta a la tabla CONFIG
$sqldatosConfig = "SELECT CONSULTORIO, HORAINICIO, HORAFIN, DURACION FROM CONFIG LIMIT 1";
$stmtdatosConfig = $pdo->prepare($sqldatosConfig);
$stmtdatosConfig->execute();
$datosConfig = $stmtdatosConfig->fetch(PDO::FETCH_ASSOC);

// Inicializar valores predeterminados
$horarios = [];
$consultorio = 1;
$duracion = 15;
$horaInicio = '00:00';
$horaFin = '23:59';

if ($datosConfig) {
    $consultorio = $datosConfig['CONSULTORIO'];
    $duracion = (int) $datosConfig['DURACION'];
    $horaInicio = substr($datosConfig['HORAINICIO'], 0, 2) . ':' . substr($datosConfig['HORAINICIO'], 2, 2);
    $horaFin = substr($datosConfig['HORAFIN'], 0, 2) . ':' . substr($datosConfig['HORAFIN'], 2, 2);
}


// Obtener la fecha seleccionada en el formulario o usar la fecha actual
$fechaSeleccionada = isset($_POST['FECHACON']) ? $_POST['FECHACON'] : date('Y-m-d');
$fechaHoy = date('Y-m-d');
$horaActual = date('H:i'); // Hora actual en formato 24 horas

// Inicializar el array de horarios
$horarios = [];
$current = strtotime($horaInicio);
$end = strtotime($horaFin);

while ($current < $end) {
    $horaFormato = date('H:i', $current); // Formato 24h para comparación

    if ($fechaSeleccionada === $fechaHoy) {
        // Si la fecha es hoy, solo mostrar horarios futuros
        if ($horaFormato > $horaActual) {
            $horarios[] = date('h:i a', $current); // Convertir a AM/PM
        }
    } else {
        // Si la fecha es futura, mostrar todas las horas del horario del negocio
        $horarios[] = date('h:i a', $current);
    }

    $current = strtotime("+$duracion minutes", $current);
}


// Reemplazar "am" y "pm" por "a.m." y "p.m."
$horarios = array_map(function($hora) {
    return str_replace(['am', 'pm'], ['a.m.', 'p.m.'], $hora);
}, $horarios);

// Guardar en log para depuración
//file_put_contents('debug_log.txt', "Hora Cliente: " . ($horaCliente ?: "No recibida") . "\n", FILE_APPEND);
//file_put_contents('debug_log.txt', "Hora Servidor: " . date('h:i a') . "\n", FILE_APPEND);
//file_put_contents('debug_log.txt', "Horarios filtrados: " . implode(', ', $horarios) . "\n", FILE_APPEND);


// Verificar si el formulario fue enviado
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Recibir los datos del formulario
    $cedula = $_POST['CEDULA'] ?? '';  
    $fechaCon = $_POST['FECHACON'] ?? '';
    $horaCon = $_POST['HORACON'] ?? null; 
    $modoAsistencia = $_POST['MODOASISTENCIA'] ?? '0';  
    $estatus = $_POST['ESTATUS'] ?? '5';  
    $tipoCita = $_POST['TIPOCITA'] ?? '1'; 
    $numDoctor = '9999999999999';  // Valor por defecto
    
    //file_put_contents('debug_post_log.txt', print_r($_POST, true), FILE_APPEND);  
    
    // Validar cédula
    if (!preg_match('/^\d{11}$/', $cedula)) {
        echo "<p class='error-message'>La cédula debe contener exactamente 11 dígitos numéricos.</p>";
    } else {
        try {
            // Validar el tipo de cita
            if (!in_array($tipoCita, ['1', '2', '3', '4'])) {
                echo "<p class='error-message'>Error: Tipo de cita inválido (valor recibido: '$tipoCita').</p>";
                exit;
            }

            // 🔹 Obtener el NUMDOCTOR desde la tabla EMPRESA usando el CONSULTORIO
            $sqlNumDoctor = "SELECT RNC FROM EMPRESA WHERE CONSULTORIO = ?";
            $stmtNumDoctor = $pdo->prepare($sqlNumDoctor);
            $stmtNumDoctor->execute([$consultorio]);
            $empresa = $stmtNumDoctor->fetch(PDO::FETCH_ASSOC);
             
            // Si se encuentra un RNC en la tabla EMPRESA, asignarlo a numDoctor
            if ($empresa) {
                $numDoctor = $empresa['RNC'];
            }

            // 🔹 Buscar la clave del paciente y su información
            $sqlPaciente = "SELECT CLAVE, TELEFONO, NOMBREAPELLIDO FROM PACIENTES WHERE CEDULA = ?";
            $stmtPaciente = $pdo->prepare($sqlPaciente);
            $stmtPaciente->execute([$cedula]);
            $paciente = $stmtPaciente->fetch(PDO::FETCH_ASSOC);

            if ($paciente) {
                $clavePac = $paciente['CLAVE'];
                $nombreApellido = $paciente['NOMBREAPELLIDO'];
                $telefono = $paciente['TELEFONO'];

                // 🔍 Verificar si el paciente ya tiene una cita en la misma fecha
                $sqlVerificarCita = "SELECT COUNT(*) FROM CITAMEDIC WHERE CLAVEPAC = ? AND FECHACON = ?";
                $stmtVerificar = $pdo->prepare($sqlVerificarCita);
                $stmtVerificar->execute([$clavePac, $fechaCon]);
                $existeCita = $stmtVerificar->fetchColumn();

                if ($existeCita > 0) {
                    echo "<p class='error-message'>Error: Este paciente ya tiene una cita para el día $fechaCon.</p>";
                } else {      
                  
                  
                  
                   // Asignar el modo de asistencia según el estatus
                   $modoAsistencia = in_array($estatus, ['0', '4', '5']) ? 1 : 0; // 1 = Presente, 0 = Ausente
                  
                  // Verificar si la fecha seleccionada es mayor que la actual
                    $estatusCita = ($fechaCon > date('Y-m-d')) ? 3 : $estatus; // 3 = Citado
                  
                    // 🔹 Insertar la nueva cita
                    $sql = "INSERT INTO CITAMEDIC (CLAVEPAC, NOMBRES, CONSULTORIO, FECHACON, HORACON, TIPOCITA, NUMDOCTOR, DURACION, NSS, TELEFONO, MODOASISTENCIA, ESTATUS) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute([$clavePac, $nombreApellido, $consultorio, $fechaCon, $horaCon,  $tipoCita, $numDoctor,  $duracion,  $cedula, $telefono, $modoAsistencia, $estatusCita]);

                    echo "<p class='success-message'>Cita registrada con éxito.</p>";
                }
            } else {
                echo "<p class='error-message'>Error: No se encontró un paciente con la cédula proporcionada.</p>";
            }
        } catch (PDOException $e) {
            echo "<p class='error-message'>Error al registrar la cita: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
} 


// Manejar la actualización del estatus a "Atendido"
if (isset($_GET['atender'])) {
    $claveCita = $_GET['atender'];
    try {
        $sqlAtender = "UPDATE CITAMEDIC SET ESTATUS = 0,SINCRONIZADO=0 WHERE CLAVE = ?";
        $stmtAtender = $pdo->prepare($sqlAtender);
        $stmtAtender->execute([$claveCita]);
        echo "<p class='success-message'>El paciente ha sido marcado como Atendido.</p>";
    } catch (PDOException $e) {
        echo "<p class='error-message'>Error al actualizar el estatus: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
}

// Mostrar citas de la fecha actual
try {
    $fechaActual = date('Y-m-d');
    $sqlCitas = "SELECT c.CLAVE, p.NOMBREAPELLIDO, c.HORACON, 
                    CASE c.ESTATUS 
                            WHEN 0 THEN 'Atendido'
                            WHEN 1 THEN 'Canceló'
                            WHEN 2 THEN 'No asistió'                          
                            WHEN 3 THEN 'Citado'
                            WHEN 4 THEN 'Llegó tarde' 
                            WHEN 5 THEN 'Esperando'
                            WHEN 6 THEN 'Pendiente aprobación'
                           END AS ESTATUS,
                       
                        CASE  c.MODOASISTENCIA 
                            WHEN 0 THEN 'Ausente'
                            WHEN 1 THEN 'Presente'
                            
                         END AS MODOASISTENCIA    
                        
                 FROM CITAMEDIC c
                 JOIN PACIENTES p ON c.CLAVEPAC = p.CLAVE
                 WHERE c.FECHACON = ?
                 ORDER BY c.HORACON";
    $stmtCitas = $pdo->prepare($sqlCitas);
    $stmtCitas->execute([$fechaActual]);
    $citas = $stmtCitas->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "<p class='error-message'>Error al obtener las citas: " . htmlspecialchars($e->getMessage()) . "</p>";
}



// Para distinguir peticiones AJAX (por ejemplo, si se usa jQuery se envía "XMLHttpRequest")
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
          strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

// Si se recibe "cedula" y NO se recibe "pre", y es petición AJAX, devolvemos el JSON
if ($isAjax && isset($_GET['cedula']) && !isset($_GET['pre'])) {
    try {
        $cedula = $_GET['cedula'];
        $sqlTelefono = "SELECT TELEFONO, NOMBREAPELLIDO FROM PACIENTES WHERE CEDULA = ?";
        $stmtTelefono = $pdo->prepare($sqlTelefono);
        $stmtTelefono->execute([$cedula]);
        $paciente = $stmtTelefono->fetch(PDO::FETCH_ASSOC);
        echo json_encode($paciente);
    } catch (PDOException $e) {
        echo json_encode(["error" => "Error al buscar el teléfono: " . $e->getMessage()]);
    }
    exit;
}

// Si no es una petición AJAX o se recibe "pre", se carga el formulario completo.
// Usaremos el parámetro "pre" para precargar la cédula.
$cedulaPreload = isset($_GET['pre']) ? $_GET['pre'] : '';

?>


<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crear Cita Médica</title>
     <link rel="stylesheet" href="../../public/assets/css/stylescitas.css">
     <!-- Agregar Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
     <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  
  
    <script>
    
    function validarCedula() {
    const cedulaInput = document.getElementById('CEDULA');
    const telefonoField = document.getElementById('telefono');

    // Elimina caracteres no numéricos
    cedulaInput.value = cedulaInput.value.replace(/\D/g, '');

    const cedula = cedulaInput.value;

    if (cedula.length === 11) {
        fetch(`crear_cita.php?cedula=${cedula}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data && !data.error) {
                document.getElementById('nombreApellido').value = data.NOMBREAPELLIDO || '';
                telefonoField.textContent = `Teléfono registrado: ${data.TELEFONO || 'No disponible'}`;
            } else {
                document.getElementById('nombreApellido').value = '';
                telefonoField.textContent = 'Paciente no encontrado.';
            }
        })
        .catch(() => {
            document.getElementById('nombreApellido').value = '';
            telefonoField.textContent = 'Error al buscar el teléfono.';
        });
    } else {
        telefonoField.textContent = 'La cédula debe contener exactamente 11 dígitos.';
    }
}


</script>  

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const fechaInput = document.getElementById('FECHACON');
        const horaSelect = document.getElementById('HORACON');

        function actualizarHorarios() {
            const fechaSeleccionada = fechaInput.value;
            
            fetch(`obtener_horarios.php?fecha=${fechaSeleccionada}`)
                .then(response => response.json())
                .then(data => {
                    horaSelect.innerHTML = ""; // Limpiar opciones previas

                    if (data.length === 0) {
                        const option = document.createElement("option");
                        option.value = "";
                        option.textContent = "No hay horarios disponibles";
                        horaSelect.appendChild(option);
                    } else {
                        data.forEach(hora => {
                            const option = document.createElement("option");
                            option.value = hora;
                            option.textContent = hora;
                            horaSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => console.error("Error al obtener los horarios:", error));
        }

        // Ejecutar la función al cambiar la fecha
        fechaInput.addEventListener('change', actualizarHorarios);

        // Llamar a la función al cargar la página para mostrar los horarios iniciales
        actualizarHorarios();
        
        
      
      // Si el campo CEDULA ya tiene un valor preestablecido, llama a validarCedula()
      const cedulaInput = document.getElementById('CEDULA');
      if (cedulaInput && cedulaInput.value.trim() !== '') {
          validarCedula();
      }  
        
        
    });
</script>



</head>
<body>
    <h1>Crear Cita Médica</h1>

<!-- Mostrar mensaje de confirmación -->
        <?php if (isset($_GET['mensaje']) && $_GET['mensaje'] === 'eliminada'): ?>
            <p class="success-message">La cita se eliminó correctamente.</p>
        <?php endif; ?>



    <!-- Formulario para crear una nueva cita -->
    <form action="crear_cita.php" method="POST">
     
     
        <label for="CEDULA">Cédula del Paciente:</label>
      
      <input type="text" id="CEDULA" name="CEDULA" required pattern="\d{11}" maxlength="11" oninput="validarCedula()"
       value="<?php echo htmlspecialchars($cedulaPreload, ENT_QUOTES, 'UTF-8'); ?>">
      
   
    <label for="nombreApellido">Nombre y Apellido:</label>
    <input type="text" id="nombreApellido" name="nombreApellido" readonly>
   
        <p id="telefono" style="color: blue;"></p>


        <label for="FECHACON">Selecciona la fecha de la Cita:</label>
        <input type="date" id="FECHACON" name="FECHACON" min="<?php echo date('Y-m-d'); ?>" required><br>
        
  
<label for="HORACON">Selecciona la hora de la Cita:</label>
<select id="HORACON" name="HORACON" required>
    <?php if (!empty($horarios)): ?>
        <?php foreach ($horarios as $hora): ?>
            <option value="<?php echo htmlspecialchars($hora); ?>">
              
            </option>
        <?php endforeach; ?>
    <?php else: ?>
        <option value="">No hay horarios disponibles</option>
    <?php endif; ?>
</select>


<label for="TIPOCITA">Tipo de Cita:</label>
<select id="TIPOCITA" name="TIPOCITA">
    <option value="1" <?php echo isset($cita['TIPOCITA']) && $cita['TIPOCITA'] === '1' ? 'selected' : ''; ?>>Control</option>
    <option value="2" <?php echo isset($cita['TIPOCITA']) && $cita['TIPOCITA'] === '2' ? 'selected' : ''; ?>>Seguimiento</option>
    <option value="3" <?php echo isset($cita['TIPOCITA']) && $cita['TIPOCITA'] === '3' ? 'selected' : ''; ?>>Consulta</option>
    <option value="4" <?php echo isset($cita['TIPOCITA']) && $cita['TIPOCITA'] === '4' ? 'selected' : ''; ?>>Resultados</option>
</select>

 <label for="estatus">Estatus:</label>
<select id="estatus" name="ESTATUS">
    <option value="0" <?php echo isset($_POST['ESTATUS']) && $_POST['ESTATUS'] == '0' ? 'selected' : ''; ?>>Atendido</option>
    <option value="1" <?php echo isset($_POST['ESTATUS']) && $_POST['ESTATUS'] == '1' ? 'selected' : ''; ?>>Canceló</option>
    <option value="2" <?php echo isset($_POST['ESTATUS']) && $_POST['ESTATUS'] == '2' ? 'selected' : ''; ?>>No asistió</option>
    <option value="3" <?php echo isset($_POST['ESTATUS']) && $_POST['ESTATUS'] == '3' ? 'selected' : ''; ?>>Citado</option>
    <option value="4" <?php echo isset($_POST['ESTATUS']) && $_POST['ESTATUS'] == '4' ? 'selected' : ''; ?>>Llegó tarde</option>
    <option value="5" <?php echo !isset($_POST['ESTATUS']) || $_POST['ESTATUS'] == '5' ? 'selected' : ''; ?>>Esperando</option>
    <option value="6" <?php echo !isset($_POST['ESTATUS']) || $_POST['ESTATUS'] == '6' ? 'selected' : ''; ?>>Pendiente aprobación</option>
    
</select>  
 

        <button type="submit">Guardar Cita</button>
        
        
        <!-- Botón para ir a gestionar citas -->
    <a href="gestion_citas.php" class="btn btn-primary mt-3">Gestionar Citas></a>
    
     <!-- Botón para ir a gestionar pacientes -->
    <a href="../pacientes/gestion_pacientes.php" class="btn btn-primary mt-3">Entrar Pacientes</a>
    
    
    </form>

    <h2>Citas Programadas para Hoy</h2>
    <?php if (!empty($citas)): ?>
        <table>
            <tr>
                <th>Nombre del Paciente</th>
                <th>Hora de la Cita</th>
                <th>Estatus</th>
                <th>Asistencia</th>
                <th>Acción</th>
            </tr>
            <?php foreach ($citas as $cita): ?>
 
  <tr class="
    <?php 
        echo $cita['ESTATUS'] === 'Atendido' ? 'status-atendido' : ''; 
        echo $cita['ESTATUS'] === 'Canceló' ? ' status-cancelo' : '';
        echo $cita['ESTATUS'] === 'No Asistió' ? ' status-no-asistio' : ''; 
        echo $cita['ESTATUS'] === 'Citado' ? ' status-citado' : '';  // 💙 Aquí aplicamos la clase para Citado
        echo $cita['ESTATUS'] === 'Llegó tarde' ? ' status-esperando' : ''; 
        echo $cita['ESTATUS'] === 'Esperando' ? ' status-esperando' : ''; 
        echo $cita['ESTATUS'] === 'Pendiente aprobación' ? ' status-pendiente-aprobacion' : ''; 
    ?>">


        <td><?php echo htmlspecialchars($cita['NOMBREAPELLIDO']); ?></td>
        <td><?php echo htmlspecialchars($cita['HORACON']); ?></td>
         <td><?php echo htmlspecialchars($cita['ESTATUS']); ?></td>
        <td><?php echo htmlspecialchars($cita['MODOASISTENCIA']); ?></td>
       
        <td>
            <a href="crear_cita.php?atender=<?php echo $cita['CLAVE']; ?>" class="btn-atender">Marcar Atendido</a>
            
        <a href="editar_cita.php?clave=<?php echo $cita['CLAVE']; ?>" class="btn-editar">
          <i class="fas fa-edit"></i>
        </a>
    
       <a href="eliminar_cita.php?atender=<?php echo $cita['CLAVE']; ?>" class="btn-eliminar" onclick="return confirm('¿Estás seguro de eliminar esta cita?');">
                <i class="fas fa-trash-alt"></i>
            </a> 
            <td>
    
</td>

            
        </td>
    </tr>
<?php endforeach; ?>

        </table>
    <?php else: ?>
        <p>No hay citas programadas para hoy.</p>
    <?php endif; ?>
    
  <!-- Bootstrap JS Bundle con Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>

    
</body>
</html>

