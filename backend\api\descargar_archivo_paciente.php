<?php
session_start();

// 3. Lógica de Seguridad y Autenticación
$allowed_roles = ['medico', 'administrador', 'admin']; 
$is_authenticated_and_authorized = isset($_SESSION['usuario']) && in_array($_SESSION['rol'] ?? '', $allowed_roles);

if (!$is_authenticated_and_authorized) { 
    header('Location: /mi_consultorio/no_autorizado.php');
    exit; // Es crucial salir después de una redirección
}

// 4. Conexión a la base de datos
require_once '../config/database.php'; 

// 5. Validar y obtener parámetros de la URL
$clave_pac     = $_GET['clave_pac'] ?? null; // CLAVEPAC del paciente
$clave_archivo = $_GET['clave_archivo'] ?? null; // CLAVE (ID) del registro del archivo en RESULTADOS_PACIENTES
$mode          = $_GET['mode'] ?? 'inline'; // 'inline' para visualizar, 'download' para descargar

// Verificar que los parámetros esenciales estén presentes
if (!$clave_pac || !$clave_archivo) {
    die("Parámetros insuficientes para la descarga. Faltan CLAVEPAC o CLAVE del archivo.");
}

$tableName = 'RESULTADOS_PACIENTES';

try {
    // 6. Obtener información del archivo desde la base de datos
    $stmt = $pdo->prepare("SELECT ARCHIVO, NOMBRE_ARCHIVO_ORIGINAL FROM " . $tableName . " WHERE CLAVE = ? AND CLAVEPAC = ?");
    $stmt->execute([$clave_archivo, $clave_pac]);
    $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

    // Si el archivo no se encuentra o no pertenece a este paciente
    if (!$resultado) {
        die("Archivo no encontrado en la base de datos o no autorizado para este paciente.");
    }

    $nombreUnicoArchivo = $resultado['ARCHIVO']; // Nombre único del archivo en el servidor
    $nombreOriginal     = $resultado['NOMBRE_ARCHIVO_ORIGINAL']; // Nombre original para la descarga

    // 7. Definir la ruta física ABSOLUTA al directorio de subida de archivos
    $uploadDir = dirname(__DIR__) . '/resultados/upload/'; 
    $rutaFisicaArchivo = $uploadDir . $nombreUnicoArchivo;
    
    // 8. Verificar si el archivo físico existe en el servidor
    if (!file_exists($rutaFisicaArchivo)) {
        die("El archivo físico no existe en el servidor. Revise la ruta de la carpeta de subida: " . htmlspecialchars($rutaFisicaArchivo));
    }

  
  
          // 1) Detectar MIME (funciona para png, jpg, pdf, docx, bmp, lo que sea…)
            $finfo    = finfo_open(FILEINFO_MIME_TYPE);
            $mimeType = finfo_file($finfo, $rutaFisicaArchivo) ?: 'application/octet-stream';
            finfo_close($finfo);

            // 2) Enviar headers mínimos
            header('Content-Type: ' . $mimeType);
            header('Content-Disposition: ' . ($mode === 'download' ? 'attachment' : 'inline') .
            '; filename="' . basename($nombreOriginal) . '"');
            header('Content-Length: ' . filesize($rutaFisicaArchivo));

            // 3) Servir el archivo
            readfile($rutaFisicaArchivo);
            exit;

} catch (PDOException $e) {
    // Manejo de errores de base de datos
    error_log("Error al descargar archivo: " . $e->getMessage()); // Registrar el error para depuración
    die("Error interno del servidor al procesar la descarga."); // Mensaje amigable al usuario
}
?>