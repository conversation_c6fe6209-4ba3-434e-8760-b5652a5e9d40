<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Pacientes</title>
    <link rel="stylesheet" href="public/assets/css/styles.css"> <!-- Asegúrate de vincular tu CSS -->
    <!-- Agregar <PERSON>trap para los Tabs -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>

<div class="container mt-5">
    <h1 class="text-center">Gestión de Pacientes</h1>

    <!-- Men<PERSON> de Pestañas -->
    <ul class="nav nav-tabs" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
            <a class="nav-link active" id="registro-tab" data-toggle="tab" href="#registro" role="tab" aria-controls="registro" aria-selected="true">Registrar/Actualizar Paciente</a>
        </li>
        <li class="nav-item" role="presentation">
            <a class="nav-link" id="buscar-tab" data-toggle="tab" href="#buscar" role="tab" aria-controls="buscar" aria-selected="false">Buscar Pacientes</a>
        </li>
    </ul>

    <div class="tab-content" id="myTabContent">
        <!-- Pestaña de Registro/Actualización -->
        <div class="tab-pane fade show active" id="registro" role="tabpanel" aria-labelledby="registro-tab">
            <form action="formulario_paciente.php" method="POST">
                <div class="form-group">
                    <label for="NOMBRES">Nombres:</label>
                    <input type="text" class="form-control" id="nombre" name="NOMBRES" required>
                </div>

                <div class="form-group">
                    <label for="APELLIDOS">Apellido:</label>
                    <input type="text" class="form-control" id="apellido" name="APELLIDOS" required>
                </div>

                <div class="form-group">
                    <label for="EDAD">Edad:</label>
                    <input type="number" class="form-control" id="edad" name="edad" required>
                </div>

                <div class="form-group">
                    <label for="CEDULA">Cédula:</label>
                    <input type="text" class="form-control" id="cedula" name="CEDULA" required>
                </div>

                <!-- Mensaje si la cédula ya existe -->
                <?php if (isset($mensaje_error)): ?>
                    <div class="alert alert-warning">
                        <?php echo $mensaje_error; ?>
                        <a href="formulario_paciente.php?id=<?php echo $paciente_id; ?>" class="btn btn-warning">Actualizar Paciente</a>
                    </div>
                <?php endif; ?>

                <!-- Mensaje de éxito -->
                <?php if (isset($mensaje_exito)): ?>
                    <div class="alert alert-success">
                        <?php echo $mensaje_exito; ?>
                    </div>
                <?php endif; ?>

                <button type="submit" class="btn btn-primary">Registrar Paciente</button>
            </form>
        </div>

        <!-- Pestaña de Búsqueda -->
        <div class="tab-pane fade" id="buscar" role="tabpanel" aria-labelledby="buscar-tab">
            <!-- Formulario de búsqueda de pacientes -->
            <form action="buscar_pacientes.php" method="GET">
                <div class="form-group">
                    <label for="buscar">Buscar por cédula o nombre:</label>
                    <input type="text" class="form-control" id="buscar" name="buscar" required>
                </div>
                <button type="submit" class="btn btn-success">Buscar</button>
            </form>

            <!-- Aquí puedes incluir la lógica para mostrar los resultados de búsqueda -->
            <?php
            if (isset($_GET['buscar'])) {
                $buscar = $_GET['buscar'];
                $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CEDULA LIKE ? OR NOMBRES LIKE ?");
                $stmt->execute(["%$buscar%", "%$buscar%"]);
                $pacientes = $stmt->fetchAll();

                if ($pacientes) {
                    echo "<table class='table table-bordered mt-3'>";
                    echo "<thead><tr><th>Nombre</th><th>Apellido</th><th>Cédula</th><th>Acciones</th></tr></thead><tbody>";
                    foreach ($pacientes as $paciente) {
                        echo "<tr>";
                        echo "<td>{$paciente['NOMBRES']}</td>";
                        echo "<td>{$paciente['APELLIDOS']}</td>";
                        echo "<td>{$paciente['CEDULA']}</td>";
                        echo "<td><a href='editar_paciente.php?id={$paciente['CLAVE']}' class='btn btn-warning'>Editar</a></td>";
                        echo "</tr>";
                    }
                    echo "</tbody></table>";
                } else {
                    echo "<p>No se encontraron pacientes.</p>";
                }
            }
            ?>
        </div>
    </div>
</div>

<!-- Scripts de Bootstrap -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

</body>
</html>
