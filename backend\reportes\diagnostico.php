<?php
// backend/reportes/diagnostico.php
// Script de diagnóstico completo para el sistema de reportes

session_start();

// Simular sesión para prueba
$_SESSION['usuario'] = 'admin';
$_SESSION['rol'] = 'admin';

echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Diagnóstico del Sistema de Reportes</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🔍 Diagnóstico del Sistema de Reportes</h1>";

// 1. Verificar archivos principales
echo "<div class='section'>";
echo "<h2>1. Verificación de Archivos</h2>";

$archivos_principales = [
    'Reportes de Citas' => 'citas_reportes.php',
    'Reportes de Pacientes' => 'pacientes_reportes.php',
    'Reportes Médicos' => 'medicos_reportes.php',
    'Reportes Financieros' => 'financieros_reportes.php',
    'Reportes Administrativos' => 'administrativos_reportes.php',
    'Reportes Personalizados' => 'personalizados_reportes.php'
];

foreach ($archivos_principales as $nombre => $archivo) {
    if (file_exists($archivo)) {
        echo "<p class='success'>✅ $nombre: $archivo</p>";
    } else {
        echo "<p class='error'>❌ $nombre: $archivo (NO ENCONTRADO)</p>";
    }
}
echo "</div>";

// 2. Verificar librerías
echo "<div class='section'>";
echo "<h2>2. Verificación de Librerías</h2>";

$librerias = [
    'ValidadorReportes' => '../lib/ValidadorReportes.php',
    'ReportePDF' => '../lib/ReportePDF.php'
];

foreach ($librerias as $nombre => $archivo) {
    if (file_exists($archivo)) {
        echo "<p class='success'>✅ $nombre: $archivo</p>";
        
        // Verificar sintaxis
        $output = [];
        $return_var = 0;
        exec("php -l \"$archivo\" 2>&1", $output, $return_var);
        
        if ($return_var === 0) {
            echo "<small class='success'>   Sintaxis correcta</small><br>";
        } else {
            echo "<small class='error'>   Error de sintaxis: " . implode(' ', $output) . "</small><br>";
        }
    } else {
        echo "<p class='error'>❌ $nombre: $archivo (NO ENCONTRADO)</p>";
    }
}
echo "</div>";

// 3. Verificar APIs
echo "<div class='section'>";
echo "<h2>3. Verificación de APIs</h2>";

$apis = [
    'Exportar Citas' => 'api/exportar_citas.php',
    'Exportar Pacientes' => 'api/exportar_pacientes.php',
    'Exportar Médicos' => 'api/exportar_medicos.php',
    'Exportar Financieros' => 'api/exportar_financieros.php',
    'Exportar Administrativos' => 'api/exportar_administrativos.php',
    'Reporte Rápido' => 'api/reporte_rapido.php',
    'Estadísticas Tiempo Real' => 'api/estadisticas_tiempo_real.php'
];

foreach ($apis as $nombre => $archivo) {
    if (file_exists($archivo)) {
        echo "<p class='success'>✅ $nombre: $archivo</p>";
    } else {
        echo "<p class='error'>❌ $nombre: $archivo (NO ENCONTRADO)</p>";
    }
}
echo "</div>";

// 4. Verificar base de datos
echo "<div class='section'>";
echo "<h2>4. Verificación de Base de Datos</h2>";

try {
    require_once '../config/database.php';
    echo "<p class='success'>✅ Conexión a base de datos establecida</p>";
    
    // Verificar tablas principales
    $tablas = ['PACIENTES', 'CITAMEDIC', 'FACTURAS', 'EXAMENFISICO', 'EMPRESA', 'usuarios'];
    
    foreach ($tablas as $tabla) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) as total FROM $tabla");
            $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            echo "<p class='success'>✅ Tabla $tabla: $total registros</p>";
        } catch (Exception $e) {
            echo "<p class='warning'>⚠️ Tabla $tabla: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p class='error'>❌ Error de conexión: " . $e->getMessage() . "</p>";
}
echo "</div>";

// 5. Verificar estados de citas
echo "<div class='section'>";
echo "<h2>5. Verificación de Estados de Citas</h2>";

if (isset($pdo)) {
    try {
        $stmt = $pdo->query("
            SELECT ASISTENCIA,
                   CASE ASISTENCIA
                       WHEN 0 THEN 'Atendido'
                       WHEN 1 THEN 'Canceló'
                       WHEN 2 THEN 'No asistió'
                       WHEN 3 THEN 'Citado'
                       WHEN 4 THEN 'Llegó tarde'
                       WHEN 5 THEN 'Esperando'
                       WHEN 6 THEN 'Pendiente aprobación'
                       ELSE 'Desconocido'
                   END as estado_texto,
                   COUNT(*) as cantidad
            FROM CITAMEDIC
            GROUP BY ASISTENCIA
            ORDER BY ASISTENCIA
        ");
        
        $estados_bd = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($estados_bd)) {
            echo "<p class='warning'>⚠️ No hay datos de citas en la base de datos</p>";
        } else {
            echo "<table>";
            echo "<tr><th>Código</th><th>Estado</th><th>Cantidad</th></tr>";
            foreach ($estados_bd as $estado) {
                echo "<tr>";
                echo "<td>{$estado['ASISTENCIA']}</td>";
                echo "<td>{$estado['estado_texto']}</td>";
                echo "<td>{$estado['cantidad']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error consultando estados: " . $e->getMessage() . "</p>";
    }
}
echo "</div>";

// 6. Verificar validador
echo "<div class='section'>";
echo "<h2>6. Verificación del Validador</h2>";

if (file_exists('../lib/ValidadorReportes.php')) {
    try {
        require_once '../lib/ValidadorReportes.php';
        $validador = new ValidadorReportes();
        
        echo "<p class='success'>✅ Clase ValidadorReportes cargada correctamente</p>";
        
        // Probar validación de estados
        $estados_prueba = [0, 1, 2, 3, 4, 5, 6, 7, -1];
        echo "<h4>Prueba de validación de estados:</h4>";
        
        foreach ($estados_prueba as $estado) {
            $validador->limpiar();
            $resultado = $validador->validarEstadoCita($estado);
            
            if ($resultado) {
                echo "<p class='success'>Estado $estado: ✅ Válido</p>";
            } else {
                echo "<p class='error'>Estado $estado: ❌ Inválido</p>";
                foreach ($validador->obtenerErrores() as $error) {
                    echo "<small>  - $error</small><br>";
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<p class='error'>❌ Error cargando ValidadorReportes: " . $e->getMessage() . "</p>";
    }
}
echo "</div>";

// 7. Verificar sintaxis del archivo principal
echo "<div class='section'>";
echo "<h2>7. Verificación de Sintaxis - citas_reportes.php</h2>";

if (file_exists('citas_reportes.php')) {
    $output = [];
    $return_var = 0;
    exec("php -l \"citas_reportes.php\" 2>&1", $output, $return_var);
    
    if ($return_var === 0) {
        echo "<p class='success'>✅ Sintaxis correcta en citas_reportes.php</p>";
    } else {
        echo "<p class='error'>❌ Error de sintaxis en citas_reportes.php:</p>";
        echo "<pre>" . implode("\n", $output) . "</pre>";
    }
} else {
    echo "<p class='error'>❌ Archivo citas_reportes.php no encontrado</p>";
}
echo "</div>";

echo "<div class='section'>";
echo "<h2>✅ Diagnóstico Completado</h2>";
echo "<p><a href='citas_reportes.php'>🔗 Ir a Reportes de Citas</a></p>";
echo "<p><a href='../../../reportes.php'>🔗 Ir al Dashboard de Reportes</a></p>";
echo "</div>";

echo "</body></html>";
?>
