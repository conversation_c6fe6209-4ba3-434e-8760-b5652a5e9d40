<?php
$pdo = require_once __DIR__ . '/../config/database.php';

$CLAVEPAC = $_POST['CLAVEPAC'] ?? '';
$CEDULA   = $_POST['CEDULA']   ?? '';

if (!$CLAVEPAC || !$CEDULA) {
    exit("❌ Faltan datos obligatorios.");
}

// 1) Buscar FUM (prioridad: OBSTETRICIA > ANTGINECOBSTETRICO)
$stmt = $pdo->prepare("SELECT FUM FROM OBSTETRICIA WHERE CLAVEPAC = :CLAVEPAC AND FUM IS NOT NULL ORDER BY FECHA_CAP DESC LIMIT 1");
$stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
$FUM = $stmt->fetchColumn();

if (!$FUM) {
    $stmt = $pdo->prepare("SELECT FUM FROM ANTGINECOBSTETRICO WHERE CLAVEPAC = :CLAVEPAC AND FUM IS NOT NULL ORDER BY CLAVE DESC LIMIT 1");
    $stmt->execute(['CLAVEPAC' => $CLAVEPAC]);
    $FUM = $stmt->fetchColumn();
}

if (!$FUM) {
  header("Location: obstetricia.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA&error=falta_fum");
  exit;
}

// 2) Calcular el próximo número de embarazo
$stmt = $pdo->prepare("SELECT COALESCE(MAX(NUMEMB), 0) FROM EMBARAZO WHERE CLAVEPAC = ?");
$stmt->execute([$CLAVEPAC]);
$maxNumEmb = (int) $stmt->fetchColumn();
$NUMEMB = $maxNumEmb + 1;

// 3) Insertar nuevo embarazo 

$sql = "INSERT INTO EMBARAZO 
          (CLAVEPAC, NUMEMB, FECHAINIEMB, ESTATUSEMB, CEDULA, SINCRONIZADO)
        VALUES 
          (:CLAVEPAC, :NUMEMB, CURRENT_DATE, 'A', :CEDULA, 0)";
$stmt = $pdo->prepare($sql);
$stmt->execute([
    'CLAVEPAC' => $CLAVEPAC,
    'NUMEMB'   => $NUMEMB,
    'CEDULA'   => $CEDULA
]);

// Obtener la CLAVE del embarazo recién creado
$CLAVEEMB = $pdo->lastInsertId();

// Crear primer control obstétrico con FUM
$stmt = $pdo->prepare("INSERT INTO OBSTETRICIA 
    (CLAVEPAC, CLAVEEMB, NUMEMB, FUM, CEDULA, ESTATUSEMB, SINCRONIZADO) 
    VALUES (:CLAVEPAC, :CLAVEEMB, :NUMEMB, :FUM, :CEDULA,'A', 0)");
$stmt->execute([
    'CLAVEPAC' => $CLAVEPAC,
    'CLAVEEMB' => $CLAVEEMB,
    'NUMEMB'   => $NUMEMB,
    'FUM'      => $FUM,
    'CEDULA'   => $CEDULA
]);



// 4) Marcar paciente como embarazada
$upd = $pdo->prepare("UPDATE PACIENTES SET STEMBARAZO = 1, SINCRONIZADO = 0 WHERE CLAVE = :CLAVEPAC");
$upd->execute(['CLAVEPAC' => $CLAVEPAC]);

// 5) Redirigir al formulario obstétrico
header("Location: obstetricia.php?CLAVEPAC=$CLAVEPAC&CEDULA=$CEDULA");
exit;



