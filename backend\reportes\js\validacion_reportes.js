// backend/reportes/js/validacion_reportes.js
// Validación del lado del cliente para reportes

class ValidadorReportesFrontend {
    constructor() {
        this.errores = [];
        this.advertencias = [];
    }

    // Validar formato de fecha
    validarFecha(fecha, nombre = 'fecha') {
        if (!fecha) {
            this.errores.push(`${nombre} es requerida`);
            return false;
        }

        const fechaObj = new Date(fecha);
        if (isNaN(fechaObj.getTime())) {
            this.errores.push(`${nombre} tiene formato inválido`);
            return false;
        }

        return true;
    }

    // Validar rango de fechas
    validarRangoFechas(fechaInicio, fechaFin) {
        if (!this.validarFecha(fechaInicio, 'Fecha de inicio')) return false;
        if (!this.validarFecha(fechaFin, 'Fecha de fin')) return false;

        const inicio = new Date(fechaInicio);
        const fin = new Date(fechaFin);
        const hoy = new Date();

        if (inicio > fin) {
            this.errores.push('La fecha de inicio no puede ser mayor que la fecha de fin');
            return false;
        }

        // Advertencia para fechas muy antiguas
        const hace5Anos = new Date();
        hace5Anos.setFullYear(hace5Anos.getFullYear() - 5);
        if (inicio < hace5Anos) {
            this.advertencias.push('La fecha de inicio es muy antigua (más de 5 años)');
        }

        // Advertencia para fechas futuras
        const en1Ano = new Date();
        en1Ano.setFullYear(en1Ano.getFullYear() + 1);
        if (fin > en1Ano) {
            this.advertencias.push('La fecha de fin está muy en el futuro');
        }

        // Advertencia para rangos muy amplios
        const diferenciaDias = (fin - inicio) / (1000 * 60 * 60 * 24);
        if (diferenciaDias > 730) { // 2 años
            this.advertencias.push('El rango de fechas es muy amplio (más de 2 años). El reporte puede tardar en generarse');
        }

        return true;
    }

    // Validar edad
    validarEdad(edadMin, edadMax) {
        if (edadMin !== '' && (isNaN(edadMin) || edadMin < 0 || edadMin > 120)) {
            this.errores.push('Edad mínima debe ser un número entre 0 y 120');
            return false;
        }

        if (edadMax !== '' && (isNaN(edadMax) || edadMax < 0 || edadMax > 120)) {
            this.errores.push('Edad máxima debe ser un número entre 0 y 120');
            return false;
        }

        if (edadMin !== '' && edadMax !== '' && parseInt(edadMin) > parseInt(edadMax)) {
            this.errores.push('La edad mínima no puede ser mayor que la edad máxima');
            return false;
        }

        return true;
    }

    // Limpiar errores y advertencias
    limpiar() {
        this.errores = [];
        this.advertencias = [];
    }

    // Obtener errores
    obtenerErrores() {
        return this.errores;
    }

    // Obtener advertencias
    obtenerAdvertencias() {
        return this.advertencias;
    }

    // Verificar si hay errores
    tieneErrores() {
        return this.errores.length > 0;
    }

    // Verificar si hay advertencias
    tieneAdvertencias() {
        return this.advertencias.length > 0;
    }

    // Mostrar errores en el DOM
    mostrarErrores(contenedorId) {
        const contenedor = document.getElementById(contenedorId);
        if (!contenedor) return;

        contenedor.innerHTML = '';

        if (this.tieneErrores()) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                <h6><i class="fas fa-exclamation-triangle me-2"></i>Errores de Validación:</h6>
                <ul class="mb-0">
                    ${this.errores.map(error => `<li>${error}</li>`).join('')}
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            contenedor.appendChild(alertDiv);
        }

        if (this.tieneAdvertencias()) {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning alert-dismissible fade show';
            alertDiv.innerHTML = `
                <h6><i class="fas fa-info-circle me-2"></i>Advertencias:</h6>
                <ul class="mb-0">
                    ${this.advertencias.map(advertencia => `<li>${advertencia}</li>`).join('')}
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            contenedor.appendChild(alertDiv);
        }
    }
}

// Funciones de utilidad para validación de formularios
function validarFormularioReporte(formId, validaciones) {
    const form = document.getElementById(formId);
    if (!form) return true;

    const validador = new ValidadorReportesFrontend();
    const formData = new FormData(form);

    // Aplicar validaciones específicas
    if (validaciones.fechas) {
        const fechaInicio = formData.get('fecha_inicio');
        const fechaFin = formData.get('fecha_fin');
        validador.validarRangoFechas(fechaInicio, fechaFin);
    }

    if (validaciones.edad) {
        const edadMin = formData.get('edad_min');
        const edadMax = formData.get('edad_max');
        validador.validarEdad(edadMin, edadMax);
    }

    // Mostrar errores
    validador.mostrarErrores('mensajes-validacion');

    return !validador.tieneErrores();
}

// Validación en tiempo real para campos de fecha
function configurarValidacionTiempoReal() {
    const fechaInicio = document.getElementById('fecha_inicio');
    const fechaFin = document.getElementById('fecha_fin');

    if (fechaInicio && fechaFin) {
        function validarFechas() {
            const validador = new ValidadorReportesFrontend();
            validador.validarRangoFechas(fechaInicio.value, fechaFin.value);
            
            // Limpiar clases anteriores
            fechaInicio.classList.remove('is-invalid', 'is-valid');
            fechaFin.classList.remove('is-invalid', 'is-valid');

            if (validador.tieneErrores()) {
                fechaInicio.classList.add('is-invalid');
                fechaFin.classList.add('is-invalid');
            } else {
                fechaInicio.classList.add('is-valid');
                fechaFin.classList.add('is-valid');
            }
        }

        fechaInicio.addEventListener('change', validarFechas);
        fechaFin.addEventListener('change', validarFechas);
    }
}

// Validación para campos de edad
function configurarValidacionEdad() {
    const edadMin = document.getElementById('edad_min');
    const edadMax = document.getElementById('edad_max');

    if (edadMin && edadMax) {
        function validarEdades() {
            const validador = new ValidadorReportesFrontend();
            validador.validarEdad(edadMin.value, edadMax.value);
            
            // Limpiar clases anteriores
            edadMin.classList.remove('is-invalid', 'is-valid');
            edadMax.classList.remove('is-invalid', 'is-valid');

            if (validador.tieneErrores()) {
                edadMin.classList.add('is-invalid');
                edadMax.classList.add('is-invalid');
            } else if (edadMin.value || edadMax.value) {
                edadMin.classList.add('is-valid');
                edadMax.classList.add('is-valid');
            }
        }

        edadMin.addEventListener('input', validarEdades);
        edadMax.addEventListener('input', validarEdades);
    }
}

// Prevenir envío de formulario con errores
function configurarPrevencionEnvio() {
    const formularios = document.querySelectorAll('form[data-validar]');
    
    formularios.forEach(form => {
        form.addEventListener('submit', function(e) {
            const tipoValidacion = form.dataset.validar;
            let validaciones = {};

            switch (tipoValidacion) {
                case 'citas':
                    validaciones = { fechas: true };
                    break;
                case 'pacientes':
                    validaciones = { fechas: true, edad: true };
                    break;
                case 'medicos':
                    validaciones = { fechas: true, edad: true };
                    break;
                case 'financieros':
                    validaciones = { fechas: true };
                    break;
                default:
                    validaciones = { fechas: true };
            }

            if (!validarFormularioReporte(form.id, validaciones)) {
                e.preventDefault();
                
                // Scroll hacia los mensajes de error
                const mensajes = document.getElementById('mensajes-validacion');
                if (mensajes) {
                    mensajes.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }
        });
    });
}

// Mostrar indicador de carga durante exportación
function mostrarIndicadorCarga() {
    const botonesExportar = document.querySelectorAll('[onclick*="exportar"]');
    
    botonesExportar.forEach(boton => {
        boton.addEventListener('click', function() {
            const icono = this.querySelector('i');
            const textoOriginal = this.innerHTML;
            
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generando...';
            
            // Restaurar después de 10 segundos (timeout)
            setTimeout(() => {
                this.disabled = false;
                this.innerHTML = textoOriginal;
            }, 10000);
        });
    });
}

// Inicializar validaciones cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', function() {
    configurarValidacionTiempoReal();
    configurarValidacionEdad();
    configurarPrevencionEnvio();
    mostrarIndicadorCarga();
    
    // Crear contenedor para mensajes si no existe
    const container = document.querySelector('.container');
    if (container && !document.getElementById('mensajes-validacion')) {
        const mensajesDiv = document.createElement('div');
        mensajesDiv.id = 'mensajes-validacion';
        mensajesDiv.style.marginBottom = '20px';
        
        // Insertar después del header
        const header = document.querySelector('.header-section');
        if (header && header.nextSibling) {
            container.insertBefore(mensajesDiv, header.nextSibling.nextSibling);
        } else {
            container.insertBefore(mensajesDiv, container.firstChild);
        }
    }
});

// Exportar para uso global
window.ValidadorReportesFrontend = ValidadorReportesFrontend;
window.validarFormularioReporte = validarFormularioReporte;
