<?php
// Configurar encabezados para permitir solicitudes CORS y definir formato JSON
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");

// Incluir la configuración de la base de datos y otros parámetros
require_once '../config/database.php';
$config = include '../config/config.php'; // Ruta al archivo de configuración

// Obtener método HTTP y datos enviados en la solicitud
$method = $_SERVER['REQUEST_METHOD']; // GET, POST, PUT, DELETE
$input = json_decode(file_get_contents("php://input"), true); // Datos en JSON
$headers = getallheaders(); // Encabezados de la solicitud


// Mostrar los datos recibidos para depuración (quitar después de verificar)


// Token de autenticación
$auth_token = $config['auth_token']; // Desde el archivo config.php

// Verificar el token de autorización
/*if (!isset($headers['Authorization']) || $headers['Authorization'] !== "Bearer $auth_token") {
    http_response_code(401);
    echo json_encode(["message" => "Acceso no autorizado"]);
    exit;
}
*/
try {
    // Conexión a la base de datos con utf8mb4 para manejar caracteres especiales
    $pdo = new PDO(
        "mysql:host={$config['db_host']};dbname={$config['db_name']};charset=utf8mb4",
        $config['db_user'],
        $config['db_password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );

    // Manejo de diferentes métodos HTTP (GET, POST, PUT, DELETE)
    switch ($method) {
        case 'GET':
            // Leer todos los pacientes o uno específico
            if (isset($_GET['CLAVE'])) {
                $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
                $stmt->execute([$_GET['CLAVE']]);
                $data = $stmt->fetch();
            } else {
                $stmt = $pdo->query("SELECT * FROM PACIENTES ORDER BY CLAVE DESC");
                $data = $stmt->fetchAll();
            }
            echo json_encode($data);
            break;

       case 'POST':
    // Crear un nuevo paciente
    if (!isset($input['NOMBRES'], $input['APELLIDOS'], $input['CEDULA'], $input['SEXO'])) {
        http_response_code(400);
        echo json_encode(["message" => "Datos incompletos"]);
        exit;
    }



    // Obtener el valor máximo de "registro" en la tabla PACIENTES y sumarle 1
    $stmt = $pdo->query("SELECT MAX(REGISTRO) AS MAX_REGISTRO FROM PACIENTES");
    $result = $stmt->fetch();
    $REGISTRO = $result['MAX_REGISTRO'] + 1; // Incrementar el valor de registro

    // Insertar el nuevo paciente con el registro calculado
    $stmt = $pdo->prepare("
        INSERT INTO PACIENTES (NOMBRES, APELLIDOS, CEDULA, SEXO, TELEFONO, CELULAR, ECORREO, NACIONALIDAD, FECHANAC, DIRECCIONRESP, REGISTRO)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $input['NOMBRES'], $input['APELLIDOS'], $input['CEDULA'], $input['SEXO'],
        $input['TELEFONO'] ?? null, $input['CELULAR'] ?? null, $input['ECORREO'] ?? null,
        $input['NACIONALIDAD'] ?? null, $input['FECHANAC'] ?? null, $input['DIRECCIONRESP'] ?? null, $REGISTRO 
        // Insertar el registro calculado
    ]);

    echo json_encode([ "message" => "Paciente registrado con éxito", "CLAVE" => $pdo->lastInsertId() ]);
    break;


        case 'PUT':
            // Actualizar un paciente
            if (!isset($_GET['CLAVE'])) {
                http_response_code(400);
                echo json_encode(["message" => "CLAVE del paciente es requerido"]);
                exit;
            }

            $stmt = $pdo->prepare("
                UPDATE PACIENTES SET
                    NOMBRES = ?, APELLIDOS = ?, CEDULA = ?, SEXO = ?, TELEFONO = ?, CELULAR = ?, ECORREO = ?, NACIONALIDAD = ?, FECHANAC = ?, DIRECCIONRESP = ?
                WHERE CLAVE = ?
            ");
            $stmt->execute([
                $input['NOMBRES'], $input['APELLIDOS'], $input['CEDULA'], $input['SEXO'],
                $input['TELEFONO'] ?? null, $input['CELULAR'] ?? null, $input['ECORREO'] ?? null,
                $input['NACIONALIDAD'] ?? null, $input['FECHANAC'] ?? null, $input['DIRECCIONRESP'] ?? null,
                $_GET['CLAVE']
            ]);

            echo json_encode(["message" => "Paciente actualizado con éxito"]);
            break;

        case 'DELETE':
            // Eliminar un paciente
            if (!isset($_GET['CLAVE'])) {
                http_response_code(400);
                echo json_encode(["message" => "CLAVE del paciente es requerido"]);
                exit;
            }

            $stmt = $pdo->prepare("DELETE FROM PACIENTES WHERE CLAVE = ?");
            $stmt->execute([$_GET['CLAVE']]);

            echo json_encode(["message" => "Paciente eliminado con éxito"]);
            break;

        case 'OPTIONS':
            // Responder a solicitudes preflight de CORS
            http_response_code(200);
            break;

        default:
            http_response_code(405);
            echo json_encode(["message" => "Método no permitido"]);
            break;
    }
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(["message" => "Error del servidor", "error" => $e->getMessage()]);
}
?>
