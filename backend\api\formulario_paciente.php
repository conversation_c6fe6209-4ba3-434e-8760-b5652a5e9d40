<?php
require_once '../config/database.php';
$config = include '../config/config.php'; // Ruta al archivo de configuración

$mensaje_error = '';
$mensaje_exito = '';

$paciente = null;
if (isset($_GET['CLAVE'])) {
    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CLAVE = ?");
    $stmt->execute([$_GET['CLAVE']]);
    $paciente = $stmt->fetch();
}

 // Procesar los datos del formulario
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Recoger datos del formulario
    $NOMBRES = $_POST['NOMBRES'];
    $APELLIDOS = $_POST['APELLIDOS'];
    $CEDULA = $_POST['CEDULA'];
    $FECHANAC = $_POST['FECHANAC'];
    $SEXO = $_POST['SEXO'];
    $NACIONALIDAD = $_POST['NACIONALIDAD'];
    $TELEFONO = $_POST['TELEFONO'];
    $RH = $_POST['RH'];  
    $ECORREO = $_POST['ECORREO'];

    // Verificar si la cédula ya existe en otro paciente
    if ($paciente) {
        // Si estamos editando, excluir el paciente actual de la verificación
        $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CEDULA = ? AND CLAVE != ?");
        $stmt->execute([$CEDULA, $paciente['CLAVE']]);
    } else {
        // Si estamos creando uno nuevo, verificar cédula normalmente
        $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CEDULA = ?");
        $stmt->execute([$CEDULA]);
    }

    $paciente_existente = $stmt->fetch();

    if ($paciente_existente) {
        // Si la cédula ya existe en otro paciente
        $mensaje_error = "¡Atención! La cédula " . $paciente_existente['CEDULA'] . " ya está registrada con el nombre: " . $paciente_existente['NOMBREAPELLIDO'] . ".";

        // Redirigir hacia el mismo formulario con la CLAVE del paciente existente y una bandera
        header("Location: formulario_paciente.php?CLAVE=" . $paciente_existente['CLAVE'] . "&cedula_duplicada=1");
        
        exit;
        
    } else {
        if ($paciente) {
            // Actualizar paciente existente
            $stmt = $pdo->prepare("UPDATE PACIENTES SET NOMBRES = ?, APELLIDOS = ?, CEDULA = ?, FECHANAC = ?, SEXO = ?, NACIONALIDAD = ?, TELEFONO = ?, RH = ?, ECORREO = ? WHERE CLAVE = ?");
            $stmt->execute([$NOMBRES, $APELLIDOS, $CEDULA, $FECHANAC, $SEXO, $NACIONALIDAD, $TELEFONO, $RH, $ECORREO, $paciente['CLAVE']]);
            $mensaje_exito = "Paciente actualizado con éxito.";
        } else {
            // Calcular el valor del nuevo REGISTRO
            $stmt = $pdo->query("SELECT MAX(REGISTRO) AS max_registro FROM PACIENTES");
            $result = $stmt->fetch();
            $nuevo_registro = $result['max_registro'] ? $result['max_registro'] + 1 : 1; // Si no hay registros, empieza en 1

            // Insertar nuevo paciente
            $stmt = $pdo->prepare("INSERT INTO PACIENTES (REGISTRO, NOMBRES, APELLIDOS, CEDULA, FECHANAC, SEXO, NACIONALIDAD, TELEFONO, RH, ECORREO) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$nuevo_registro, $NOMBRES, $APELLIDOS, $CEDULA, $FECHANAC, $SEXO, $NACIONALIDAD, $TELEFONO, $RH, $ECORREO]);
            $mensaje_exito = "Paciente registrado con éxito.";
        }

        // Redirigir después de la inserción o actualización si hay éxito
        if ($mensaje_exito) {
            header("Location: gestion_pacientes.php?tab=registrar");
            exit;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestión de Pacientes</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">

<script>
    function validarFormulario() {
        var nombres = document.getElementById("NOMBRES").value;
        var apellidos = document.getElementById("APELLIDOS").value;
        var cedula = document.getElementById("CEDULA").value;
        var fechanac = document.getElementById("FECHANAC").value;
        var telefono = document.getElementById("TELEFONO").value;
        var correo = document.getElementById("ECORREO").value;
     
        if (nombres.length < 2 || nombres.length > 50) {
            alert("El nombre debe tener entre 2 y 50 caracteres.");
            return false;
        }
        
        if (apellidos.length < 2 || apellidos.length > 50) {
            alert("El apellido debe tener entre 2 y 50 caracteres.");
            return false;
        }

        if (cedula.length !== 11 || !/^\d+$/.test(cedula)) {
            alert("La cédula debe tener exactamente 11 caracteres numéricos.");
            return false;
        }

        if (correo && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(correo)) {
            alert("Por favor ingrese un correo electrónico válido.");
            return false;
        }

        if (telefono && !/^[0-9\s\-\(\)]+$/.test(telefono)) {
            alert("Por favor ingrese un teléfono válido (solo números, guiones y espacios).");
            return false;
        }

        return true;
    }

function cancelarYEnfocarCedula() {
    // Retrocede al estado anterior
    window.history.back();

    // Forzar el foco solo en el campo CEDULA después de un breve retraso
    setTimeout(() => {
        // Asegurarse de enfocar correctamente el campo con id="CEDULA"
        const campoCedula = document.getElementById('CEDULA');
        if (campoCedula) {
            campoCedula.focus(); // Enfoca el campo
            campoCedula.select(); // Opcional: selecciona el texto si hay un valor
        }
    }, 100);
}


</script>

</head>


<body>
<div class="container mt-5">
    <?php 
    // Si se detectó cédula duplicada y se redirigió, mostrar un mensaje
    if (isset($_GET['cedula_duplicada']) && $_GET['cedula_duplicada'] == 1) {
        echo '<div class="alert alert-warning">La cédula ingresada ya existe. 
        Se han cargado los datos del paciente existente para su visualización/edición.</div>';
    }
    ?>

  <form action="formulario_paciente.php<?php echo isset($paciente) ? '?CLAVE=' . htmlspecialchars($paciente['CLAVE']) : ''; ?>" method="POST" onsubmit="return validarFormulario();">
  
  
  
      <div class="form-group">  
        <label for="CEDULA">Cédula:</label>
        <input type="text" class="form-control" id="CEDULA" name="CEDULA" value="<?php echo $paciente ? htmlspecialchars($paciente['CEDULA']) : ''; ?>" required oninput="validarNumeros(event)" maxlength="11">

    </div>
  
  
    <div class="form-group">
        <label for="NOMBRES">Nombre:</label>
        <input type="text" class="form-control" id="NOMBRES" name="NOMBRES" value="<?php echo $paciente ? htmlspecialchars($paciente['NOMBRES']) : ''; ?>" required>
    </div>

    <div class="form-group">
        <label for="APELLIDOS">Apellido:</label>
        <input type="text" class="form-control" id="APELLIDOS" name="APELLIDOS" value="<?php echo $paciente ? htmlspecialchars($paciente['APELLIDOS']) : ''; ?>" required>
    </div>

   

    <div class="form-group">
        <label for="FECHANAC">Fecha de Nacimiento:</label>
        <input type="date" class="form-control" id="FECHANAC" name="FECHANAC" value="<?php echo $paciente ? htmlspecialchars($paciente['FECHANAC']) : ''; ?>" required oninput="validarFechaNacimiento(event)">
    </div>

    <div class="form-group">
        <label for="SEXO">Sexo:</label>
        <select class="form-control" id="SEXO" name="SEXO">
            <option value="" <?= !$paciente || htmlspecialchars($paciente['SEXO']) == '' ? 'selected' : '' ?>>Seleccionar</option>
            <option value="M" <?= $paciente && htmlspecialchars($paciente['SEXO']) == 'M' ? 'selected' : '' ?>>Masculino</option>
            <option value="F" <?= $paciente && htmlspecialchars($paciente['SEXO']) == 'F' ? 'selected' : '' ?>>Femenino</option>
        </select>
    </div>

    <div class="form-group">
        <label for="NACIONALIDAD">Nacionalidad:</label>
        <input type="text" class="form-control" id="NACIONALIDAD" name="NACIONALIDAD" value="<?php echo $paciente ? htmlspecialchars($paciente['NACIONALIDAD']) : ''; ?>">
    </div>


      <!-- Campo Teléfono -->
    <div class="form-group">
        <label for="TELEFONO">Teléfono:</label>
       <input type="texr" class="form-control" id="TELEFONO" name="TELEFONO" value="<?php echo $paciente ? htmlspecialchars($paciente['TELEFONO']) : ''; ?>" maxlength="10" pattern="\d*"  
       oninput="this.value = this.value.replace(/[^0-9]/g, '').slice(0, 10);">
    </div>
    
    
    <div class="form-group">
        <label for="RH">Tipo de Sangre:</label>
        <select class="form-control" id="RH" name="RH">
            <option value="" <?= !$paciente || htmlspecialchars($paciente['RH']) == '' ? 'selected' : '' ?>>Seleccionar</option>
            <option value="O+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'O+' ? 'selected' : '' ?>>O+</option>
            <option value="A+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'A+' ? 'selected' : '' ?>>A+</option>
            <option value="B+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'B+' ? 'selected' : '' ?>>B+</option>
            <option value="AB+" <?= $paciente && htmlspecialchars($paciente['RH']) == 'AB+' ? 'selected' : '' ?>>AB+</option>
            <option value="O-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'O-' ? 'selected' : '' ?>>O-</option>
            <option value="A-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'A-' ? 'selected' : '' ?>>A-</option>
            <option value="B-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'B-' ? 'selected' : '' ?>>B-</option>
            <option value="AB-" <?= $paciente && htmlspecialchars($paciente['RH']) == 'AB-' ? 'selected' : '' ?>>AB-</option>
        </select>
    </div>

    <div class="form-group">
        <label for="ECORREO">Correo Electrónico:</label>
        <input type="email" class="form-control" id="ECORREO" name="ECORREO" 
        value="<?php echo $paciente ? htmlspecialchars($paciente['ECORREO']) : ''; ?>">
    </div>

    <!-- Mensajes de error o éxito -->
    <?php if ($mensaje_error): ?>
        <div class="alert alert-warning"><?php echo htmlspecialchars($mensaje_error); ?></div>
    <?php endif; ?>

    <?php if ($mensaje_exito): ?>
        <div class="alert alert-success"><?php echo htmlspecialchars($mensaje_exito); ?></div>
    <?php endif; ?>

    <button type="submit" class="btn btn-primary" id="btnIngresar" disabled>
    <?php echo $paciente ? 'Actualizar' : 'Registrar'; ?> Paciente
</button>
    
<?php if (isset($_GET['cedula_duplicada']) && $_GET['cedula_duplicada'] == 1): ?>
    <!-- Botón de cancelar con la clase y el evento -->
    <button type="button" class="btn btn-warning" onclick="cancelarYEnfocarCedula();">Cancelar Paciente</button>
<?php endif; ?>

<!-- Coloca aquí el script antes de la etiqueta </body> -->
<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Obtener los elementos del formulario
        const nombres = document.getElementById('NOMBRES');
        const apellidos = document.getElementById('APELLIDOS');
        const cedula = document.getElementById('CEDULA');
        const fechanac = document.getElementById('FECHANAC');
        const btnIngresar = document.getElementById('btnIngresar');

        // Función que verifica si todos los campos obligatorios están llenos
        function verificarCampos() {
            if (nombres.value.trim() !== '' && 
                apellidos.value.trim() !== '' && 
                cedula.value.trim() !== '' && 
                fechanac.value.trim() !== '') {
                btnIngresar.disabled = false; // Habilitar el botón si los campos están completos
            } else {
                btnIngresar.disabled = true;  // Deshabilitar el botón si faltan campos
            }
        }

        // Agregar eventos para que se llame a verificarCampos() cuando los campos cambien
        nombres.addEventListener('input', verificarCampos);
        apellidos.addEventListener('input', verificarCampos);
        cedula.addEventListener('input', verificarCampos);
        fechanac.addEventListener('input', verificarCampos);

        // Llamar a la función para verificar los campos al cargar la página
        verificarCampos();
    });
</script>



</form>

<script src="/mi_consultorio/public/js/formateo.js"></script>

<script>
function validarNumeros(event) {
    var input = event.target;
    var valor = input.value;

    // Eliminar todo lo que no sea un número
    input.value = valor.replace(/[^0-9]/g, '');

    // Limitar la cantidad de dígitos a 11
    if (input.value.length > 11) {
        input.value = input.value.slice(0, 11); // Truncar a 11 dígitos
    }
}


function validarFechaNacimiento(event) {
    var input = event.target;
    var fechaNacimiento = new Date(input.value);
    var fechaHoy = new Date();
    // Verificar que la fecha de nacimiento no sea mayor que la fecha actual
    if (fechaNacimiento > fechaHoy) {
        input.setCustomValidity("La fecha de nacimiento no puede ser en el futuro.");
    } else {
        input.setCustomValidity("");
    }
}

</script>

</div>

</body>
</html>
