<?php
// Este archivo se incluirá en historia_clinica.php cuando se seleccione "Orina II" en Resultados de Laboratorio
// Asegúrate de que este archivo esté en la carpeta "formularios" dentro de tu proyecto

// Verificar si hay un paciente seleccionado
if (!isset($_SESSION['CLAVEPAC']) || empty($_SESSION['CLAVEPAC'])) {
    echo '<div class="alert alert-warning">No hay paciente seleccionado</div>';
    exit;
}

// Obtener el ID del paciente de la sesión
$clavePac = intval($_SESSION['CLAVEPAC']);

// Función para obtener los exámenes de orina avanzados del paciente
function getExamenesOrinaAvanzados($pdo, $clavePac) {
    $sql = "SELECT CLAVE as id, DATE_FORMAT(FECHA_CAP, '%Y-%m-%d') as fecha 
            FROM EXAMENORINA WHERE CLAVEPAC = ? AND (PROT24 IS NOT NULL OR MICROALB IS NOT NULL OR ALB_CR IS NOT NULL) 
            ORDER BY FECHA_CAP DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clavePac]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Función para obtener un examen de orina específico
function getExamenOrinaAvanzado($pdo, $id) {
    $sql = "SELECT * FROM EXAMENORINA WHERE CLAVE = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$id]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

// Manejar acciones AJAX
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    $action = $_POST['action'];
    
    if ($action === 'getExamenesOrinaAvanzados') {
        echo json_encode(getExamenesOrinaAvanzados($pdo, $clavePac));
        exit;
    }
    else if ($action === 'getExamenOrinaAvanzado') {
        $id = intval($_POST['id']);
        echo json_encode(getExamenOrinaAvanzado($pdo, $id));
        exit;
    }
    else if ($action === 'saveExamenOrinaAvanzado') {
        $id = isset($_POST['id']) ? intval($_POST['id']) : 0;
        $osmol = $_POST['osmol'];
        $prot24 = $_POST['prot24'];
        $microalb = $_POST['microalb'];
        $alb_cr = $_POST['alb_cr'];
        $c_higr = $_POST['c_higr'];
        $piuria = $_POST['piuria'];
        
        try {
            if ($id > 0) {
                // Actualizar registro existente
                $sql = "UPDATE EXAMENORINA SET 
                        OSMOL = ?, PROT24 = ?, MICROALB = ?, ALB_CR = ?, 
                        C_HIGR = ?, PIURIA = ? 
                        WHERE CLAVE = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $osmol, $prot24, $microalb, $alb_cr, 
                    $c_higr, $piuria, $id
                ]);
            } else {
                // Insertar nuevo registro
                $sql = "INSERT INTO EXAMENORINA (
                        CLAVEPAC, OSMOL, PROT24, MICROALB, ALB_CR, 
                        C_HIGR, PIURIA) 
                        VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $clavePac, $osmol, $prot24, $microalb, $alb_cr, 
                    $c_higr, $piuria
                ]);
                
                $id = $pdo->lastInsertId();
            }
            
            echo json_encode(['success' => true, 'id' => $id]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
    else if ($action === 'deleteExamenOrinaAvanzado') {
        $id = intval($_POST['id']);
        
        try {
            $sql = "DELETE FROM EXAMENORINA WHERE CLAVE = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$id]);
            
            echo json_encode(['success' => true]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        }
        exit;
    }
}

// Obtener lista de exámenes de orina avanzados
$examenes = getExamenesOrinaAvanzados($pdo, $clavePac);
?>

<div class="container-fluid">
    <h4 class="mb-3">Examen de Orina II (Avanzado)</h4>
    
    <div class="row">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    Registros
                </div>
                <div class="card-body">
                    <div class="lista-registros" id="orinaAvanzadoList">
                        <?php if (count($examenes) > 0): ?>
                            <ul class="list-unstyled m-2">
                                <?php foreach ($examenes as $item): ?>
                                    <li><a href="#" class="orina-avanzado-item" data-id="<?php echo $item['id']; ?>"><?php echo $item['fecha']; ?></a></li>
                                <?php endforeach; ?>
                            </ul>
                        <?php else: ?>
                            <p class="text-center">No hay registros</p>
                        <?php endif; ?>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-primary" id="btnNuevoOrinaAvanzado">Nuevo</button>
                        <button class="btn btn-sm btn-info" id="btnEditarOrinaAvanzado">Editar</button>
                        <button class="btn btn-sm btn-danger" id="btnEliminarOrinaAvanzado">Eliminar</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    Datos del Examen de Orina Avanzado
                </div>
                <div class="card-body">
                    <form id="formOrinaAvanzado">
                        <input type="hidden" id="orinaAvanzadoId" name="id" value="0">
                        
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                <label for="osmolOrina">Osmolaridad</label>
                                <input type="text" class="form-control" id="osmolOrina" name="osmol">
                                <small class="form-text text-muted">mOsm/kg</small>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="prot24Orina">Proteínas 24h</label>
                                <input type="text" class="form-control" id="prot24Orina" name="prot24">
                                <small class="form-text text-muted">g/24h</small>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="microalbOrina">Microalbuminuria</label>
                                <input type="text" class="form-control" id="microalbOrina" name="microalb">
                                <small class="form-text text-muted">mg/L</small>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group col-md-4">
                                <label for="alb_crOrina">Relación Albúmina/Creatinina</label>
                                <input type="text" class="form-control" id="alb_crOrina" name="alb_cr">
                                <small class="form-text text-muted">mg/g</small>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="c_higrOrina">Cilindros Hialino-Granulosos</label>
                                <input type="text" class="form-control" id="c_higrOrina" name="c_higr">
                                <small class="form-text text-muted">por campo</small>
                            </div>
                            <div class="form-group col-md-4">
                                <label for="piuriaOrina">Piuria</label>
                                <input type="text" class="form-control" id="piuriaOrina" name="piuria">
                                <small class="form-text text-muted">+/+++</small>
                            </div>
                        </div>
                        
                        <div class="text-right">
                            <button type="button" class="btn btn-secondary" id="btnCancelarOrinaAvanzado">Cancelar</button>
                            <button type="button" class="btn btn-primary" id="btnGuardarOrinaAvanzado">Guardar</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentOrinaAvanzadoId = 0;
    let editMode = false;
    
    // Inicializar formulario
    disableForm(true);
    $('#btnEditarOrinaAvanzado').prop('disabled', true);
    $('#btnEliminarOrinaAvanzado').prop('disabled', true);
    $('#btnCancelarOrinaAvanzado').hide();
    $('#btnGuardarOrinaAvanzado').hide();
    
    // Si hay registros, seleccionar el primero
    if ($('.orina-avanzado-item').length > 0) {
        $('.orina-avanzado-item:first').click();
    }
    
    // Cargar examen de orina avanzado al hacer clic en un elemento de la lista
    $(document).on('click', '.orina-avanzado-item', function(e) {
        e.preventDefault();
        
        $('.orina-avanzado-item').removeClass('font-weight-bold');
        $(this).addClass('font-weight-bold');
        
        const id = $(this).data('id');
        currentOrinaAvanzadoId = id;
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: {
                action: 'getExamenOrinaAvanzado',
                id: id
            },
            dataType: 'json',
            success: function(data) {
                if (data) {
                    $('#orinaAvanzadoId').val(data.CLAVE);
                    $('#osmolOrina').val(data.OSMOL);
                    $('#prot24Orina').val(data.PROT24);
                    $('#microalbOrina').val(data.MICROALB);
                    $('#alb_crOrina').val(data.ALB_CR);
                    $('#c_higrOrina').val(data.C_HIGR);
                    $('#piuriaOrina').val(data.PIURIA);
                    
                    disableForm(true);
                    $('#btnEditarOrinaAvanzado').prop('disabled', false);
                    $('#btnEliminarOrinaAvanzado').prop('disabled', false);
                    $('#btnCancelarOrinaAvanzado').hide();
                    $('#btnGuardarOrinaAvanzado').hide();
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al cargar examen de orina avanzado:", error);
                alert("Error al cargar examen de orina avanzado. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Nuevo examen de orina avanzado
    $('#btnNuevoOrinaAvanzado').click(function() {
        clearForm();
        disableForm(false);
        editMode = false;
        
        $('#btnEditarOrinaAvanzado').prop('disabled', true);
        $('#btnEliminarOrinaAvanzado').prop('disabled', true);
        $('#btnCancelarOrinaAvanzado').show();
        $('#btnGuardarOrinaAvanzado').show();
    });
    
    // Editar examen de orina avanzado
    $('#btnEditarOrinaAvanzado').click(function() {
        if (currentOrinaAvanzadoId > 0) {
            disableForm(false);
            editMode = true;
            
            $('#btnNuevoOrinaAvanzado').prop('disabled', true);
            $('#btnEliminarOrinaAvanzado').prop('disabled', true);
            $('#btnCancelarOrinaAvanzado').show();
            $('#btnGuardarOrinaAvanzado').show();
        }
    });
    
    // Eliminar examen de orina avanzado
    $('#btnEliminarOrinaAvanzado').click(function() {
        if (currentOrinaAvanzadoId > 0) {
            if (confirm("¿Está seguro de eliminar este examen de orina avanzado?")) {
                $.ajax({
                    url: window.location.href,
                    type: 'POST',
                    data: {
                        action: 'deleteExamenOrinaAvanzado',
                        id: currentOrinaAvanzadoId
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert("Examen de orina avanzado eliminado correctamente.");
                            location.reload(); // Recargar para actualizar la lista
                        } else {
                            alert("Error al eliminar examen de orina avanzado: " + response.error);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error al eliminar examen de orina avanzado:", error);
                        alert("Error al eliminar examen de orina avanzado. Consulte la consola para más detalles.");
                    }
                });
            }
        }
    });
    
    // Cancelar
    $('#btnCancelarOrinaAvanzado').click(function() {
        if (editMode && currentOrinaAvanzadoId > 0) {
            // Volver a cargar el examen actual
            $('.orina-avanzado-item[data-id="' + currentOrinaAvanzadoId + '"]').click();
        } else {
            clearForm();
        }
        
        disableForm(true);
        $('#btnNuevoOrinaAvanzado').prop('disabled', false);
        $('#btnEditarOrinaAvanzado').prop('disabled', currentOrinaAvanzadoId === 0);
        $('#btnEliminarOrinaAvanzado').prop('disabled', currentOrinaAvanzadoId === 0);
        $('#btnCancelarOrinaAvanzado').hide();
        $('#btnGuardarOrinaAvanzado').hide();
    });
    
    // Guardar examen de orina avanzado
    $('#btnGuardarOrinaAvanzado').click(function() {
        const formData = {
            action: 'saveExamenOrinaAvanzado',
            id: editMode ? currentOrinaAvanzadoId : 0,
            osmol: $('#osmolOrina').val(),
            prot24: $('#prot24Orina').val(),
            microalb: $('#microalbOrina').val(),
            alb_cr: $('#alb_crOrina').val(),
            c_higr: $('#c_higrOrina').val(),
            piuria: $('#piuriaOrina').val()
        };
        
        $.ajax({
            url: window.location.href,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    alert("Examen de orina avanzado guardado correctamente.");
                    location.reload(); // Recargar para actualizar la lista
                } else {
                    alert("Error al guardar examen de orina avanzado: " + response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error("Error al guardar examen de orina avanzado:", error);
                alert("Error al guardar examen de orina avanzado. Consulte la consola para más detalles.");
            }
        });
    });
    
    // Funciones auxiliares
    function clearForm() {
        $('#formOrinaAvanzado')[0].reset();
        $('#orinaAvanzadoId').val(0);
        currentOrinaAvanzadoId = 0;
    }
    
    function disableForm(disabled) {
        $('#formOrinaAvanzado input, #formOrinaAvanzado textarea').prop('disabled', disabled);
    }
});
</script>
