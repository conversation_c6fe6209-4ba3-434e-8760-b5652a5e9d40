<?php

include_once '../config/config.php';

// Configuración de la base de datos MySQL
//$host = 'localhost';        // Dirección del servidor MySQL
//$usuario = 'usuario_db';    // Nombre de usuario de la base de datos
//$contrasena = 'contraseña'; // Contraseña de la base de datos
//$basededatos = 'nombre_db'; // Nombre de la base de datos

// Crear la conexión
//$conn = new mysqli($host, $usuario, $contrasena, $basededatos);

// Verificar si la conexión fue exitosa
//if ($conn->connect_error) {
//    die("Error de conexión: " . $conn->connect_error);
//}

header("Content-Type: application/json");
//header('Access-Control-Allow-Origin: *'); // Permitir solicitudes desde cualquier origen (ajusta según tus necesidades)

$conn = getDBConnection();


// Establecer la codificación de caracteres (opcional, para evitar problemas con caracteres especiales)
$conn->set_charset("utf8");

// Consulta para obtener los datos de los pacientes
$query = "SELECT CLAVE , VISITA , REGISTRO , NOMBRES , APELLIDOS , CEDULA , SEXO , NACIONALIDAD ,ESTADOCIVIL , FECHANAC , LUGARNAC ,OCUPACION , RELIGION ,FECHAINGRESO , RH , CALLE ,  PROVINCIA , LOCALIDAD , MUNICIPIO , PAIS , REFERENCIA , TELEFONO , CELULAR , TELTRABAJO ,  FAX , ECORREO , ARS , PLANES , AFILIADO , VIGENCIA , POLIZA , CATEGORIA , ALERGIA , NAF ,  NSS , NOMBRERESP , DIRECCIONRESP , CEDULARESP , TELEFONORESP , FAMILIARPROX , DIRECCIONFAMILIAR ,  TELEFONOFAMILIAR , REMITIDO , OBSERVACIONES , ESTATUS , STEMBARAZO , PESOHABITUAL , NIVELESCOLAR , PROCEDENCIA , SINCEDULA , PROFESION , RECORDCLINICA , LAST_MODIFIED , SINCRONIZADO FROM PACIENTES WHERE SINCRONIZADO = 0"; // Ajusta esta consulta según las columnas que necesites
$resultado = $conn->query($query);

// Verificar si hay resultados
if ($resultado->num_rows > 0) {
    // Crear un array para almacenar los datos
    $pacientes = [];

    // Recorrer cada fila de resultados y agregarla al array
    while ($fila = $resultado->fetch_assoc()) {
        // Cada fila es un paciente, y se agrega al array
        $pacientes[] = $fila;
    }

    // Convertir el array de pacientes a formato JSON y devolverlo
    echo json_encode($pacientes);
    
    
    // Actualizar el campo SINCRONIZADO a 1
$updateStmt = $conn->prepare("
    UPDATE PACIENTES
    SET SINCRONIZADO = 1
    WHERE SINCRONIZADO = 0
");
if (!$updateStmt->execute()) {
    error_log("Error al actualizar SINCRONIZADO: " . $updateStmt->error);
}
    
    // Si no se encuentran pacientes, devolver mensaje de error
    
} else {  
   echo json_encode([
     "status" => "error",
      "message" => "No se encontraron Pacientes sin actualizar"
    ]);
}






// Cerrar la conexión
$conn->close();
?>
