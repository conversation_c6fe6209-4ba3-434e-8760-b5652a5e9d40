<?php
// verificar_sincronizacion.php

header('Content-Type: application/json');
//include_once '../config/config.php';
//require_once __DIR__ . '../config/config.php';

require_once __DIR__ . '/../config/config.php';
//$conn = getDBConnection();



$conn = getDBConnection();
$conn->set_charset("utf8");

if ($conn->connect_error) {
    echo json_encode(['error' => 'Error de conexión: ' . $conn->connect_error]);
    exit;
}

// Obtener el nombre de la tabla
$tabla = $_GET['tabla'] ?? '';

if (!$tabla) {
    echo json_encode(['error' => 'Nombre de tabla no proporcionado']);
    exit;
}

// Consultar registros por sincronizar
$sql = "SELECT COUNT(*) AS total_registros FROM $tabla WHERE SINCRONIZADO = 0";
$result = $conn->query($sql);

if ($result) {
    $row = $result->fetch_assoc();
    echo json_encode(['total_registros' => (int) $row['total_registros']]);
} else {
    echo json_encode(['error' => 'Error en la consulta: ' . $conn->error]);
}

$conn->close();
?>
