<?php
// buscar_pacientes.php
require_once '../config/database.php';

// Consulta: Traer todos los pacientes ordenados por NOMBRES
$stmt = $pdo->query("SELECT * FROM PACIENTES ORDER BY NOMBRES ASC");
$pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Buscar Pacientes</title>
  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css">
  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .container {
      margin-top: 2rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="mb-4">Lista de Pacientes</h1>
    <!-- La tabla se mostrará completa; DataTables incorporará su buscador -->
    <table id="pacientesTabla" class="table table-striped table-bordered">
      <thead>
        <tr>
          <th>Cédula</th>
          <th>Registro</th>
          <th>Nombres</th>
          <th>Apellidos</th>
          <th>Edad</th>
          <th>Acciones</th>
        </tr>
      </thead>
      <tbody>
        <?php foreach ($pacientes as $paciente): ?>
          <tr>
            <td><?= htmlspecialchars($paciente['CEDULA']) ?></td>
            <td><?= htmlspecialchars($paciente['REGISTRO']) ?></td>
            <td><?= htmlspecialchars($paciente['NOMBRES']) ?></td>
            <td><?= htmlspecialchars($paciente['APELLIDOS']) ?></td>
            <td><?= htmlspecialchars($paciente['EDAD']) ?></td>
            <td>
              <!-- Botón para editar (icono de lápiz) -->
              <a href="editar_paciente.php?CLAVE=<?= htmlspecialchars($paciente['CLAVE']) ?>" 
                 title="Editar" class="btn btn-info btn-sm">
                <i class="fas fa-pencil-alt"></i>
              </a>
              <!-- Botón para eliminar (icono de basura) -->
              <a href="eliminar_paciente.php?CLAVE=<?= htmlspecialchars($paciente['CLAVE']) ?>" 
                 onclick="return confirm('¿Está seguro de eliminar este paciente?');" 
                 title="Eliminar" class="btn btn-danger btn-sm">
                <i class="fas fa-trash"></i>
              </a>
            </td>
          </tr>
        <?php endforeach; ?>
      </tbody>
    </table>
  </div>
  
  <!-- jQuery (versión completa) -->
  <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
  <!-- Bootstrap JS Bundle -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
  <!-- DataTables JS -->
  <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
  <script>
    $(document).ready(function() {
      // Inicializa DataTables para que la tabla sea ordenable y tenga búsqueda interna
      $('#pacientesTabla').DataTable({
        paging: true,
        searching: true,
        ordering: true,
        order: [[2, 'asc']], // Ordena por NOMBRES de forma ascendente
        language: {
          search: "Buscar:",
          zeroRecords: "No se encontraron pacientes",
          info: "Mostrando _START_ a _END_ de _TOTAL_ pacientes",
          infoEmpty: "No hay pacientes disponibles",
          paginate: {
            first: "Primero",
            last: "Último",
            next: "Siguiente",
            previous: "Anterior"
          }
        }
      });
    });
  </script>
</body>
</html>
