<?php
require_once 'backend/config/database.php'; // Configuración de la base de datos

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'];

    // Verificar si el correo existe en la base de datos
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch();

    if ($user) {
        // Generar un token único y temporal para la recuperación
        $token = bin2hex(random_bytes(16)); // Generar un token seguro
        $expira = date("Y-m-d H:i:s", strtotime("+1 hour")); // Expira en 1 hora

        // Guardar el token en la base de datos
        $stmt = $pdo->prepare("UPDATE usuarios SET reset_token = ?, reset_expira = ? WHERE email = ?");
        $stmt->execute([$token, $expira, $email]);

        // Enviar el correo de recuperación
        $reset_link = "https://www.marcsoftware.com/mi_consultorio/restablecer_password.php?token=$token";
        $mensaje = "Hola, haz clic en el siguiente enlace para restablecer tu contraseña: $reset_link";
        mail($email, "Recuperación de Contraseña", $mensaje, "From: <EMAIL>");

        echo "Se ha enviado un correo de recuperación. Revisa tu bandeja de entrada.";
    } else {
        echo "No existe una cuenta asociada a ese correo.";
    }
}
