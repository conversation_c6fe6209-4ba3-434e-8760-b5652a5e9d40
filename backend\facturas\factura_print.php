<?php
require_once '../config/database.php';
header('Content-Type: text/html; charset=UTF-8');
ini_set('display_errors', 1);
error_reporting(E_ALL);

$clave = $_GET['clave'] ?? null;
if (!$clave) {
  echo "<p>Error: Clave no especificada.</p>";
  exit;
}

try {
  $stmt = $pdo->prepare("SELECT f.CLAVEPAC, f.PAGADO, f.NUMFACT, f.FECHA, f.FECHAPAGO, f.CONCEPTO, f.MODOPGO, f.PRECIO, f.CEDULA,
                                p.NOMBRES, p.APELLIDOS, p.REGISTRO
                         FROM FACTURAS f
                         LEFT JOIN PACIENTES p ON f.CLAVEPAC = p.CLAVE
                         WHERE f.CLAVE = ?");
  $stmt->execute([$clave]);
  $factura = $stmt->fetch(PDO::FETCH_ASSOC);

  if (!$factura) {
    echo "<p>Error: Factura no encontrada.</p>";
    exit;
  }

  $stmt2 = $pdo->prepare("SELECT NOMBRE, CLINICA, CALLE, PROVINCIA, TELEFONODR, TELEFONO FROM EMPRESA WHERE CONSULTORIO = 1");
  $stmt2->execute();
  $empresa = $stmt2->fetch(PDO::FETCH_ASSOC);

  $stmt3 = $pdo->query("SELECT IMPRIMENCABEZADO, PATHCONFIG FROM CONFIG");
  $config = $stmt3->fetch(PDO::FETCH_ASSOC);

  function convertirALetras($monto) {
    $f = new NumberFormatter("es", NumberFormatter::SPELLOUT);
    return ucfirst($f->format($monto)) . " pesos con 00/100";
  }

  $letras = convertirALetras($factura['PAGADO']);
  $fechaPago = $factura['FECHAPAGO'];
  $fechaHoy = date('Y-m-d');
  $nombrePaciente = $factura['NOMBRES'] . ' ' . $factura['APELLIDOS'];

} catch (Exception $e) {
  echo "<pre>Error: " . $e->getMessage() . "</pre>";
  exit;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="utf-8">
  <title>Factura Médica #<?= $factura['NUMFACT'] ?></title>

  <!-- Favicon médico -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">
  <link rel="shortcut icon" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23059669'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E">

  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* Variables CSS médicas */
    :root {
      --medical-primary: #059669;
      --medical-secondary: #10b981;
      --medical-accent: #0ea5e9;
      --medical-info: #3b82f6;
      --medical-warning: #f59e0b;
      --medical-danger: #ef4444;
      --medical-light: #ffffff;
      --medical-gray-50: #f9fafb;
      --medical-gray-100: #f3f4f6;
      --medical-gray-200: #e5e7eb;
      --medical-gray-600: #4b5563;
      --medical-dark: #1f2937;
    }

    /* Estilos base para impresión */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: var(--medical-light);
      color: var(--medical-dark);
      line-height: 1.6;
      margin: 0;
      padding: 20px;
    }

    /* Contenedor principal de la factura */
    .factura-container {
      max-width: 800px;
      margin: 0 auto;
      background: var(--medical-light);
      border-radius: 15px;
      box-shadow: 0 8px 32px rgba(5, 150, 105, 0.1);
      overflow: hidden;
      border: 2px solid var(--medical-gray-200);
    }

    /* Header médico */
    .factura-header {
      background: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
      color: white;
      padding: 2rem;
      position: relative;
      overflow: hidden;
    }

    .factura-header::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 200px;
      height: 200px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      transform: translate(50%, -50%);
    }

    .header-content {
      position: relative;
      z-index: 2;
    }

    .empresa-info h1 {
      font-size: 1.8rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .empresa-info h2 {
      font-size: 1.2rem;
      font-weight: 500;
      margin-bottom: 1rem;
      opacity: 0.9;
    }

    .empresa-detalles {
      font-size: 0.95rem;
      opacity: 0.8;
      line-height: 1.4;
    }

    .logo-container {
      position: absolute;
      top: 2rem;
      right: 2rem;
      z-index: 3;
    }

    .logo-container img {
      max-height: 80px;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    /* Información de la factura */
    .factura-info {
      background: var(--medical-gray-50);
      padding: 1.5rem 2rem;
      border-bottom: 1px solid var(--medical-gray-200);
    }

    .info-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 1rem;
      align-items: center;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .info-item i {
      color: var(--medical-primary);
      width: 20px;
    }

    .info-label {
      font-weight: 600;
      color: var(--medical-dark);
    }

    .info-value {
      color: var(--medical-gray-600);
    }

    .factura-numero {
      text-align: right;
      font-size: 1.1rem;
    }

    .factura-numero .numero {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--medical-primary);
    }

    /* Contenido principal */
    .factura-body {
      padding: 2rem;
    }

    .paciente-info {
      background: var(--medical-light);
      border: 2px solid var(--medical-gray-100);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    .paciente-nombre {
      font-size: 1.3rem;
      font-weight: 700;
      color: var(--medical-primary);
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    /* Detalles del pago */
    .pago-detalles {
      background: linear-gradient(135deg, var(--medical-gray-50), var(--medical-light));
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      border: 1px solid var(--medical-gray-200);
    }

    .monto-principal {
      text-align: center;
      margin-bottom: 1.5rem;
    }

    .monto-numero {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--medical-primary);
      margin-bottom: 0.5rem;
    }

    .monto-letras {
      font-size: 1rem;
      color: var(--medical-gray-600);
      font-style: italic;
      background: var(--medical-light);
      padding: 0.8rem 1.5rem;
      border-radius: 8px;
      border: 1px solid var(--medical-gray-200);
      margin-top: 1rem;
    }

    .concepto-pago {
      background: var(--medical-light);
      border-left: 4px solid var(--medical-primary);
      padding: 1rem 1.5rem;
      margin-top: 1.5rem;
      border-radius: 0 8px 8px 0;
    }

    .concepto-label {
      font-weight: 600;
      color: var(--medical-dark);
      margin-bottom: 0.5rem;
    }

    .concepto-texto {
      font-size: 1.1rem;
      color: var(--medical-gray-600);
    }

    /* Sección de firmas */
    .firmas-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin: 2rem 0;
      padding: 1.5rem;
      background: var(--medical-gray-50);
      border-radius: 12px;
    }

    .firma-box {
      text-align: center;
      padding: 1rem;
    }

    .firma-linea {
      width: 80%;
      height: 2px;
      background: var(--medical-gray-300);
      margin: 2rem auto 0.5rem;
      border-radius: 1px;
    }

    .firma-label {
      font-weight: 600;
      color: var(--medical-dark);
      font-size: 0.9rem;
    }

    .qr-section {
      text-align: center;
      padding: 1rem;
    }

    .qr-code {
      border-radius: 8px;
      box-shadow: 0 4px 15px rgba(5, 150, 105, 0.2);
      margin-bottom: 0.5rem;
    }

    .qr-label {
      font-size: 0.85rem;
      color: var(--medical-gray-600);
      font-weight: 500;
    }

    /* Footer de la factura */
    .factura-footer {
      background: var(--medical-gray-50);
      padding: 1.5rem 2rem;
      border-top: 1px solid var(--medical-gray-200);
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 1rem;
      font-size: 0.9rem;
    }

    .footer-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .footer-item i {
      color: var(--medical-primary);
      width: 16px;
    }

    .footer-label {
      font-weight: 600;
      color: var(--medical-dark);
    }

    .footer-value {
      color: var(--medical-gray-600);
    }

    /* Estilos para impresión */
    @media print {
      body {
        margin: 0;
        padding: 10px;
      }

      .factura-container {
        box-shadow: none;
        border: 1px solid #000;
        border-radius: 0;
      }

      .factura-header {
        background: #059669 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .monto-numero {
        color: #059669 !important;
      }

      .info-item i,
      .footer-item i {
        color: #059669 !important;
      }
    }

    /* Responsive para pantalla */
    @media (max-width: 768px) {
      .info-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }

      .firmas-section {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .factura-footer {
        grid-template-columns: 1fr;
        gap: 0.5rem;
      }
    }
  </style>
</head>
  </style>
</head>
<body>
<div class="factura-container">
  <!-- Header médico -->
  <div class="factura-header">
    <?php if ($config['IMPRIMENCABEZADO'] === 'S'): ?>
      <!-- Logo flotante -->
      <div class="logo-container">
        <img src="https://marcsoftware.com/mi_consultorio/assets/img/homs.jpg" alt="Logo Médico" class="logo">
      </div>

      <!-- Información de la empresa -->
      <div class="header-content">
        <div class="empresa-info">
          <h1><i class="fas fa-hospital me-2"></i><?= $empresa['NOMBRE'] ?? 'Consultorio Médico' ?></h1>
          <h2><?= $empresa['CLINICA'] ?? '' ?></h2>
          <div class="empresa-detalles">
            <div><i class="fas fa-map-marker-alt me-2"></i><?= $empresa['CALLE'] ?? '' ?>, <?= $empresa['PROVINCIA'] ?? '' ?></div>
            <div><i class="fas fa-phone me-2"></i>Tel.: <?= $empresa['TELEFONODR'] ?? '' ?> / <?= $empresa['TELEFONO'] ?? '' ?></div>
          </div>
        </div>
      </div>
    <?php endif; ?>
  </div>

  <!-- Información de la factura -->
  <div class="factura-info">
    <div class="info-grid">
      <div class="info-item">
        <i class="fas fa-calendar-alt"></i>
        <div>
          <div class="info-label">Fecha de Pago:</div>
          <div class="info-value"><?= date('d/m/Y', strtotime($fechaPago)) ?></div>
        </div>
      </div>

      <div class="info-item">
        <i class="fas fa-user-injured"></i>
        <div>
          <div class="info-label">Clave Paciente:</div>
          <div class="info-value"><?= $factura['CLAVEPAC'] ?></div>
        </div>
      </div>

      <div class="factura-numero">
        <div class="info-label">Factura No.</div>
        <div class="numero">#<?= $factura['NUMFACT'] ?></div>
      </div>
    </div>
  </div>
  <!-- Contenido principal -->
  <div class="factura-body">
    <!-- Información del paciente -->
    <div class="paciente-info">
      <div class="paciente-nombre">
        <i class="fas fa-user-injured"></i>
        Recibimos del Señor(a): <?= $nombrePaciente ?>
      </div>
      <div style="font-size: 0.9rem; color: var(--medical-gray-600); margin-top: 0.5rem;">
        <i class="fas fa-id-card me-2"></i>Cédula: <?= $factura['CEDULA'] ?? 'No especificada' ?>
      </div>
    </div>

    <!-- Detalles del pago -->
    <div class="pago-detalles">
      <div class="monto-principal">
        <div style="font-size: 1.1rem; color: var(--medical-dark); margin-bottom: 0.5rem;">
          <i class="fas fa-money-bill-wave me-2"></i>Monto Recibido
        </div>
        <div class="monto-numero">RD$ <?= number_format($factura['PAGADO'], 2) ?></div>

        <div class="monto-letras">
          <i class="fas fa-quote-left me-2"></i>
          <strong>En letras:</strong> <?= $letras ?>
        </div>
      </div>

      <div class="concepto-pago">
        <div class="concepto-label">
          <i class="fas fa-clipboard-list me-2"></i>Por concepto de:
        </div>
        <div class="concepto-texto"><?= $factura['CONCEPTO'] ?></div>
      </div>
    </div>

    <!-- Sección de firmas y QR -->
    <div class="firmas-section">
      <div class="firma-box">
        <div class="firma-linea"></div>
        <div class="firma-label">
          <i class="fas fa-signature me-2"></i>Firma del Paciente
        </div>
      </div>

      <div class="qr-section">
        <img src="https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=<?= urlencode($factura['NUMFACT']) ?>"
             alt="Código QR" class="qr-code">
        <div class="qr-label">
          <i class="fas fa-qrcode me-2"></i>Código QR - Factura #<?= $factura['NUMFACT'] ?>
        </div>
      </div>
    </div>
  </div>

  <!-- Footer de la factura -->
  <div class="factura-footer">
    <div class="footer-item">
      <i class="fas fa-print"></i>
      <div>
        <div class="footer-label">Fecha de Impresión:</div>
        <div class="footer-value"><?= date('d/m/Y H:i', strtotime($fechaHoy)) ?></div>
      </div>
    </div>

    <div class="footer-item">
      <i class="fas fa-keyboard"></i>
      <div>
        <div class="footer-label">Fecha de Digitación:</div>
        <div class="footer-value"><?= date('d/m/Y', strtotime($factura['FECHA'])) ?></div>
      </div>
    </div>

    <div class="footer-item">
      <i class="fas fa-credit-card"></i>
      <div>
        <div class="footer-label">Método de Pago:</div>
        <div class="footer-value"><?= $factura['MODOPGO'] ?></div>
      </div>
    </div>
  </div>
</div>
<script>window.print();</script>
</body>
</html>