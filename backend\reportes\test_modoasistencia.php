<?php
// backend/reportes/test_modoasistencia.php
// Script para verificar el campo MODOASISTENCIA

session_start();
require_once __DIR__ . '/../config/database.php';

// Simular sesión para prueba
$_SESSION['usuario'] = 'admin';
$_SESSION['rol'] = 'admin';

echo "<!DOCTYPE html>
<html lang='es'>
<head>
    <meta charset='UTF-8'>
    <title>Prueba Campo MODOASISTENCIA</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>🔍 Prueba del Campo MODOASISTENCIA</h1>";

try {
    // 1. Verificar estructura de la tabla
    echo "<div class='section'>";
    echo "<h2>1. Estructura de la Tabla CITAMEDIC</h2>";
    
    $stmt = $pdo->query("DESCRIBE CITAMEDIC");
    $columnas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $tiene_modoasistencia = false;
    $tiene_asistencia = false;
    $tiene_estatus = false;
    
    echo "<table>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Clave</th><th>Default</th></tr>";
    
    foreach ($columnas as $columna) {
        $destacar = '';
        if (in_array($columna['Field'], ['MODOASISTENCIA', 'ASISTENCIA', 'ESTATUS'])) {
            $destacar = 'style="background-color: #ffffcc;"';
        }
        
        echo "<tr $destacar>";
        echo "<td><strong>{$columna['Field']}</strong></td>";
        echo "<td>{$columna['Type']}</td>";
        echo "<td>{$columna['Null']}</td>";
        echo "<td>{$columna['Key']}</td>";
        echo "<td>{$columna['Default']}</td>";
        echo "</tr>";
        
        if ($columna['Field'] == 'MODOASISTENCIA') $tiene_modoasistencia = true;
        if ($columna['Field'] == 'ASISTENCIA') $tiene_asistencia = true;
        if ($columna['Field'] == 'ESTATUS') $tiene_estatus = true;
    }
    echo "</table>";
    
    echo "<h3>Campos Relevantes Encontrados:</h3>";
    echo "<p class='" . ($tiene_modoasistencia ? 'success' : 'error') . "'>";
    echo ($tiene_modoasistencia ? '✅' : '❌') . " Campo MODOASISTENCIA: " . ($tiene_modoasistencia ? 'ENCONTRADO' : 'NO ENCONTRADO');
    echo "</p>";
    
    echo "<p class='" . ($tiene_asistencia ? 'warning' : 'error') . "'>";
    echo ($tiene_asistencia ? '⚠️' : '❌') . " Campo ASISTENCIA: " . ($tiene_asistencia ? 'Encontrado (alternativo)' : 'NO ENCONTRADO');
    echo "</p>";
    
    echo "<p class='" . ($tiene_estatus ? 'warning' : 'error') . "'>";
    echo ($tiene_estatus ? '⚠️' : '❌') . " Campo ESTATUS: " . ($tiene_estatus ? 'Encontrado (diferente propósito)' : 'NO ENCONTRADO');
    echo "</p>";
    
    echo "</div>";
    
    // 2. Verificar datos en MODOASISTENCIA
    if ($tiene_modoasistencia) {
        echo "<div class='section'>";
        echo "<h2>2. Datos en el Campo MODOASISTENCIA</h2>";
        
        $stmt = $pdo->query("
            SELECT MODOASISTENCIA, 
                   CASE MODOASISTENCIA 
                       WHEN 0 THEN 'Atendido'
                       WHEN 1 THEN 'Canceló'
                       WHEN 2 THEN 'No asistió'
                       WHEN 3 THEN 'Citado'
                       WHEN 4 THEN 'Llegó tarde'
                       WHEN 5 THEN 'Esperando'
                       WHEN 6 THEN 'Pendiente aprobación'
                       ELSE 'Desconocido'
                   END as estado_texto,
                   COUNT(*) as cantidad
            FROM CITAMEDIC 
            GROUP BY MODOASISTENCIA 
            ORDER BY MODOASISTENCIA
        ");
        
        $datos_modoasistencia = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($datos_modoasistencia)) {
            echo "<p class='warning'>⚠️ No hay datos en la tabla CITAMEDIC</p>";
        } else {
            echo "<table>";
            echo "<tr><th>Código MODOASISTENCIA</th><th>Estado</th><th>Cantidad</th><th>Porcentaje</th></tr>";
            
            $total_registros = array_sum(array_column($datos_modoasistencia, 'cantidad'));
            
            foreach ($datos_modoasistencia as $dato) {
                $porcentaje = round(($dato['cantidad'] / $total_registros) * 100, 1);
                echo "<tr>";
                echo "<td><strong>{$dato['MODOASISTENCIA']}</strong></td>";
                echo "<td>{$dato['estado_texto']}</td>";
                echo "<td>{$dato['cantidad']}</td>";
                echo "<td>{$porcentaje}%</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p><strong>Total de registros:</strong> $total_registros</p>";
        }
        echo "</div>";
        
        // 3. Prueba de consulta con filtros
        echo "<div class='section'>";
        echo "<h2>3. Prueba de Consulta con Filtros</h2>";
        
        $fecha_inicio = date('Y-m-01');
        $fecha_fin = date('Y-m-t');
        
        echo "<h4>Consulta por rango de fechas (mes actual):</h4>";
        echo "<p><strong>Período:</strong> $fecha_inicio a $fecha_fin</p>";
        
        // Consulta base
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM CITAMEDIC 
            WHERE FECHACON BETWEEN ? AND ?
        ");
        $stmt->execute([$fecha_inicio, $fecha_fin]);
        $total_mes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        
        echo "<p><strong>Total de citas en el mes actual:</strong> $total_mes</p>";
        
        if ($total_mes > 0) {
            echo "<h4>Distribución por MODOASISTENCIA en el mes actual:</h4>";
            
            $stmt = $pdo->prepare("
                SELECT MODOASISTENCIA, 
                       CASE MODOASISTENCIA 
                           WHEN 0 THEN 'Atendido'
                           WHEN 1 THEN 'Canceló'
                           WHEN 2 THEN 'No asistió'
                           WHEN 3 THEN 'Citado'
                           WHEN 4 THEN 'Llegó tarde'
                           WHEN 5 THEN 'Esperando'
                           WHEN 6 THEN 'Pendiente aprobación'
                           ELSE 'Desconocido'
                       END as estado_texto,
                       COUNT(*) as cantidad
                FROM CITAMEDIC 
                WHERE FECHACON BETWEEN ? AND ?
                GROUP BY MODOASISTENCIA 
                ORDER BY MODOASISTENCIA
            ");
            $stmt->execute([$fecha_inicio, $fecha_fin]);
            $distribucion = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>Código</th><th>Estado</th><th>Cantidad</th><th>Porcentaje</th></tr>";
            foreach ($distribucion as $dato) {
                $porcentaje = round(($dato['cantidad'] / $total_mes) * 100, 1);
                echo "<tr>";
                echo "<td>{$dato['MODOASISTENCIA']}</td>";
                echo "<td>{$dato['estado_texto']}</td>";
                echo "<td>{$dato['cantidad']}</td>";
                echo "<td>{$porcentaje}%</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Prueba de filtro específico
            echo "<h4>Prueba de filtro específico (MODOASISTENCIA = 0):</h4>";
            
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as total 
                FROM CITAMEDIC 
                WHERE FECHACON BETWEEN ? AND ? AND MODOASISTENCIA = ?
            ");
            $stmt->execute([$fecha_inicio, $fecha_fin, 0]);
            $atendidas = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<p><strong>Citas atendidas (MODOASISTENCIA = 0):</strong> $atendidas</p>";
            
            // Verificar que la consulta no tenga problemas de parámetros
            echo "<h4>Verificación de consulta compleja:</h4>";
            
            $where_conditions = ["FECHACON BETWEEN ? AND ?"];
            $params = [$fecha_inicio, $fecha_fin];
            
            // Simular filtro de doctor
            $doctor = '1';
            if ($doctor) {
                $where_conditions[] = "NUMDOCTOR = ?";
                $params[] = $doctor;
            }
            
            // Simular filtro de estado
            $estatus = '0';
            if ($estatus !== '') {
                $where_conditions[] = "MODOASISTENCIA = ?";
                $params[] = $estatus;
            }
            
            $where_clause = "WHERE " . implode(" AND ", $where_conditions);
            
            echo "<pre>";
            echo "Consulta: SELECT COUNT(*) FROM CITAMEDIC $where_clause\n";
            echo "Parámetros: " . implode(', ', $params) . "\n";
            echo "Número de parámetros: " . count($params) . "\n";
            echo "Número de placeholders (?): " . substr_count($where_clause, '?') . "\n";
            echo "</pre>";
            
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM CITAMEDIC $where_clause");
            $stmt->execute($params);
            $resultado = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<p class='success'>✅ Consulta ejecutada correctamente. Resultado: $resultado</p>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div class='section'>";
        echo "<p class='error'>❌ No se puede realizar la prueba sin el campo MODOASISTENCIA</p>";
        echo "</div>";
    }
    
    // 4. Recomendaciones
    echo "<div class='section'>";
    echo "<h2>4. Estado del Sistema</h2>";
    
    if ($tiene_modoasistencia) {
        echo "<p class='success'>✅ El campo MODOASISTENCIA está disponible y funcionando correctamente</p>";
        echo "<p class='success'>✅ Todas las consultas han sido actualizadas para usar MODOASISTENCIA</p>";
        echo "<p class='success'>✅ El sistema de reportes debería funcionar sin errores de parámetros SQL</p>";
    } else {
        echo "<p class='error'>❌ El campo MODOASISTENCIA no existe en la tabla CITAMEDIC</p>";
        
        if ($tiene_asistencia) {
            echo "<p class='warning'>⚠️ Se podría usar el campo ASISTENCIA como alternativa</p>";
        } elseif ($tiene_estatus) {
            echo "<p class='warning'>⚠️ Se podría usar el campo ESTATUS como alternativa (verificar propósito)</p>";
        }
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='section'>";
    echo "<p class='error'>❌ Error durante la prueba: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Archivo:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Línea:</strong> " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}

echo "<div class='section'>";
echo "<h2>✅ Prueba Completada</h2>";
echo "<p><a href='citas_reportes.php'>🔗 Probar Reportes de Citas</a></p>";
echo "<p><a href='diagnostico.php'>🔗 Ejecutar Diagnóstico Completo</a></p>";
echo "</div>";

echo "</body></html>";
?>
