<?php
session_start();

// Verificar autenticación
if (!isset($_SESSION['usuario']) || $_SESSION['rol'] !== 'doctor') {
    header('Location: login.php');
    exit;
}

$usuario = htmlspecialchars($_SESSION['usuario']);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel del Doctor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">Panel del Doctor</h1>
                <div class="alert alert-success">
                    <h4><PERSON><PERSON><PERSON><PERSON>, Dr. <?php echo $usuario; ?></h4>
                    <p>Panel médico funcionando correctamente.</p>
                </div>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="bi bi-file-medical fs-1 text-primary"></i>
                                <h5 class="card-title mt-2">Historia Clínica</h5>
                                <a href="backend/historia/buscar_paciente.php" class="btn btn-primary">
                                    Acceder
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="bi bi-calendar-check fs-1 text-success"></i>
                                <h5 class="card-title mt-2">Citas Médicas</h5>
                                <a href="backend/citas/gestion_citas.php" class="btn btn-success">
                                    Ver Agenda
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-1 text-info"></i>
                                <h5 class="card-title mt-2">Pacientes</h5>
                                <a href="backend/pacientes/gestion_pacientes.php" class="btn btn-info">
                                    Gestionar
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="logout.php" class="btn btn-danger">
                        <i class="bi bi-box-arrow-right"></i> Cerrar Sesión
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
