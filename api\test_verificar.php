<?php
// test_verificar.php
header('Content-Type: text/plain; charset=UTF-8');
require_once __DIR__ . '/../config/config.php';
$conn = getDBConnection();

$tabla = 'PACIENTES';
if (!($res = $conn->query("SELECT COUNT(*) AS total FROM $tabla WHERE SINCRONIZADO = 0"))) {
    die("❌ Query falló: " . $conn->error);
}
$row = $res->fetch_assoc();
echo "👉 Registros SINCRONIZADO=0 en $tabla: " . $row['total'];
