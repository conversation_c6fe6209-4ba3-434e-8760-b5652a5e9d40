<?php
// public_html/mi_consultorio/api/descargar_archivo.php

define('UPLOAD_DIR', __DIR__ . '/../backend/resultados/upload/');

// Recogemos parámetros
$cedula  = $_GET['cedula']  ?? '';
$archivo = basename($_GET['archivo'] ?? '');
$mode    = $_GET['mode']    ?? 'download';  // download (por defecto) o view

// Validaciones básicas
if (!$cedula || !$archivo) {
    header($_SERVER['SERVER_PROTOCOL'].' 400 Bad Request');
    echo json_encode(['status'=>'Error','message'=>'Parámetros incompletos.']);
    exit;
}

$ruta = UPLOAD_DIR . $archivo;
if (!is_readable($ruta)) {
    header($_SERVER['SERVER_PROTOCOL'].' 404 Not Found');
    echo json_encode(['status'=>'Error','message'=>'Archivo no encontrado.']);
    exit;
}

// Detectar MIME
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mime  = finfo_file($finfo, $ruta);
finfo_close($finfo);

// Cabeceras para la transferencia
header('Content-Description: File Transfer');
header('Content-Type: ' . $mime);

// Inline vs Attachment
if ($mode === 'view') {
    header('Content-Disposition: inline; filename="'.$archivo.'"');
} else {
    header('Content-Disposition: attachment; filename="'.$archivo.'"');
}

header('Expires: 0');
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Content-Length: ' . filesize($ruta));

// Enviar fichero
readfile($ruta);
exit;