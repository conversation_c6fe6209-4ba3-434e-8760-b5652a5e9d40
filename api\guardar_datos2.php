<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

include_once '../config/config.php';

header("Content-Type: application/json");

// Función para convertir una fecha del formato MM/DD/YYYY a YYYY-MM-DD
 function convertirFecha($fecha) {
    // Si ya es un formato válido de MySQL datetime
    if (DateTime::createFromFormat('Y-m-d H:i:s', $fecha)) {
        return $fecha; // No hay necesidad de convertir
    }
    
    // Si está en formato MM/dd/YYYY
    $fechaObj = DateTime::createFromFormat('m/d/Y', $fecha);
    if ($fechaObj) {
        return $fechaObj->format('Y-m-d'); // Convertir a formato MySQL
    }

    // Si está en otro formato conocido (agrega más formatos según tu necesidad)
    $fechaObj = DateTime::createFromFormat('d/m/Y', $fecha);
    if ($fechaObj) {
        return $fechaObj->format('Y-m-d');
    }

    // Si no es válida, retorna null o lo que consideres necesario
    return null;
}



// Leer datos JSON de la solicitud
$input = file_get_contents('php://input');
$headers = getallheaders();
file_put_contents('headers_log.txt', print_r($headers, true), FILE_APPEND);

// Registrar la solicitud en un archivo de log
file_put_contents('request_log.txt', date('Y-m-d H:i:s') . " - " . $input . "\n", FILE_APPEND);


$data = json_decode($input, true);

if (!$data) {
    echo json_encode(["status" => "error", "message" => "JSON inválido"]);
    exit;
}

$conn = getDBConnection();
$conn->set_charset("utf8");
// Procesar cada registro en el JSON

foreach ($data as $paciente) {
    // Convertir fechas al formato correcto para MySQL
    if (!empty($paciente['FECHANAC'])) {
        $paciente['FECHANAC'] = convertirFecha($paciente['FECHANAC']);
    }
    if (!empty($paciente['FECHAINGRESO'])) {
        $paciente['FECHAINGRESO'] = convertirFecha($paciente['FECHAINGRESO']);
    }
    if (!empty($paciente['VIGENCIA'])) {
        $paciente['VIGENCIA'] = convertirFecha($paciente['VIGENCIA']);
    }

    if (isset($paciente['LAST_MODIFIED']) && !empty($paciente['LAST_MODIFIED'])) {
    $paciente['LAST_MODIFIED'] = convertirFecha($paciente['LAST_MODIFIED']);
    } else {
    $paciente['LAST_MODIFIED'] =convertirFecha(now) ; 
    }


    // Verificar que los campos requeridos no estén vacíos
    if (empty($paciente['NOMBRES']) || empty($paciente['APELLIDOS'])
    || empty($paciente['CEDULA'])  || empty($paciente['VISITA'])   ) {
        echo json_encode(["status" => "error", "message" => "Campos obligatorios faltantes"]);
        exit;
    }
        
    
   // Insertar o actualizar los datos en la base
    $stmt = $conn->prepare("INSERT INTO PACIENTES 
        (CLAVE, VISITA, REGISTRO, NOMBRES, APELLIDOS, CEDULA, SEXO, NACIONALIDAD, ESTADOCIVIL, FECHANAC, 
        LUGARNAC, OCUPACION, RELIGION, FECHAINGRESO, RH, CALLE, PROVINCIA, LOCALIDAD, MUNICIPIO, PAIS, REFERENCIA, 
        TELEFONO, CELULAR, TELTRABAJO, FAX, ECORREO, ARS, PLANES, AFILIADO, VIGENCIA, POLIZA, CATEGORIA, ALERGIA, 
        NAF, NSS, NOMBRERESP, DIRECCIONRESP, CEDULARESP, TELEFONORESP, FAMILIARPROX, DIRECCIONFAMILIAR, 
        TELEFONOFAMILIAR, REMITIDO, OBSERVACIONES, ESTATUS, STEMBARAZO, PESOHABITUAL, NIVELESCOLAR, PROCEDENCIA, 
        SINCEDULA, PROFESION, RECORDCLINICA, LAST_MODIFIED, SINCRONIZADO) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 
                ?, ?, IFNULL(?, NOW()), ?) 
        ON DUPLICATE KEY UPDATE 
        VISITA = VALUES(VISITA), REGISTRO = VALUES(REGISTRO), 
        NOMBRES = VALUES(NOMBRES), APELLIDOS = VALUES(APELLIDOS), 
        CEDULA = VALUES(CEDULA), SEXO = VALUES(SEXO), 
        NACIONALIDAD = VALUES(NACIONALIDAD), 
        ESTADOCIVIL = VALUES(ESTADOCIVIL),
        FECHANAC = VALUES(FECHANAC), LUGARNAC = VALUES(LUGARNAC), 
        OCUPACION = VALUES(OCUPACION), RELIGION = VALUES(RELIGION), 
        FECHAINGRESO = VALUES(FECHAINGRESO), RH = VALUES(RH), 
        CALLE = VALUES(CALLE), PROVINCIA = VALUES(PROVINCIA), 
        LOCALIDAD = VALUES(LOCALIDAD), MUNICIPIO = VALUES(MUNICIPIO), 
        PAIS = VALUES(PAIS), REFERENCIA = VALUES(REFERENCIA), 
        TELEFONO = VALUES(TELEFONO), CELULAR = VALUES(CELULAR), 
        TELTRABAJO = VALUES(TELTRABAJO), FAX = VALUES(FAX), 
        ECORREO = VALUES(ECORREO), ARS = VALUES(ARS), 
        PLANES = VALUES(PLANES), AFILIADO = VALUES(AFILIADO),
        VIGENCIA = VALUES(VIGENCIA), POLIZA = VALUES(POLIZA),
        CATEGORIA = VALUES(CATEGORIA), ALERGIA = VALUES(ALERGIA),
        NAF = VALUES(NAF), NSS = VALUES(NSS), NOMBRERESP = VALUES(NOMBRERESP), 
        DIRECCIONRESP = VALUES(DIRECCIONRESP), CEDULARESP = VALUES(CEDULARESP), TELEFONORESP = VALUES(TELEFONORESP), 
        FAMILIARPROX = VALUES(FAMILIARPROX), 
        DIRECCIONFAMILIAR = VALUES(DIRECCIONFAMILIAR), 
        TELEFONOFAMILIAR = VALUES(TELEFONOFAMILIAR), 
        REMITIDO = VALUES(REMITIDO), OBSERVACIONES = VALUES(OBSERVACIONES), ESTATUS = VALUES(ESTATUS), 
        STEMBARAZO = VALUES(STEMBARAZO), PESOHABITUAL = VALUES(PESOHABITUAL), NIVELESCOLAR = VALUES(NIVELESCOLAR), 
        PROCEDENCIA = VALUES(PROCEDENCIA), SINCEDULA = VALUES(SINCEDULA), PROFESION = VALUES(PROFESION), 
        RECORDCLINICA = VALUES(RECORDCLINICA),
        
        LAST_MODIFIED = IFNULL(VALUES(LAST_MODIFIED), NOW()),
        SINCRONIZADO = 1");

    $stmt->bind_param(
        "ssssssssssssssssssssssssssssssssssssssssssssssssssssss",
        $paciente['CLAVE'],
        $paciente['VISITA'],
        $paciente['REGISTRO'],
        $paciente['NOMBRES'],
        $paciente['APELLIDOS'],
        $paciente['CEDULA'],
        $paciente['SEXO'],
        $paciente['NACIONALIDAD'],
        $paciente['ESTADOCIVIL'],
        $paciente['FECHANAC'],
        $paciente['LUGARNAC'],
        $paciente['OCUPACION'],
        $paciente['RELIGION'],
        $paciente['FECHAINGRESO'],
        $paciente['RH'],
        $paciente['CALLE'],
        $paciente['PROVINCIA'],
        $paciente['LOCALIDAD'],
        $paciente['MUNICIPIO'],
        $paciente['PAIS'],
        $paciente['REFERENCIA'],
        $paciente['TELEFONO'],
        $paciente['CELULAR'],
        $paciente['TELTRABAJO'],
        $paciente['FAX'],
        $paciente['ECORREO'],
        $paciente['ARS'],
        $paciente['PLANES'],
        $paciente['AFILIADO'],
        $paciente['VIGENCIA'],
        $paciente['POLIZA'],
        $paciente['CATEGORIA'],
        $paciente['ALERGIA'],
        $paciente['NAF'],
        $paciente['NSS'],
        $paciente['NOMBRERESP'],
        $paciente['DIRECCIONRESP'],
        $paciente['CEDULARESP'],
        $paciente['TELEFONORESP'],
        $paciente['FAMILIARPROX'],
        $paciente['DIRECCIONFAMILIAR'],
        $paciente['TELEFONOFAMILIAR'],
        $paciente['REMITIDO'],
        $paciente['OBSERVACIONES'],
        $paciente['ESTATUS'],
        $paciente['STEMBARAZO'],
        $paciente['PESOHABITUAL'],
        $paciente['NIVELESCOLAR'],
        $paciente['PROCEDENCIA'],
        $paciente['SINCEDULA'],
        $paciente['PROFESION'],
        $paciente['RECORDCLINICA'],
        $paciente['LAST_MODIFIED'],
        $paciente['SINCRONIZADO']
    );

    $stmt->execute();
    

    
}

echo json_encode(["status" => "success", "message" => "Datos sincronizados correctamente"]);

// Cerrar la conexión
$conn->close();
?>
