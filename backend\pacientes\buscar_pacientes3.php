<?php
// buscar_pacientes.php

require_once '../config/database.php';

// Consulta: Traer todos los pacientes ordenados por NOMBRES
$stmt = $pdo->query("SELECT * FROM PACIENTES ORDER BY NOMBRES ASC");
$pacientes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Si se recibe una petición POST, devolvemos el JSON y salimos
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo json_encode($pacientes);
    exit;
}
?>
<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Buscar Pacientes</title>
  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/css/bootstrap.min.css">
  <!-- DataTables CSS -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <style>
    body {
      background-color: #f8f9fa;
    }
    .container {
      margin-top: 2rem;
    }
  </style>
</head>
<body>
<div class="container">
  <h1 class="text-center mb-4">Buscar Pacientes</h1>
  
  <!-- Aquí se mostrará la tabla con los datos -->
  <div id="resultadosBusqueda"></div>
</div>

<!-- jQuery (versión completa) -->
<script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.min.js"></script>

<script>
// Función para cargar la tabla de pacientes con DataTables
function cargarPacientes() {
  // Realizamos la petición POST para obtener los datos
  fetch('buscar_pacientes.php', {
    method: 'POST',
    body: JSON.stringify({}), // En este caso no enviamos filtros
    headers: { 'Content-Type': 'application/json' }
  })
  .then(response => response.json())
  .then(data => {
    // Verificamos que data sea un array
    if (!Array.isArray(data)) {
      console.error("La respuesta no es un array:", data);
      return;
    }
    
    let html = `
      <table id="tablaPacientes" class="table table-striped table-bordered table-hover">
        <thead>
          <tr>
            <th>Cédula</th>
            <th>Registro</th>
            <th>Nombres</th>
            <th>Apellidos</th>
            <th>Edad</th>
            <th>Acciones</th>
          </tr>
        </thead>
        <tbody>
    `;
    data.forEach(paciente => {
      html += `
        <tr>
          <td>${paciente.CEDULA}</td>
          <td>${paciente.REGISTRO}</td>
          <td>${paciente.NOMBRES}</td>
          <td>${paciente.APELLIDOS}</td>
          <td>${paciente.EDAD}</td>
          <td>
            <a href="#" onclick="editarPaciente(${paciente.CLAVE})" title="Editar">
              <i class="fas fa-pencil-alt"></i>
            </a>
            <a href="#" onclick="eliminarPaciente(${paciente.CLAVE})" title="Eliminar">
              <i class="fas fa-trash"></i>
            </a>
          </td>
        </tr>
      `;
    });
    html += `
        </tbody>
      </table>
    `;
    document.getElementById('resultadosBusqueda').innerHTML = html;
    
    // Inicializar DataTables (después de insertar la tabla en el DOM)
    $('#tablaPacientes').DataTable({
      ordering: true,
      searching: true,
      language: {
        search: "Buscar:",
        zeroRecords: "No se encontraron resultados",
        emptyTable: "No hay pacientes disponibles",
        loadingRecords: "Cargando..."
      }
    });
  })
  .catch(error => {
    console.error('Error al cargar los pacientes:', error);
  });
}

// Funciones de ejemplo para editar y eliminar (modifícalas según tus necesidades)
function editarPaciente(clave) {
  // Aquí podrías redirigir a un formulario de edición o abrir un modal
  alert('Editar paciente con CLAVE: ' + clave);
}
function eliminarPaciente(clave) {
  if (confirm('¿Estás seguro de eliminar este paciente?')) {
    alert('Eliminar paciente con CLAVE: ' + clave);
  }
}

// Cargar la tabla de pacientes cuando el documento esté listo
$(document).ready(function() {
  cargarPacientes();
});
</script>
</body>
</html>
