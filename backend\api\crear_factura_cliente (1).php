<?php
// Habilitar la visualización de errores para depuración (QUITAR EN PRODUCCIÓN)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Conexión a la base de datos MAESTRA (para obtener la configuración de los consultorios)
// Ajusta esta ruta si tu database.php (maestro) no está en ../config/
require_once __DIR__ . '/../config/database.php';

$masterPdo = getMasterPdo();

// Obtener el subdominio del consultorio de la URL
$subdominio_seleccionado = $_GET['subdominio'] ?? '';
$pdo = null; // Variable para la conexión al tenant específico
$empresa = null; // Variable para los datos de la EMPRESA del tenant
$error_tenant_connection = null; // Para errores específicos de la conexión al tenant

if ($subdominio_seleccionado) {
    try {
        // Recuperar la cadena de conexión del tenant de la base de datos maestra
        $stmt_master = $masterPdo->prepare("SELECT CadenaConexionDB FROM Consultorios WHERE Subdominio = ? AND Estado = 'Activo'");
        $stmt_master->execute([$subdominio_seleccionado]);
        $cfg = $stmt_master->fetch(PDO::FETCH_ASSOC);

        if ($cfg && !empty($cfg['CadenaConexionDB'])) {
            // Parsear la cadena de conexión
            // Ejemplo de CadenaConexionDB: "mysql:host=localhost;dbname=db_tenant_x;user=user_x;password=******"
            // Se asume este formato. Si es diferente, ajusta la lógica de parsing.
            preg_match('/host=([^;]+)/', $cfg['CadenaConexionDB'], $matches_host); $host = $matches_host[1] ?? '';
            preg_match('/dbname=([^;]+)/', $cfg['CadenaConexionDB'], $matches_dbname); $dbname = $matches_dbname[1] ?? '';
            preg_match('/user=([^;]+)/', $cfg['CadenaConexionDB'], $matches_user); $user = $matches_user[1] ?? '';
            preg_match('/password=([^;]+)/', $cfg['CadenaConexionDB'], $matches_pass); $password = $matches_pass[1] ?? '';
            
            if ($host && $dbname && $user !== '') { // Password puede ser vacío
                $dsn = "mysql:host={$host};dbname={$dbname};charset=utf8mb4";
                $pdo = new PDO($dsn, $user, $password, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]);

                // 1) Obtener datos del consultorio (EMPRESA) del tenant actual
                $stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
                $empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

            } else {
                $error_tenant_connection = "Datos de conexión incompletos o inválidos para el subdominio '{$subdominio_seleccionado}'.";
            }
        } else {
            $error_tenant_connection = "Configuración de conexión no encontrada o inactiva para el subdominio '{$subdominio_seleccionado}'.";
        }
    } catch (PDOException $e) {
        $error_tenant_connection = "Error al conectar o consultar la base de datos del consultorio '{$subdominio_seleccionado}': " . htmlspecialchars($e->getMessage());
    }
} else {
    $error_tenant_connection = "No se ha seleccionado ningún consultorio. Por favor, selecciona uno desde el Histórico de Facturas.";
}

// --- Lógica para obtener y listar Facturas Existentes (del tenant seleccionado) ---
$facturas_existentes = [];
if ($pdo && $empresa) { // Solo si la conexión al tenant fue exitosa
    $cliente_rnc_actual = $empresa['RNC'] ?? '';
    if (!empty($cliente_rnc_actual)) {
        // Primero, obtener la CLAVE del cliente_software si existe
        $stmt_cliente_clave = $pdo->prepare("SELECT CLAVE FROM CLIENTES_SOFTWARE WHERE RNC = ?");
        $stmt_cliente_clave->execute([$cliente_rnc_actual]);
        $cliente_software = $stmt_cliente_clave->fetch(PDO::FETCH_ASSOC);

        if ($cliente_software) {
            $cliente_clave_actual = $cliente_software['CLAVE'];
            // Luego, obtener todas las facturas_software para esa CLAVE de cliente
            $stmt_facturas = $pdo->prepare("SELECT * FROM FACTURAS_SOFTWARE WHERE CLIENTE_CLAVE = ? ORDER BY FECHA_CREACION DESC");
            $stmt_facturas->execute([$cliente_clave_actual]);
            $facturas_existentes = $stmt_facturas->fetchAll(PDO::FETCH_ASSOC);
        }
    }
}
// --- FIN Lógica para obtener y listar Facturas Existentes ---


// 2) Procesar POST para crear o eliminar una factura
// 2) Procesar POST para crear o eliminar una factura
if ($_SERVER["REQUEST_METHOD"] === "POST" && $pdo && $empresa) { // Solo procesar si hay conexión al tenant
    
    // Lógica para eliminar una factura
    if (isset($_POST['action']) && $_POST['action'] === 'delete_invoice') {
        $id_factura_a_eliminar = $_POST['id_factura'] ?? null;
        $subdominio_param = $_POST['subdominio'] ?? ''; // Recuperamos el subdominio del formulario oculto

        if ($id_factura_a_eliminar) {
            try {
                // Eliminar la factura de la base de datos del tenant
                $stmt = $pdo->prepare("DELETE FROM FACTURAS_SOFTWARE WHERE CLAVE = ?");
                $stmt->execute([$id_factura_a_eliminar]);

                // ¡Redireccionamos para refrescar la página!
                // Esto también evita los errores de "Undefined array key"
                header("Location: crear_factura_cliente.php?subdominio=" . urlencode($subdominio_param) . "&status=deleted");
                exit();
            } catch (Exception $e) {
                echo "<div class='alert alert-danger mt-3'>Error al eliminar factura: " . htmlspecialchars($e->getMessage()) . "</div>";
            }
        }
    }
    // Lógica para crear una factura (usamos elseif para que no se ejecute con la acción de eliminar)
    elseif (isset($_POST['action']) && $_POST['action'] === 'create_invoice') {
        // Recoger campos
        $concepto = trim($_POST['concepto'] ?? '');
        $precio = floatval($_POST['precio'] ?? 0);
        $moneda = $_POST['moneda'] ?? 'USD';
        $modopago = $_POST['modopago'] ?? 'Transferencia';
        $fechapago = $_POST['fechapago'] ?? date('Y-m-d');

        // Campos bancarios
        $banco_destino = $_POST['banco_destino'] ?? '';
        $cuenta_marcsoftware = trim($_POST['cuenta_marcsoftware'] ?? '');
        $beneficiario_marcsoftware = $_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions';
        
        // El resto del código para crear la factura...
        try {
            // Datos del cliente desde EMPRESA (del tenant actual)
            $cliente_nombre = $empresa['NOMBRE'] ?? 'Cliente';
            $cliente_rnc = $empresa['RNC'] ?? '';
            $cliente_telefono = $empresa['TELEFONO'] ?? '';
            $cliente_email = $empresa['CORREOE'] ?? '';
            $cliente_direccion = trim(($empresa['CALLE'] ?? '') . ', ' . ($empresa['MUNICIPIO'] ?? '') . ', ' . ($empresa['PROVINCIA'] ?? ''), ', ');
            $cliente_ciudad = $empresa['MUNICIPIO'] ?? '';
            $cliente_provincia = $empresa['PROVINCIA'] ?? '';
            $cliente_especialidad = $empresa['ESPECIALIDAD'] ?? '';
            $cliente_subdominio = $empresa['SUBDOMINIO'] ?? '';

            // 2.1) Verificar o crear cliente en la tabla CLIENTES_SOFTWARE del tenant actual
            $stmt = $pdo->prepare("SELECT CLAVE FROM CLIENTES_SOFTWARE WHERE RNC = ?");
            $stmt->execute([$cliente_rnc]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$row) {
                // Crear tabla clientes si no existe (fallback, idealmente ya existe)
                $pdo->exec("CREATE TABLE IF NOT EXISTS CLIENTES_SOFTWARE (
                    CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                    NOMBRE_COMPLETO VARCHAR(200),
                    RNC VARCHAR(20),
                    TELEFONO VARCHAR(20),
                    EMAIL VARCHAR(100),
                    DIRECCION VARCHAR(200),
                    CIUDAD VARCHAR(100),
                    PROVINCIA VARCHAR(100),
                    ESPECIALIDAD VARCHAR(100),
                    SUBDOMINIO VARCHAR(50),
                    FECHA_REGISTRO DATE,
                    ESTATUS CHAR(1) DEFAULT 'A'
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

                $ins = $pdo->prepare("INSERT INTO CLIENTES_SOFTWARE
                    (NOMBRE_COMPLETO,RNC,TELEFONO,EMAIL,DIRECCION,CIUDAD,PROVINCIA,ESPECIALIDAD,SUBDOMINIO,FECHA_REGISTRO)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURDATE())");
                $ins->execute([
                    $cliente_nombre,
                    $cliente_rnc,
                    $cliente_telefono,
                    $cliente_email,
                    ($empresa['CALLE'] ?? '') . ', ' . ($empresa['MUNICIPIO'] ?? '') . ', ' . ($empresa['PROVINCIA'] ?? ''),
                    $cliente_ciudad,
                    $cliente_provincia,
                    $cliente_especialidad,
                    $cliente_subdominio
                ]);
                $cliente_clave = $pdo->lastInsertId();
            } else {
                $cliente_clave = $row['CLAVE'];
            }

            // 2.2) Crear tabla facturas si no existe (fallback) en la base de datos del tenant
            $pdo->exec("CREATE TABLE IF NOT EXISTS FACTURAS_SOFTWARE (
                CLAVE INT AUTO_INCREMENT PRIMARY KEY,
                CLIENTE_CLAVE INT,
                NUMERO_FACTURA VARCHAR(20),
                CONCEPTO TEXT,
                PRECIO DECIMAL(10,2),
                MONEDA VARCHAR(5),
                MODO_PAGO VARCHAR(20),
                BANCO_DESTINO VARCHAR(50),
                CUENTA_DESTINO VARCHAR(30),
                BENEFICIARIO VARCHAR(100),
                FECHA_FACTURA DATE,
                FECHA_VENCIMIENTO DATE,
                ESTATUS VARCHAR(10) DEFAULT 'PENDIENTE',
                FECHA_CREACION DATETIME,
                USUARIO_CREACION INT,
                FOREIGN KEY (CLIENTE_CLAVE) REFERENCES CLIENTES_SOFTWARE(CLAVE) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");

            // 2.3) Generar número automático para la factura
            $max = $pdo
                ->query("SELECT MAX(CAST(SUBSTRING(NUMERO_FACTURA,4) AS UNSIGNED)) AS m FROM FACTURAS_SOFTWARE")
                ->fetch(PDO::FETCH_ASSOC)['m'];
            $num = 'MS-' . (($max !== null ? intval($max) : 1000) + 1);

            // 2.4) Incluir datos de transferencia en el concepto si el modo de pago es Transferencia
            $fullConcept = $concepto;
            if ($modopago === 'Transferencia' && $banco_destino) {
                $fullConcept .= "\n\nDATOS PARA TRANSFERENCIA:\n"
                                 . "Banco: {$banco_destino}\n"
                                 . "Cuenta: {$cuenta_marcsoftware}\n"
                                 . "Beneficiario: {$beneficiario_marcsoftware}";
            }

            // 2.5) Insertar la factura en la base de datos del tenant
            $fecha_venc = date('Y-m-d', strtotime('+30 days', strtotime($fechapago)));
            $stmt = $pdo->prepare("INSERT INTO FACTURAS_SOFTWARE
                (CLIENTE_CLAVE,NUMERO_FACTURA,CONCEPTO,PRECIO,MONEDA,MODO_PAGO,
                 BANCO_DESTINO,CUENTA_DESTINO,BENEFICIARIO,FECHA_FACTURA,FECHA_VENCIMIENTO,
                 ESTATUS, FECHA_CREACION,USUARIO_CREACION)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'PENDIENTE', NOW(), 1)");
            $stmt->execute([
                $cliente_clave, $num, $fullConcept, $precio, $moneda, $modopago,
                $banco_destino, $cuenta_marcsoftware, $beneficiario_marcsoftware,
                $fechapago, $fecha_venc
            ]);

            // 3) Mostrar mensaje y enlace de impresión
            $lastId = $pdo->lastInsertId();
            echo "<div class='alert alert-success mt-3'>";
            echo "    <h4>✅ Factura {$num} creada exitosamente</h4>";
            echo "    <p><strong>Cliente:</strong> {$cliente_nombre}</p>";
            echo "    <p><strong>RNC:</strong> {$cliente_rnc}</p>";
            echo "    <p><strong>Monto:</strong> {$moneda} {$precio}</p>";
            echo "    <p><strong>Vencimiento:</strong> {$fecha_venc}</p>";
            echo "    <a href='generar_factura_pdf.php?id_factura={$lastId}&subdominio=" . urlencode($subdominio_seleccionado) . "' target='_blank' class='btn btn-primary mt-3'>";
            echo "        🖨️ Imprimir Factura Profesional";
            echo "    </a>";
            echo "</div>";

            // Vuelve a cargar las facturas existentes para mostrar la recién creada
            if (isset($cliente_software['CLAVE'])) {
                $stmt_facturas = $pdo->prepare("SELECT * FROM FACTURAS_SOFTWARE WHERE CLIENTE_CLAVE = ? ORDER BY FECHA_CREACION DESC");
                $stmt_facturas->execute([$cliente_software['CLAVE']]);
                $facturas_existentes = $stmt_facturas->fetchAll(PDO::FETCH_ASSOC);
            }

        } catch (Exception $e) {
            echo "<div class='alert alert-danger mt-3'>Error al crear factura: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    }
}

?>

<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <title>Crear Factura - MarcSoftware Solutions</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
  <h2>🏢 Crear Factura para <?= htmlspecialchars($empresa['NOMBRE'] ?? 'Consultorio Seleccionado') ?></h2>
  <p class="text-muted">Facturar servicios de desarrollo de software a clientes</p>

  <?php if ($error_tenant_connection): ?>
    <div class="alert alert-danger"><?= $error_tenant_connection ?></div>
    <p>Por favor, regresa al <a href="historico_facturas.php" class="btn btn-info">Histórico de Facturas</a> y selecciona un consultorio válido.</p>
  <?php else: ?>

    <?php if ($empresa): ?>
    <div class="card mb-4">
      <div class="card-header bg-info text-white">
        <h5>👨‍⚕️ Cliente Actual (Datos desde tabla EMPRESA del consultorio '<?= htmlspecialchars($subdominio_seleccionado) ?>')</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <strong>Nombre:</strong> <?= htmlspecialchars($empresa['NOMBRE'] ?? 'N/A') ?><br>
            <strong>RNC:</strong> <?= htmlspecialchars($empresa['RNC'] ?? 'N/A') ?><br>
            <strong>Especialidad:</strong> <?= htmlspecialchars($empresa['ESPECIALIDAD'] ?? 'N/A') ?>
          </div>
          <div class="col-md-6">
            <strong>Teléfono:</strong> <?= htmlspecialchars($empresa['TELEFONO'] ?? 'N/A') ?><br>
            <strong>Email:</strong> <?= htmlspecialchars($empresa['CORREOE'] ?? 'N/A') ?><br>
            <strong>Subdominio:</strong> <?= htmlspecialchars($empresa['SUBDOMINIO'] ?? 'N/A') ?>
          </div>
        </div>
        <div class="mt-2">
          <strong>Dirección:</strong> <?= htmlspecialchars(($empresa['CALLE'] ?? '') . ', ' . ($empresa['MUNICIPIO'] ?? '') . ', ' . ($empresa['PROVINCIA'] ?? '')) ?>
        </div>
      </div>
    </div>
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5>📄 Facturas Emitidas para este Consultorio</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($facturas_existentes)): ?>
                <ul class="list-group">
                 <?php foreach ($facturas_existentes as $factura_existente): ?>
    <li class="list-group-item d-flex justify-content-between align-items-center">
        <div>
            <strong>Factura No. <?= htmlspecialchars($factura_existente['NUMERO_FACTURA']) ?></strong>
            <br>
            Monto: <?= htmlspecialchars($factura_existente['MONEDA']) ?> <?= number_format($factura_existente['PRECIO'], 2) ?>
            (Vence: <?= htmlspecialchars($factura_existente['FECHA_VENCIMIENTO']) ?>)
        </div>
        <div>
            <a href="generar_factura_pdf.php?id_factura=<?= htmlspecialchars($factura_existente['CLAVE']) ?>&subdominio=<?= urlencode($subdominio_seleccionado) ?>" target="_blank" class="btn btn-sm btn-info me-2">
                Ver PDF
            </a>
            <!-- Botón de edición que ahora apunta a editar_factura_cliente.php -->
            <a href="editar_factura_cliente.php?id_factura=<?= htmlspecialchars($factura_existente['CLAVE']) ?>&subdominio=<?= urlencode($subdominio_seleccionado) ?>" class="btn btn-sm btn-warning me-2">
                Editar
            </a>
            <form method="POST" style="display:inline-block;" onsubmit="return confirm('¿Estás seguro de que deseas eliminar la factura <?= htmlspecialchars($factura_existente['NUMERO_FACTURA']) ?>? Esta acción no se puede deshacer.');">
                <input type="hidden" name="action" value="delete_invoice">
                <input type="hidden" name="id_factura" value="<?= htmlspecialchars($factura_existente['CLAVE']) ?>">
                <input type="hidden" name="subdominio" value="<?= urlencode($subdominio_seleccionado) ?>">
                <button type="submit" class="btn btn-sm btn-danger">Eliminar</button>
            </form>
        </div>
    </li>
<?php endforeach; ?>

                </ul>
            <?php else: ?>
                <p>No hay facturas emitidas para este consultorio aún.</p>
            <?php endif; ?>
        </div>
    </div>
    <form method="POST">
      <div class="card mb-4">
        <div class="card-header"><h5>💰 Detalles de Facturación</h5></div>
        <div class="card-body">
          <div class="mb-3">
            <label class="form-label">Concepto/Servicio:</label>
            <textarea name="concepto" class="form-control" rows="3" required
              placeholder="Ej: Sistema Integral ConsulSoft + Primer mes mantenimiento"><?= htmlspecialchars($_POST['concepto'] ?? '') ?></textarea>
          </div>
          <div class="row">
            <div class="col-md-4 mb-3">
              <label class="form-label">Precio:</label>
              <input type="number" name="precio" step="0.01" class="form-control" value="<?= htmlspecialchars($_POST['precio'] ?? '') ?>" required>
            </div>
            <div class="col-md-4 mb-3">
              <label class="form-label">Moneda:</label>
              <select name="moneda" class="form-control">
                <option value="USD" <?= (($_POST['moneda'] ?? '') === 'USD') ? 'selected' : '' ?>>USD (Dólares)</option>
                <option value="DOP" <?= (($_POST['moneda'] ?? '') === 'DOP') ? 'selected' : '' ?>>RD$ (Pesos)</option>
              </select>
            </div>
            <div class="col-md-4 mb-3">
              <label class="form-label">Modo de Pago:</label>
              <select name="modopago" class="form-control" id="modopago" onchange="toggleBankFields()">
                <option value="Transferencia" <?= (($_POST['modopago'] ?? '') === 'Transferencia') ? 'selected' : '' ?>>Transferencia</option>
                <option value="Efectivo" <?= (($_POST['modopago'] ?? '') === 'Efectivo') ? 'selected' : '' ?>>Efectivo</option>
                <option value="Cheque" <?= (($_POST['modopago'] ?? '') === 'Cheque') ? 'selected' : '' ?>>Cheque</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <div id="bank-fields" class="card mb-4" style="display:none;">
        <div class="card-header"><h5>🏦 Datos Bancarios - MarcSoftware Solutions</h5></div>
        <div class="card-body">
          <p class="text-info">El cliente transferirá a estas cuentas:</p>
          <div class="row">
            <div class="col-md-4 mb-3">
              <label class="form-label">Banco Destino:</label>
              <select name="banco_destino" class="form-control">
                <option value="">Seleccionar Banco</option>
                <option <?= (($_POST['banco_destino'] ?? '') === 'Banco Popular') ? 'selected' : '' ?>>Banco Popular</option>
                <option <?= (($_POST['banco_destino'] ?? '') === 'Banreservas') ? 'selected' : '' ?>>Banreservas</option>
                <option <?= (($_POST['banco_destino'] ?? '') === 'Banco BHD León') ? 'selected' : '' ?>>Banco BHD León</option>
                <option <?= (($_POST['banco_destino'] ?? '') === 'Banco Scotiabank') ? 'selected' : '' ?>>Banco Scotiabank</option>
              </select>
            </div>
            <div class="col-md-4 mb-3">
              <label class="form-label">Cuenta MarcSoftware:</label>
              <input type="text" name="cuenta_marcsoftware" class="form-control" placeholder="Número de cuenta" value="<?= htmlspecialchars($_POST['cuenta_marcsoftware'] ?? '') ?>">
            </div>
            <div class="col-md-4 mb-3">
              <label class="form-label">Beneficiario:</label>
              <input type="text" name="beneficiario_marcsoftware" class="form-control" value="<?= htmlspecialchars($_POST['beneficiario_marcsoftware'] ?? 'MarcSoftware Solutions') ?>">
            </div>
          </div>
        </div>
      </div>

      <div class="mb-3">
        <label class="form-label">Fecha de Factura:</label>
        <input type="date" name="fechapago" class="form-control" value="<?= htmlspecialchars($_POST['fechapago'] ?? date('Y-m-d')) ?>">
      </div>
      
      <!-- === AÑADE ESTA LÍNEA === -->
    <input type="hidden" name="action" value="create_invoice">
      
      
      
      
      <button type="submit" class="btn btn-success btn-lg mb-4">
        💾 Crear Factura para <?= htmlspecialchars($empresa['NOMBRE'] ?? 'Consultorio') ?>
      </button>
    </form>
  <?php endif; // Fin del bloque si no hay error_tenant_connection ?>

</div>

<script>
function toggleBankFields() {
  const modo = document.getElementById('modopago').value;
  document.getElementById('bank-fields').style.display = modo === 'Transferencia' ? 'block' : 'none';
}
document.addEventListener('DOMContentLoaded', toggleBankFields);
</script>
</body>
</html>