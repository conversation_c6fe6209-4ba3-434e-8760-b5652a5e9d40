<?php
// Incluir el archivo de PHPMailer
require 'libs/phpmailer/PHPMailerAutoload.php';  // Ajusta la ruta si es necesario

// Crear una instancia de PHPMailer
$mail = new PHPMailer;

// Configurar el servidor de correo
$mail->isSMTP();
$mail->Host = 'smtp.example.com';  // Configura tu servidor SMTP
$mail->SMTPAuth = true;
$mail->Username = '<EMAIL>';  // Tu email
$mail->Password = 'your-email-password';     // Tu contraseña de correo
$mail->SMTPSecure = 'tls';                  // Encriptación (opcional)
$mail->Port = 587;                          // Puerto SMTP (587 es común para TLS)

// Configurar el remitente y el destinatario
$mail->setFrom('<EMAIL>', 'Nombre del Remitente');
$mail->addAddress('<EMAIL>', 'Nombre del Destinatario');  // Destinatario

// Configurar el contenido del correo
$mail->isHTML(true);
$mail->Subject = 'Correo de prueba';
$mail->Body    = '<h1>Este es un correo de prueba enviado con PHPMailer</h1>';
$mail->AltBody = 'Este es un correo de prueba enviado con PHPMailer (texto plano)';

// Enviar el correo y verificar si fue exitoso
if(!$mail->send()) {
    echo 'Error al enviar el correo: ' . $mail->ErrorInfo;
} else {
    echo 'Correo enviado exitosamente';
}
?>
