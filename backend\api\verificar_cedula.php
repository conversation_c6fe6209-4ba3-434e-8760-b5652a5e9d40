<?php

// Incluir la configuración de la base de datos y otros parámetros
require_once '../config/database.php';
$config = include '../config/config.php'; // Ruta al archivo de configuración



if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $NOMBRES = $_POST['NOMBRES'];
    $APELLIDOS = $_POST['APELLIDOS'];
    $EDAD = $_POST['FECHANAC'];
    $CEDULA = $_POST['CEDULA'];

    // Verificar si la cédula ya existe
    $stmt = $pdo->prepare("SELECT * FROM PACIENTES WHERE CEDULA = ?");
    $stmt->execute([$CEDULA]);
    $paciente_existente = $stmt->fetch();

    if ($paciente_existente) {
        // Si la cédula ya existe, mostrar mensaje y opción para actualizar
        $mensaje_error = "¡Atención! La cédula ya está registrada con el nombre: " . $paciente_existente['NOMBRES'] . ". ¿Deseas actualizar el registro?";
        $accion = 'actualizar'; // Indica que la acción es actualizar
        $paciente_id = $paciente_existente['CLAVE']; // ID del paciente existente
    } else {
        // Si no existe, proceder a insertar el nuevo paciente
        $stmt = $pdo->prepare("INSERT INTO PACIENTES (NOMBRES, APELLIDOS, FECHANAC, CEDULA) VALUES (?, ?, ?, ?)");
        $stmt->execute([$NOMBRES, $APELLIDOS, $EDAD, $CEDULA]);
        $mensaje_exito = "Paciente registrado con éxito.";
    }
}
?>

