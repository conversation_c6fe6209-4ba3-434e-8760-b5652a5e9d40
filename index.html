<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Paciente</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
</head>
<body>

<div class="container mt-5">
    <h2>Registro de Paciente</h2>
    <form id="RegistroPaciente" method="post">
        <div class="form-group">
            <label for="NOMBRES">Nombres</label>
            <input type="text" class="form-control" id="NOMBRES" name="NOMBRES" required>
        </div>
        <div class="form-group">
            <label for="APELLIDOS">Apellidos</label>
            <input type="text" class="form-control" id="APELLIDOS" name="APELLIDOS" required>
        </div>
        <div class="form-group">
            <label for="CEDULA">Cédula</label>
            <input type="text" class="form-control" id="CEDULA" name="CEDULA" required>
        </div>
        <div class="form-group">
            <label for="SEXO">Sexo</label>
            <select class="form-control" id="SEXO" name="SEXO" required>
                <option value="Masculino">Masculino</option>
                <option value="Femenino">Femenino</option>
            </select>
        </div>
        <div class="form-group">
            <label for="TELEFONO">Teléfono</label>
            <input type="text" class="form-control" id="TELEFONO" name="TELEFONO" required>
        </div>
        <div class="form-group">
            <label for="CELULAR">Celular</label>
            <input type="text" class="form-control" id="CELULAR" name="CELULAR" required>
        </div>
        <div class="form-group">
            <label for="ECORREO">Correo Electrónico</label>
            <input type="email" class="form-control" id="ECORREO" name="ECORREO" required>
        </div>
        <div class="form-group">
            <label for="NACIONALIDAD">Nacionalidad</label>
            <input type="text" class="form-control" id="NACIONALIDAD" name="NACIONALIDAD" required>
        </div>
        <div class="form-group">
            <label for="FECHANAC">Fecha de Nacimiento</label>
            <input type="text" class="form-control" id="FECHANAC" name="FECHANAC" required>
        </div>
        <div class="form-group">
            <label for="DIRECCIONRESP">Dirección de Residencia</label>
            <input type="text" class="form-control" id="DIRECCIONRESP" name="DIRECCIONRESP" required>
        </div>
        <button type="submit" class="btn btn-primary">Registrar Paciente</button>
    </form>
    <div id="mensaje" class="mt-3"></div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script>
// Expresión regular para validar correo electrónico
function validarCorreo(correo) {
    var regexCorreo = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return regexCorreo.test(correo);
}

// Función para validar la fecha de nacimiento
function validarFechaNacimiento(fechaNacimiento) {
    var fecha = new Date(fechaNacimiento);
    var hoy = new Date();
    if (fecha > hoy) {
        alert("La fecha de nacimiento no puede ser mayor a la fecha actual.");
        return false;
    }
    return true;
}

// Función para validar la cédula
function validarCedula(CEDULA) {
    if (cedula.length !== 11 || isNaN(cedula)) {
        alert("La cédula debe tener 11 dígitos numéricos.");
        return false;
    }
    return true;
}

$(document).ready(function() {
    // Inicializamos Flatpickr para la fecha
    flatpickr("#FECHANAC", {
        dateFormat: "Y-m-d", // Formato de la fecha
        allowInput: true // Permite escribir la fecha manualmente
    });

    $('#registroPaciente').on('submit', function(e) {
        e.preventDefault(); // Previene el envío tradicional del formulario

        // Obtenemos los datos del formulario
        var formData = {
            NOMBRES: $('#NOMBRES').val(),
            APELLIDOS: $('#APELLIDOS').val(),
            CEDULA: $('#CEDULA').val(),
            SEXO: $('#SEXO').val(),
            TELEFONO: $('#TELEFONO').val(),
            CELULAR: $('#CELULAR').val(),
            ECORREO: $('#ECORREO').val(),
            NACIONALIDAD: $('#NACIONALIDAD').val(),
            FECHANAC: $('#FECHANAC').val(),
            DIRECCIONRESP: $('#DIRECCIONRESP').val()
        };

        // Validaciones
        if (!validarCedula(formData.CEDULA)) return;
        if (!validarFechaNacimiento(formData.FECHANAC)) return;
        if (!validarCorreo(formData.ECORREO)) {
            alert("Por favor ingresa un correo electrónico válido.");
            return;
        }

        // Convertimos los datos a formato JSON
        var jsonData = JSON.stringify(formData);

        // Hacemos la petición AJAX para enviar los datos
        $.ajax({
            url: 'https://marcsoftware.com/mi_consultorio/backend/api/pacientes.php',
            type: 'POST',
            contentType: 'application/json',
            data: jsonData,
            success: function(response) {
                console.log(response); // Imprime la respuesta completa para depuración

                // Verificamos si la respuesta contiene el mensaje de éxito
                if (response.message === "Paciente registrado con éxito") {
                    $('#mensaje').html('<div class="alert alert-success">Paciente registrado con éxito</div>');
                    $('#registroPaciente')[0].reset(); // Resetea el formulario
                } else {
                    $('#mensaje').html('<div class="alert alert-danger">Hubo un error al registrar al paciente</div>');
                    console.log("Error: " + response.message); // Imprime el mensaje de error recibido
                }
            },
            error: function(xhr, status, error) {
                console.error(error); // Imprime el error en la consola para depuración
                $('#mensaje').html('<div class="alert alert-danger">Hubo un error al registrar al paciente</div>');
            }
        });
    });
});
</script>

</body>
</html>
