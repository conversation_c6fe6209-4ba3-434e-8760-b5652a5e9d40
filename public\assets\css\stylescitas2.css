body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
    color: #333;
}

h1 {
    text-align: center;
    background-color: #4CAF50;
    color: white;
    padding: 20px 0;
    margin: 0;
}

.filters {
    text-align: center;
    margin: 20px 0;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.filters button {
    background-color: #4CAF50 !important;
    color: white !important;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
    transition: background-color 0.3s ease !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.filters button i {
    margin-right: 8px !important;
}

.filters button:hover {
    background-color: #45a049 !important;
}

.citas-section {
    width: 90%;
    margin: 20px auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

table thead {
    background-color: #f2f2f2;
}

table th, table td {
    border: 1px solid #ddd;
    text-align: left;
    padding: 8px;
}

table th {
    background-color: #4CAF50;
    color: white;
}

table tr:nth-child(even) {
    background-color: #f9f9f9;
}

table tr:hover {
    background-color: #f1f1f1;
}

table button {
    background-color: #007BFF;
    color: white;
    border: none;
    padding: 5px 10px;
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

table button:hover {
    background-color: #0056b3;
}

.icon-button {
    border: none;
    background: none;
    cursor: pointer;
    font-size: 16px;
    color: #007BFF;
    transition: color 0.3s ease;
}

.icon-button:hover {
    color: #0056b3;
}
