<?php
/**
 * Centro de Reportes Médicos
 * Sistema integral de reportes y análisis para consultorio médico
 *
 * <AUTHOR> de Consultorio
 * @version 2.0
 */

session_start();
$rolesPermitidos = ['admin', 'doctor', 'secretaria'];

if (!isset($_SESSION['usuario']) || !in_array($_SESSION['rol'], $rolesPermitidos)) {
    header('Location: login.php');
    exit;
}

require_once __DIR__ . '/backend/config/database.php';
date_default_timezone_set('America/Santo_Domingo');

$stmt_empresa = $pdo->query("SELECT * FROM EMPRESA LIMIT 1");
$empresa = $stmt_empresa->fetch(PDO::FETCH_ASSOC);

$usuario = htmlspecialchars($_SESSION['usuario']);
$rol = $_SESSION['rol'];
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Centro de Reportes - <?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio Médico'; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Variables CSS para paleta médica */
        :root {
            --medical-primary: #1e40af;
            --medical-secondary: #3b82f6;
            --medical-accent: #0ea5e9;
            --medical-success: #10b981;
            --medical-warning: #f59e0b;
            --medical-danger: #ef4444;
            --medical-info: #06b6d4;
            --medical-light: #f8fafc;
            --medical-dark: #1e293b;
            --medical-gray-50: #f9fafb;
            --medical-gray-100: #f3f4f6;
            --medical-gray-200: #e5e7eb;
            --medical-gray-300: #d1d5db;
            --medical-gray-400: #9ca3af;
            --medical-gray-500: #6b7280;
            --medical-gray-600: #4b5563;
            --medical-gray-700: #374151;
            --medical-gray-800: #1f2937;
            --medical-gray-900: #111827;
            --medical-white: #ffffff;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --border-radius: 12px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Estilos base */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--medical-light) 0%, var(--medical-gray-50) 100%);
            color: var(--medical-gray-800);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* Header médico moderno */
        .medical-header {
            background: linear-gradient(135deg,
                var(--medical-primary) 0%,
                var(--medical-secondary) 50%,
                var(--medical-accent) 100%);
            color: var(--medical-white);
            padding: 3rem 0;
            margin-bottom: 3rem;
            position: relative;
            overflow: hidden;
        }

        .medical-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .medical-header .container {
            position: relative;
            z-index: 2;
        }

        .medical-header h1 {
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .medical-header .lead {
            font-size: 1.2rem;
            opacity: 0.9;
            font-weight: 400;
        }

        /* Breadcrumb médico */
        .medical-breadcrumb {
            background: transparent;
            padding: 0;
        }

        .medical-breadcrumb .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: var(--transition);
        }

        .medical-breadcrumb .breadcrumb-item a:hover {
            color: var(--medical-white);
        }

        .medical-breadcrumb .breadcrumb-item.active {
            color: rgba(255, 255, 255, 0.6);
        }

        /* Cards de estadísticas */
        .stats-card {
            background: linear-gradient(135deg,
                var(--medical-white) 0%,
                var(--medical-gray-50) 100%);
            border: 1px solid var(--medical-gray-200);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-md);
            transition: var(--transition);
            overflow: hidden;
            position: relative;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg,
                var(--medical-primary) 0%,
                var(--medical-secondary) 50%,
                var(--medical-accent) 100%);
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .stats-card .card-body {
            padding: 2rem;
            text-align: center;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: var(--medical-white);
            background: linear-gradient(135deg,
                var(--medical-primary) 0%,
                var(--medical-secondary) 100%);
            box-shadow: var(--shadow-md);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--medical-primary);
            margin-bottom: 0.5rem;
            line-height: 1;
        }

        .stats-label {
            font-size: 0.9rem;
            color: var(--medical-gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Cards de reportes */
        .report-card {
            background: var(--medical-white);
            border: 1px solid var(--medical-gray-200);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            transition: var(--transition);
            overflow: hidden;
            height: 100%;
        }

        .report-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--shadow-xl);
            border-color: var(--medical-primary);
        }

        .report-card .card-body {
            padding: 2rem;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .report-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: var(--medical-white);
            transition: var(--transition);
        }

        .report-card:hover .report-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .report-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--medical-gray-800);
            margin-bottom: 1rem;
            text-align: center;
        }

        .report-description {
            color: var(--medical-gray-600);
            text-align: center;
            margin-bottom: 1.5rem;
            flex-grow: 1;
        }

        .report-features {
            background: var(--medical-gray-50);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .report-features small {
            color: var(--medical-gray-500);
            line-height: 1.6;
        }

        /* Botones médicos */
        .btn-medical {
            border-radius: 8px;
            font-weight: 600;
            padding: 0.75rem 1.5rem;
            transition: var(--transition);
            border: none;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.875rem;
        }

        .btn-medical-primary {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
            color: var(--medical-white);
            box-shadow: var(--shadow-md);
        }

        .btn-medical-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: var(--medical-white);
        }

        /* Colores específicos para cada tipo de reporte */
        .report-citas .report-icon {
            background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
        }

        .report-pacientes .report-icon {
            background: linear-gradient(135deg, var(--medical-success) 0%, #34d399 100%);
        }

        .report-medicos .report-icon {
            background: linear-gradient(135deg, var(--medical-info) 0%, #38bdf8 100%);
        }

        .report-financieros .report-icon {
            background: linear-gradient(135deg, var(--medical-warning) 0%, #fbbf24 100%);
        }

        .report-administrativos .report-icon {
            background: linear-gradient(135deg, var(--medical-gray-600) 0%, var(--medical-gray-500) 100%);
        }

        .report-personalizados .report-icon {
            background: linear-gradient(135deg, var(--medical-danger) 0%, #f87171 100%);
        }
    </style>
</head>
<body>
    <!-- Header Médico Moderno -->
    <div class="medical-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <div class="d-flex align-items-center mb-3">
                        <div class="me-4">
                            <i class="fas fa-chart-line fa-3x"></i>
                        </div>
                        <div>
                            <h1 class="mb-2">Centro de Reportes Médicos</h1>
                            <p class="lead mb-0">Sistema integral de análisis y reportes para consultorio médico</p>
                        </div>
                    </div>

                    <div class="d-flex align-items-center">
                        <div class="me-4">
                            <i class="fas fa-hospital-user me-2"></i>
                            <span><?php echo $empresa ? htmlspecialchars($empresa['NOMBRE']) : 'Consultorio Médico'; ?></span>
                        </div>
                        <div>
                            <i class="fas fa-calendar-alt me-2"></i>
                            <span><?php echo date('d/m/Y H:i'); ?></span>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 text-end">
                    <nav aria-label="breadcrumb" class="mb-3">
                        <ol class="breadcrumb medical-breadcrumb justify-content-end">
                            <li class="breadcrumb-item">
                                <a href="index.php">
                                    <i class="fas fa-home me-1"></i>Inicio
                                </a>
                            </li>
                            <li class="breadcrumb-item active">
                                <i class="fas fa-chart-bar me-1"></i>Reportes
                            </li>
                        </ol>
                    </nav>

                    <div class="d-flex align-items-center justify-content-end">
                        <div class="me-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-white bg-opacity-20 rounded-circle p-2 me-2">
                                    <i class="fas fa-user-md"></i>
                                </div>
                                <div class="text-start">
                                    <div class="fw-bold"><?php echo $usuario; ?></div>
                                    <small class="opacity-75"><?php echo ucfirst($rol); ?></small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Estadísticas Médicas Rápidas -->
    <div class="container mb-5">
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-center mb-1">
                    <i class="fas fa-chart-pulse me-2 text-primary"></i>
                    Estadísticas en Tiempo Real
                </h2>
                <p class="text-center text-muted mb-4">
                    Resumen ejecutivo de la actividad del consultorio
                </p>
            </div>
        </div>

        <div class="row g-4 mb-5">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="card-body">
                        <div class="stats-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stats-number" id="total-pacientes">-</div>
                        <div class="stats-label">Total Pacientes</div>
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-user-plus me-1"></i>
                                <span id="pacientes-nuevos">0</span> nuevos este mes
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="card-body">
                        <div class="stats-icon" style="background: linear-gradient(135deg, var(--medical-success) 0%, #34d399 100%);">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stats-number" id="citas-hoy">-</div>
                        <div class="stats-label">Citas de Hoy</div>
                        <div class="mt-2">
                            <small class="text-success">
                                <i class="fas fa-arrow-up me-1"></i>
                                <span id="citas-hoy-cambio">+0%</span> vs ayer
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="card-body">
                        <div class="stats-icon" style="background: linear-gradient(135deg, var(--medical-warning) 0%, #fbbf24 100%);">
                            <i class="fas fa-file-invoice-dollar"></i>
                        </div>
                        <div class="stats-number" id="ingresos-mes">-</div>
                        <div class="stats-label">Ingresos del Mes</div>
                        <div class="mt-2">
                            <small class="text-warning">
                                <i class="fas fa-coins me-1"></i>
                                <span id="ingresos-cambio">+0%</span> vs mes anterior
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card">
                    <div class="card-body">
                        <div class="stats-icon" style="background: linear-gradient(135deg, var(--medical-info) 0%, #38bdf8 100%);">
                            <i class="fas fa-notes-medical"></i>
                        </div>
                        <div class="stats-number" id="historias-mes">-</div>
                        <div class="stats-label">Historias del Mes</div>
                        <div class="mt-2">
                            <small class="text-info">
                                <i class="fas fa-file-medical me-1"></i>
                                Registros médicos
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container">

        <!-- Categorías de Reportes Médicos -->
        <div class="row mb-4">
            <div class="col-12">
                <h3 class="text-center mb-1">
                    <i class="fas fa-folder-open me-2 text-primary"></i>
                    Categorías de Reportes
                </h3>
                <p class="text-center text-muted mb-4">
                    Seleccione el tipo de reporte que desea generar
                </p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Reportes de Citas -->
            <div class="col-lg-4 col-md-6">
                <div class="report-card report-citas">
                    <div class="card-body">
                        <div class="report-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <h5 class="report-title">Reportes de Citas</h5>
                        <p class="report-description">
                            Análisis completo de citas médicas, horarios, asistencia y productividad del consultorio
                        </p>
                        <div class="d-grid">
                            <a href="backend/reportes/citas_reportes.php" class="btn btn-medical btn-medical-primary">
                                <i class="fas fa-chart-bar me-2"></i>Ver Reportes
                            </a>
                        </div>
                        <div class="report-features">
                            <small>
                                <i class="fas fa-check me-1 text-success"></i> Citas por día/semana/mes<br>
                                <i class="fas fa-check me-1 text-success"></i> Análisis de asistencia<br>
                                <i class="fas fa-check me-1 text-success"></i> Productividad por doctor<br>
                                <i class="fas fa-check me-1 text-success"></i> Horarios más solicitados
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reportes de Pacientes -->
            <div class="col-lg-4 col-md-6">
                <div class="report-card report-pacientes">
                    <div class="card-body">
                        <div class="report-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="report-title">Reportes de Pacientes</h5>
                        <p class="report-description">
                            Demografía, registros, actividad y análisis detallado de la base de pacientes
                        </p>
                        <div class="d-grid">
                            <a href="backend/reportes/pacientes_reportes.php" class="btn btn-medical btn-medical-primary">
                                <i class="fas fa-user-chart me-2"></i>Ver Reportes
                            </a>
                        </div>
                        <div class="report-features">
                            <small>
                                <i class="fas fa-check me-1 text-success"></i> Demografía de pacientes<br>
                                <i class="fas fa-check me-1 text-success"></i> Tendencias de registro<br>
                                <i class="fas fa-check me-1 text-success"></i> Actividad y retención<br>
                                <i class="fas fa-check me-1 text-success"></i> Análisis por ARS
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reportes Médicos -->
            <div class="col-lg-4 col-md-6">
                <div class="report-card report-medicos">
                    <div class="card-body">
                        <div class="report-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <h5 class="report-title">Reportes Médicos</h5>
                        <p class="report-description">
                            Historias clínicas, exámenes, diagnósticos y análisis de tratamientos médicos
                        </p>
                        <div class="d-grid">
                            <a href="backend/reportes/medicos_reportes.php" class="btn btn-medical btn-medical-primary">
                                <i class="fas fa-notes-medical me-2"></i>Ver Reportes
                            </a>
                        </div>
                        <div class="report-features">
                            <small>
                                <i class="fas fa-check me-1 text-success"></i> Exámenes físicos<br>
                                <i class="fas fa-check me-1 text-success"></i> Resultados de laboratorio<br>
                                <i class="fas fa-check me-1 text-success"></i> Diagnósticos frecuentes<br>
                                <i class="fas fa-check me-1 text-success"></i> Tratamientos prescritos
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reportes Financieros -->
            <div class="col-lg-4 col-md-6">
                <div class="report-card report-financieros">
                    <div class="card-body">
                        <div class="report-icon">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h5 class="report-title">Reportes Financieros</h5>
                        <p class="report-description">
                            Facturación, ingresos, pagos y análisis financiero completo del consultorio
                        </p>
                        <div class="d-grid">
                            <a href="backend/reportes/financieros_reportes.php" class="btn btn-medical btn-medical-primary">
                                <i class="fas fa-dollar-sign me-2"></i>Ver Reportes
                            </a>
                        </div>
                        <div class="report-features">
                            <small>
                                <i class="fas fa-check me-1 text-success"></i> Ingresos por período<br>
                                <i class="fas fa-check me-1 text-success"></i> Estado de pagos<br>
                                <i class="fas fa-check me-1 text-success"></i> Análisis de ARS<br>
                                <i class="fas fa-check me-1 text-success"></i> Flujo de caja
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reportes Administrativos -->
            <div class="col-lg-4 col-md-6">
                <div class="report-card report-administrativos">
                    <div class="card-body">
                        <div class="report-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h5 class="report-title">Reportes Administrativos</h5>
                        <p class="report-description">
                            Usuarios, sistema, sincronización y operaciones administrativas del consultorio
                        </p>
                        <div class="d-grid">
                            <a href="backend/reportes/administrativos_reportes.php" class="btn btn-medical btn-medical-primary">
                                <i class="fas fa-tasks me-2"></i>Ver Reportes
                            </a>
                        </div>
                        <div class="report-features">
                            <small>
                                <i class="fas fa-check me-1 text-success"></i> Actividad de usuarios<br>
                                <i class="fas fa-check me-1 text-success"></i> Estado de sincronización<br>
                                <i class="fas fa-check me-1 text-success"></i> Estadísticas del sistema<br>
                                <i class="fas fa-check me-1 text-success"></i> Logs de auditoría
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reportes Personalizados -->
            <div class="col-lg-4 col-md-6">
                <div class="report-card report-personalizados">
                    <div class="card-body">
                        <div class="report-icon">
                            <i class="fas fa-magic"></i>
                        </div>
                        <h5 class="report-title">Reportes Personalizados</h5>
                        <p class="report-description">
                            Crear reportes específicos según las necesidades particulares del consultorio
                        </p>
                        <div class="d-grid">
                            <a href="backend/reportes/personalizados_reportes.php" class="btn btn-medical btn-medical-primary">
                                <i class="fas fa-wand-magic-sparkles me-2"></i>Crear Reporte
                            </a>
                        </div>
                        <div class="report-features">
                            <small>
                                <i class="fas fa-check me-1 text-success"></i> Filtros personalizados<br>
                                <i class="fas fa-check me-1 text-success"></i> Exportar a Excel/PDF<br>
                                <i class="fas fa-check me-1 text-success"></i> Programar reportes<br>
                                <i class="fas fa-check me-1 text-success"></i> Plantillas guardadas
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Acciones Rápidas Médicas -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card" style="background: linear-gradient(135deg, var(--medical-white) 0%, var(--medical-gray-50) 100%); border: 1px solid var(--medical-gray-200); border-radius: var(--border-radius); box-shadow: var(--shadow-md);">
                    <div class="card-header" style="background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%); color: var(--medical-white); border-radius: calc(var(--border-radius) - 1px) calc(var(--border-radius) - 1px) 0 0;">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Reportes Rápidos
                        </h5>
                        <small class="opacity-75">Genere reportes instantáneos con un solo clic</small>
                    </div>
                    <div class="card-body p-4">
                        <div class="row g-3">
                            <div class="col-lg-3 col-md-6">
                                <button class="btn w-100 py-3"
                                        style="background: linear-gradient(135deg, var(--medical-primary) 0%, var(--medical-secondary) 100%);
                                               color: var(--medical-white);
                                               border: none;
                                               border-radius: 8px;
                                               transition: var(--transition);
                                               box-shadow: var(--shadow);"
                                        onclick="generarReporteRapido('citas_hoy')"
                                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='var(--shadow-lg)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow)'">
                                    <i class="fas fa-calendar-day fa-lg mb-2 d-block"></i>
                                    <div class="fw-bold">Citas de Hoy</div>
                                    <small class="opacity-75">Reporte diario</small>
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <button class="btn w-100 py-3"
                                        style="background: linear-gradient(135deg, var(--medical-success) 0%, #34d399 100%);
                                               color: var(--medical-white);
                                               border: none;
                                               border-radius: 8px;
                                               transition: var(--transition);
                                               box-shadow: var(--shadow);"
                                        onclick="generarReporteRapido('pacientes_nuevos')"
                                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='var(--shadow-lg)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow)'">
                                    <i class="fas fa-user-plus fa-lg mb-2 d-block"></i>
                                    <div class="fw-bold">Pacientes Nuevos</div>
                                    <small class="opacity-75">Últimos registros</small>
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <button class="btn w-100 py-3"
                                        style="background: linear-gradient(135deg, var(--medical-warning) 0%, #fbbf24 100%);
                                               color: var(--medical-white);
                                               border: none;
                                               border-radius: 8px;
                                               transition: var(--transition);
                                               box-shadow: var(--shadow);"
                                        onclick="generarReporteRapido('ingresos_mes')"
                                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='var(--shadow-lg)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow)'">
                                    <i class="fas fa-money-bill-wave fa-lg mb-2 d-block"></i>
                                    <div class="fw-bold">Ingresos del Mes</div>
                                    <small class="opacity-75">Reporte financiero</small>
                                </button>
                            </div>
                            <div class="col-lg-3 col-md-6">
                                <button class="btn w-100 py-3"
                                        style="background: linear-gradient(135deg, var(--medical-info) 0%, #38bdf8 100%);
                                               color: var(--medical-white);
                                               border: none;
                                               border-radius: 8px;
                                               transition: var(--transition);
                                               box-shadow: var(--shadow);"
                                        onclick="generarReporteRapido('resumen_general')"
                                        onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='var(--shadow-lg)'"
                                        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow)'">
                                    <i class="fas fa-chart-line fa-lg mb-2 d-block"></i>
                                    <div class="fw-bold">Resumen General</div>
                                    <small class="opacity-75">Dashboard completo</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer Médico -->
    <footer class="mt-5 py-5" style="background: linear-gradient(135deg, var(--medical-gray-50) 0%, var(--medical-white) 100%); border-top: 1px solid var(--medical-gray-200);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-hospital fa-2x text-primary"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">Centro de Reportes Médicos</h6>
                            <small class="text-muted">
                                Sistema integral de análisis para consultorio médico
                            </small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="d-flex justify-content-end gap-3">
                        <a href="index.php" class="btn btn-medical btn-medical-primary">
                            <i class="fas fa-home me-2"></i>Volver al Dashboard
                        </a>
                        <button class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>Imprimir Página
                        </button>
                    </div>
                </div>
            </div>
            <hr class="my-4" style="border-color: var(--medical-gray-300);">
            <div class="row">
                <div class="col-12 text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Sistema seguro y confiable para la gestión médica
                        <span class="mx-2">•</span>
                        <i class="fas fa-clock me-1"></i>
                        Última actualización: <?php echo date('d/m/Y H:i'); ?>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Cargar estadísticas rápidas al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            cargarEstadisticasRapidas();
        });

        function cargarEstadisticasRapidas() {
            fetch('backend/reportes/api/estadisticas_rapidas.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-pacientes').textContent = data.total_pacientes || '0';
                    document.getElementById('citas-hoy').textContent = data.citas_hoy || '0';
                    document.getElementById('ingresos-mes').textContent = 'RD$' + (data.ingresos_mes || '0');
                    document.getElementById('historias-mes').textContent = data.historias_mes || '0';
                })
                .catch(error => {
                    console.error('Error cargando estadísticas:', error);
                });
        }

        function generarReporteRapido(tipo) {
            // Mostrar loading
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generando...';
            btn.disabled = true;

            // Generar reporte
            fetch('backend/reportes/api/reporte_rapido.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tipo: tipo })
            })
            .then(response => response.blob())
            .then(blob => {
                // Crear enlace de descarga
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `reporte_${tipo}_${new Date().toISOString().split('T')[0]}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
            })
            .catch(error => {
                console.error('Error generando reporte:', error);
                alert('Error al generar el reporte');
            })
            .finally(() => {
                // Restaurar botón
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        }
    </script>
</body>
</html>
